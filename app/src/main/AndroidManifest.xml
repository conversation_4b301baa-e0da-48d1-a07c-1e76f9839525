<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- https://stackoverflow.com/a/39669092 -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.hardware.sensor.proximity" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />

    <uses-permission android:name="android.permission.READ_PHONE_STATE" tools:node="remove" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:name=".application.DuaApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_label"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        tools:replace="android:supportsRtl,android:allowBackup"
        android:theme="@style/DayNightAppTheme.Starting"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning"
        tools:targetApi="n">

        <activity
            android:name=".rewards.RewardsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme" />

        <activity
            android:name=".disabled.DisabledActivity"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme"
            android:exported="false" />

        <activity
            android:name="com.duaag.android.profile_builder.ProfileBuilderActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme" />

        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="face,ocr" />


        <service
            android:name=".calls.services.CallService"
            android:enabled="true"
            android:foregroundServiceType="microphone|camera|connectedDevice"
            android:exported="false" />

        <!-- Trigger Google Play services to install the backported photo picker module. -->
        <service android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false"
            tools:ignore="MissingClass">
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>
            <meta-data android:name="photopicker_activity:0:required" android:value="" />
        </service>

        <activity
            android:name=".calls.CallActivity"
            android:label="@string/title_activity_call_activity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:showOnLockScreen="true"
            android:showWhenLocked="true"
            android:theme="@style/FullScreenDayNightAppTheme" />

        <activity
            android:name=".premium_subscription.PremiumActivity"
            android:label="@string/title_activity_premium_activity"
            android:screenOrientation="portrait"
            android:launchMode="singleInstance"
            android:theme="@style/FullScreenDayNightAppTheme" />

        <activity
            android:name=".signInWithSpotted.SignInWithSpottedActivity"
            android:label="@string/title_activity_signUpWithSpotted_activity"
            android:screenOrientation="portrait"
            android:launchMode="singleInstance"
            android:theme="@style/FullScreenDayNightAppTheme" />

        <activity
            android:name=".image_verification.ImageVerificationActivity"
            android:label="@string/title_activity_image_verification"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreenDayNightAppTheme" />

        <activity
            android:name=".change_location.ChangeLocationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme" />

        <activity
            android:name=".manage_pictures.ManagePicturesActivity"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme" />

        <activity
            android:name=".settings.SettingsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".profile_new.editprofile.EditProfileActivity"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".settings.SettingsWebViewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme" />

        <activity
            android:name=".home.HomeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme" />

        <activity
            android:name=".launcher.SplashActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:exported="true"
            android:theme="@style/DayNightAppTheme.Starting">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="@string/dynamic_link_host"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter  android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https"
                    android:host="dua.onelink.me" />
            </intent-filter>


            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="https"
                    android:host="www.dua.com"
                    android:pathPrefix="/app/ticket" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="duacom" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="https"
                    android:host="app.dua-dev-app.com"
                    android:pathPrefix="/sunny-hill.html" />
            </intent-filter>
        </activity>

        <activity
            android:name=".login.StartActivity"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".signup.SignUpActivity"
            android:screenOrientation="portrait"
            android:theme="@style/DayNightAppTheme"
            android:windowSoftInputMode="adjustResize" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />

        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token"/>

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="true"
            android:label="@string/app_name" />

        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <service
            android:name=".firebase.DuaFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </service>

        <service
            android:name=".firebase.service.ReplyMessageService"
            android:directBootAware="true"
            android:enabled="true"
            android:exported="false" />

        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${file_provider}"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="app.rive.runtime.kotlin.RiveInitializer"
                android:value="androidx.startup" />
        </provider>

        <!-- The below code is for android OS version below N -->
        <receiver
            android:name=".broadcasts.ConnectivityReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <receiver android:name=".calls.broadast_receivers.CallNotificationActionReceiver" />

        <receiver
            android:name=".crosspath.data.geofence.CrossPathGeofenceBroadcastReceiver"
            android:enabled="true" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_dua_notification" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/pink_500" />
        <meta-data
            android:name="com.google.android.places.API_KEY"
            android:value="@string/maps_api_key" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/maps_api_key" />
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/ad_mob_app_id"/>
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data android:name="applovin.sdk.key"
            android:value="@string/applovin_sdk_key"/>
        <meta-data
            android:name="CLEVERTAP_ACCOUNT_ID"
            android:value="@string/clevertap_account_id"/>
        <meta-data
            android:name="CLEVERTAP_TOKEN"
            android:value="@string/clevertap_token"/>
        <meta-data
            android:name="CLEVERTAP_NOTIFICATION_ICON"
            android:value="ic_dua_notification"/> <!-- name of your file in the drawable directory without the file extension. -->
        <meta-data
            android:name="CLEVERTAP_INAPP_EXCLUDE"
            android:value="SplashActivity" />
        <meta-data
            android:name="com.facebook.sdk.AutoInitEnabled"
            android:value="true" />
        <meta-data
            android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
            android:value="true" />

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource" />

        <meta-data
            android:name="CLEVERTAP_IDENTIFIER"
            android:value="Identity" />

    </application>

</manifest>