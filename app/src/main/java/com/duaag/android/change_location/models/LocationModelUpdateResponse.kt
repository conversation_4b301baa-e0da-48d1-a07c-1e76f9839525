package com.duaag.android.change_location.models

import android.location.Location
import android.os.Parcelable
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.change_location.viewmodels.MapViewModel.Companion.CURRENT_LOCATION_ID
import com.duaag.android.user.DuaAccount
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.*
import kotlin.math.ln


@Parcelize
data class LocationModelUpdateResponse(
    val id: String = UUID.randomUUID().toString(),
    @SerializedName("address")
    val address: String?,
    @SerializedName("latitude")
    val latitude: Double?,
    @SerializedName("longitude")
    val longitude: Double?,
    var time: Long? = System.currentTimeMillis(),
    var isSelected: Boolean = false,
    var isCurrentLocation: Boolean = false
) : Parcelable {

    fun toLocationModel(): LocationModel {
        val city = this.address?.substringBefore(",") ?: ""
        val country = this.address?.substringAfter(",")?.trim() ?: ""
        val latitude = this.latitude.toString()
        val longitude = this.longitude.toString()
        val model = LocationModel(System.currentTimeMillis(), city, country, latitude, longitude)
        return model
    }

    companion object {
        fun getCurrentLocationModel(isSelected: Boolean): LocationModelUpdateResponse {
            return LocationModelUpdateResponse(
                CURRENT_LOCATION_ID,
                DuaApplication.instance.getString(R.string.my_current_locat),
                DuaAccount.latitude ?: 0.0,
                DuaAccount.longitude ?: 0.0,
                isSelected = isSelected,
                isCurrentLocation = true
            )
        }
    }
}