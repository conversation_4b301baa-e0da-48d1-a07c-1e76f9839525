package com.duaag.android.change_location.fragments

import android.content.Context
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.base.LocationBaseActivity
import com.duaag.android.base.models.UserModel
import com.duaag.android.change_location.ChangeLocationActivity
import com.duaag.android.change_location.adapters.LocationsAdapter
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.viewmodels.MyLocationsViewModel
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.databinding.LocationsListFragmentBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.utils.setOnSingleClickListener
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import timber.log.Timber
import javax.inject.Inject


class LocationsListFragment : Fragment() {

    companion object {
        const val TAG = "MyLocationsFragment"
        const val ZOOM_LEVEL = 10f

        fun newInstance() = LocationsListFragment()
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val changeLocationViewModel by viewModels<MyLocationsViewModel>({ activity as ChangeLocationActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as ChangeLocationActivity }) { viewModelFactory }
    private var _binding: LocationsListFragmentBinding? = null
    private val binding get() = _binding!!

    var map: GoogleMap? = null
    var currentLocation: LocationModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val user = requireActivity().intent.extras?.getParcelable<UserModel>("UserModel")
        val latitude = user?.profile?.latitude
        val longitude = user?.profile?.longitude
        val city = user?.profile?.address?.substringBefore(",") ?: ""
        val country = user?.profile?.address?.substringAfter(",")?.trim() ?: ""
        currentLocation = LocationModel(System.currentTimeMillis(),city, country, latitude.toString(), longitude.toString())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = LocationsListFragmentBinding.inflate(inflater)

        initMap()

        initView()

        setObservers()

        setListeners()

        setFlightCount()

        return binding.root
    }

    private fun initView(){
        binding.lastLocationsRv.layoutManager = LinearLayoutManager(requireContext())
        binding.lastLocationsRv.adapter = LocationsAdapter(changeLocationViewModel, LocationsAdapter.LocationClickListener{ location ->
            Timber.tag(TAG).d("Last Locations clicked: $location")
            location?.let {loc->
                val city = loc.city
                val country = loc.country
                val latitude = loc.latitude
                val longitude = loc.longitude
                val chosenLocation = LocationModel(city = city, country = country, latitude = latitude, longitude = longitude)
                val extras = Bundle().apply { putParcelable("chosen_location", chosenLocation) }
                findNavController().navigate(R.id.action_myLocationsFragment_to_mapFragment, extras)
                firebaseLogEvent(FirebaseAnalyticsEventsName.LAST_LOCATIONS)
            }
        })

        binding.popularLocationsRv.layoutManager = LinearLayoutManager(requireContext())
        binding.popularLocationsRv.adapter = LocationsAdapter(changeLocationViewModel, LocationsAdapter.LocationClickListener{ location ->
            location?.let {loc->
                val city = loc.city
                val country = loc.country
                val latitude = loc.latitude
                val longitude = loc.longitude
                val chosenLocation = LocationModel(city = city, country = country, latitude = latitude, longitude = longitude)
                val extras = Bundle().apply { putParcelable("chosen_location", chosenLocation) }
                findNavController().navigate(R.id.action_myLocationsFragment_to_mapFragment, extras)
                firebaseLogEvent(FirebaseAnalyticsEventsName.POPULAR_LOCATIONS)
            }

        })
    }

    private fun initMap(){
        val mapFragment = childFragmentManager.findFragmentById(R.id.map) as SupportMapFragment?
        mapFragment?.getMapAsync { googleMap ->
            map = googleMap
            with(googleMap) {
                uiSettings.isScrollGesturesEnabled = false
                uiSettings.isZoomGesturesEnabled = false
                val latLng = LatLng(currentLocation?.latitude?.toDouble()!!, currentLocation?.longitude?.toDouble()!!)
                moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, ZOOM_LEVEL))
                addMarker(MarkerOptions().position(latLng))
            }
        }
    }

    private fun setObservers() {
        changeLocationViewModel.lastItems.observe(viewLifecycleOwner) {
            val profile = homeViewModel.userProfile.value?.profile
            it.removeAll{ it.address == profile?.address || it.address == profile?.actualAddress }

            if(it.isNullOrEmpty()) {
                binding.lastLocationText.visibility = View.GONE
                binding.lastLocationsRv.visibility = View.GONE
            } else {
                binding.lastLocationText.visibility = View.VISIBLE
                binding.lastLocationsRv.visibility = View.VISIBLE
            }

            (binding.lastLocationsRv.adapter as LocationsAdapter).setData(it.take(2))
        }

        changeLocationViewModel.popularLocations.observe(viewLifecycleOwner) {
            if(it.isNullOrEmpty()) {
                binding.popularLocationsText.visibility = View.GONE
                binding.popularLocationsRv.visibility = View.GONE
            } else {
                binding.popularLocationsText.visibility = View.VISIBLE
                binding.popularLocationsRv.visibility = View.VISIBLE
            }

            val popularLocations = it.toMutableList()
            popularLocations.removeAll { it.address == homeViewModel.userProfile.value?.profile?.address }

            (binding.popularLocationsRv.adapter as LocationsAdapter).setData(popularLocations)
        }

        changeLocationViewModel.onFlyChange.observe(viewLifecycleOwner) {
            setFlightCount()
        }

        homeViewModel.userProfile.observe(viewLifecycleOwner) {
            binding.address.text = it.profile.address

            if(it.profile.actualAddress == null){
                binding.myCurrentLocationText.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                binding.currentLocation.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                binding.currentLocationIcon.setImageResource(R.drawable.ic_location_button_disabled)
                binding.locationIcon.setImageResource(R.drawable.ic_location_pin)
            }else {
                binding.myCurrentLocationText.setTextColor(resources.getColor(R.color.title_primary, requireActivity().theme))
                binding.currentLocation.setTextColor(resources.getColor(R.color.description_primary, requireActivity().theme))
                binding.currentLocationIcon.setImageResource(R.drawable.ic_location_button)
                binding.locationIcon.setImageResource(R.drawable.ic_plane_small)
            }

            binding.currentLocation.text = it.profile.actualAddress ?: it.profile.address
        }
    }

    private fun setListeners() {
        binding.addLocation.setOnSingleClickListener {
            findNavController().navigate(R.id.action_myLocationsFragment_to_locationSearchFragment)
        }

        binding.closeButton.setOnSingleClickListener { activity?.finish() }

        binding.myLocation.setOnSingleClickListener {
            val locationActivity = (activity as ChangeLocationActivity)
            if(!locationActivity.isLocationPermissionEnabled) {
                if(locationActivity.shouldProvideRationale())
                    locationActivity.provideRationale(ClevertapEventSourceValues.CHANGE_LOCATION.value)
                else
                    locationActivity.requestPermissions(ClevertapEventSourceValues.CHANGE_LOCATION.value)
            } else {
                onMyLocationClicked()
            }
        }
    }

    fun onMyLocationClicked() {
        val user = homeViewModel.userProfile.value

        if(user?.profile?.actualAddress != null){
            val profile = homeViewModel.userProfile.value?.profile
            profile?.let {
                val city = it.actualAddress?.substringBefore(",") ?: ""
                val country = it.actualAddress?.substringAfter(",")?.trim() ?: ""
                val latitude = it.actualLatitude.toString()
                val longitude = it.actualLongitude.toString()
                val model = LocationModel(System.currentTimeMillis(), city, country, latitude = latitude, longitude = longitude)
                val extras = Bundle().apply { putParcelable("chosen_location", model) }
                findNavController().navigate(R.id.action_myLocationsFragment_to_mapFragment, extras)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as ChangeLocationActivity).changeLocationComponent.inject(this)
        Timber.tag("VIEWMODEL").d(changeLocationViewModel.toString())
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String?>,
        grantResults: IntArray
    ) {
        if (requestCode == LocationBaseActivity.REQUEST_PERMISSIONS_REQUEST_CODE) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                onMyLocationClicked()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        map = null
        _binding = null
    }


    private fun setFlightCount() {
        if (homeViewModel.userProfile.value?.premiumType != null) {
            binding.locationCount.text = "∞"
        } else {
            binding.locationCount.text = "${changeLocationViewModel.getRemainingFlights()}"
        }
    }

}
