package com.duaag.android.change_location.fragments

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.change_location.ChangeLocationActivity
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.models.LocationType
import com.duaag.android.change_location.viewmodels.MapViewModel
import com.duaag.android.change_location.viewmodels.MyLocationsViewModel
import com.duaag.android.clevertap.*
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.databinding.ConfirmLocationFragmentBinding
import com.duaag.android.exceptions.LimitReachedException
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.*
import com.duaag.android.views.OutOfImpressionsDialog
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import timber.log.Timber
import javax.inject.Inject


class ConfirmLocationFragment : Fragment() {


    companion object{
        const val ZOOM_LEVEL = 10f
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val changeLocationViewModel by viewModels<MyLocationsViewModel>({ activity as ChangeLocationActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as ChangeLocationActivity }) { viewModelFactory }
    private val mapViewModel by viewModels<MapViewModel>({ activity as ChangeLocationActivity }) { viewModelFactory }

    private var _binding: ConfirmLocationFragmentBinding? = null
    private val binding get() = _binding!!

    var locationModel: LocationModel? = null
    var location = LatLng(DuaAccount.latitude ?: 0.0, DuaAccount.longitude ?: 0.0)

    var map: GoogleMap? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as ChangeLocationActivity).changeLocationComponent.inject(this)
        Timber.tag("VIEWMODEL").d(changeLocationViewModel.toString())
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        _binding = ConfirmLocationFragmentBinding.inflate(inflater)

        initMap()

        initObservers()

        setListeners()

        setInitialLocation()

        setFlightCount()

        return binding.root
    }

    private fun initMap() {
        val mapFragment = childFragmentManager.findFragmentById(R.id.map) as SupportMapFragment?
        mapFragment?.getMapAsync { googleMap ->
            map = googleMap
            with(googleMap) {
                uiSettings.isScrollGesturesEnabled = false
                uiSettings.isZoomControlsEnabled = true
                uiSettings.isZoomGesturesEnabled = false
                moveCamera(com.google.android.gms.maps.CameraUpdateFactory.newLatLngZoom(location, ZOOM_LEVEL))
                addMarker(com.google.android.gms.maps.model.MarkerOptions().position(location))
            }
        }
    }

    private fun setListeners(){
        binding.backBtn.setOnSingleClickListener {
            requireActivity().onBackPressed()
        }

        binding.cancelBtn.setOnSingleClickListener {
            requireActivity().onBackPressed()
        }

        binding.confirmLocation.setOnSingleClickListener { confirmLocationButton ->
            confirmLocationButton.isClickable = false
            if (homeViewModel.hasRemainingFlights()) {
                locationModel?.let { location ->
                    val profile = homeViewModel.userProfile.value?.profile
                    val city = (profile?.actualAddress ?: profile?.address)?.substringBefore(",") ?: ""
                    val country = (profile?.actualAddress ?: profile?.address)?.substringAfter(",")?.trim() ?: ""
                    val latitude = (profile?.actualLatitude ?: profile?.latitude).toString()
                    val longitude = (profile?.actualLongitude ?: profile?.longitude).toString()
                    val currentActualLocation = LocationModel(System.currentTimeMillis(), city, country, latitude = latitude, longitude = longitude)
                    val locationType = if(currentActualLocation.latitude == location.latitude && currentActualLocation.longitude == location.longitude) LocationType.NORMAL else LocationType.FLY

                    changeLocationViewModel.updateLocationFlow(location, locationType, currentActualLocation)

                    toggleLoadingVisibility(true)
                }
            } else {
                confirmLocationButton.isClickable = true
                handleOutOfFlights()
            }
        }
    }

    private fun setInitialLocation() {
        val initialLocation = arguments?.getParcelable<LocationModel>("chosen_location")
        initialLocation?.let { mapViewModel.setLocation(it) }
    }

    private fun goToPremium(){
        requireActivity().openPremiumPaywall(
            viewPagerStartPosition = BenefitsPremiumAdapter.PremiumPaywallList.FLIGHTS_ITEM,
            ClevertapEventSourceValues.CHANGE_LOCATION,
            placementId = PurchaselyPlacement.OUT_OF_FLIGHTS.id,
            userModel = homeViewModel.userProfile.value
        )
    }


    private fun initObservers() {

        mapViewModel.chosenLocation.observe(viewLifecycleOwner, Observer {
            it?.let {
                locationModel = it
                val latitude = it.latitude.takeIf { str -> str.isNotBlank() }?.toDouble() ?: 0.0
                val longitude = it.longitude.takeIf { str -> str.isNotBlank() }?.toDouble() ?: 0.0
                location = LatLng(latitude, longitude)
                binding.startLocationName.text = homeViewModel.userProfile.value?.profile?.address
                binding.endLocationName.text = locationModel?.address

                if(homeViewModel.userProfile.value?.profile?.actualAddress == locationModel?.address) {
                    binding.startLocationIcon.setImageResource(R.drawable.ic_plane_small)
                    binding.endLocationIcon.setImageResource(R.drawable.ic_location)
                }

                map?.let {
                    //replace marker
                    it.addMarker(com.google.android.gms.maps.model.MarkerOptions().position(location))

                    //move to coordinates
                    it.moveCamera(com.google.android.gms.maps.CameraUpdateFactory.newLatLngZoom(location, ZOOM_LEVEL))
                }
                Timber.tag("PLACES").d("Place: ${it.address}, ${it.time}")
            }

        })

        changeLocationViewModel.limitReached.observe(viewLifecycleOwner) {
            toggleLoadingVisibility(false)
            binding.confirmLocation.isClickable = true
            when (it) {
                is LimitReachedException -> {
                    changeLocationViewModel.setFlyingCounterResetTime()
                    handleOutOfFlights()
                }
                else -> {
                    ToastUtil.toast("An error occurred")
                }
            }
        }

        changeLocationViewModel.locationChangeSuccess.observe(viewLifecycleOwner) {
            toggleLoadingVisibility(false)

            val returnIntent = Intent()
            activity?.setResult(Activity.RESULT_OK, returnIntent)
            activity?.finish()
        }
    }

    private fun handleOutOfFlights() {
        when {
            DuaApplication.instance.getPremiumAvailable() -> {
                firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_CHANGELOCATION)
                goToPremium()
            }
            DuaApplication.instance.getBillingAvailable() -> {
                requireActivity().openPremiumPaywall(
                    eventSourceClevertap = ClevertapEventSourceValues.CHANGE_LOCATION,
                    placementId = PurchaselyPlacement.DYNAMIC_FLIGHT.id,
                    userModel = homeViewModel.userProfile.value
                )
            }
            else -> {
                val outOfImpressionsDialog = OutOfImpressionsDialog.newInstance(InteractionType.IS_FLYING)
                outOfImpressionsDialog.show(childFragmentManager, "outOfFlyingDialog")

                val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                sendClevertapEvent(
                    ClevertapEventEnum.OUT_OF_IMPRESSIONS_POPUP, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType
                    )
                )

                val eventSourceValue = ClevertapEventSourceValues.CHANGE_LOCATION.value

                sendClevertapEvent(
                    ClevertapEventEnum.INVITE_A_FRIEND_SCREENVIEW, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSourceValue))
            }
        }
    }

    private fun toggleLoadingVisibility(showLoading: Boolean = false) {
        if(showLoading) {
            binding.confirmLocation.visibility = View.INVISIBLE
            binding.progressBar.visibility = View.VISIBLE
        } else {
            binding.confirmLocation.visibility = View.VISIBLE
            binding.progressBar.visibility = View.GONE
        }
    }

    private fun setFlightCount() {
        if (homeViewModel.userProfile.value?.premiumType != null) {
            binding.locationCount.text = "∞"
        } else {
            binding.locationCount.text = "${changeLocationViewModel.getRemainingFlights()}"
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        map = null
        _binding = null
    }
}