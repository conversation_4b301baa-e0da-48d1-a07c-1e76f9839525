package com.duaag.android.change_location.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.Resource
import com.duaag.android.base.models.UserModel
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.models.LocationType
import com.duaag.android.change_location.repositories.LocationsRepository
import com.duaag.android.clevertap.*
import com.duaag.android.counters.data.models.CounterEntity
import com.duaag.android.counters.domain.GetUserCountersByConfNamesUseCase
import com.duaag.android.counters.domain.UpdateUserCounterUseCase
import com.duaag.android.di.ActivityScope
import com.duaag.android.exceptions.LimitReachedException
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.livedata.SingleLiveData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.lang.Exception
import javax.inject.Inject

@ActivityScope
class MyLocationsViewModel @Inject constructor(
    private val locationsRepository: LocationsRepository,
    val userRepository: UserRepository,
    private val getUserCountersByConfNamesUseCase: GetUserCountersByConfNamesUseCase,
    private val updateUserCounterUseCase: UpdateUserCounterUseCase
) : ViewModel() {

    companion object {
        const val CURRENT_LOCATION_ID = "-1"
    }

    //this observable is used to hold the items last locations
    private val _lastItems: MutableLiveData<ArrayList<LocationModel>> = MutableLiveData(arrayListOf())
    val lastItems: LiveData<ArrayList<LocationModel>>
        get() = _lastItems

    //this observable is used to hold popular locations
    private val _popularLocations: MutableLiveData<List<LocationModel>> = MutableLiveData(arrayListOf())
    val popularLocations: LiveData<List<LocationModel>>
        get() = _popularLocations

    private val _deleteUser: SingleLiveData<Boolean> = SingleLiveData()
    val deleteUser: LiveData<Boolean>
        get() = _deleteUser

    private val _onFlyChange: SingleLiveData<Boolean> = SingleLiveData()
    val onFlyChange: LiveData<Boolean>
        get() = _onFlyChange

    private val _locationChangeSuccess: SingleLiveData<Void> = SingleLiveData()
    val locationChangeSuccess: LiveData<Void>
        get() = _locationChangeSuccess

    private val _limitReached: SingleLiveData<Exception> = SingleLiveData()
    val limitReached: LiveData<Exception>
        get() = _limitReached

    val user = userRepository.user


    var flyingCounterEntity: CounterEntity? = null
    var flyingCount: Int = 0
    var flyingLimit: Int = 1

    fun userDelete() {
        _deleteUser.value = true
    }

    init {
        viewModelScope.launch(Dispatchers.IO) {
            val dbItems = locationsRepository.getLocations()

            val allLocations = arrayListOf<LocationModel>()
            allLocations.addAll(dbItems)

            withContext(Dispatchers.Main) {
                _lastItems.value = allLocations
            }
        }

        viewModelScope.launch(Dispatchers.IO) {
            locationsRepository.getTopCities()
                .catch { ex -> ex.printStackTrace() }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Resource.Success -> {
                                _popularLocations.value = it.data
                            }
                            Resource.Loading -> {}
                            else -> {}
                        }
                    }
                }
        }
    }

    fun getFlyingCounter(specificCounterName:String) {
        viewModelScope.launch(Dispatchers.IO) {
            val configurationNames = listOf(specificCounterName)
            getUserCountersByConfNamesUseCase.invoke(configurationNames)
                .catch { e ->
                    Timber.e(e)
                }
                .collect { list ->
                flyingCounterEntity = list.firstOrNull()
                if (flyingCounterEntity != null) {
                    withContext(Dispatchers.Main) {
                        setUserLimits(flyingCounterEntity?.total ?: 0, flyingCounterEntity?.configuration?.limit ?: 1)
                    }
                }
            }
        }

    }

    fun increaseFlyingCount() {
        flyingCount++
        viewModelScope.launch(Dispatchers.IO) {
            flyingCounterEntity?.let {
                updateUserCounterUseCase.invoke(it.copy(total = flyingCount))
            }
        }
        _onFlyChange.value = true
    }

    fun getRemainingFlights(): Int = flyingLimit - flyingCount

    /**
     * There has to be a total of 2 chosen locations stored in device
     */
    fun addLocationItem(locationModel: LocationModel) {
        val items = _lastItems.value
        items?.add(0, locationModel)

        getUsersCurrentFlyLocation()?.let { items?.add(0, it) }

        items?.let {
            var currentItems = ArrayList(it)
            if (items.any { it.address == locationModel.address}) {
                return
            }
            if (currentItems.size > 2) {
                currentItems = ArrayList(currentItems.take(2))
            }
            viewModelScope.launch(Dispatchers.IO) {
                locationsRepository.updateLocationsInDb(currentItems)
            }
            _lastItems.value = it
        }
    }

    fun setItems(items: ArrayList<LocationModel>) {
        _lastItems.value = items
    }


    fun updateUserLocationInDb(userModel: UserModel?) {
        viewModelScope.launch(Dispatchers.IO) {
            if (userModel != null) {
                userRepository.updateUserInDB(userModel)
            }
        }
    }

    fun setFlyingCounterResetTime() {
        user.value?.let {
            getFlyingCounter(it.counterConfigurationNames.flyCounterCN)
        }
    }

    fun setUserLimits(flyingCount: Int, flyingLimit: Int) {
        this.flyingCount = flyingCount
        this.flyingLimit = flyingLimit
        _onFlyChange.value = true
    }

    fun updateUserLocation(isCurrentLocation: Boolean, selectedLocation: LocationModel, currentLocation: LocationModel) {

        val model = user.value

        if (isCurrentLocation) {
            model?.profile?.address = selectedLocation.address
            model?.profile?.latitude = selectedLocation.latitude.toDouble()
            model?.profile?.longitude = selectedLocation.longitude.toDouble()
            model?.profile?.actualAddress = null
            model?.profile?.actualLatitude = null
            model?.profile?.actualLongitude = null
        } else {
            model?.profile?.address = selectedLocation.address
            model?.profile?.latitude = selectedLocation.latitude.toDouble()
            model?.profile?.longitude = selectedLocation.longitude.toDouble()
            model?.profile?.actualAddress = currentLocation.address
            model?.profile?.actualLatitude = currentLocation.latitude.toDouble()
            model?.profile?.actualLongitude = currentLocation.longitude.toDouble()
        }
        updateUserLocationInDb(model)

    }


    fun updateLocationFlow(locationModel: LocationModel, type: LocationType, currentLocation: LocationModel){
        viewModelScope.launch(Dispatchers.IO) {
            val coordinates = mapOf("latitude" to locationModel.latitude, "longitude" to locationModel.longitude)
            locationsRepository.updateLocationFlow(type.typeName, coordinates)
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        when (ex) {
                            is LimitReachedException -> {
                                setFlyingCounterResetTime()
                                _limitReached.value = ex
                            }
                            else -> {
                                ToastUtil.toast("An error occurred")
                            }
                        }
                    }
                }
                .collect {
                    withContext(Dispatchers.Main){
                        when(it){
                            is Resource.Success -> {
                                val premiumTypeValue = getPremiumTypeEventProperty(user.value)
                                if(type == LocationType.FLY) {
                                    increaseFlyingCount()
                                    if (flyingCount >= flyingLimit) {
                                        setFlyingCounterResetTime()
                                    }

                                    val changeLocationValue =
                                        if (popularLocations.value?.any { it.city == locationModel.city } == true) {
                                            CleverTapChangeLocationType.POPULAR_LOCATION.value
                                        } else if (lastItems.value?.any{ it.city == locationModel.city } == true){
                                            CleverTapChangeLocationType.LAST_LOCATION.value
                                        } else {
                                            CleverTapChangeLocationType.NEW_LOCATION.value
                                        }

                                    sendClevertapEvent(
                                        ClevertapEventEnum.CHANGE_LOCATION, mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                                            ClevertapEventPropertyEnum.CHANGE_LOCATION_TYPE.propertyName to changeLocationValue,
                                            ClevertapEventPropertyEnum.CITY_CHOSEN.propertyName to locationModel.address)
                                    )

                                    addLocationItem(locationModel)
                                } else {
                                    sendClevertapEvent(
                                        ClevertapEventEnum.CHANGE_LOCATION, mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                                            ClevertapEventPropertyEnum.CHANGE_LOCATION_TYPE.propertyName to CleverTapChangeLocationType.CURRENT_LOCATION.value,
                                            ClevertapEventPropertyEnum.CITY_CHOSEN.propertyName to locationModel.address)
                                    )
                                }

                                val city = locationModel.address
                                city.let { firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_LOCATION, mapOf("city" to city)) }

                                updateUserLocation(type == LocationType.NORMAL, it.data.toLocationModel(), currentLocation)
                                _locationChangeSuccess.call()
                            }
                            is Resource.Loading -> {}
                            is Resource.Error -> {}
                        }
                    }
                }

        }
    }

    fun getUsersCurrentFlyLocation(): LocationModel? {
        val profile = user.value?.profile
        return if(profile?.actualAddress != null){
            val city = profile.address?.substringBefore(",") ?: ""
            val country = profile.address?.substringAfter(",")?.trim() ?: ""
            val latitude = profile.latitude.toString()
            val longitude =  profile.longitude.toString()
            val currentFlyAddress = LocationModel(System.currentTimeMillis(), city, country, latitude = latitude, longitude = longitude)
            currentFlyAddress
        } else {
            null
        }
    }

}
