package com.duaag.android.change_location.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.models.LocationSearchModelItem
import com.duaag.android.change_location.repositories.LocationsRepository
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.livedata.SingleLiveData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.launch
import retrofit2.HttpException
import timber.log.Timber
import javax.inject.Inject

class MapViewModel @Inject constructor(private val locationsRepository: LocationsRepository,
                                       val userRepository: UserRepository) : ViewModel() {

    companion object {
        const val CURRENT_LOCATION_ID = "-1"
        const val TIMEOUT = 500L
        const val MIN_CHAR_LENGTH = 2
    }

    //this observable is used to hold the items in MyLocationFragment
    private val _items: MutableLiveData<ArrayList<LocationModel>> = MutableLiveData(arrayListOf())
    val items: LiveData<ArrayList<LocationModel>>
        get() = _items

    //this observable is used to hold the chosen location from Map
    private val _chosenLocation: SingleLiveData<LocationModel> = SingleLiveData()
    val chosenLocation: LiveData<LocationModel>
        get() = _chosenLocation

    fun setLocation(location: LocationModel) {
        _chosenLocation.value = location
    }

    init {
        viewModelScope.launch(Dispatchers.IO) {
            val dbItems = locationsRepository.getLocations()

            val allLocations = arrayListOf<LocationModel>()
            allLocations.addAll(dbItems)

            _items.postValue(allLocations)
        }
    }

    val query = MutableStateFlow("")
    private var _locations = query
            .debounce(TIMEOUT)
            .mapLatest {
                if (it.length >= MIN_CHAR_LENGTH) {
                    getLocations(it)
                } else {
                    emptyList()
                }
            }
            .catch { Timber.tag("AUTOCOMPLETE").d(it.message ?: "") }

    val locations = _locations.asLiveData()

    private suspend fun getLocations(query: String): List<LocationSearchModelItem> {
        val locations = viewModelScope.async {
            try {
                locationsRepository.autocomplete(query)
            } catch (e: Exception) {
                if (e is HttpException && e.code() == 404)
                    mutableListOf(LocationSearchModelItem.getEmptyLocation())
                else mutableListOf(LocationSearchModelItem.getErrorLocation())
            }
        }
        return locations.await()
    }

    fun searchLocation(query: String) {
        viewModelScope.launch {
            <EMAIL> = query
        }
    }
}
