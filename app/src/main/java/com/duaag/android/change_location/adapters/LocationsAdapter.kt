package com.duaag.android.change_location.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.viewmodels.MyLocationsViewModel
import com.duaag.android.databinding.ItemLocationBinding
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.calculateDistanceBetween

class LocationsAdapter(val viewModel: MyLocationsViewModel,
                       private val clickListener: LocationClickListener) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var items: ArrayList<LocationModel> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ItemLocationBinding = ItemLocationBinding.inflate(layoutInflater, parent, false)
        return LocationViewHolder(binding)
    }

    fun setData(data: List<LocationModel>) {
        val diffCallback = LocationDiffUtil(items, data)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        items.clear()
        items.addAll(data)
        diffResult.dispatchUpdatesTo(this)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as LocationViewHolder).bind(items[position])
    }

    inner class LocationViewHolder(val binding: ItemLocationBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION && position >= 0 && position < items.size) {
                    clickListener.onClick(items[position])
                }
            }
        }
        fun bind(model: LocationModel?) {
            val user = viewModel.user.value

            val myLatitude: Double = user?.profile?.actualLatitude ?: user?.profile?.latitude ?: DuaAccount.latitude ?: 0.0
            val myLongitude: Double  = user?.profile?.actualLongitude ?:user?.profile?.longitude ?: DuaAccount.longitude ?: 0.0

            val startLat = model?.latitude?.toDoubleOrNull() ?: myLatitude
            val startLon = model?.longitude?.toDoubleOrNull() ?: myLongitude

            val distance = calculateDistanceBetween(startLat, startLon, myLatitude, myLongitude).toInt()/1000

            binding.locationName.text = model?.address
            binding.distance.text = "${distance}km"
        }
    }

    class LocationDiffUtil(private val oldList: List<LocationModel>, private val newList: List<LocationModel>) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size


        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].time == newList[newItemPosition].time
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].latitude == newList[newItemPosition].latitude
                    && oldList[oldItemPosition].longitude == newList[newItemPosition].longitude
                    && oldList[oldItemPosition].time == newList[newItemPosition].time
                    && oldList[oldItemPosition].city == newList[newItemPosition].city
                    && oldList[oldItemPosition].country == newList[newItemPosition].country
        }

        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            return super.getChangePayload(oldItemPosition, newItemPosition)
        }
    }

    class LocationClickListener(val clickListener: (model: LocationModel?) -> Unit) {
        fun onClick(model: LocationModel) = clickListener(model)
    }
}