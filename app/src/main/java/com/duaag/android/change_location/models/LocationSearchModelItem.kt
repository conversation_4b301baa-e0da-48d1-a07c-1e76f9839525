package com.duaag.android.change_location.models
import com.google.gson.annotations.SerializedName

data class LocationSearchModelItem(
    @SerializedName("city")
    val city: String,
    @SerializedName("country")
    val country: String,
    @SerializedName("fullLocation")
    val fullLocation: String,
    @SerializedName("iso")
    val iso: String,
    @SerializedName("latitude")
    val latitude: String,
    @SerializedName("longitude")
    val longitude: String,
    var emptyLocation: Boolean = false,
    var isErrorItem: Boolean = false
){
    companion object {
        fun getEmptyLocation() = LocationSearchModelItem("", "", "", "", "", "", emptyLocation = true)
        fun getErrorLocation() = LocationSearchModelItem("", "", "", "", "", "", isErrorItem = true)
    }
}