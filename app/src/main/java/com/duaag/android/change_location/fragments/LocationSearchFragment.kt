package com.duaag.android.change_location.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.SearchView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.base.models.UserModel
import com.duaag.android.change_location.ChangeLocationActivity
import com.duaag.android.change_location.adapters.LocationsAdapter
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.viewmodels.MapViewModel
import com.duaag.android.change_location.viewmodels.MyLocationsViewModel
import com.duaag.android.databinding.LocationSearchFragmentBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import timber.log.Timber
import javax.inject.Inject


class LocationSearchFragment : Fragment() {

    companion object {
        const val TAG = "LocationSearchFragment"
        fun newInstance() = LocationSearchFragment()
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as ChangeLocationActivity }) { viewModelFactory }
    private val changeLocationViewModel by viewModels<MyLocationsViewModel>({ activity as ChangeLocationActivity }) { viewModelFactory }
    private val mapViewModel by viewModels<MapViewModel>({ activity as ChangeLocationActivity }) { viewModelFactory }
    private var _binding: LocationSearchFragmentBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val user = requireActivity().intent.extras?.getParcelable<UserModel>("UserModel")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = LocationSearchFragmentBinding.inflate(inflater)


        binding.popularLocations.layoutManager = LinearLayoutManager(requireContext())
        binding.popularLocations.adapter = LocationsAdapter(changeLocationViewModel, LocationsAdapter.LocationClickListener{ location ->
            Timber.tag(LocationsListFragment.TAG).d("Search Popular Locations clicked: $location")
            location?.let { loc->
                val city = loc.city
                val country = loc.country
                val latitude = loc.latitude
                val longitude = loc.longitude
                val chosenLocation = LocationModel(city = city, country = country, latitude = latitude, longitude = longitude)
                val extras = Bundle().apply { putParcelable("chosen_location", chosenLocation) }
                findNavController().navigate(R.id.action_locationSearchFragment_to_mapFragment, extras)
                firebaseLogEvent(FirebaseAnalyticsEventsName.FLY_TO_NEW_LOCATION)
                firebaseLogEvent(FirebaseAnalyticsEventsName.POPULAR_LOCATIONS)
            }

        })

        binding.searchLocations.layoutManager = LinearLayoutManager(requireContext())
        binding.searchLocations.adapter = LocationsAdapter(changeLocationViewModel, LocationsAdapter.LocationClickListener{ location ->
            Timber.tag(LocationsListFragment.TAG).d("Search Locations clicked: $location")
            location?.let {loc->
                val city = loc.city
                val country = loc.country
                val latitude = loc.latitude
                val longitude = loc.longitude
                val chosenLocation = LocationModel(city = city, country = country, latitude = latitude, longitude = longitude)
                val extras = Bundle().apply { putParcelable("chosen_location", chosenLocation) }
                findNavController().navigate(R.id.action_locationSearchFragment_to_mapFragment, extras)
                firebaseLogEvent(FirebaseAnalyticsEventsName.FLY_TO_NEW_LOCATION)
            }
        })

        changeLocationViewModel.popularLocations.observe(viewLifecycleOwner) {
            if(it.isNullOrEmpty()) {
                binding.popularLocationsText.visibility = View.GONE
                binding.popularLocations.visibility = View.GONE
            } else {
                binding.popularLocationsText.visibility = View.VISIBLE
                binding.popularLocations.visibility = View.VISIBLE
            }

            val popularLocations = it.toMutableList()
            popularLocations.removeAll { it.address == homeViewModel.userProfile.value?.profile?.address }

            (binding.popularLocations.adapter as LocationsAdapter).setData(popularLocations)
        }

        mapViewModel.locations.observe(viewLifecycleOwner) {
            if(it.isNotEmpty()) {
                binding.popularLocationsText.visibility = View.GONE
                binding.popularLocations.visibility = View.GONE
            }

            (binding.searchLocations.adapter as LocationsAdapter).setData(it.map {
                LocationModel(System.currentTimeMillis(), it.city, it.country, it.latitude, it.longitude)
            })
        }

        binding.autocomplete.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                mapViewModel.searchLocation(query.toString())
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                if (newText?.length ?: 0 < 1) {
                    clearAutoComplete()
                } else {
                    mapViewModel.searchLocation(newText.toString())
                }
                return false
            }
        })

        binding.closeBtn.setOnClickListener {
            findNavController().popBackStack()
        }

        return binding.root
    }


    private fun clearAutoComplete() {
        mapViewModel.searchLocation("")
        (binding.searchLocations.adapter as LocationsAdapter).setData(emptyList())
        binding.popularLocationsText.visibility = View.VISIBLE
        binding.popularLocations.visibility = View.VISIBLE
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as ChangeLocationActivity).changeLocationComponent.inject(this)
        Timber.tag("VIEWMODEL").d(changeLocationViewModel.toString())
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}
