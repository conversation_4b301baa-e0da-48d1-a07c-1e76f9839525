package com.duaag.android.change_location.di

import androidx.lifecycle.ViewModel
import com.duaag.android.change_location.viewmodels.MapViewModel
import com.duaag.android.change_location.viewmodels.MyLocationsViewModel
import com.duaag.android.di.ViewModelKey
import com.duaag.android.home.viewmodels.BillingViewModel
import com.duaag.android.home.viewmodels.HomeViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class ChangeLocationViewModelModule {

    @Binds
    @IntoMap
    @ViewModelKey(HomeViewModel::class)
    abstract fun bindHomeViewModel(myViewModel: HomeViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(MyLocationsViewModel::class)
    abstract fun bindViewModel(myViewModel: MyLocationsViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(MapViewModel::class)
    abstract fun bindMapViewModel(myViewModel: MapViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(BillingViewModel::class)
    abstract fun bindBillingViewModel(billingViewModel: BillingViewModel): ViewModel

}