package com.duaag.android.change_location.repositories


import com.duaag.android.api.*
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.models.LocationModelUpdateResponse
import com.duaag.android.change_location.models.LocationSearchModelItem
import com.duaag.android.db.LocationsDao
import com.duaag.android.di.ApplicationScope
import com.duaag.android.di.IoDispatcher
import com.duaag.android.exceptions.LimitReachedException
import com.google.gson.Gson
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

@Singleton
class LocationsRepository @Inject constructor(private val locationsDao: LocationsDao,
                                              @Named("private") private val privateService: UserService,
                                              @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
                                              @ApplicationScope private val externalScope: CoroutineScope
) {

    fun getLocations() = locationsDao.getLocations()

    suspend fun insertLocation(location: LocationModel) {
        withContext(ioDispatcher) {
            externalScope.launch {
                locationsDao.insertLocation(location)
            }.join()
        }
    }

    suspend fun deleteLocation(time: Long) {
        withContext(ioDispatcher) {
            externalScope.launch {
                locationsDao.deleteLocation(time)
            }.join()
        }
    }


    suspend fun updateLocationsInDb(locations: List<LocationModel>) {
        withContext(ioDispatcher) {
            externalScope.launch {
                locationsDao.updateData(locations)
            }.join()
        }
    }


    fun updateLocationFlow(type: String, coordinates: Map<String, Any>): Flow<Resource<LocationModelUpdateResponse>> = flow {
        emit(Resource.Loading)
        if (coordinates["latitude"] == 0.0 || coordinates["longitude"] == 0.0) {
            throw Exception("latitude or longitude is 0.0")
        }
        try {
            val response = privateService.setLocation(type = type, coordinates = coordinates)
            when {
                response.isSuccessful -> {
                    emit(Resource.Success(response.body()!!))
                }
                response.code() == 403 -> {

                    val error = response.errorBody()!!.string()
                    val errorObject = Gson().fromJson(
                        error,
                        InteractionErrorBody::class.java
                    )
                    when (errorObject.type) {
                        InteractionErrorType.FLY.value -> {
                            throw LimitReachedException(InteractionErrorType.FLY)
                        }
                        else -> throw Exception("Error")
                    }
                }
                else -> {
                    throw(Exception("Error"))
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

    suspend fun autocomplete(searchString: String): List<LocationSearchModelItem> {
        return privateService.searchCities(searchString)
    }


    fun getTopCities(): Flow<Resource<List<LocationModel>>> = flow {
        emit(Resource.Loading)
        try {
            val response = privateService.topCities()
            if (response.isSuccessful) {
                val result = response.body()!!
                emit(Resource.Success(result))
            } else {
                throw Exception(response.errorBody()!!.string())
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }
}