package com.duaag.android.change_location

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.Location
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import com.android.billingclient.api.*
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.FastLocation
import com.duaag.android.base.LocationBaseActivity
import com.duaag.android.change_location.di.ChangeLocationComponent
import com.duaag.android.change_location.viewmodels.MyLocationsViewModel
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.databinding.ChangeLocationActivityBinding
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.firebase.NotificationType
import com.duaag.android.firebase.model.NotificationModel
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.models.VerifyPaymentModel
import com.duaag.android.home.viewmodels.BillingViewModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.*
import com.duaag.android.views.EnvelopeDialog
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationResult
import com.google.android.gms.maps.MapsInitializer
import com.google.android.gms.maps.OnMapsSdkInitializedCallback
import com.google.firebase.dynamiclinks.*
import com.google.firebase.Firebase
import timber.log.Timber
import javax.inject.Inject
import com.uxcam.UXCam


class ChangeLocationActivity : LocationBaseActivity(), OnMapsSdkInitializedCallback {

    companion object {
        const val CHANGE_LOCATION_SUCCESS = 46
        const val TAG = "ChangeLocationActivity"
    }

    lateinit var changeLocationComponent: ChangeLocationComponent

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedViewModel by viewModels<MyLocationsViewModel> { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel> { viewModelFactory }
    private val billingViewModel by viewModels<BillingViewModel> { viewModelFactory }
    private var _binding: ChangeLocationActivityBinding? = null
    private val binding get() = _binding!!
    private var billingClient: BillingClient? = null
    private var billingStateListener :BillingClientStateListener? = null

    @Inject
    lateinit var duaAccount: DuaAccount


    override fun onCreate(savedInstanceState: Bundle?) {
        changeLocationComponent = (application as DuaApplication).appComponent.changeLocationComponent().create()
        changeLocationComponent.inject(this)


        super.onCreate(savedInstanceState)
        MapsInitializer.initialize(applicationContext, MapsInitializer.Renderer.LATEST, this);

        _binding = DataBindingUtil.setContentView(this, R.layout.change_location_activity)

        billingClient = BillingClient.newBuilder(this)
            .setListener(PurchasesUpdatedListener { billingResult, purchases ->
                // To be implemented in a later section.

                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    // The BillingClient is ready. You can query purchases here.
                    Timber.tag("BILLING").d("purchases: $purchases")
                    if (!purchases.isNullOrEmpty()) {
                        Timber.tag("BILLING").d("purchases: $purchases")
                        purchases.forEach { purchase ->
                            purchase?.let {
                                Timber.tag("BILLING").d("purchaseState: ${purchase.purchaseState}")

                                if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                                    val model = VerifyPaymentModel(
                                        "play-store",
                                        it.skus.first(),
                                        it.orderId!!,
                                        it.purchaseTime.toString(),
                                        it.purchaseToken,
                                        getPackageTypeForId(it.skus.first()),
                                        null
                                    )
                                    homeViewModel.verifyPayment(model,purchase)
                                }
                            }
                        }
                    }
                }
            })
            .enablePendingPurchases()
            .build()

        binding.apply {
            lifecycleOwner = this@ChangeLocationActivity
        }
        sharedViewModel.user.observe(this) {
            it?.let {
                sharedViewModel.getFlyingCounter(it.counterConfigurationNames.flyCounterCN)
            }
        }
        sharedViewModel.deleteUser.observe(this, {
            duaAccount.deleteAllData()
        })

        billingViewModel.buyPackage.observe(this, Observer { playPackage ->
            playPackage?.let { buyItem(it) }
        })

        homeViewModel.consumePurchase.observe(this, {
            consumeProduct(it)
        })

        findNavController(R.id.nav_host_fragment_start).addOnDestinationChangedListener { _, destination, _ ->
            if (destination.id == R.id.myLocationsFragment) {
                hideKeyboard()
            }
        }

        homeViewModel.showEnvelopeDialog.observe(this) {
            if (it) {
                lifecycleScope.launchWhenResumed {
                    if (supportFragmentManager.findFragmentByTag("EnvelopeDialog") == null)
                        EnvelopeDialog.newInstance(
                            homeViewModel.getInteractionLimit().toString(),
                            ClevertapEventSourceValues.CHANGE_LOCATION.value
                        ).show(supportFragmentManager, "EnvelopeDialog")
                }
            }
        }

    }

    private val onUpdateDataReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val extras = intent.extras
            if (extras != null) {
                if (extras.containsKey("action") && extras.containsKey("jsonData"))
                    onDataUpdateFromNotification(NotificationModel(extras))
            } else if (intent.getStringExtra("action") != null && intent.getStringExtra("jsonData") != null) {
                Bundle().apply {
                    putString("action", intent.getStringExtra("action"))
                    putString("jsonData", intent.getStringExtra("jsonData"))
                    onDataUpdateFromNotification(NotificationModel(this))
                }
            }
        }
    }

    private fun onDataUpdateFromNotification(notificationModel: NotificationModel) {
        if (notificationModel.type == NotificationType.USER_DELETED) {
            sharedViewModel.userDelete()
        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this).registerReceiver(onUpdateDataReceiver,
                IntentFilter(NotificationHelper.UPDATE_DATA)
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        billingStateListener = null
        billingClient?.endConnection()
        billingClient = null

        LocalBroadcastManager.getInstance(this).unregisterReceiver(onUpdateDataReceiver)
        _binding = null
    }

    override fun getMainView(): View {
        return binding.root
    }

    override fun getLocationCallback(): LocationCallback {
        return object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                Timber.tag(TAG).d("onLocationResult: $locationResult")

                if (locationResult == null) {
                    Timber.tag(TAG).d("onLocationResult: locationResult is null")
                    return
                }
                var lastLocation: Location? = null
                for (location in locationResult.locations) {
                    Timber.tag(TAG).d("onLocationResult: getting new location")
                    lastLocation = location
                }
                if (lastLocation != null) {
                    if (lastLocation.latitude == 0.0 || lastLocation.longitude == 0.0) {
                        return
                    }
                    if (calculateIfAllowedToUpdate(lastLocation)) {
                        homeViewModel.updateLocation(lastLocation)
                    }
                } else {
                    requestLocation(ClevertapEventSourceValues.CHANGE_LOCATION.value)
                }

            }
        }
    }

    override fun getFastLocationCallback(): FastLocation.Companion.FastLocationInterface {
        return object : FastLocation.Companion.FastLocationInterface {
            override fun onLocationChanged(location: Location) {
                Timber.tag(TAG).d("onLocationResult: $location")
                if (location.latitude == 0.0 || location.longitude == 0.0) {
                    return
                }
                if (calculateIfAllowedToUpdate(location)) {
                    homeViewModel.updateLocation(location)
                }
            }

        }
    }

    fun calculateIfAllowedToUpdate(location: Location): Boolean {
        if (DuaAccount.latitude == null || DuaAccount.longitude == null) {
            return true
        }
        fun distance(): Float? {
            val results = FloatArray(1)
            Location.distanceBetween(
                location.latitude,
                location.longitude,
                DuaAccount.latitude ?: 0.0,
                DuaAccount.longitude ?: 0.0,
                results
            )
            return results[0]
        }

        val distance = distance()
        return if (distance != null && distance > 1000) {
            (distance / 1000).toDouble().roundToDecimals(1) > getDistanceThreshold()
        } else {
            false
        }
    }

    private fun getDistanceThreshold() =
        if (homeViewModel.isUserInFlyMode()) HomeActivity.FLY_LOCATION_THRESHOLD_KM else HomeActivity.NORMAL_LOCATION_THRESHOLD_KM


    //region Play Billing
    private fun buyItem(item: String) {
        billingStateListener = object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                Timber.tag("BILLING").d("result: ${billingResult.responseCode}")
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    // The BillingClient is ready. You can query purchases here.
                    querySkuDetails(item)
                } else if (billingResult.responseCode == BillingClient.BillingResponseCode.BILLING_UNAVAILABLE) {
                    ToastUtil.toast("Billing not available")
                }
            }

            override fun onBillingServiceDisconnected() {
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
            }
        }

        billingStateListener?.let { billingClient?.startConnection(it) }
    }

    private fun querySkuDetails(productId: String) {
        val params = QueryProductDetailsParams.newBuilder()
        val productList = QueryProductDetailsParams.Product.newBuilder()
            .setProductType(BillingClient.ProductType.INAPP)
            .setProductId(productId)
            .build()
        params.setProductList(listOf(productList))



        billingClient?.queryProductDetailsAsync(params.build()) { billingResult, productDetails ->
            // Process the result.
            Timber.tag("BIILING").d("billingResult: $billingResult billingResult: $productDetails")

            val productDetailsParamsList = listOf(
                BillingFlowParams.ProductDetailsParams.newBuilder()
                    .setProductDetails(productDetails.first())
                    .build()
            )

            val billingFlowParams = BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(productDetailsParamsList)
                .build()

            // Launch the billing flow
            val billingResult = billingClient?.launchBillingFlow(this@ChangeLocationActivity, billingFlowParams)
            Timber.tag(TAG).d("launchBillingFlow: responseCode: $billingResult")
        }
    }

    private fun consumeProduct(purchaseToken: String) {
        val params = ConsumeParams.newBuilder()
                .setPurchaseToken(purchaseToken)
                .build()


        billingClient?.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    // The BillingClient is ready. You can query purchases here.

                    billingClient?.consumeAsync(params) { billingResult, outToken ->
                        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                            Timber.tag("BILLING").d("CONSUMED: $outToken")
                            // Handle the success of the consume operation.
                            billingViewModel.dismissDialog()
                        }
                    }
                }
            }

            override fun onBillingServiceDisconnected() {
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
            }
        })
    }
    //endregion

    private val chosenComponentReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            unregisterReceiver(this)

            val clickedComponent: ComponentName? = intent?.getParcelableExtra(
                    Intent.EXTRA_CHOSEN_COMPONENT
            );

            clickedComponent?.let {
                homeViewModel.showEnvelopeDialog()
            }

        }

    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String?>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        try {
            val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment_start) as NavHostFragment
            val currentFragment = navHostFragment.childFragmentManager.fragments[0]
            currentFragment.onRequestPermissionsResult(requestCode, permissions, grantResults)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun shareDua(referralId: Uri) {
        // Use custom action only for your app to receive the broadcast
        val shareAction = "$packageName.share.SHARE_ACTION"
        val receiver = Intent(shareAction)
        val flag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else  PendingIntent.FLAG_UPDATE_CURRENT
        val pi = PendingIntent.getBroadcast(
            this, 0, receiver, flag
        )
        val shareIntent = Intent.createChooser(getShareIntent(referralId), null, pi.intentSender)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            registerReceiver(chosenComponentReceiver, IntentFilter(shareAction), Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(chosenComponentReceiver, IntentFilter(shareAction))
        }
        startActivity(shareIntent)
    }

    private fun getShareIntent(referralId: Uri): Intent {
        val text = "${resources.getString(R.string.invite_text)}\n$referralId"
        return Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, text)
            type = "text/plain"
        }
    }

    private fun generateInvitationLink(referralId: String) {

        val invitationLink = "${BuildConfig.INVITATION_LINK}$referralId"
        Firebase.dynamicLinks.shortLinkAsync {
            link = Uri.parse(invitationLink)
            domainUriPrefix = BuildConfig.DOMAIN_URI_PREFIX
            androidParameters(packageName) {
                minimumVersion = BuildConfig.DYNAMIC_LINKS_ANDROID_MINIMUM_VERSION
            }
            iosParameters(BuildConfig.IOS_ID) {
                appStoreId = BuildConfig.DYNAMIC_LINKS_APP_STORE_ID
                minimumVersion = BuildConfig.DYNAMIC_LINKS_IOS_MINIMUM_VERSION
            }
            socialMetaTagParameters {
                title = getString(R.string.refer_your_friend_to_join)
                description = getString(R.string.install_dua_to_join_our_wonderful)
                imageUrl =
                        Uri.parse(BuildConfig.DYNAMIC_LINKS_IMAGE_URL)
            }
        }.addOnSuccessListener { shortDynamicLink ->
            shortDynamicLink.shortLink?.let {
                homeViewModel.showReferralFriendsShare(it)
            }

        }
    }

    fun checkShareCompat() {
        homeViewModel.getReferral()
        homeViewModel.referralIdGenerated.observe(this, {
            if (NetworkChecker.isNetworkConnected(this)) {
                generateInvitationLink(it)
            } else ToastUtil.toast(getString(R.string.no_internet_connection))
        })

        homeViewModel.showReferralFriendsShare.observe(this, { uri ->
            // check if the activity resolves
            if (null == getShareIntent(uri).resolveActivity(packageManager)) {
                ToastUtil.toast("Your device doesn't support this action")
            } else shareDua(uri)
        })

    }

    override fun onMapsSdkInitialized(renderer: MapsInitializer.Renderer) {
        when (renderer) {
            MapsInitializer.Renderer.LATEST -> Timber.tag(TAG).d("The latest version of the renderer is used.")
            MapsInitializer.Renderer.LEGACY -> Timber.tag(TAG).d("The legacy version of the renderer is used.")
        }
    }
}
