package com.duaag.android.change_location.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.models.LocationSearchModelItem
import com.duaag.android.change_location.viewmodels.MapViewModel
import com.duaag.android.databinding.EmptyLocationAutocompleteItemBinding
import com.duaag.android.databinding.ErrorLocationAutocompleteItemBinding
import com.duaag.android.databinding.LocationAutocompleteItemBinding

class AutocompleteLocationAdapter(val viewModel: MapViewModel) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        const val VIEW_LOCATION = 0
        const val VIEW_EMPTY_LOCATION = 1
        const val VIEW_ERROR_LOCATION = 2
    }

    private var items: ArrayList<LocationSearchModelItem> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            VIEW_LOCATION -> {
                val binding: LocationAutocompleteItemBinding = LocationAutocompleteItemBinding.inflate(layoutInflater, parent, false)
                LocationViewHolder(binding)
            }
            VIEW_EMPTY_LOCATION -> {
                val binding: EmptyLocationAutocompleteItemBinding = EmptyLocationAutocompleteItemBinding.inflate(layoutInflater, parent, false)
                EmptyLocationViewHolder(binding)
            }
            else -> {
                val binding: ErrorLocationAutocompleteItemBinding = ErrorLocationAutocompleteItemBinding.inflate(layoutInflater, parent, false)
                ErrorLocationViewHolder(binding)
            }
        }
    }

    fun setData(data: List<LocationSearchModelItem>) {
        val diffCallback = LocationDiffUtil(items, data)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        items.clear()
        items.addAll(data)
        diffResult.dispatchUpdatesTo(this)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            VIEW_LOCATION -> (holder as LocationViewHolder).bind(items[position])
            VIEW_ERROR_LOCATION -> (holder as ErrorLocationViewHolder)
            else -> (holder as EmptyLocationViewHolder)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when {
            items[position].emptyLocation -> VIEW_EMPTY_LOCATION
            items[position].isErrorItem -> VIEW_ERROR_LOCATION
            else -> VIEW_LOCATION
        }
    }

    inner class LocationViewHolder(val binding: LocationAutocompleteItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(model: LocationSearchModelItem) {
            binding.model = model
            binding.clickListener = LocationHandler()
            binding.topLine.isVisible = bindingAdapterPosition == 0
        }

        inner class LocationHandler {
            fun onClick(model: LocationSearchModelItem) {
                val locationModel = LocationModel(System.currentTimeMillis(), model.city, model.country, model.latitude, model.longitude)
                viewModel.setLocation(locationModel)
            }
        }
    }

    inner class EmptyLocationViewHolder(val binding: EmptyLocationAutocompleteItemBinding) : RecyclerView.ViewHolder(binding.root)

    inner class ErrorLocationViewHolder(val binding: ErrorLocationAutocompleteItemBinding) : RecyclerView.ViewHolder(binding.root)


    class LocationDiffUtil(private val oldList: List<LocationSearchModelItem>, private val newList: List<LocationSearchModelItem>) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size


        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].fullLocation == newList[newItemPosition].fullLocation
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].latitude == newList[newItemPosition].latitude
                    && oldList[oldItemPosition].longitude == newList[newItemPosition].longitude
                    && oldList[oldItemPosition].fullLocation == newList[newItemPosition].fullLocation
                    && oldList[oldItemPosition].country == newList[newItemPosition].country
                    && oldList[oldItemPosition].emptyLocation == newList[newItemPosition].emptyLocation
        }

        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            return super.getChangePayload(oldItemPosition, newItemPosition)
        }
    }
}