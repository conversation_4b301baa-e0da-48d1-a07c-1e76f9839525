package com.duaag.android.change_location.di

import com.duaag.android.change_location.ChangeLocationActivity
import com.duaag.android.change_location.fragments.LocationSearchFragment
import com.duaag.android.change_location.fragments.ConfirmLocationFragment
import com.duaag.android.change_location.fragments.LocationsListFragment
import com.duaag.android.di.ActivityScope
import com.duaag.android.views.EnvelopeDialog
import com.duaag.android.views.InAppPackagesDialog
import com.duaag.android.views.OutOfImpressionsDialog
import dagger.Subcomponent

@ActivityScope
@Subcomponent(modules = [ChangeLocationViewModelModule::class])
interface ChangeLocationComponent {

    // Factory to create instances of RegistrationComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): ChangeLocationComponent
    }

    // Classes that can be injected by this Component
    fun inject(activity: ChangeLocationActivity)
    fun inject(fragment: LocationsListFragment)
    fun inject(fragment: ConfirmLocationFragment)
    fun inject(fragment: InAppPackagesDialog)
    fun inject(fragment: OutOfImpressionsDialog)
    fun inject(fragment: LocationSearchFragment)
    fun inject(fragment: EnvelopeDialog)
}