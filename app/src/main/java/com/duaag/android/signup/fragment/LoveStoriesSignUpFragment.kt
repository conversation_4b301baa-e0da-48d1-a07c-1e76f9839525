package com.duaag.android.signup.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentLoveStoriesSignUpBinding
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.adapters.LoveStoryCardAdapter
import com.duaag.android.signup.fragment.adapters.ReviewAdapter
import com.duaag.android.signup.models.LoveStoryCardItem
import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.HorizontalMarginItemDecoration
import com.duaag.android.utils.convertDpToPx
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.duaag.android.views.LastItemPaddingVerticalDecoration
import com.google.android.material.tabs.TabLayoutMediator
import javax.inject.Inject
import kotlin.math.abs

class LoveStoriesSignUpFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<LoveStorySignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private val signUpViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity  } ) { viewModelFactory }

    private var _binding: FragmentLoveStoriesSignUpBinding? = null
    private val binding get() = _binding!!

    private var tabLayoutMediator: TabLayoutMediator? = null

    val items by lazy {
        listOf(
            LoveStoryCardItem(
                R.drawable.love_story_card_one,
                getString(R.string.onboarding_stories_caption),
                "${getString(R.string.onboarding_stories_name_c1_card_1)} ${getString(R.string.and_string)} ${getString(R.string.onboarding_stories_name_c2_card_1)}",
                getString(R.string.onboarding_stories_married)
            ),
            LoveStoryCardItem(
                R.drawable.love_story_card_two,
                getString(R.string.onboarding_stories_caption),
                "${getString(R.string.onboarding_stories_name_c2_card_2)} ${getString(R.string.and_string)} ${getString(R.string.onboarding_stories_name_c1_card_2)}",
                getString(R.string.onboarding_stories_married_card_2)
            ),
        )
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLoveStoriesSignUpBinding.inflate(inflater)


        if(signUpViewModel.shouldSkipSignUpPersistStep(SignUpPersistStepsEnum.LOVE_STORY)) {
            signUpViewModel.setSignUpPersistStepAsSkipped(SignUpPersistStepsEnum.LOVE_STORY)

            findNavController().navigate(LoveStoriesSignUpFragmentDirections.actionLoveStoriesSignUpFragmentToUploadPhotosFragment2())
        }

        setLoveStoryCards()

        sendReviewsViewEvent()

        return binding.root
    }

    private fun setLoveStoryCards() {
        val adapter = LoveStoryCardAdapter(items)

        setupViewPager(adapter, binding.pager)
        addPageTransformer(binding.pager)
        addItemDecoration(binding.pager)
    }

    private fun setupViewPager(adapter: LoveStoryCardAdapter, pager: ViewPager2?) {
        pager?.offscreenPageLimit = 1
        pager?.adapter = adapter
        binding.tabLayout.let {
            pager?.let { it1 ->
                tabLayoutMediator = TabLayoutMediator(it, it1) { _, _ ->}
                tabLayoutMediator?.attach()
            }
        }
    }

    private fun addPageTransformer(pager: ViewPager2?) {
        // Add a PageTransformer that translates the next and previous items horizontally
        // towards the center of the screen, which makes them visible
        val pageTranslationX = getPageTranslationX()
        val pageTransformer = ViewPager2.PageTransformer { page: View, position: Float ->
            page.translationX = -pageTranslationX * position
            // Next line scales the item's height.
            page.scaleY = 1 - (0.10f * abs(position))
            // Fading effect
            page.alpha = 0.25f + (1 - abs(position))
        }
        pager?.setPageTransformer(pageTransformer)
    }

    private fun getPageTranslationX(): Float {
        val nextItemVisiblePx = resources.getDimension(R.dimen.viewpager_next_item_visible)
        val currentItemHorizontalMarginPx = resources.getDimension(R.dimen.viewpager_current_item_horizontal_margin)
        return nextItemVisiblePx + currentItemHorizontalMarginPx
    }

    private fun addItemDecoration(pager: ViewPager2?) {
        // The ItemDecoration gives the current (centered) item horizontal margin so that
        // it doesn't occupy the whole screen width. Without it the items overlap
        val itemDecoration = HorizontalMarginItemDecoration(
            requireContext(),
            R.dimen.viewpager_current_item_horizontal_margin
        )
        pager?.addItemDecoration(itemDecoration)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val stories = viewModel.dataUi.value.stories.toMutableList()
        binding.storiesList.addItemDecoration(LastItemPaddingVerticalDecoration(convertDpToPx(24f).toInt()))
        binding.storiesList.layoutManager = LinearLayoutManager(requireContext())
        binding.storiesList.adapter = ReviewAdapter(stories)

        binding.continueBtn.setOnSingleClickListener {
            signUpViewModel.setLoveStorySkipped()

            findNavController().navigate(LoveStoriesSignUpFragmentDirections.actionLoveStoriesSignUpFragmentToUploadPhotosFragment2())
        }
    }


    private fun sendReviewsViewEvent(){
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        sendClevertapEvent(ClevertapEventEnum.LOVE_STORY_SCREENVIEW,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            )
        )
        sendUxCamEvent(ClevertapEventEnum.LOVE_STORY_SCREENVIEW,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}