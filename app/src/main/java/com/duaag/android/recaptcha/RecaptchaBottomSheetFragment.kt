package com.duaag.android.recaptcha

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.duaag.android.databinding.FragmentRecaptchaBottomSheetBinding
import com.duaag.android.utils.setOnSingleClickListener
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class RecaptchaBottomSheetFragment : BottomSheetDialogFragment() {

    companion object {
        fun newInstance(listener: ReCaptchaOptionsClickListener?): RecaptchaBottomSheetFragment {
            return RecaptchaBottomSheetFragment().apply {
                optionListener = listener
            }
        }
    }

    private var _binding: FragmentRecaptchaBottomSheetBinding? = null
    private val binding get() = _binding!!
    private var optionListener: ReCaptchaOptionsClickListener? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRecaptchaBottomSheetBinding.inflate(inflater)

        binding.privacy.setOnSingleClickListener {
            optionListener?.onPrivacyClicked(this)
        }

        binding.terms.setOnSingleClickListener {
            optionListener?.onTermsClicked(this)
        }

        return binding.root
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =
            super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.let {
                BottomSheetBehavior.from<FrameLayout?>(bottomSheet).state =
                    BottomSheetBehavior.STATE_EXPANDED
            }

        }
        return dialog
    }

    interface ReCaptchaOptionsClickListener {
        fun onPrivacyClicked(fragment: RecaptchaBottomSheetFragment)
        fun onTermsClicked(fragment: RecaptchaBottomSheetFragment)
    }

    override fun onDestroy() {
        super.onDestroy()
        optionListener = null
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}