package com.duaag.android.aws_liveness.domain.usecase

import com.duaag.android.api.ResourceV2
import com.duaag.android.aws_liveness.domain.model.LivenessSessionId
import com.duaag.android.aws_liveness.domain.model.SignUpConfiguration
import com.duaag.android.user.UserRepository
import javax.inject.Inject

class GetUserSignUpConfigurationsUseCase @Inject constructor(
    private val repository: UserRepository
) {
    suspend operator fun invoke(): ResourceV2<SignUpConfiguration> {
        return repository.getUserSignUpConfigurations()
    }
}