package com.duaag.android.aws_liveness.presentation.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.duaag.android.R
import com.duaag.android.databinding.UploadSucessDialogLayoutBinding
import com.duaag.android.databinding.VerificationFailedDialogLayoutBinding
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.getS3Url
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import timber.log.Timber


class LivenessImageUploadSuccessDialog : DialogFragment() {

    private var _binding: UploadSucessDialogLayoutBinding? = null
    private val binding get() = _binding!!

    private var imageUrl: String? = null
    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateLocale(context)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)
        imageUrl = arguments?.getString(ARG_IMAGE_URL)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }
        _binding = UploadSucessDialogLayoutBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindUserImage(binding.uploadedImage,imageUrl)
        binding.okBtn.setOnSingleClickListener{
            dismissAllowingStateLoss()
        }
    }


    private fun bindUserImage(imageView: ImageView, invalidImageKey: String?) {
        invalidImageKey?.let {
            val circularProgressDrawable = CircularProgressDrawable(imageView.context)
            circularProgressDrawable.strokeWidth = 5f
            circularProgressDrawable.centerRadius = 30f
            circularProgressDrawable.start()
            val imageUrl = getS3Url(it)
            Glide.with(imageView.context)
                .load(imageUrl)
                .transform(
                    MultiTransformation(
                        CenterCrop(),
                        CircleCrop()
                    )
                )
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .apply(
                    RequestOptions()
                        .placeholder(circularProgressDrawable)
                        .error(R.drawable.ic_broken_image)
                )
                .into(imageView)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        const val ARG_IMAGE_URL = "arg_image_url"
        fun newInstance(imageUrl: String? = null): LivenessImageUploadSuccessDialog {
            val fragment = LivenessImageUploadSuccessDialog()
            val args = Bundle()
            args.putString(ARG_IMAGE_URL, imageUrl)
            fragment.arguments = args
            return fragment
        }
    }
}