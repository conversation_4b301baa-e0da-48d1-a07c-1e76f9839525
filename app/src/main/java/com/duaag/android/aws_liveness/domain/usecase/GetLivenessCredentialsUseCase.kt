package com.duaag.android.aws_liveness.domain.usecase

import com.duaag.android.api.ResourceV2
import com.duaag.android.aws_liveness.domain.model.LivenessCredentials
import com.duaag.android.user.UserRepository
import javax.inject.Inject

class GetLivenessCredentialsUseCase @Inject constructor(
    private val repository: UserRepository
) {
    suspend operator fun invoke(): ResourceV2<LivenessCredentials> {
        return repository.getLivenessCredentials()
    }
}