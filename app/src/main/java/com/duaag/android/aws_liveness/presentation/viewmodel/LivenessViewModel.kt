package com.duaag.android.aws_liveness.presentation.viewmodel

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.duaag.android.api.Resource
import com.duaag.android.api.ResourceV2
import com.duaag.android.application.DuaApplication
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.aws_liveness.domain.model.LivenessError
import com.duaag.android.aws_liveness.domain.model.LivenessUiData
import com.duaag.android.aws_liveness.domain.usecase.GetLivenessCredentialsUseCase
import com.duaag.android.aws_liveness.domain.usecase.GetSessionIdUseCase
import com.duaag.android.aws_liveness.domain.usecase.SkipImageVerificationUseCase
import com.duaag.android.aws_liveness.domain.usecase.SubmitSessionIdUseCase
import com.duaag.android.base.models.Badge2VerificationState
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.counters.domain.SyncUserCountersUseCase
import com.duaag.android.di.IoDispatcher
import com.duaag.android.di.MainDispatcher
import com.duaag.android.signup.models.ImageMetaDataResultModel
import com.duaag.android.signup.models.ImageModel
import com.duaag.android.signup.models.ImageResult
import com.duaag.android.signup.models.ImageVerificationModel
import com.duaag.android.signup.models.InvalidProfileImageReason
import com.duaag.android.signup.models.PictureModel
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel.Companion.MAX_WORDS_ALLOWED
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel.Companion.UPLOAD_TAG
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.getFileSize
import com.duaag.android.utils.removeMetadataFromImage
import com.duaag.android.uxcam.sendUxCamEvent
import com.google.mlkit.vision.text.Text
import id.zelory.compressor.Compressor
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.util.Random
import java.util.UUID
import javax.inject.Inject

class LivenessViewModel @Inject constructor(
    private val getLivenessCredentialsUseCase: GetLivenessCredentialsUseCase,
    private val getSessionIdUseCase: GetSessionIdUseCase,
    private val submitSessionIdUseCase: SubmitSessionIdUseCase,
    private val syncUserCountersUseCase: SyncUserCountersUseCase,
    private val skipImageVerificationUseCase: SkipImageVerificationUseCase,
    val userRepository: UserRepository,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher
) : ViewModel() {

    private val _uiData = MutableStateFlow(LivenessUiData())
    val uiData: StateFlow<LivenessUiData> = _uiData

    val user = userRepository.user
    var accountModel = userRepository.getAccount()

    fun getLivenessCredentials() {
        viewModelScope.launch(ioDispatcher) {
            val result = getLivenessCredentialsUseCase()
            withContext(mainDispatcher) {
                _uiData.value = _uiData.value.copy(
                    livenessCredentials = if (result is ResourceV2.Success) result.data else null,
                    error = if (result is ResourceV2.Error) result.message else null
                )
            }

        }
    }

    fun getSessionId() {
        viewModelScope.launch(ioDispatcher) {
            val result = getSessionIdUseCase()
            withContext(mainDispatcher) {
                _uiData.value = _uiData.value.copy(
                    sessionId = if (result is ResourceV2.Success) result.data.sessionId else null,
                    error = if (result is ResourceV2.Error) result.message else null
                )
            }
        }
    }

    fun submitSessionId(sessionId: String, pictureKey: String?=null) {
        viewModelScope.launch(ioDispatcher) {
            submitSessionIdUseCase(sessionId,pictureKey)
                .catch { e ->
                    val error = e as? LivenessError
                    withContext(mainDispatcher) {
                        _uiData.update {
                            it.copy(errorLiveness = error, verificationPassedSuccessfully = false)
                        }
                    }
                }
                .collect {
                    withContext(mainDispatcher) {
                        if (it is Resource.Success) {
                            _uiData.update {
                                it.copy(verificationPassedSuccessfully = true)
                            }
                        }
                    }
                }
        }
    }


    fun onFaceLivenessDetectorError() {
        _uiData.update {
            it.copy(errorLiveness = LivenessError.UnknownError)
        }
    }

    fun onErrorHandled() {
        _uiData.update {
            it.copy(error = null, errorLiveness = null, verificationPassedSuccessfully = false)
        }
    }

    fun addNewImage(outputUri: Uri, currentPictureMetadata: ImageMetaDataResultModel?) {
        val item = ImageModel(
            id = Random().nextLong(), outputUri.toString(),
            isSelected = true,
            photoScreenshot = currentPictureMetadata?.photoScreenshot ?: false,
            isPhotoNotFromCamera = currentPictureMetadata?.isPhotoNotFromCamera ?: false
        )
        _uiData.update {
            it.copy(
                uploadedImageData = it.uploadedImageData?.copy(
                    image = item,
                    currentPictureMetadata = currentPictureMetadata
                )
            )
        }
    }

    fun setFaceDetected(faceDetected: Boolean) {
        _uiData.update {
            it.copy(
                uploadedImageData = it.uploadedImageData?.copy(
                    image = it.uploadedImageData.image?.copy(
                        faceDetected = faceDetected,
                        isVerified = !faceDetected,
                        isValid = faceDetected
                    ),
                    faceDetectionCompleted = true
                ),
            )
        }
    }

    fun setTextDetected(text: Text) {
        val textDetected = text.text.split(" ", "\n").size >= MAX_WORDS_ALLOWED

        _uiData.update {
            it.copy(
                uploadedImageData = it.uploadedImageData?.copy(
                    image = it.uploadedImageData.image?.copy(
                        textDetected = textDetected,
                    ),
                    textDetectionCompleted = true
                ),
            )
        }
    }

    fun setFaceDetectionCompleted() {
        _uiData.update {
            it.copy(uploadedImageData = it.uploadedImageData?.copy(faceDetectionCompleted = true))
        }

    }

    fun setTextDetectionCompleted() {

        _uiData.update {
            it.copy(uploadedImageData = it.uploadedImageData?.copy(textDetectionCompleted = true))
        }
    }

    fun uploadImage() {

        val image = File(_uiData.value.uploadedImageData?.image?.url!!.substringAfter(":"))

        viewModelScope.launch(Dispatchers.IO) {
            try {
                val compressedImageFile = Compressor.compress(DuaApplication.instance, image)
                Timber.tag("COMPRESS").d("Size: ${getFileSize(compressedImageFile.length())}")

                val fileName = AWSInteractor.getResourceUrl(
                    UUID.randomUUID().toString(),
                    compressedImageFile.extension
                )

                removeMetadataFromImage(compressedImageFile.absolutePath)

                AWSInteractor.uploadFile(compressedImageFile, fileName, object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState) {
//                        if (TransferState.COMPLETED === state) {
//                            Timber.tag(UPLOAD_TAG).d("State: ${state.name}")
//                          /*  _uiData.update {
//                                it.copy(uploadedImageData = it.uploadedImageData?.copy(image =it.uploadedImageData.image?.copy(s3Key = fileName)))
//                            }*/
//                                              }
                            if(state == TransferState.FAILED){
                                _uiData.update{
                                    it.copy(uploadedImageData = it.uploadedImageData?.copy(uploadFinished = true, uploadProgress = -1, uploadFailed = true, imagesVerified = null))
                                }
                            } else if(state == TransferState.COMPLETED){
                                sendUploadImageEvent()
                                _uiData.update{
                                    it.copy(uploadedImageData = it.uploadedImageData?.copy(uploadFinished = true, uploadProgress = 100, uploadFailed = null, imagesVerified = null, image = it.uploadedImageData.image?.copy(s3Key = fileName)))
                                }
                                afterUploadImage()
                            }

                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                        val percentDone =
                            (bytesCurrent.toFloat() / bytesTotal.toFloat() * 100).toInt()
                        _uiData.update{
                            it.copy(uploadedImageData = it.uploadedImageData?.copy(uploadProgress = percentDone))
                        }
                        Timber.tag(UPLOAD_TAG).d("ID:$id|bytesCurrent: $bytesCurrent|bytesTotal: $bytesTotal|$percentDone%")
                    }

                    override fun onError(id: Int, ex: Exception) {
                        ex.printStackTrace()
                        _uiData.update{
                            it.copy(uploadedImageData = it.uploadedImageData?.copy(uploadFinished = true, uploadProgress = -1, uploadFailed = true))
                        }
                        ex.printStackTrace()
                    }

                })
            } catch (e: Exception) {
                e.printStackTrace()
                _uiData.update{
                    it.copy(uploadedImageData = it.uploadedImageData?.copy(uploadFinished = true, uploadProgress = -1, uploadFailed = true))
                }
            }
        }

    }

    private fun sendUploadImageEvent() {
        val signUpOrSignInMediumValue = ClevertapSignUpOrSignInMediumValues.NULL.value
        val premiumType = getPremiumTypeEventProperty(user.value)

        sendClevertapEvent(
            ClevertapEventEnum.UPLOAD_IMAGE, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
            )
        )
        sendUxCamEvent(
            ClevertapEventEnum.UPLOAD_IMAGE, mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInMediumValue,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
            )
        )
    }

    private fun afterUploadImage() {
        val imageToUpload = _uiData.value.uploadedImageData?.image
       imageToUpload?.let { image->
            if(!image.isVerified && image.faceDetected) {
               val pictureModel = PictureModel(image.textDetected, image.s3Key ?: "", image.photoScreenshot, image.isPhotoNotFromCamera)
                verifyUploadedImages(pictureModel)
            } else {
                val imageModel = ImageResult(isValid = false, key = image.s3Key ?:"", invalidReason = InvalidProfileImageReason.NO_FACE_DETECTED.value)

                _uiData.update {
                    it.copy(uploadedImageData = it.uploadedImageData?.copy(imageVerificationFailed = imageModel, uploadFailed = true, uploadProgress = -1))
                }

            }

      } ?: run {
           _uiData.update {
               it.copy(uploadedImageData = it.uploadedImageData?.copy(uploadFinished = true, uploadFailed = true, uploadProgress = -1))
           }
       }

    }

    private fun verifyUploadedImages(pictureModel: PictureModel) {
        val model = ImageVerificationModel(listOf(pictureModel))

        viewModelScope.launch(Dispatchers.IO) {
            userRepository.verifyUploadedImages(model)
                .catch { ex ->
                    _uiData.update{
                        it.copy(uploadedImageData = it.uploadedImageData?.copy(uploadProgress = -1, uploadFailed = true, uploadFinished = true))
                    }
                    ex.printStackTrace()
                }
                .collect { result ->
                    withContext(Dispatchers.Main){
                        when(result) {
                            is Resource.Success -> {
                                if(result.data.images.isNotEmpty())
                                _uiData.update {
                                    it.copy(uploadedImageData = it.uploadedImageData?.copy(imageVerified = result.data.images.map { image->image.isValid }.toBooleanArray() ))
                                }

                                val faceNotDetected = _uiData.value.uploadedImageData?.image?.let {
                                    if(!it.faceDetected){
                                        ImageResult(false, it.url ?: "", it.url)
                                    } else null
                                }

                                _uiData.update {
                                    it.copy(uploadedImageData = it.uploadedImageData?.copy(image = it.uploadedImageData.image?.copy(isValid = result.data.images.any {image -> image.key == it.uploadedImageData.image.s3Key && image.isValid },isVerified = result.data.images.any {image -> image.key == it.uploadedImageData.image.s3Key })))
                                }




                                when {
                                    result.data.images.any { img -> !img.isValid} -> {
                                        _uiData.update {
                                            it.copy(uploadedImageData = it.uploadedImageData?.copy(imageVerificationFailed = result.data.images[0] ))
                                        }
                                    }

                                    result.data.images.none { img -> !img.isValid } && faceNotDetected == null -> {
                                        //add new verified keys that came from api
                                        val newImages = result.data.images.toMutableList()

                                       val images =  newImages.filter { it.isValid }
                                        _uiData.update {
                                            it.copy(uploadedImageData = it.uploadedImageData?.copy(imagesVerified = images))
                                        }
                                    }
                                    else -> {
                                        result.data.images.forEach { verified ->
                                            verified.location = if(_uiData.value.uploadedImageData?.image?.s3Key == verified.key) _uiData.value.uploadedImageData?.image?.url else null
                                        }
                                        _uiData.update {
                                            it.copy(uploadedImageData = it.uploadedImageData?.copy(imagesVerified =  result.data.images.filter {image-> image.isValid }))
                                        }

                                    }
                                }

                                _uiData.update{
                                    it.copy(uploadedImageData = it.uploadedImageData?.copy( uploadFinished = true))
                                }

                            }
                            is Resource.Loading -> {}
                            is Resource.Error -> {}
                        }
                    }
                }
        }
    }

    fun resetUploadProgress() {
        _uiData.update {
            it.copy(uploadedImageData = it.uploadedImageData?.copy(uploadProgress = null, uploadFinished = null, uploadFailed = null, userPicturesUpdated = null))
        }
    }

    fun resetImageDetection() {
        _uiData.update {
            it.copy(uploadedImageData = it.uploadedImageData?.copy(faceDetectionCompleted = null, textDetectionCompleted = null))
        }
    }

    fun resetUploadImageData() {
        _uiData.update {
            it.copy(uploadedImageData = LivenessUiData.UploadedImageData())
        }
    }

    fun updateImages(image: String,) {
        val images : MutableList<String>? = userRepository.user.value?.profile?.pictures?.map { it.url }?.toMutableList()
        images?.add(0,image)
        viewModelScope.launch(ioDispatcher) {
            if (!images.isNullOrEmpty()) {
                when (val result = userRepository.updateAllUserPictures(images)) {
                is ResourceV2.Success -> {
                    syncUserCountersUseCase.invoke()
                        .catch { e -> Timber.e(e) }
                        .collect{ Timber.d("Counters updated") }

                    userRepository.updateProfilePercentageInClevertap()
                    _uiData.update {
                        it.copy(uploadedImageData = it.uploadedImageData?.copy(userPicturesUpdated = true))
                    }
                }

                is ResourceV2.Error -> {
                    Timber.tag("updateImages").d("updateImages ${result.message}")
                    withContext(mainDispatcher){
                        _uiData.update {
                            it.copy(uploadedImageData = it.uploadedImageData?.copy(userPicturesUpdated = false))
                        }
                    }
            } }
        } else skipUpdateImageProcess()
    }
        }

    fun onVerificationPassedSuccessfully() {
        _uiData.update {
            LivenessUiData(hasAlreadyBeenVerified = true)
        }
    }

    fun onLivenessVerificationFailed(error: LivenessError) {
        _uiData.update{
            it.copy(verificationFailed = true,failedImageUrl = error.image)
        }
    }

    fun onLivenessVerificationFailedDialogShown() {
        _uiData.update {
            it.copy(verificationFailed = false, failedImageUrl = null)
        }
    }

    fun setPictureKey(pictureKey: String?) {
        _uiData.update {
            it.copy(pictureKey = pictureKey)
        }
    }

    fun skipUpdateImageProcess() {
        _uiData.update {
            it.copy(uploadedImageData = it.uploadedImageData?.copy(userPicturesUpdated = true))
        }
    }

    fun skipVerificationStep() {
        _uiData.update {
            it.copy(skipVerificationStep= true)
        }
    }

    fun onSkipVerificationStep() {
        _uiData.update {
            it.copy(skipVerificationStep = false)
        }
    }


    fun setUserGender(gender: String?) {
        _uiData.update {
            it.copy(userGender = gender)
        }
    }

    fun skipImageVerification() {
        viewModelScope.launch {
            skipImageVerificationUseCase()
        }
    }

    fun updateBadge2VerificationState(verificationState: Badge2VerificationState?) {
        _uiData.update {
            it.copy(badge2VerificationState = verificationState )
        }
    }

    fun retryVerification() {
        _uiData.update {
            it.copy(hasAlreadyBeenVerified = null)
        }
    }
}