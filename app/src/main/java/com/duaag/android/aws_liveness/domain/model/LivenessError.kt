package com.duaag.android.aws_liveness.domain.model

sealed class LivenessError( val image: String?) : Exception(image) {
    class InvalidLivenessSessionStatus(image: String?) : LivenessError(image)

    class LivenessUnderThreshold(image: String?) : LivenessError(image)

    class LivenessFaceMismatch( image: String?) : LivenessError(image)

    object UnknownError : LivenessError(null) {
        private fun readResolve(): Any = UnknownError
    }
}