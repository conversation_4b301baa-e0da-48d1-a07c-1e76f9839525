package com.duaag.android.aws_liveness.data.network.mapper

import com.duaag.android.aws_liveness.data.model.SignUpConfigurationResponse
import com.duaag.android.aws_liveness.domain.model.PhotosConfiguration
import com.duaag.android.aws_liveness.domain.model.SignUpConfiguration

fun SignUpConfigurationResponse.toDomainModel(): SignUpConfiguration {
    return SignUpConfiguration(
        name = this.name,
        birthday = this.birthday,
        gender = this.gender,
        photos = this.photos?.toDomainModel(),
        community = this.community,
        lookingFor = this.lookingFor,
        doYouHaveChildren = this.doYouHaveChildren,
        doYouSmoke = this.doYouSmoke,
        religion = this.religion,
        languages = this.languages,
        badge2 = this.badge2,
        locationPermission = this.locationPermission,
        notificationPermission = this.notificationPermission,
        hasRequiredLanguages = this.hasRequiredLanguages,
        hasRequiredLivenessOnSignUp = this.hasRequiredLivenessOnSignUp,
        hasRequiredWhatAreYouLookingFor= this.hasRequiredWhatAreYouLookingFor,
        hasRequiredDoYouHaveChildren = this.hasRequiredDoYouHaveChildren,
        hasRequiredDoYouSmoke = this.hasRequiredDoYouSmoke,
        hasRequiredReligion = this.hasRequiredReligion,
        minimumRequiredPhotos = this.minimumRequiredPhotos
    )
}

fun SignUpConfigurationResponse.PhotosConfigurationResponse.toDomainModel(): PhotosConfiguration {
    return PhotosConfiguration(
        one = this.one,
        two = this.two,
        threeOrMore = this.threeOrMore
    )
}
