package com.duaag.android.aws_liveness.domain.model

import android.os.Build
import androidx.annotation.RequiresApi
import com.amplifyframework.auth.AWSCredentials
import com.amplifyframework.auth.AWSCredentialsProvider
import com.amplifyframework.auth.AWSTemporaryCredentials
import com.amplifyframework.auth.AuthException
import com.amplifyframework.core.Consumer
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import javax.inject.Inject
import aws.smithy.kotlin.runtime.time.Instant as AwsInstant
import java.time.Instant as JavaInstant

class LivenessCredentialsProvider @Inject constructor(private var credentials: LivenessCredentials) :
    AWSCredentialsProvider<AWSCredentials> {
    override fun fetchAWSCredentials(
        onSuccess: Consumer<AWSCredentials>, onError: Consumer<AuthException>
    ) {
        val accessKeyId = credentials.accessKeyId
        val secretAccessKey = credentials.secretAccessKey
        val sessionToken = credentials.sessionToken
        val expiration = credentials.expiration
        if (!accessKeyId.isNullOrEmpty() && !secretAccessKey.isNullOrEmpty() && !sessionToken.isNullOrEmpty() && !expiration.isNullOrEmpty()) {
            val awsTempCredentials = parseIso8601ToAwsInstant(expiration)?.let { expiration ->
                AWSTemporaryCredentials(
                    accessKeyId = accessKeyId,
                    secretAccessKey = secretAccessKey,
                    sessionToken = sessionToken,
                    expiration = expiration
                )
            }

            if (awsTempCredentials != null) {
                onSuccess.accept(awsTempCredentials)
            }
        }


    }

    private fun parseIso8601ToAwsInstant(expiration: String): AwsInstant? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                parseJavaInstant(expiration)?.let { javaInstantToAwsInstant(it) }
            } else {
                parseLegacyInstant(expiration)?.let { dateToAwsInstant(it) }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    private fun dateToAwsInstant(date: Date): AwsInstant {
        val epochSeconds = date.time / 1000
        val nanoseconds = ((date.time % 1000) * 1_000_000).toInt()
        return AwsInstant.fromEpochSeconds(epochSeconds, nanoseconds)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun javaInstantToAwsInstant(instant: JavaInstant): AwsInstant {
        return AwsInstant.fromEpochSeconds(instant.epochSecond, instant.nano)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun parseJavaInstant(expiration: String): JavaInstant? {
        return try {
            JavaInstant.parse(expiration)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    private fun parseLegacyInstant(iso8601String: String): Date? {
        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
            dateFormat.timeZone = TimeZone.getTimeZone("UTC")
            dateFormat.parse(iso8601String)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}