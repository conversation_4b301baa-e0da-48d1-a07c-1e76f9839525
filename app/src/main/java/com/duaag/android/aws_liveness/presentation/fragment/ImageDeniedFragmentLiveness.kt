package com.duaag.android.aws_liveness.presentation.fragment

import android.content.Context
import android.graphics.Paint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.duaag.android.R
import com.duaag.android.aws_liveness.presentation.viewmodel.LivenessViewModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentImageDeniedSignUpBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.fragment.guidelines.DialogClickListener
import com.duaag.android.signup.fragment.guidelines.GuidelinesDialogFragment
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.bindImage
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.getS3Url
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import javax.inject.Inject

class ImageDeniedFragmentLiveness : Fragment(), DialogClickListener {


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel: LivenessViewModel by viewModels<LivenessViewModel>({
        when (val act = activity) {
            is HomeActivity -> act
            is SignUpActivity -> act
            else -> throw IllegalStateException("Unknown Activity")
        }
    }) {
        viewModelFactory
    }
    private val sharedSignUpViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private var _binding: FragmentImageDeniedSignUpBinding? = null
    private val binding get() = _binding!!

    private var invalidReason: String? = null
    private var invalidImageKey: String? = null

    private var authMethod: AuthMethod? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            invalidReason = it.getString(ARG_REASON)
            invalidImageKey = it.getString(ARG_IMAGE_KEY)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        when (val activity = requireActivity()) {
            is HomeActivity -> activity.homeComponent.inject(this)
            is SignUpActivity -> activity.signUpComponent.inject(this)
            else -> throw IllegalStateException("Unknown Activity")
        }
        updateLocale(context)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentImageDeniedSignUpBinding.inflate(inflater, container, false)
        binding.guidelinesText.paintFlags =
            binding.guidelinesText.paintFlags or Paint.UNDERLINE_TEXT_FLAG
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel.userRepository.getAccount().observe(viewLifecycleOwner) { accountModel ->
            accountModel?.let {
                authMethod = when {
                    it.phone == null -> AuthMethod.PHONE
                    it.email == null -> AuthMethod.EMAIL
                    else -> authMethod
                }
            }
        }

        val premiumType = getPremiumTypeEventProperty(viewModel.user.value)
        binding.guidelinesText.setOnSingleClickListener {
            val dialog = GuidelinesDialogFragment.newInstance(
                methodName = authMethod?.methodName,
                premiumTypeEventProperty = premiumType
            )
            dialog.show(childFragmentManager, "dialog")
        }
        binding.replacePhotoBtn.setOnSingleClickListener {
            sendClevertapEvent(
                ClevertapEventEnum.REPLACE_PHOTO,
                mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod?.methodName)
            )
            sendUxCamEvent(
                ClevertapEventEnum.REPLACE_PHOTO,
                mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod?.methodName)
            )
            findNavController().popBackStack()
        }

        bindUserInvalidImage(binding.image, invalidImageKey)
        bindExampleImages()

        sendClevertapImageDeniedEvent()
    }

    private fun sendClevertapImageDeniedEvent() {
        val premiumType = getPremiumTypeEventProperty(viewModel.user.value)
        sendClevertapEvent(
            ClevertapEventEnum.IMAGE_UPLOAD_DENIED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod?.methodName,
                ClevertapEventPropertyEnum.IMAGE_DENIED_REASON.propertyName to invalidReason,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType

            )
        )
        sendUxCamEvent(
            ClevertapEventEnum.IMAGE_UPLOAD_DENIED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod?.methodName,
                ClevertapEventPropertyEnum.IMAGE_DENIED_REASON.propertyName to invalidReason
            )
        )
    }

    private fun bindUserInvalidImage(imageView: ImageView, invalidImageKey: String?) {
        invalidImageKey?.let {
            val circularProgressDrawable = CircularProgressDrawable(imageView.context)
            circularProgressDrawable.strokeWidth = 5f
            circularProgressDrawable.centerRadius = 30f
            circularProgressDrawable.start()
            val roundedCorners = convertDpToPixel(16f, requireContext())
            val imageUrl = getS3Url(it)
            Glide.with(imageView.context)
                .load(imageUrl)
                .transform(
                    MultiTransformation(
                        CenterCrop(),
                        GranularRoundedCorners(
                            roundedCorners,
                            roundedCorners,
                            roundedCorners,
                            roundedCorners
                        )
                    )
                )
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .apply(
                    RequestOptions()
                        .placeholder(circularProgressDrawable)
                        .error(R.drawable.ic_broken_image)
                )
                .into(imageView)
        }
    }

    private fun bindExampleImages() {
        val isMale = if(activity is SignUpActivity)
            sharedSignUpViewModel.gender.value?.value == GenderType.MAN.value
        else
            homeViewModel.userProfile.value?.gender == GenderType.MAN.value

        val clearFaceImageRes = if(isMale) R.drawable.clear_face_male else R.drawable.clear_face_female
        val hideFaceImageRes = if(isMale) R.drawable.hide_face_male else R.drawable.hide_face_female
        val groupPhotoImageRes = if(isMale) R.drawable.no_groups_img_male else R.drawable.no_groups_img_female

        bindImage(binding.clearFaceImage, clearFaceImageRes)
        bindImage(binding.hideFaceImage, hideFaceImageRes)
        bindImage(binding.groupPhotoImage, groupPhotoImageRes)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        const val ARG_REASON = "arg_reason"
        const val ARG_IMAGE_KEY = "arg_image_key"
    }

    override fun onButtonClicked() {
        binding.replacePhotoBtn.performClick()
    }
}