package com.duaag.android.aws_liveness.presentation.dialog

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.duaag.android.R
import com.duaag.android.databinding.VerificationFailedDialogLayoutBinding
import com.duaag.android.home.fragments.VerifyYourProfileDialog.VerifyYourProfileDialogListener
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.getS3Url
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import timber.log.Timber


class LivenessVerificationFailedDialog : DialogFragment() {

    private var listener: LivenessVerificationFailedDialogListener? = null
    private var _binding: VerificationFailedDialogLayoutBinding? = null
    private val binding get() = _binding!!

    private var failedImageUrl: String? = null
    override fun onAttach(context: Context) {
        super.onAttach(context)
        try {
              listener =   activity as LivenessVerificationFailedDialogListener
        } catch (e: ClassCastException) {
            throw ClassCastException("$context must implement LivenessVerificationFailedDialogListener")
        }
        updateLocale(context)
    }

    interface LivenessVerificationFailedDialogListener {
        fun onTryAgainClicked()
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)
        failedImageUrl = arguments?.getString(ARG_FAILED_IMAGE_URL)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }
        _binding = VerificationFailedDialogLayoutBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.tryAgainBtn.setOnSingleClickListener{
            listener?.onTryAgainClicked()
            dismissAllowingStateLoss()
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null

    }

    override fun onDetach() {
        super.onDetach()
        listener = null
    }

    companion object {
        const val ARG_FAILED_IMAGE_URL = "arg_failed_image_url"
        fun newInstance(imageUrl: String? = null): LivenessVerificationFailedDialog {
            val fragment = LivenessVerificationFailedDialog()
            val args = Bundle()
            args.putString(ARG_FAILED_IMAGE_URL, imageUrl)
            fragment.arguments = args
            return fragment
        }
    }
}