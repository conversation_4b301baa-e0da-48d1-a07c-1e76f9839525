package com.duaag.android.aws_liveness.presentation.fragment

import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.MimeTypeMap
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.aws_liveness.domain.model.LivenessUiData
import com.duaag.android.aws_liveness.presentation.dialog.LivenessImageUploadSuccessDialog
import com.duaag.android.aws_liveness.presentation.viewmodel.LivenessViewModel
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendChangeProfilePhotoEvent
import com.duaag.android.databinding.FragmentFaceMissMatchLivenessBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.models.ImageMetaDataResultModel
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.getPictureMetaData
import com.duaag.android.utils.getS3Url
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.ProgressDialog
import com.duaag.android.vision.ImageVisionInteractor
import com.duaag.android.vision.VisionImageProcessor
import com.duaag.android.vision.facedetector.FaceDetectorProcessor
import com.duaag.android.vision.textdetector.TextRecognitionProcessor
import com.google.mlkit.vision.text.Text
import com.yalantis.ucrop.UCrop
import com.yalantis.ucrop.UCropActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.util.UUID
import javax.inject.Inject


class FaceMissMatchLivenessFragment : Fragment() {

    val args: FaceMissMatchLivenessFragmentArgs by navArgs()

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel: LivenessViewModel by viewModels<LivenessViewModel>({
        when (val act = activity) {
            is HomeActivity -> act
            is SignUpActivity -> act
            else -> throw IllegalStateException("Unknown Activity")
        }
    }) {
        viewModelFactory
    }
    private val choosePictureViewModel: ChoosePictureViewModel by viewModels<ChoosePictureViewModel> { viewModelFactory }
    private var _binding: FragmentFaceMissMatchLivenessBinding? = null
    private val binding get() = _binding!!
    private val sharedSignUpViewModel by viewModels<SharedSignUpViewModel>({  activity as SignUpActivity }) { viewModelFactory }

    private var currentPictureMetadata: ImageMetaDataResultModel? = null
    private var imageVisionInteractor: ImageVisionInteractor? = null
    private var faceDetectionCallback: FaceDetectorProcessor.FaceDetectionCallback? = null
    private var textDetectionCallback: TextRecognitionProcessor.TextDetectionCallback? = null
    private var uploadProgressDialog: ProgressDialog? = null

    private val pickMedia =
        registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
            if (uri != null) {
                lifecycleScope.launch(Dispatchers.IO) {
                    currentPictureMetadata = getPictureMetaData(requireContext(), uri)
                }
                beginUCrop(uri)
            } else {
                Timber.tag("PhotoPicker").d("No media selected")
            }
        }




    override fun onAttach(context: Context) {
        super.onAttach(context)
        when (val activity = requireActivity()) {
            is HomeActivity -> activity.homeComponent.inject(this)
            is SignUpActivity -> activity.signUpComponent.inject(this)
            else -> throw IllegalStateException("Unknown Activity")
        }
        updateLocale(context)
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentFaceMissMatchLivenessBinding.inflate(inflater, container, false)
        imageVisionInteractor = createImageProcessors()
        return binding.root
    }

    private fun createImageProcessors(): ImageVisionInteractor? {
        faceDetectionCallback = object : FaceDetectorProcessor.FaceDetectionCallback {
            override fun onSuccess(faceDetected: Boolean) {
                Timber.tag("IMAGE_DETECTION").d("onSuccess: faceDetected $faceDetected")
                viewModel.setFaceDetected(faceDetected)
            }

            override fun onError(exception: Exception) {
                Timber.tag("IMAGE_DETECTION").d("onError: faceDetected exception $exception")
                exception.printStackTrace()

                viewModel.setFaceDetectionCompleted()
            }
        }

        textDetectionCallback = object : TextRecognitionProcessor.TextDetectionCallback {
            override fun onSuccess(text: Text) {
                Timber.tag("IMAGE_DETECTION").d("onSuccess: textDetected ${text.text}")
                viewModel.setTextDetected(text)
            }

            override fun onError(exception: Exception) {
                Timber.tag("IMAGE_DETECTION").d("onError: textDetected exception $exception")
                exception.printStackTrace()

                viewModel.setTextDetectionCompleted()
            }
        }

        val imageProcessors = mutableListOf<VisionImageProcessor>()
        try {

            //Face Detection
            val faceDetector =
                FaceDetectorProcessor(requireContext(), null, faceDetectionCallback!!)
            imageProcessors.add(faceDetector)


            //Text Detection
            val textDetector = TextRecognitionProcessor(requireContext(), textDetectionCallback!!)
            imageProcessors.add(textDetector)
        } catch (e: Exception) {
            Timber.tag("IMAGE_DETECTION").e(e, "Can not create image processor: ${e.message}")
            return null
        }

        return ImageVisionInteractor(imageProcessors)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindImages(binding, args)
        binding.closeButton.setOnSingleClickListener {
            findNavController().popBackStack()
        }
        binding.uploadNewPhotoButton.setOnSingleClickListener {
            val premiumType = getPremiumTypeEventProperty(DuaApplication.instance.userRepository.user.value)
            val signUpSignInMedium = if(activity is SignUpActivity) sharedSignUpViewModel.authMethod?.methodName else null
            sendChangeProfilePhotoEvent(
                verificationSource = ClevertapVerificationSourceValues.VERIFICATION_FAILED.value,
                premiumType = premiumType,
                signUpSignInMedium = signUpSignInMedium ,
                eventSource = ClevertapEventSourceValues.VERIFICATION_FAILED.value
            )

            pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
        }

        viewModel.uiData
            .asLiveData()
            .observe(viewLifecycleOwner){uiData->
                handleImageUpload(uiData)
            }
    }
    private fun handleImageUpload(uiData: LivenessUiData) {
        uiData.uploadedImageData?.let { data ->
            handleImageUploadProcess(data)
            trackUploadProgress(data)
            handleImageVerificationFailure(data)
            handleUploadCompletion(data)
        }
    }

    private fun handleImageUploadProcess(data: LivenessUiData.UploadedImageData) {
        if (data.faceDetectionCompleted == true && data.textDetectionCompleted == true) {
            startImageUploadProcess()
        }
    }

    private fun trackUploadProgress(data: LivenessUiData.UploadedImageData) {
        data.uploadProgress?.let { progress ->
            uploadProgressDialog?.updateProgress(progress)
            if (progress == -1) {
                uploadProgressDialog?.dismiss()
                viewModel.resetUploadProgress()
            }
        }
    }

    private fun handleImageVerificationFailure(data: LivenessUiData.UploadedImageData) {
        data.imageVerificationFailed?.let { verificationFailed ->
            val bundle = bundleOf(
                ImageDeniedFragmentLiveness.ARG_REASON to verificationFailed.invalidReason,
                ImageDeniedFragmentLiveness.ARG_IMAGE_KEY to verificationFailed.key,
            )
            findNavController().navigateSafer(R.id.action_faceMissMatchLivenessFragment_to_imageDeniedFragmentLiveness, bundle)
            viewModel.resetUploadImageData()
        }
    }


    private fun handleUploadCompletion(data: LivenessUiData.UploadedImageData) {
        if (data.uploadFinished == true) {

            if (data.uploadFailed == true) {
                ToastUtil.toast("Upload Failed!")
                uploadProgressDialog?.dismiss()
                viewModel.resetUploadProgress()
            }

            if (!data.imagesVerified.isNullOrEmpty()) {
                val imageKey =  data.imagesVerified.firstOrNull()?.key
                imageKey?.let {
                    if(data.userPicturesUpdated != true) {
                    viewModel.updateImages(it)
                }
                }
                if(data.userPicturesUpdated == true){
                    val dialog = LivenessImageUploadSuccessDialog.newInstance(data.imagesVerified.firstOrNull()?.key)
                    dialog.show(requireActivity().supportFragmentManager,"UploadSuccessDialog")
                    findNavController().popBackStack()
                    uploadProgressDialog?.dismiss()
                    if(activity is SignUpActivity) {
                        viewModel.setPictureKey(data.imagesVerified.firstOrNull()?.key)
                        choosePictureViewModel.setVerifiedPictureFromLiveness(data.imagesVerified)
                    }
                    viewModel.resetUploadProgress()
                } else if(data.userPicturesUpdated == false) {
                    uploadProgressDialog?.dismiss()
                    viewModel.resetUploadProgress()
                }


            }
        }
    }

    private fun startImageUploadProcess() {
        uploadProgressDialog = ProgressDialog(requireContext(), resources.getString(R.string.uploading_images))
        uploadProgressDialog?.show()
        viewModel.uploadImage()
        viewModel.resetImageDetection()
    }

    private fun bindImages(
        binding: FragmentFaceMissMatchLivenessBinding,
        args: FaceMissMatchLivenessFragmentArgs
    ) {
        val circularProgressDrawable = CircularProgressDrawable(binding.faceScanImage.context)
        circularProgressDrawable.strokeWidth = 5f
        circularProgressDrawable.centerRadius = 30f
        circularProgressDrawable.start()
        val roundedCorners = convertDpToPixel(24f, requireContext())
        val faceScanImageUrl = args.faceScanReferenceImage?.let { getS3Url(it) }
        Glide.with(binding.faceScanImage)
            .load(faceScanImageUrl)
            .transform(
                MultiTransformation(
                    CenterCrop(),
                    GranularRoundedCorners(
                        roundedCorners,
                        roundedCorners,
                        roundedCorners,
                        roundedCorners
                    )
                )
            )
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .apply(
                RequestOptions()
                    .placeholder(circularProgressDrawable)
                    .error(R.drawable.ic_broken_image)
            )
            .into(binding.faceScanImage)

        val profilePhotoImageUrl = args.profilePhotoImage?.let { getS3Url(it) }
        Glide.with(binding.profilePhotoImage)
            .load(profilePhotoImageUrl)
            .transform(
                MultiTransformation(
                    CenterCrop(),
                    GranularRoundedCorners(
                        roundedCorners,
                        roundedCorners,
                        roundedCorners,
                        roundedCorners
                    )
                )
            )
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .apply(
                RequestOptions()
                    .placeholder(circularProgressDrawable)
                    .error(R.drawable.ic_broken_image)
            )
            .into(binding.profilePhotoImage)

    }

    private fun beginUCrop(uri: Uri) {
        try {
            var randomFileName = UUID.randomUUID().toString()
            val extension: String?
            val cr = requireContext().contentResolver
            val type = cr.getType(uri)
            if (uri.toString().contains("file:")) extension = findExtension(uri)
            else extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(type)

            if (extension != null) randomFileName = "$randomFileName.$extension"
            Timber.tag("URITYPE").d("fileName: \$fileName")

            val destination = Uri.fromFile(File(requireContext().cacheDir, randomFileName))
            val options = UCrop.Options()
            options.setHideBottomControls(true)
            options.setCropGridRowCount(5)
            options.setCropGridColumnCount(4)
            options.setAllowedGestures(UCropActivity.ALL, UCropActivity.SCALE, UCropActivity.ROTATE)
            options.setToolbarTitle("")
            options.setDimmedLayerColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.background
                )
            )

            UCrop.of(uri, destination)
                .withAspectRatio(3f, 4f)
                .withOptions(options)

                .start(requireContext(), this,22)
        } catch (e: Exception) {
            e.printStackTrace()
        }


    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
      //  super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == 22 && data != null) {
            val resultUri = UCrop.getOutput(data)
            resultUri?.let { outputUri ->
                if (!NetworkChecker.isNetworkConnected(requireContext())) {
                    ToastUtil.toast(getString(R.string.no_internet_string))
                    return
                }
                handleCroppedImage(outputUri)
            }
        }
    }

    private fun handleCroppedImage(outputUri: Uri) {
        viewModel.addNewImage(outputUri, currentPictureMetadata)
        imageVisionInteractor?.detectInImage(outputUri, requireActivity().contentResolver)
    }

    private fun findExtension(uri: Uri): String? {
        val stringUri = uri.toString()
        val lastIndex = stringUri.lastIndexOf('.')
        if (lastIndex == -1) {
            return null
        }
        return stringUri.substring(lastIndex + 1)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        imageVisionInteractor?.stopImageProcessors()
        imageVisionInteractor = null
        uploadProgressDialog?.dismiss()
        uploadProgressDialog = null

        _binding = null
    }
}