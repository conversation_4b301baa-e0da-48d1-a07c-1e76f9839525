package com.duaag.android.aws_liveness.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class SignUpConfigurationResponse(
    @SerializedName("name") val name: Int? = null,
    @SerializedName("birthday") val birthday: Int? = null,
    @SerializedName("gender") val gender: Int? = null,
    @SerializedName("photos") val photos: PhotosConfigurationResponse? = null,
    @SerializedName("community") val community: Int? = null,
    @SerializedName("lookingFor") val lookingFor: Int? = null,
    @SerializedName("doYouHaveChildren") val doYouHaveChildren: Int? = null,
    @SerializedName("doYouSmoke") val doYouSmoke: Int? = null,
    @SerializedName("religion") val religion: Int? = null,
    @SerializedName("languages") val languages: Int? = null,
    @SerializedName("badge2") val badge2: Int? = null,
    @SerializedName("locationPermission") val locationPermission: Int? = null,
    @SerializedName("notificationPermission") val notificationPermission: Int? = null,
    @SerializedName("hasRequiredLanguages") val hasRequiredLanguages: Boolean? = null,
    @SerializedName("hasRequiredLivenessOnSignUp") val hasRequiredLivenessOnSignUp: Boolean? = null,
    @SerializedName("hasRequiredWhatAreYouLookingFor") val hasRequiredWhatAreYouLookingFor: Boolean? = null,
    @SerializedName("hasRequiredDoYouHaveChildren") val hasRequiredDoYouHaveChildren: Boolean? = null,
    @SerializedName("hasRequiredDoYouSmoke") val hasRequiredDoYouSmoke: Boolean? = null,
    @SerializedName("hasRequiredReligion") val hasRequiredReligion: Boolean? = null,
    @SerializedName("minimumRequiredPhotos") val minimumRequiredPhotos: Int? = null,
) {
    @Keep
    data class PhotosConfigurationResponse(
        @SerializedName("one") val one: Int? = null,
        @SerializedName("two") val two: Int? = null,
        @SerializedName("threeOrMore") val threeOrMore: Int? = null
    )
}


