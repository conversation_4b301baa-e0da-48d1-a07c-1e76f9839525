package com.duaag.android.aws_liveness.domain.model


data class SignUpConfiguration(
    val name: Int? = null,
    val birthday: Int? = null,
    val gender: Int? = null,
    val photos: PhotosConfiguration? = null,
    val community: Int? = null,
    val lookingFor: Int? = null,
    val doYouHaveChildren: Int? = null,
    val doYouSmoke: Int? = null,
    val religion: Int? = null,
    val languages: Int? = null,
    val badge2: Int? = null,
    val locationPermission: Int? = null,
    val notificationPermission: Int? = null,
    val hasRequiredLanguages: Boolean? = null,
    val hasRequiredLivenessOnSignUp: Boolean? = null,
    val hasRequiredWhatAreYouLookingFor: Boolean? = null,
    val hasRequiredDoYouHaveChildren: Boolean? = null,
    val hasRequiredDoYouSmoke: Boolean? = null,
    val hasRequiredReligion: Boolean? = null,
    val minimumRequiredPhotos: Int? = null,
    )

data class PhotosConfiguration(
    val one: Int? = null,
    val two: Int? = null,
    val threeOrMore: Int? = null
)

