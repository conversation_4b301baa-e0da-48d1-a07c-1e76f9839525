package com.duaag.android.aws_liveness.presentation.fragment

import android.Manifest
import android.app.Activity.RESULT_OK
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.MimeTypeMap
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.aws_liveness.domain.model.LivenessUiData
import com.duaag.android.aws_liveness.presentation.dialog.LivenessImageUploadSuccessDialog
import com.duaag.android.aws_liveness.presentation.dialog.LivenessVerificationFailedDialog
import com.duaag.android.aws_liveness.presentation.viewmodel.LivenessViewModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendChangeProfilePhotoEvent
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentMandatoryVerificationBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.signup.models.ImageMetaDataResultModel
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getPictureMetaData
import com.duaag.android.utils.imageCircle
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.setCustomStyledText
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.duaag.android.views.ProgressDialog
import com.duaag.android.vision.ImageVisionInteractor
import com.duaag.android.vision.VisionImageProcessor
import com.duaag.android.vision.facedetector.FaceDetectorProcessor
import com.duaag.android.vision.textdetector.TextRecognitionProcessor
import com.google.mlkit.vision.text.Text
import com.yalantis.ucrop.UCrop
import com.yalantis.ucrop.UCropActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import sendVerifyYourProfileInitiatedAnalyticsEvent
import timber.log.Timber
import java.io.File
import java.util.UUID
import javax.inject.Inject

class MandatoryVerificationFragment : Fragment() {

    private var _binding: FragmentMandatoryVerificationBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel: LivenessViewModel by viewModels<LivenessViewModel>({
        activity as HomeActivity
    }) {
        viewModelFactory
    }
    private val choosePictureViewModel: ChoosePictureViewModel by viewModels<ChoosePictureViewModel> { viewModelFactory }

    private var currentPictureMetadata: ImageMetaDataResultModel? = null
    private var imageVisionInteractor: ImageVisionInteractor? = null
    private var faceDetectionCallback: FaceDetectorProcessor.FaceDetectionCallback? = null
    private var textDetectionCallback: TextRecognitionProcessor.TextDetectionCallback? = null
    private var uploadProgressDialog: ProgressDialog? = null

    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            onPermissionGranted()
        } else {
            handlePermissionDenied(requireActivity())
        }
    }

    private val pickMedia =
        registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
            if (uri != null) {
                lifecycleScope.launch(Dispatchers.IO) {
                    currentPictureMetadata = getPictureMetaData(requireContext(), uri)
                }
                beginUCrop(uri)
            } else {
                Timber.tag("PhotoPicker").d("No media selected")
            }
        }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateLocale(context)
        (requireActivity() as? HomeActivity)?.homeComponent?.inject(this)

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentMandatoryVerificationBinding.inflate(inflater, container, false)
        imageVisionInteractor = createImageProcessors()


        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val eventPremiumType = getPremiumTypeEventProperty(viewModel.user.value)
        val verificationSource = ClevertapVerificationSourceValues.REGULAR.value

        binding.changeYourPhotoCaption.setCustomStyledText(fullText = requireContext().getString(R.string.change_your_photo_caption),
            targetText = requireContext().getString(R.string.change_your_photo_title),
            fontName = "tt_norms_pro_medium",
            underline = true,
            linkColor = ContextCompat.getColor(requireContext(), R.color.title_primary),
            onClick = {
                val eventSource = ClevertapVerificationSourceValues.IMAGE_VERIFICATION.value
                sendChangeProfilePhotoEvent(eventSource, eventPremiumType)
                sendUxCamEvent(
                    ClevertapEventEnum.CHANGE_PROFILE_PHOTO,
                    mapOf(
                        ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to verificationSource,
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType
                    )
                )
                pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
            })

        viewModel.user.observe(viewLifecycleOwner) { userModel ->
            userModel?.let {
                if (userModel.profile.pictureUrl.isNotEmpty()) {
                    imageCircle(binding.userImage, userModel.profile.pictureUrl)
                }
                viewModel.updateBadge2VerificationState(userModel.getVerificationState())
            }
        }



        viewModel.uiData.asLiveData().observe(viewLifecycleOwner) { uiData ->
            handleLivenessVerificationUi(uiData)
            handleImageUpload(uiData)
        }
        binding.continueBtn.setOnSingleClickListener {
            requestPermission(requireActivity())
        }

        binding.btnSettings.setOnSingleClickListener {
            sendClevertapEvent(ClevertapEventEnum.SETTINGS_SCREENVIEW,
                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapVerificationSourceValues.IMAGE_VERIFICATION.value)
            )
            sendUxCamEvent(ClevertapEventEnum.SETTINGS_SCREENVIEW,
                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                    ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapVerificationSourceValues.IMAGE_VERIFICATION.value)
            )

            val intent = Intent(requireContext(), SettingsActivity::class.java)
            startActivity(intent)
        }

        sendVerifyYourProfileInitiatedAnalyticsEvent(verificationSource, eventPremiumType)
    }

    private fun handleLivenessVerificationUi(uiState: LivenessUiData?) {
        if (uiState?.verificationFailed == true) {
            LivenessVerificationFailedDialog.newInstance(uiState.failedImageUrl)
                .show(childFragmentManager, "LivenessVerificationFailedDialog")
            viewModel.onLivenessVerificationFailedDialogShown()
        }
    }

    private fun handleImageUpload(uiData: LivenessUiData) {
        uiData.uploadedImageData?.let { data ->
            handleImageUploadProcess(data)
            trackUploadProgress(data)
            handleImageVerificationFailure(data)
            handleUploadCompletion(data)
        }
    }

    private fun handleImageUploadProcess(data: LivenessUiData.UploadedImageData) {
        if (data.faceDetectionCompleted == true && data.textDetectionCompleted == true) {
            startImageUploadProcess()
        }
    }

    private fun startImageUploadProcess() {
        uploadProgressDialog =
            ProgressDialog(requireContext(), resources.getString(R.string.uploading_images))
        uploadProgressDialog?.show()
        viewModel.uploadImage()
        viewModel.resetImageDetection()
    }

    private fun trackUploadProgress(data: LivenessUiData.UploadedImageData) {
        data.uploadProgress?.let { progress ->
            uploadProgressDialog?.updateProgress(progress)
            if (progress == -1) {
                uploadProgressDialog?.dismiss()
                viewModel.resetUploadProgress()
            }
        }
    }

    private fun handleImageVerificationFailure(data: LivenessUiData.UploadedImageData) {
        data.imageVerificationFailed?.let { verificationFailed ->
            val bundle = bundleOf(
                ImageDeniedFragmentLiveness.ARG_REASON to verificationFailed.invalidReason,
                ImageDeniedFragmentLiveness.ARG_IMAGE_KEY to verificationFailed.key,
            )
            findNavController().navigateSafer(
                R.id.action_mandatoryVerificationFragment_to_imageDeniedFragmentLiveness, bundle
            )
            viewModel.resetUploadImageData()
        }
    }

    private fun handleUploadCompletion(data: LivenessUiData.UploadedImageData) {
        if (data.uploadFinished == true) {

            if (data.uploadFailed == true) {
                ToastUtil.toast("Upload Failed!")
                uploadProgressDialog?.dismiss()
                viewModel.resetUploadProgress()
            }

            if (!data.imagesVerified.isNullOrEmpty()) {
                val imageKey = data.imagesVerified.firstOrNull()?.key
                imageKey?.let {
                    if(data.userPicturesUpdated != true) {
                        viewModel.updateImages(it)
                    }
                }
                if (data.userPicturesUpdated == true) {
                    val dialog =
                        LivenessImageUploadSuccessDialog.newInstance(data.imagesVerified.firstOrNull()?.key)
                    dialog.show(requireActivity().supportFragmentManager, "UploadSuccessDialog")
                    uploadProgressDialog?.dismiss()
                    viewModel.resetUploadProgress()
                } else if (data.userPicturesUpdated == false) {
                    uploadProgressDialog?.dismiss()
                    viewModel.resetUploadProgress()
                }

            }

        }
    }

    private fun createImageProcessors(): ImageVisionInteractor? {
        faceDetectionCallback = object : FaceDetectorProcessor.FaceDetectionCallback {
            override fun onSuccess(faceDetected: Boolean) {
                Timber.tag("IMAGE_DETECTION").d("onSuccess: faceDetected $faceDetected")
                viewModel.setFaceDetected(faceDetected)
            }

            override fun onError(exception: Exception) {
                Timber.tag("IMAGE_DETECTION").d("onError: faceDetected exception $exception")
                exception.printStackTrace()

                viewModel.setFaceDetectionCompleted()
            }
        }

        textDetectionCallback = object : TextRecognitionProcessor.TextDetectionCallback {
            override fun onSuccess(text: Text) {
                Timber.tag("IMAGE_DETECTION").d("onSuccess: textDetected ${text.text}")
                viewModel.setTextDetected(text)
            }

            override fun onError(exception: Exception) {
                Timber.tag("IMAGE_DETECTION").d("onError: textDetected exception $exception")
                exception.printStackTrace()

                viewModel.setTextDetectionCompleted()
            }
        }

        val imageProcessors = mutableListOf<VisionImageProcessor>()
        try {

            //Face Detection
            val faceDetector =
                FaceDetectorProcessor(requireContext(), null, faceDetectionCallback!!)
            imageProcessors.add(faceDetector)


            //Text Detection
            val textDetector = TextRecognitionProcessor(requireContext(), textDetectionCallback!!)
            imageProcessors.add(textDetector)
        } catch (e: Exception) {
            Timber.tag("IMAGE_DETECTION").e(e, "Can not create image processor: ${e.message}")
            return null
        }

        return ImageVisionInteractor(imageProcessors)
    }


    private fun requestPermission(
        activity: FragmentActivity, permission: String = Manifest.permission.CAMERA
    ) {
        val hasPermission = ContextCompat.checkSelfPermission(
            activity, permission
        ) == PackageManager.PERMISSION_GRANTED

        if (hasPermission) {
            onPermissionGranted()
        } else {
            if (shouldShowRationale(activity)) {
                showSettingsRedirectDialog(activity)
                return
            }
            cameraPermissionLauncher.launch(permission)
        }
    }

    private fun shouldShowRationale(
        activity: FragmentActivity, permission: String = Manifest.permission.CAMERA
    ): Boolean {
        return activity.shouldShowRequestPermissionRationale(permission)
    }

    private fun onPermissionGranted() {
        findNavController().navigateSafer(R.id.action_global_livenessProfileVerification)
    }

    private fun handlePermissionDenied(
        activity: FragmentActivity, permission: String = Manifest.permission.CAMERA
    ) {
        if (activity.shouldShowRequestPermissionRationale(permission)) {
            showPermissionDeniedDialog(activity)
        } else {
            showSettingsRedirectDialog(activity)
        }
    }

    private fun showPermissionDeniedDialog(
        context: Context, permission: String = Manifest.permission.CAMERA
    ) {
        AlertDialog.Builder(context).setTitle("Permission Denied")
            .setMessage("Without this permission the app is unable to function properly.")
            .setPositiveButton("Retry") { _, _ ->
                cameraPermissionLauncher.launch(permission)
            }.setNegativeButton("Cancel", null).create().show()
    }

    private fun showSettingsRedirectDialog(context: Context) {
        AlertDialog.Builder(context).setTitle("Permission Denied Permanently")
            .setMessage("You have denied the permission permanently. Please enable it in settings.")
            .setPositiveButton("Go to Settings") { _, _ ->

                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri: Uri = Uri.fromParts("package", context.packageName, null)
                intent.data = uri
                context.startActivity(intent)
            }.setNegativeButton("Cancel", null).create().show()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == RESULT_OK && requestCode == 23 && data != null) {
            val resultUri = UCrop.getOutput(data)
            resultUri?.let { outputUri ->
                if (!NetworkChecker.isNetworkConnected(requireContext())) {
                    ToastUtil.toast(getString(R.string.no_internet_string))
                    return
                }
                handleCroppedImage(outputUri)
            }
        }
    }

    private fun handleCroppedImage(outputUri: Uri) {
        viewModel.addNewImage(outputUri, currentPictureMetadata)
        imageVisionInteractor?.detectInImage(outputUri, requireActivity().contentResolver)
    }

    private fun beginUCrop(uri: Uri) {
        try {
            var randomFileName = UUID.randomUUID().toString()
            val extension: String?
            val cr = requireContext().contentResolver
            val type = cr.getType(uri)
            if (uri.toString().contains("file:")) extension = findExtension(uri)
            else extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(type)

            if (extension != null) randomFileName = "$randomFileName.$extension"
            Timber.tag("URITYPE").d("fileName: \$fileName")

            val destination = Uri.fromFile(File(requireContext().cacheDir, randomFileName))
            val options = UCrop.Options()
            options.setHideBottomControls(true)
            options.setCropGridRowCount(5)
            options.setCropGridColumnCount(4)
            options.setAllowedGestures(UCropActivity.ALL, UCropActivity.SCALE, UCropActivity.ROTATE)
            options.setToolbarTitle("")
            options.setDimmedLayerColor(
                ContextCompat.getColor(
                    requireContext(), R.color.background
                )
            )

            UCrop.of(uri, destination).withAspectRatio(3f, 4f).withOptions(options)

                .start(requireContext(), this, 23)
        } catch (e: Exception) {
            e.printStackTrace()
        }


    }

    private fun findExtension(uri: Uri): String? {
        val stringUri = uri.toString()
        val lastIndex = stringUri.lastIndexOf('.')
        if (lastIndex == -1) {
            return null
        }
        return stringUri.substring(lastIndex + 1)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}