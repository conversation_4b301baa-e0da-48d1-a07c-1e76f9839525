package com.duaag.android.aws_liveness.domain.usecase

import com.duaag.android.api.Resource
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class SubmitSessionIdUseCase @Inject constructor(
    private val repository: UserRepository
) {
    operator fun invoke(sessionId: String, pictureKey: String? = null): Flow<Resource<Unit>> {
     return repository.submitSessionId(sessionId,pictureKey)
    }

}
