package com.duaag.android.aws_liveness.data.model

import com.google.errorprone.annotations.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class LivenessCredentialsOutput(
    @SerializedName("accessKeyId") val accessKeyId: String?=null,
    @SerializedName("secretAccessKey") val secretAccessKey: String?=null,
    @SerializedName("sessionToken") val sessionToken: String?=null,
    @SerializedName("expiration") val expiration: String?=null
)