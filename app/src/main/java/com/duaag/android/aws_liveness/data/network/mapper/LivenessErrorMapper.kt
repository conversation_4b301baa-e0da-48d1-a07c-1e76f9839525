package com.duaag.android.aws_liveness.data.network.mapper

import com.duaag.android.aws_liveness.domain.model.LivenessError
import com.duaag.android.base.models.DuaErrorBody
import com.duaag.android.base.models.DuaErrorType

object LivenessErrorMapper {
    fun mapToDomain(errorResponse: DuaErrorBody?): LivenessError {
        if (errorResponse != null) {
            return when (errorResponse.type) {
                DuaErrorType.LIVENESS_FACE_MISMATCH_ERROR.value -> LivenessError.LivenessFaceMismatch(
                    image = errorResponse.image
                )

                DuaErrorType.LIVENESS_UNDER_THRESHOLD_ERROR.value -> LivenessError.LivenessUnderThreshold(
                    image = errorResponse.image
                )

                DuaErrorType.INVALID_LIVENESS_SESSION_STATUS_ERROR.value -> LivenessError.InvalidLivenessSessionStatus(
                    image = errorResponse.image
                )

                else -> LivenessError.UnknownError
            }
        }
        return LivenessError.UnknownError
    }

}