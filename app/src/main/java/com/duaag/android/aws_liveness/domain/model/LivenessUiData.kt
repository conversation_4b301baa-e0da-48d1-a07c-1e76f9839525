package com.duaag.android.aws_liveness.domain.model

import com.duaag.android.base.models.Badge2VerificationState
import com.duaag.android.signup.models.ImageMetaDataResultModel
import com.duaag.android.signup.models.ImageModel
import com.duaag.android.signup.models.ImageResult

data class LivenessUiData(
    val livenessCredentials: LivenessCredentials? = null,
    val sessionId: String? = null,
    val verificationPassedSuccessfully: Boolean = false,
    val error: String? = null,
    val showVerificationFailed: Boolean = false,
    val errorLiveness: LivenessError? = null,
    val region: String = "eu-west-1",
    val verificationFailed: Boolean? = false,
    val failedImageUrl: String? = null,
    val uploadedImageData: UploadedImageData? = UploadedImageData(),
    val pictureKey: String? = null,
    val hasSkipVerificationOption : Boolean? = null,
    val skipVerificationStep :Boolean? = null,
    val userGender: String? = null,
    val badge2VerificationState: Badge2VerificationState? = null,
    val hasAlreadyBeenVerified: Boolean? = false
) {
    data class UploadedImageData(
        val image: ImageModel? = null,
        val currentPictureMetadata: ImageMetaDataResultModel? = null,
        val textDetectionCompleted: Boolean? = null,
        val faceDetectionCompleted: Boolean? = null,
        val uploadProgress: Int? = null,
        val uploadFailed: Boolean? = null,
        val uploadFinished: Boolean? = null,
        val imagesVerified: List<ImageResult>? = null,
        val imageVerificationFailed: ImageResult? = null,
        val imageVerified: BooleanArray? = null,
        val userPicturesUpdated: Boolean? = null
    )
}