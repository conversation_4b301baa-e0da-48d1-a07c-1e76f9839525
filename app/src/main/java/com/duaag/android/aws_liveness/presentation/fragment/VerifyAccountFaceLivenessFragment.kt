package com.duaag.android.aws_liveness.presentation.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.amplifyframework.ui.liveness.ui.FaceLivenessDetector
import com.amplifyframework.ui.liveness.ui.LivenessColorScheme
import com.duaag.android.R
import com.duaag.android.aws_liveness.domain.model.LivenessCredentialsProvider
import com.duaag.android.aws_liveness.domain.model.LivenessError
import com.duaag.android.aws_liveness.domain.model.LivenessUiData
import com.duaag.android.aws_liveness.presentation.viewmodel.LivenessViewModel
import com.duaag.android.base.models.Badge2VerificationState
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapVerificationFailedReasonValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.home.HomeActivity
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.uxcam.sendUxCamEvent
import com.uxcam.UXCamKt
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject


class VerifyAccountFaceLivenessFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel: LivenessViewModel by viewModels<LivenessViewModel>({
        when (val act = activity) {
            is HomeActivity -> act
            is SignUpActivity -> act
            else -> throw IllegalStateException("Unknown Activity")
        }
    }) {
       viewModelFactory
    }
    private val sharedSignUpViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        when (val activity = requireActivity()) {
            is HomeActivity -> activity.homeComponent.inject(this)
            is SignUpActivity -> activity.signUpComponent.inject(this)
            else -> throw IllegalStateException("Unknown Activity")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initLiveness()
    }

    private fun initLiveness() {
        viewModel.getLivenessCredentials()
        viewModel.getSessionId()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        sendProfileVerificationProcessing()
    }

    private fun sendProfileVerificationProcessing() {
        val premiumType = getPremiumTypeEventProperty(viewModel.user.value)
        val properties = hashMapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType)
        if(activity is SignUpActivity) {
            properties[ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName] = ClevertapVerificationSourceValues.ONBOARDING.value
            properties[ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName] = sharedSignUpViewModel.authMethod?.methodName
        }
        sendClevertapEvent(ClevertapEventEnum.PROFILE_VERIFICATION_PROCESSING, properties)
        sendUxCamEvent(ClevertapEventEnum.PROFILE_VERIFICATION_PROCESSING, properties)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                LivenessDetectionComponentWrapper(viewModel)
            }
        }
    }

    @Composable
    fun LivenessDetectionComponentWrapper(viewModel: LivenessViewModel) {
        val uiData by viewModel.uiData.collectAsStateWithLifecycle()

        uiData.sessionId?.let { sessionId ->
            uiData.livenessCredentials?.let { credentials ->
                LivenessCredentialsProvider(credentials)
            }?.let { credentialsProvider ->
                LivenessDetectionComponent(
                    sessionId = sessionId,
                    region = uiData.region,
                    credentialsProvider = credentialsProvider,
                    uiData = uiData,
                    viewModel = viewModel
                )
            }
        }
    }

    @Composable
    fun LivenessDetectionComponent(
        sessionId: String,
        region: String,
        credentialsProvider: LivenessCredentialsProvider,
        uiData: LivenessUiData,
        viewModel: LivenessViewModel
    ) {
        MaterialTheme(
            colorScheme = LivenessColorScheme.default(),

        ) {
            if(uiData.verificationPassedSuccessfully) {
                if(uiData.pictureKey.isNullOrEmpty()) viewModel.onVerificationPassedSuccessfully()
                handleNavigation(uiData)
            }
            if (uiData.errorLiveness == null && uiData.error.isNullOrEmpty()) {
                val view = LocalView.current


                Box(
                    modifier = Modifier
                        .onGloballyPositioned { coordinates->
                        UXCamKt.occludeSensitiveComposable(
                            identifier = "SensitiveFaceLivenessComposable",
                            view= view,
                            coordinates = coordinates
                        )
                    }
                ) {
                    FaceLivenessDetector(
                        sessionId = sessionId,
                        region = region,
                        credentialsProvider = credentialsProvider,
                        disableStartView = true,
                        onComplete = {
                            viewModel.submitSessionId(sessionId, uiData.pictureKey)
                        },
                        onError = { error ->
                            viewModel.onFaceLivenessDetectorError()
                        })
                }
            } else {
                when(uiData.errorLiveness){
                    is LivenessError.LivenessFaceMismatch -> {

                        sendVerificationFailedPopupEvent(ClevertapVerificationFailedReasonValues.DIFFERENT_PROFILE_FACE)

                        val action = VerifyAccountFaceLivenessFragmentDirections.actionVerifyAccountFaceLivenessFragmentToFaceMissMatchLivenessFragment(uiData.errorLiveness.image,viewModel.user.value?.profile?.pictureUrl ?: uiData.pictureKey)
                        findNavController().navigate(action)
                    }
                    null -> {/*not used*/}
                    else -> {
                        sendVerificationFailedPopupEvent(ClevertapVerificationFailedReasonValues.UNABLE_TO_VERIFY)
                        viewModel.onLivenessVerificationFailed(uiData.errorLiveness)
                        findNavController().popBackStack()
                    }
                }
                viewModel.onErrorHandled()
            }
        }
    }

    private fun handleNavigation(uiData: LivenessUiData) {
        if (uiData.badge2VerificationState == Badge2VerificationState.REQUIRED) {
            val navOptions = NavOptions.Builder()
                .setPopUpTo(R.id.mandatoryVerificationFragment, inclusive = true)
                .build()

            viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
                viewModel.user.value?.copy(badge2VerificationState = null)
                    ?.let { viewModel.userRepository.updateUser(it) }
                withContext(Dispatchers.Main){

                    findNavController().navigate(R.id.nav_graph, null, navOptions)
                }
            }

        } else {
            findNavController().popBackStack(R.id.verifyProfileWithBadge2PopUp, true)
        }
    }

    private fun sendVerificationFailedPopupEvent(failedReason: ClevertapVerificationFailedReasonValues) {
        val premiumType = getPremiumTypeEventProperty(viewModel.user.value)
        val properties = hashMapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
            ClevertapEventPropertyEnum.VERIFICATION_FAILED_REASON.propertyName to failedReason.value
        )
        if(activity is SignUpActivity) {
            properties[ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName] = sharedSignUpViewModel.authMethod?.methodName
            properties[ClevertapEventPropertyEnum.VERIFICATION_SOURCE.propertyName] = ClevertapVerificationSourceValues.ONBOARDING.value
        }

        sendClevertapEvent(
            ClevertapEventEnum.VERIFICATION_FAILED_POPUP,
            properties
        )
        sendUxCamEvent(
            ClevertapEventEnum.VERIFICATION_FAILED_POPUP,
            properties
        )
    }
}
