package com.duaag.android

//class SwitchMode : AppCompatActivity() {
//    companion object {
//        fun newIntent(context: Context?): Intent? {
//            return Intent(context, SwitchMode::class.java)
//        }
//    }
//    //used for removing decimal points
//    val CONSTANT_VALUE_ = 10000000000L
//    // callback used to help animate colors and save the current position used later to do layout changes
//    private val onPageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
//        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
//            super.onPageScrolled(position, positionOffset, positionOffsetPixels)
//            mColorAnimation.currentPlayTime = ((positionOffset + position) * CONSTANT_VALUE_).toLong()
//        }
//        override fun onPageSelected(position: Int) {
//            super.onPageSelected(position)
//            viewModel.setPagerPosition(position)
//        }
//    }
//    private lateinit var mColorAnimation: ValueAnimator
//    private var _binding: ActivitySwitchModeBinding? = null
//    private val binding get() = _binding!!
//    val viewModel: LoveStoryCardsViewModel by viewModels()
//    private var tabLayoutMediator: TabLayoutMediator? = null
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        _binding = DataBindingUtil.setContentView(this, R.layout.activity_switch_mode)
//        binding.lifecycleOwner = this
//        binding.viewModel = viewModel
//        binding.handlers = Handlers()
//        //make status bar color equal to main layout background
//        setTransparentStatusBar()
//        //set viewpager adapter, and attach tab layout to viewPager
//        val pager = binding.switchPager
//        val adapter = LoveStoryCardAdapter(viewModel._switchModes, LoveStoryCardAdapter.ModeCardClickListener { mode ->
//            when (mode) {
//                SwitchModeData.SwitchModeType.LOVE -> finishWithAnimation()
//                else -> ToastUtil.toast(getString(R.string.comming_soon))
//            }
//        })
//        setupViewPager(adapter, pager)
//        //register our callback to viewpager
//        pager.registerOnPageChangeCallback(onPageChangeCallback)
//        //page transformer that makes visible and smaller non selected items on viewpager
//        addPageTransformer(pager)
//        //add decoration to viewpager to avoid overlapping items
//        addItemDecoration(pager)
//        //setup colors for animation and change layout background color
//        animateBackgroundColor()
//    }
//    private fun animateBackgroundColor() {
//        mColorAnimation = ValueAnimator.ofObject(ArgbEvaluator(), getColor(R.color.love), getColor(R.color.biz), getColor(R.color.friends))
//        mColorAnimation.addUpdateListener { animator ->
//            binding.switchBaseLayout.setBackgroundColor(animator.animatedValue as Int)
//        }
//        // (3 - 1) = number of pages minus 1
//        mColorAnimation.duration = (3 - 1) * CONSTANT_VALUE_
//    }
//    private fun setupViewPager(adapter: LoveStoryCardAdapter, pager: ViewPager2?) {
//        pager?.offscreenPageLimit = 1
//        pager?.adapter = adapter
//        binding.tabLayout.let {
//            pager?.let { it1 ->
//                tabLayoutMediator = TabLayoutMediator(it, it1) { _, _ ->}
//                tabLayoutMediator?.attach()
//            }
//        }
//    }
//    private fun addPageTransformer(pager: ViewPager2?) {
//        // Add a PageTransformer that translates the next and previous items horizontally
//        // towards the center of the screen, which makes them visible
//        val pageTranslationX = getPageTranslationX()
//        val pageTransformer = ViewPager2.PageTransformer { page: View, position: Float ->
//            page.translationX = -pageTranslationX * position
//            // Next line scales the item's height.
//            page.scaleY = 1 - (0.25f * abs(position))
//            // Fading effect
//            page.alpha = 0.25f + (1 - abs(position))
//        }
//        pager?.setPageTransformer(pageTransformer)
//    }
//    private fun getPageTranslationX(): Float {
//        val nextItemVisiblePx = resources.getDimension(R.dimen.viewpager_next_item_visible)
//        val currentItemHorizontalMarginPx = resources.getDimension(R.dimen.viewpager_current_item_horizontal_margin)
//        return nextItemVisiblePx + currentItemHorizontalMarginPx
//    }
//    private fun addItemDecoration(pager: ViewPager2?) {
//        // The ItemDecoration gives the current (centered) item horizontal margin so that
//        // it doesn't occupy the whole screen width. Without it the items overlap
//        val itemDecoration = HorizontalMarginItemDecoration(
//            this,
//            R.dimen.viewpager_current_item_horizontal_margin
//        )
//        pager?.addItemDecoration(itemDecoration)
//    }
//    override fun onDestroy() {
//        super.onDestroy()
//        binding.switchPager.adapter = null
//        _binding = null
//        tabLayoutMediator?.detach()
//        tabLayoutMediator = null
//    }
//    private fun finishWithAnimation() {
//        finish()
//        ActivityNavigator.applyPopAnimationsToPendingTransition(this)
//    }
//    override fun onBackPressed() {
//        super.onBackPressed()
//        ActivityNavigator.applyPopAnimationsToPendingTransition(this)
//    }
//    inner class Handlers {
//        fun onButtonClicked(@Suppress("UNUSED_PARAMETER") mode: SwitchModeData.SwitchModeType?) {
//            finishWithAnimation()
//        }
//    }
//}