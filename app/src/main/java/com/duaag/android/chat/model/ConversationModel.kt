package com.duaag.android.chat.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.duaag.android.base.models.INSTAGRAM_STATUS_DISCONNECTED
import com.duaag.android.base.models.InstagramToken
import com.duaag.android.firebase.model.CallModel
import com.duaag.android.firebase.model.Caller
import com.duaag.android.home.models.Profile
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.recommender.domain.model.RmodModel
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.util.*

@Entity(tableName = "conversations")
@Parcelize
data class ConversationModel(@SerializedName("id")
                             val id: String?,
                             @SerializedName("userId")
                             @PrimaryKey
                             val userId: String,
                             @SerializedName("lastMessageText")
                             var lastMessageText: String?,
                             @SerializedName("lastMessageSender")
                             var lastMessageSender: String?,
                             @SerializedName("lastMessageType")
                             var lastMessageType: String,
                             @SerializedName("lastMessageTime")
                             var lastMessageTime: Long,
                             @SerializedName("name")
                             val name: String,
                             @SerializedName("gender")
                             val gender: String,
                             @SerializedName("pictureUrl")
                             val pictureUrl: String,
                             @SerializedName("thumbnailUrl")
                             val thumbnailUrl: String?,
                             @SerializedName("bluredThumbnailUrl")
                             val bluredThumbnailUrl: String?,
                             @SerializedName("seen")
                             var seen: Int,
                             @SerializedName("isStarred")
                             var isStarred: Int,
                             @SerializedName("type")
                             var type: String,
                             @SerializedName("hasBadge1")
                             var hasBadge1: Boolean,
                             @SerializedName("badge2")
                             var badge2: String?,
                             @SerializedName("wasInstachat")
                             var wasInstachat: Boolean,
                             @SerializedName("areIncomingCallsAllowed")
                             var areIncomingCallsAllowed: Boolean?,
                             @SerializedName("areOutgoingCallsAllowed")
                             var areOutgoingCallsAllowed: Boolean?,
                             @SerializedName("createdAt")
                             var createdAt: Long,
                             @SerializedName("isRMOD")
                             var isRMOD: Boolean,
                             @SerializedName("isDeleted")
                             var isDeleted: Int,
                             var nextCursor: String? = null,
                             var matchId: Int? = null,
) : Parcelable {

    @Ignore
    @IgnoredOnParcel
    var rmodItem: RmodModel? = null
    @IgnoredOnParcel var isMatchesSection: Boolean = false
    @IgnoredOnParcel var isEmpty: Boolean = false
    @IgnoredOnParcel var isLoading: Boolean = false
    @IgnoredOnParcel val showSeen: Boolean get() = seen == 0
    @IgnoredOnParcel val getIsDeleted: Boolean get() = isDeleted == 1

    @Suppress("PROPERTY_WONT_BE_SERIALIZED")
    var isFromMatchView: Boolean = false

    @Suppress("PROPERTY_WONT_BE_SERIALIZED")
    var hasFailed: Boolean = false

    @IgnoredOnParcel val showStar: Boolean get() = isStarred==1
    @IgnoredOnParcel var isMobAd: Boolean = false
    @IgnoredOnParcel var isMobAdShimmer: Boolean = false

    @IgnoredOnParcel var isFilterItem: Boolean = false
    @Ignore
    @Transient
    @IgnoredOnParcel var maxNativeAdView: MaxNativeAdView? = null

    @IgnoredOnParcel var isTyping: Boolean = false

    @IgnoredOnParcel  var lastIsTypingTrueTimestamp: Long = 0L

    @IgnoredOnParcel var activityType: String?  = null


    companion object{
        fun getEmptyConversation(): ConversationModel{
            return ConversationModel(UUID.randomUUID().toString(),"", "","","txt",0,"","","","","",0,0,ConversationType.DEFAULT.value,false,"",false,false,false,0,false, 0)
        }
    }
}

enum class ConversationType(val value:String) {
    DEFAULT("default"),DUA_BOT("duabot"),INSTANT_CHAT("instachat")
}

fun ConversationModel.asRecommendedModel() =
        RecommendedUserModel(
                cognitoUserId = userId,
                existingInteractionId ="",
                age = 0,
                firstName = name,
                gender = gender,
                profile = Profile(
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        pictureUrl,
                        thumbnailUrl,
                        emptyList(),
                        null
                ),
                id = 0,
                isMatch = null,
                false,
                badge2 =badge2,
               instagramStatus =  INSTAGRAM_STATUS_DISCONNECTED,
               tags =  emptyList(),
               zodiacSign = null,
               instagramToken =  InstagramToken(null, null, null))

fun ConversationModel.asCallModel(isVoiceCall: Boolean) =
    CallModel(
        Caller(name,pictureUrl,userId,hasBadge1,badge2),
        id!!,
        isVoiceCall,
        null,
        false,
        userId,
        null
      )
