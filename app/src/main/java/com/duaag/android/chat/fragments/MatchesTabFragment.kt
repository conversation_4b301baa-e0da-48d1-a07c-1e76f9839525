package com.duaag.android.chat.fragments

import android.app.AlertDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.ColorStateList
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.widget.SearchView
import androidx.core.content.ContextCompat
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.Observer
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.api.Result
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.fragment.BaseFragment
import com.duaag.android.chat.adapters.MatchesAdapter
import com.duaag.android.chat.adapters.MatchesClickListener
import com.duaag.android.chat.adapters.MessagesAdapter
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationType
import com.duaag.android.chat.model.ReportBody
import com.duaag.android.chat.model.UserMatchesModel
import com.duaag.android.chat.viewmodel.MatchesTabViewModel
import com.duaag.android.clevertap.ClevertapAddPhotoSourceValues
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapReportReasonsValues
import com.duaag.android.clevertap.ClevertapReportSourceValues
import com.duaag.android.clevertap.ClevertapUnMatchReasonsValue
import com.duaag.android.clevertap.ClevertapUnmatchTypeValues
import com.duaag.android.clevertap.UploadImageSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentMatchesTabBinding
import com.duaag.android.exceptions.NoConnectivityException
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.interfaces.ChatButtonClickListener
import com.duaag.android.home.models.InteractionBody
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsAddPhotoSourceValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.firebaseScreenLogEvent
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.NEW_IMAGES_REQUEST
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.OPENED_FROM
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.UPLOAD_IMAGE_SOURCE
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.PremiumActivity.Companion.PREMIUM_INTENT
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.recommender.domain.model.RmodModel
import com.duaag.android.recommender.domain.model.toConversationModel
import com.duaag.android.report.viewmodel.ReportViewModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user_feed.UserFeedFragment
import com.duaag.android.user_feed.UserFeedFragmentDirections
import com.duaag.android.utils.AgeUtils
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.calculateTimeLeft
import com.duaag.android.utils.hasTimestampPassed
import com.duaag.android.utils.setMaxLength
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.showRemoveRmodDialog
import com.duaag.android.utils.updateLocale
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject


class MatchesTabFragment : BaseFragment(), ChatButtonClickListener {

    companion object {
        const val SEARCH_KEY = "SEARCH_KEY"

        @JvmStatic
        fun newInstance() =
                MatchesTabFragment().apply {
                    arguments = Bundle().apply {
                    }
                }
    }


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs
    private val matchesTabViewModel by viewModels<MatchesTabViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val reportViewModel by viewModels<ReportViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private var _binding: FragmentMatchesTabBinding? = null
    private val binding get() = _binding!!
    private var searchQuery: String? = null

    private val premiumBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            val isPremium = intent?.getBooleanExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, false)
                    ?: false
            if (isPremium) {
                matchesTabViewModel.initData(true)
                if (activity is HomeActivity) (activity as HomeActivity).removeAdChat()
            }
        }
    }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (savedInstanceState != null) {
            searchQuery = savedInstanceState.getString(SEARCH_KEY)
        }
    }

    override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        _binding = FragmentMatchesTabBinding.inflate(inflater)
        initBaseFunctions()
        updateUnreadConversationsCounter()
        updateMatchesCounter()

        if (matchesTabViewModel.getIsSearching() ) {
            keepSearchOpen()
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                while (isActive) {
                    val rmod = homeViewModel.rmodItem.value

                    if (rmod != null && (!rmod.isHidden || rmod.isDeleted && !rmod.isSeen)) {

                        homeViewModel.rmodItem.value?.rmodActiveUntil?.let {
                            val hasExpired = hasTimestampPassed(it)
                            if (hasExpired) {
                                homeViewModel.rmodItem.value?.isHidden = true
                                getConversationsAdapter().setRmodItem(null)
                            } else {
                                updateTimeInRmodItem(calculateTimeLeft(it), rmodItem = homeViewModel.rmodItem.value!!)
                            }
                        }
                    }
                    delay(1000)
                }
            }
        }

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        matchesTabViewModel.onSetDefaultData.observe(viewLifecycleOwner, Observer {
            setDefaultData()
            closeSearch()
        })
    }

    override fun initialize() {

        val layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        binding.recyclerviewMessages.layoutManager = layoutManager
//        binding.recyclerviewMessages.itemAnimator = null

        val adapterMatches = MatchesAdapter(MatchesClickListener { userModel ->
            Timber.tag("isSuperMatch").e("${userModel.receivedSuperMatch}")
            if (userModel.seen == 0) {
                matchesTabViewModel.updateSeenMatchAdapter(userModel.id)
                matchesTabViewModel.seenMatch(userModel.id).observe(viewLifecycleOwner, Observer {
                    when (it) {
                        is Result.Success -> {
                        }
                        is Result.Error -> {
                        }
                        is Result.Loading -> {
                        }
                    }
                })

                if(userModel.getIsDeleted) {
                    matchesTabViewModel.removeMatchByUserId(userId = userModel.user.cognitoUserId)
                }

            }

            val conversationModel = ConversationModel(
                    null,
                    userModel.user.cognitoUserId,
                    "",
                    "",
                    "txt",
                    userModel.time,
                    userModel.user.firstName,
                    "",
                    userModel.user.profile.pictureUrl,
                    null,
                    null,
                    1,
                    userModel.receivedSuperMatch,
                    ConversationType.DEFAULT.value,
                    userModel.user.hasBadge1,
                    userModel.user.badge2,
                    false,
                    false,
                    false,
                    0,
                    false,
                    userModel.isDeleted,
                    null,
                    userModel.id
            )

            conversationModel.isFromMatchView = true
            if (parentFragment?.findNavController()?.currentDestination?.id == R.id.userFeedFragment) {
                parentFragment?.findNavController()?.navigate(
                        UserFeedFragmentDirections.actionUserFeedFragmentToConversationFragment3(
                                conversationModel, null
                        )
                )
            }

            clearSearchView()

        }, matchesTabViewModel)

        val adapterMessages = MessagesAdapter(adapterMatches,
                matchesTabViewModel,
                MessagesAdapter.MessagesClickListener { userModel ->
                    if (userModel.isMobAd) {
                        showAdMobReportDialog(userModel)
                    } else {
                        if(userModel.getIsDeleted) {
                            matchesTabViewModel.removeConversationByCognitoId(userId = userModel.userId)
                        }

                        if (parentFragment?.findNavController()?.currentDestination?.id == R.id.userFeedFragment) {
                            val rmodModel = if(userModel.isRMOD) homeViewModel.rmodItem.value else null
                            parentFragment?.findNavController()?.navigate(
                                UserFeedFragmentDirections
                                    .actionUserFeedFragmentToConversationFragment3(
                                        userModel,
                                        rmodModel
                                    )
                            )
                        }
                        if (userModel.showSeen) {
                            matchesTabViewModel.makeConversationSeen(userModel.id)
                        }
                    }

                    if (getSearchView()?.isIconified != false)
                        clearSearchView()

                },
                MessagesAdapter.MessagesLongClick {
                    showConversationLongClickBottomSheet(it)
                },
                homeViewModel.userProfile.value?.cognitoUserId,
                this,
                object : MessagesAdapter.MatchesSectionCallback {
                    override fun loadMoreMatches(filter: String?, nextCursor: String?) {
                        <EMAIL>(filter, nextCursor)
                    }

                    override fun loadMoreConversations(filter: String?, nextCursor: String?) {
                        <EMAIL>(filter, nextCursor)
                    }

                    override fun setSearchData() {
                        <EMAIL>()
                    }

                    override fun setDefaultData() {
                        <EMAIL>()
                    }

                    override fun updateCounters() {
                        <EMAIL>()
                        <EMAIL>()
                    }

                    override fun clearSearchView() {
                        <EMAIL>()
                    }

                    override fun onRmodItemClicked(rmodModel: RmodModel) {
                        if(rmodModel.isDeleted)
                            homeViewModel.rmodItem.value?.isSeen = true

                        val conversationModel = rmodModel.toConversationModel().apply { isFromMatchView = true }
                        val direction = UserFeedFragmentDirections.actionUserFeedFragmentToConversationFragment3(conversationModel, rmodModel)

                        if(findNavController().currentDestination?.id == R.id.userFeedFragment) {
                            findNavController().navigate(direction)
                        }

                        matchesTabViewModel.setIsSearching(false)
                    }

                    override fun onRmodItemLongClicked(rmodModel: RmodModel) {
                        showConversationLongClickBottomSheet(rmodModel.toConversationModel())
                    }

                })


        adapterMessages.setHasStableIds(true)
        binding.recyclerviewMessages.adapter = adapterMessages


        matchesTabViewModel.searchConversation.observe(viewLifecycleOwner, {
            Timber.tag("sharedFragment").e(it.toString())
            if (matchesTabViewModel.getIsSearching())
                adapterMessages.setConversations(it as ArrayList<ConversationModel>, rmodItem = homeViewModel.rmodItem.value)
        })

        matchesTabViewModel.searchMatches.observe(viewLifecycleOwner, {
            if (matchesTabViewModel.getIsSearching())
                adapterMatches.setData(it as ArrayList<UserMatchesModel>)
            Timber.tag("sharedFragment").e(it.toString())
        })


        binding.recyclerviewMessages.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val mLayoutManager: LinearLayoutManager =
                        recyclerView.layoutManager as LinearLayoutManager
                val nextCursor =
                        if (matchesTabViewModel.getIsSearching()) matchesTabViewModel.conversationsSearchNextCursor else matchesTabViewModel.conversationsNextCursor
                val filter =
                        if (matchesTabViewModel.getIsSearching() && getSearchView() != null && getSearchView()!!.query.isNotEmpty()) getSearchView()?.query.toString() else null

                if (!matchesTabViewModel.isConversationsLoading && nextCursor != null) {
                    if (mLayoutManager.findLastCompletelyVisibleItemPosition() == adapterMessages.itemCount - 1) {
                        if (NetworkChecker.isNetworkConnected(requireContext()))
                            loadMoreConversations(filter, nextCursor)
                    }
                }
            }
        })

        //register recieveer for premium subscription events
        val premiumSubscriptionFilter = IntentFilter(PREMIUM_INTENT)
        LocalBroadcastManager.getInstance(requireContext())
                .registerReceiver(premiumBroadcastReceiver, premiumSubscriptionFilter)
    }

    private fun showConversationLongClickBottomSheet(conversationModel: ConversationModel) {
        if (conversationModel.type == ConversationType.DUA_BOT.value && conversationModel.seen == 0) return
        val type: ConversationsLongClickBottomSheetFragment.LongClickType =
                when {
                    conversationModel.isRMOD -> ConversationsLongClickBottomSheetFragment.LongClickType.RMOD
                    conversationModel.seen == 0 -> ConversationsLongClickBottomSheetFragment.LongClickType.READ
                    conversationModel.type == ConversationType.DUA_BOT.value -> ConversationsLongClickBottomSheetFragment.LongClickType.DUA_BOOT
                    else -> ConversationsLongClickBottomSheetFragment.LongClickType.UNREAD
                }
        val fragment = ConversationsLongClickBottomSheetFragment.newInstance(object :
                ConversationsLongClickBottomSheetFragment.ConversationsLongClickListener {
            override fun onButtonsClick(
                    button: View,
                    fragment: ConversationsLongClickBottomSheetFragment
            ) {
                when (button.id) {
                    R.id.button1 -> {
                        matchesTabViewModel.unSeenConversation(conversationModel.id!!)
                                .observe(viewLifecycleOwner,
                                        {
                                            when (it) {
                                                is Result.Success -> matchesTabViewModel.makeConversationSeen(
                                                        conversationModel.id,
                                                        0
                                                )
                                                is Result.Error -> {
                                                }
                                                Result.Loading -> {
                                                }
                                            }
                                        })
                    }
                    R.id.button2 -> {
                        showUnMatchDialog(conversationModel)
                    }
                    R.id.button3 -> {
                        showReportDialog(conversationModel)
                    }
                    R.id.remove_rmod_btn -> {
                        showRemoveRmodDialog(requireContext()
                        ) {
                            homeViewModel.rmodItem.value?.isHidden = true
                            getConversationsAdapter().setRmodItem(null)

                            val body = InteractionBody(
                                type = InteractionType.DISLIKE.value,
                                receiverUserId = conversationModel.userId,
                                isRMOD = true,
                            )
                            homeViewModel.removeRmod(body, homeViewModel.rmodItem.value)
                        }
                    }
                }

                fragment.dismissAllowingStateLoss()
            }

        }, type)


        if (childFragmentManager.findFragmentByTag("ConversationsLongClickBottomSheetFragment") == null) {
            fragment?.show(childFragmentManager, "ConversationsLongClickBottomSheetFragment")
        }
    }

    override fun setToolbar() {
    }

    override fun observeErrors() {
    }

    override fun setCallBacks() {
        homeViewModel.userProfile.observe(viewLifecycleOwner, Observer {user->
            val isShadowBanned = user?.profile?.isShadowBanned ?: false

            binding.swipeRefresh.visibility = if (isShadowBanned) View.GONE else View.VISIBLE
            binding.layoutShadow.root.visibility = if (isShadowBanned) View.VISIBLE else View.GONE

        })
        binding.layoutShadow.shadowLockBtn.setOnSingleClickListener {
               onAddPhotoClicked()
          }
        matchesTabViewModel.checkIfDataAreEmpty()
        populateLists()
        matchesTabViewModel.matches.observe(viewLifecycleOwner, Observer {
            if (!matchesTabViewModel.getIsSearching()) {
                getConversationsAdapter().setMatches(ArrayList(it))
                matchesTabViewModel.checkIfDataAreEmpty()
            }
        })
        matchesTabViewModel.conversations.observe(viewLifecycleOwner, Observer {
            if (!matchesTabViewModel.getIsSearching()) {
                getConversationsAdapter().setConversations(ArrayList(it), rmodItem = homeViewModel.rmodItem.value)
                matchesTabViewModel.checkIfDataAreEmpty()
            }
        })
        matchesTabViewModel.matchesSearch.observe(viewLifecycleOwner, Observer {
            if (matchesTabViewModel.getIsSearching()) {
                getConversationsAdapter().setMatches(ArrayList(it))
                matchesTabViewModel.checkIfDataAreEmpty()
            }
        })
        matchesTabViewModel.conversationsSearch.observe(viewLifecycleOwner, Observer {
            if (matchesTabViewModel.getIsSearching()) {
                getConversationsAdapter().setConversations(
                    ArrayList(it),
                    matchesTabViewModel.matchesSearch.value?.isNullOrEmpty() != true,
                    rmodItem = homeViewModel.rmodItem.value
                )
                matchesTabViewModel.checkIfDataAreEmpty()
            }
        })
        homeViewModel.webSocketConversations.observe(viewLifecycleOwner, Observer {
            it?.let { webSocketConversations ->
                matchesTabViewModel.onMessageReceives(webSocketConversations)
                matchesTabViewModel.checkToRemoveMatch(webSocketConversations)
                lifecycleScope.launchWhenResumed {
                    if (matchesTabViewModel.conversations.value != null && matchesTabViewModel.conversations.value!!.isNotEmpty()) {
                        binding.recyclerviewMessages.scrollToPosition(0)
                    }
                }
                homeViewModel.clearConversations()
                updateDotNotification()
            }
        })

        homeViewModel.onMatchFragmentShowed.observe(viewLifecycleOwner, Observer {

        })

        binding.btnFilter.setOnClickListener {
            homeViewModel.showFilter()
        }

        binding.swipeRefresh.setOnRefreshListener {
            if (matchesTabViewModel.getIsSearching()) {
                matchesTabViewModel.loadMoreMatches(getSearchView()?.query.toString(), null, true)
                matchesTabViewModel.loadMoreConversations(getSearchView()?.query.toString(), null, true)
            } else {
                matchesTabViewModel.initData(true)
                matchesTabViewModel.setStatusNoUsers(false)
            }
        }

        matchesTabViewModel.hideSwipeRefresh.observe(viewLifecycleOwner) {
            binding.swipeRefresh.isRefreshing = false
        }

        matchesTabViewModel.showFilter.observe(viewLifecycleOwner) { showFilterBtn ->
            getSearchView()?.let {
                if (showFilterBtn) {
                    binding.filterContainer.visibility = View.VISIBLE
                    enableSearchView(it, false)
                } else {
                    binding.filterContainer.visibility = View.GONE
                    enableSearchView(it, true)
                }
            }
        }

        matchesTabViewModel.statusNoUsers.observe(viewLifecycleOwner) { showNoUsersFound ->
            if (showNoUsersFound) {
                getNoUsersView()?.visibility = View.VISIBLE
                getNewMatchesBackgroundContainer()?.backgroundTintList = ColorStateList.valueOf(
                    ContextCompat.getColor(
                        requireContext(),
                        android.R.color.transparent
                    )
                )

                getMatchesRecyclerView()?.visibility = View.GONE

                getMessagesText()?.visibility = View.GONE
                getUnreadMessageCount()?.visibility = View.GONE
                getMatchesText()?.visibility = View.GONE
                getMatchesCounter()?.visibility = View.GONE

            } else {
                getNoUsersView()?.visibility = View.GONE
                getNewMatchesBackgroundContainer()?.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(requireContext(), R.color.border))

                getMatchesRecyclerView()?.visibility = View.VISIBLE
                getMessagesText()?.visibility = View.VISIBLE
                getMatchesText()?.visibility = View.VISIBLE

                showUnreadMessagesIfNotEmpty()
                showMatchesCounterIfNotEmpty()

                updateMatchesCounter()
            }
        }

        matchesTabViewModel.networkError.observe(viewLifecycleOwner) { hasNoInternet ->
            if (hasNoInternet) {
                getNoUsersView()?.visibility = View.VISIBLE
                getNewMatchesBackgroundContainer()?.backgroundTintList = ColorStateList.valueOf(
                    ContextCompat.getColor(
                        requireContext(),
                        android.R.color.transparent
                    )
                )
            } else {
                binding.filterContainer.visibility = View.GONE
                getNewMatchesBackgroundContainer()?.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(requireContext(), R.color.border))
            }
        }


        homeViewModel.navigateToChat.observe(viewLifecycleOwner) {
            (parentFragment as UserFeedFragment).navigateToChat(it)
            matchesTabViewModel.makeConversationSeenAfterNotificationClick(it.id)
        }

        homeViewModel.newMatch.observe(viewLifecycleOwner) { list ->
            list?.let {
                list.forEach { matchesTabViewModel.addNewMatch(it) }
                updateDotNotification()
                lifecycleScope.launchWhenResumed {
                    if (matchesTabViewModel.matches.value != null && matchesTabViewModel.matches.value!!.isNotEmpty()) {
                        getMatchesRecyclerView()?.scrollToPosition(0)
                    }
                }
                homeViewModel.clearNewMatches()
            }

        }

        homeViewModel.newConversation.observe(viewLifecycleOwner) { list ->
            list?.let {
                list.forEach { matchesTabViewModel.addNewConversation(it) }
                updateDotNotification()
                lifecycleScope.launchWhenResumed {
                    if (matchesTabViewModel.conversations.value != null && matchesTabViewModel.conversations.value!!.isNotEmpty()) {
                        binding.recyclerviewMessages.scrollToPosition(0)
                    }
                }
                homeViewModel.clearNewConversations()
            }
        }

        homeViewModel.unMatches.observe(viewLifecycleOwner) { list ->
            list?.let {
                list.forEach { matchesTabViewModel.unMatchFromOthers(it) }
                homeViewModel.clearNewUnMatches()
            }
        }

        homeViewModel.deleteUserMatchByUserIdFromConversation1to1.observe(viewLifecycleOwner) {
            it?.let {
                matchesTabViewModel.removeMatchByUserId(it)
            }
        }

        matchesTabViewModel.showChatMobAd.observe(viewLifecycleOwner) {
            if (activity is HomeActivity) (activity as HomeActivity).addNewChatMobAd(it)
        }

        matchesTabViewModel.checkForUnreadConversationBadge.observe(viewLifecycleOwner) {
            updateUnreadConversationsCounter()
        }

        matchesTabViewModel.matchesCountBadge.observe(viewLifecycleOwner) {
            updateMatchesCounter()
        }

        homeViewModel.callEnded.observe(viewLifecycleOwner) {
            lifecycleScope.launch {
                delay(500)
                matchesTabViewModel.getPreviousConversations()
            }
        }

        reportViewModel.reportSuspendedUser.observe(viewLifecycleOwner) {
            showUserSuspended(it)
        }

        homeViewModel.rmodItem.observe(viewLifecycleOwner) { rmodModel ->
            rmodModel?.let { getConversationsAdapter().setRmodItem(it) }
        }
    }
    private fun onAddPhotoClicked() {
        sendAddPhotoEvents()

        val intent = Intent(context, ManagePicturesActivity::class.java)
        intent.putExtra(UPLOAD_IMAGE_SOURCE, arguments?.getString(OPENED_FROM) ?: UploadImageSourceValues.UNLOCK_ALL_PHOTOS.value)
        startActivityForResult(intent, NEW_IMAGES_REQUEST)
    }
    private fun sendAddPhotoEvents() {
        sendClevertapEvent(
            ClevertapEventEnum.ADD_PHOTO,
            mapOf(ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapAddPhotoSourceValues.MATCHES.value)
        )
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.ADD_PHOTO,
            mapOf(FirebaseAnalyticsParameterName.EVENT_SOURCE.value to FirebaseAnalyticsAddPhotoSourceValues.MATCHES.value)
        )
    }
    private fun clearSearchView() {
        getSearchView()?.setQuery("", false)
        getSearchView()?.isIconified = true
    }

    fun showInputMethod() {
        val imm =
                requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY)
    }

    fun showUnMatchDialog(conversationModel: ConversationModel) {
        // setup the alert builder
        val builder = android.app.AlertDialog.Builder(context)

        val list = arrayOf(
                getString(R.string.no_reason), getString(R.string.offensive_messages), getString(
                R.string.inappropriate_photos
        ), getString(R.string.bad_offline), getString(R.string.feels_like_spam)
        )
        builder.setItems(list) { _, which ->
            when (which) {
                0 -> {
                    matchesTabViewModel.unMatch(conversationModel.userId)
                            .observe(
                                    viewLifecycleOwner,
                                    Observer {
                                        when (it) {
                                            is Result.Success -> {
                                                firebaseLogEvent(
                                                        FirebaseAnalyticsEventsName.UNMATCH_IN_MESSAGES,
                                                        mapOf(
                                                                FirebaseAnalyticsParameterName.UMATCH_POPUP_COUNT.value to 1L,
                                                                FirebaseAnalyticsParameterName.NO_REASON.value to 1L
                                                        )
                                                )

                                                val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                                val unmatchSource = ClevertapUnmatchTypeValues.IN_MESSAGES.value

                                                sendClevertapEvent(
                                                    ClevertapEventEnum.UNMATCH, mapOf(
                                                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                                        ClevertapEventPropertyEnum.UNMATCH_SOURCE.propertyName to unmatchSource,
                                                        ClevertapEventPropertyEnum.UNMATCH_REASONS.propertyName to ClevertapUnMatchReasonsValue.NO_REASON.value))
                                            }
                                            is Result.Error -> {
                                                when (it.exception) {
                                                    is NoConnectivityException -> {
                                                        matchesTabViewModel.networkError(true)
                                                    }
                                                }
                                            }
                                            Result.Loading -> {
                                            }
                                        }
                                    })
                    matchesTabViewModel.removeConversation(conversationModel)
                }

                1 -> {
                    firebaseLogEvent(
                            FirebaseAnalyticsEventsName.UNMATCH_IN_MESSAGES, mapOf(
                            FirebaseAnalyticsParameterName.UMATCH_POPUP_COUNT.value to 1L,
                            FirebaseAnalyticsParameterName.OFFENSIVE_MESSAGES.value to 1L
                    )
                    )

                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                    val unmatchSource = ClevertapUnmatchTypeValues.IN_MESSAGES.value

                    sendClevertapEvent(
                        ClevertapEventEnum.UNMATCH, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.UNMATCH_SOURCE.propertyName to unmatchSource,
                            ClevertapEventPropertyEnum.UNMATCH_REASONS.propertyName to ClevertapUnMatchReasonsValue.OFFENSIVE_MESSAGES.value))

                    val reportBody = ReportBody(
                            conversationModel.userId,
                            "offensive_messages",
                            null
                    )
                    reportUser(reportBody, conversationModel)
                }

                2 -> {
                    firebaseLogEvent(
                            FirebaseAnalyticsEventsName.UNMATCH_IN_MESSAGES, mapOf(
                            FirebaseAnalyticsParameterName.UMATCH_POPUP_COUNT.value to 1L,
                            FirebaseAnalyticsParameterName.INAPPROPRIATE_PHOTOS.value to 1L
                    )
                    )

                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                    val unmatchSource = ClevertapUnmatchTypeValues.IN_MESSAGES.value

                    sendClevertapEvent(
                        ClevertapEventEnum.UNMATCH, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.UNMATCH_SOURCE.propertyName to unmatchSource,
                            ClevertapEventPropertyEnum.UNMATCH_REASONS.propertyName to ClevertapUnMatchReasonsValue.INAPPROPRIATE_PHOTOS.value))

                    val reportBody = ReportBody(
                            conversationModel.userId,
                            "inappropriate_photos",
                            null
                    )
                    reportUser(reportBody, conversationModel)
                }

                3 -> {
                    firebaseLogEvent(
                            FirebaseAnalyticsEventsName.UNMATCH_IN_MESSAGES, mapOf(
                            FirebaseAnalyticsParameterName.UMATCH_POPUP_COUNT.value to 1L,
                            FirebaseAnalyticsParameterName.BAD_OFFLINE_BEHAVIOUR.value to 1L
                    )
                    )

                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                    val unmatchSource = ClevertapUnmatchTypeValues.IN_MESSAGES.value

                    sendClevertapEvent(
                        ClevertapEventEnum.UNMATCH, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.UNMATCH_SOURCE.propertyName to unmatchSource,
                            ClevertapEventPropertyEnum.UNMATCH_REASONS.propertyName to ClevertapUnMatchReasonsValue.BAD_OFFLINE_BEHAVIOUR.value))


                    val reportBody = ReportBody(
                            conversationModel.userId,
                            "bad_offline_behaviour",
                            null
                    )
                    reportUser(reportBody, conversationModel)
                }

                4 -> {
                    firebaseLogEvent(
                            FirebaseAnalyticsEventsName.UNMATCH_IN_MESSAGES, mapOf(
                            FirebaseAnalyticsParameterName.UMATCH_POPUP_COUNT.value to 1L,
                            FirebaseAnalyticsParameterName.FEELS_LIKE_SPAM.value to 1L
                    )
                    )

                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                    val unmatchsource = ClevertapUnmatchTypeValues.IN_MESSAGES.value

                    sendClevertapEvent(
                        ClevertapEventEnum.UNMATCH, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.UNMATCH_SOURCE.propertyName to unmatchsource,
                            ClevertapEventPropertyEnum.UNMATCH_REASONS.propertyName to ClevertapUnMatchReasonsValue.FEELS_LIKE_SPAM.value))

                    val reportBody = ReportBody(
                            conversationModel.userId,
                            "spam",
                            null
                    )
                    reportUser(reportBody, conversationModel)
                }

            }
        }

        // create and show the alert dialog
        val dialog = builder.create()
        dialog.show()
    }

    private fun showUserSuspended(suspendedPeriod: String) {
        MaterialAlertDialogBuilder(requireContext())
                .setTitle(resources.getString(R.string.reports_not_active))
                .setMessage(resources.getString(R.string.report_suspended_users, suspendedPeriod))
                .setNegativeButton(resources.getString(R.string.understood)) { _, _ -> }
                .show()
                .getButton(AlertDialog.BUTTON_NEGATIVE)
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
    }

    fun reportUser(reportBody: ReportBody, conversationModel: ConversationModel) {
        reportViewModel.limitReached.observe(viewLifecycleOwner, {
            matchesTabViewModel.removeConversation(conversationModel)
        })
        reportViewModel.reportUser.observe(viewLifecycleOwner, Observer {
            when (it) {
                is Resource.Success -> {
                    matchesTabViewModel.removeConversation(conversationModel)
                    homeViewModel.userProfileProgressBarShow(false)
                }
                is Resource.Error -> {
                    ToastUtil.toast(getString(R.string.smthg_went_wrong))
                    homeViewModel.userProfileProgressBarShow(false)
                    logError(ErrorStatus.REPORT_USER)
                }
                Resource.Loading -> {
                    homeViewModel.userProfileProgressBarShow(true)
                }
            }
        })
        reportViewModel.reportUser(reportBody)
    }

    fun showReportDialog(conversationModel: ConversationModel) {
        // setup the alert builder
        val builder = AlertDialog.Builder(requireContext())

        // add a list
        val list = arrayOf(
                getString(R.string.hate_speech),
                getString(R.string.spam),
                getString(R.string.sexual_harassment),
                getString(
                        R.string.other_unm
                )
        )
        builder.setItems(list) { _, which ->
            when (which) {
                0 -> {
                    reportFirebaseEventLog(FirebaseAnalyticsParameterName.HATE_SPEECH_COUNT)
                    val reportBody = ReportBody(
                            conversationModel.userId,
                            "hate_speech",
                            null
                    )

                    val reportReasonsValues = ClevertapReportReasonsValues.HATE_SPEECH.values
                    reportClevertap(reportReasonsValues)

                    reportUser(reportBody, conversationModel)
                }

                1 -> {
                    reportFirebaseEventLog(FirebaseAnalyticsParameterName.SPAM_COUNT)
                    val reportBody = ReportBody(
                            conversationModel.userId,
                            "spam",
                            null
                    )

                    val reportReasonsValues = ClevertapReportReasonsValues.SPAM.values
                    reportClevertap(reportReasonsValues)

                    reportUser(reportBody, conversationModel)
                }

                2 -> {
                    reportFirebaseEventLog(FirebaseAnalyticsParameterName.SEXUAL_HARRASMENT_COUNT)
                    val reportBody = ReportBody(
                            conversationModel.userId,
                            "sexual_harassment",
                            null
                    )

                    val reportReasonsValues = ClevertapReportReasonsValues.SEXUAL_HARRASMENT.values
                    reportClevertap(reportReasonsValues)

                    reportUser(reportBody, conversationModel)
                }

                3 -> {
                    showOtherDialog(conversationModel)
                }
            }
        }

        // create and show the alert dialog
        val dialog = builder.create()
        dialog.show()
        initiatedReportEvent()
    }

  private  fun reportFirebaseEventLog(parameterName: FirebaseAnalyticsParameterName) {
        firebaseLogEvent(
                FirebaseAnalyticsEventsName.REPORT_POPUP_IN_MESSAGES, mapOf(
                parameterName.value to 1L,
                FirebaseAnalyticsParameterName.REPORT_IN_MSG_POPUP.value to 1L,
                FirebaseAnalyticsParameterName.REPORTER_AGE.value to AgeUtils.getAge(homeViewModel.userProfile.value?.birthDate)
                        .toLong(),
                FirebaseAnalyticsParameterName.REPORTER_GENDER.value to homeViewModel.userProfile.value!!.gender
        )
        )
    }

   private fun reportClevertap(reportReasonsValues: String) {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userRepository.user.value)

        val reportSourceValues = ClevertapReportSourceValues.MESSAGE_LIST.values

        sendClevertapEvent(
            ClevertapEventEnum.REPORT_PROFILE, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.REPORT_SOURCE.propertyName to reportSourceValues,
                ClevertapEventPropertyEnum.REPORT_REASONS.propertyName to reportReasonsValues)
        )
    }

  private  fun initiatedReportEvent() {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
        val eventSourceScreen = ClevertapReportSourceValues.MESSAGE_LIST.values
        sendClevertapEvent(
            ClevertapEventEnum.REPORT_PROFILE_INITIATED, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.REPORT_SOURCE.propertyName to eventSourceScreen
            )
        )
    }

    private fun showOtherDialog(conversationModel: ConversationModel) {
        val builder = AlertDialog.Builder(context)
        builder.setTitle(getString(R.string.report_reason))
        // set the custom layout
        val customLayout: View = layoutInflater.inflate(R.layout.report_dialog, null)
        val editText = customLayout.findViewById<EditText>(R.id.edit_text_report)
        editText.setMaxLength(280)
        builder.setView(customLayout)
        // add a button
        @Suppress("UNUSED_ANONYMOUS_PARAMETER")
        builder.setPositiveButton(getString(R.string.submit)) { dialog, which ->
            reportFirebaseEventLog(FirebaseAnalyticsParameterName.OTHER_COUNT)
            // send data from the AlertDialog to the Activity
            val reportBody = ReportBody(
                    conversationModel.userId,
                    "other",
                    editText.text.toString()
            )

            val reportReasonsValues = ClevertapReportReasonsValues.OTHER.values
            reportClevertap(reportReasonsValues)

            reportUser(reportBody, conversationModel)
        }
        @Suppress("UNUSED_ANONYMOUS_PARAMETER")
        builder.setNegativeButton(getString(R.string.cancel)) { dialog, which -> }

        // create and show the alert dialog
        val dialog = builder.create()
        dialog.show()

        dialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(requireContext(), R.color.disable_primary))
        dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(requireContext(), R.color.disable_primary))
        dialog.getButton(AlertDialog.BUTTON_POSITIVE).isEnabled = false

        editText.doAfterTextChanged {
            when {
                editText.text.isEmpty() -> {
                    dialog.getButton(AlertDialog.BUTTON_POSITIVE).isEnabled = false
                    dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(requireContext(), R.color.disable_primary))
                }
                else -> {
                    dialog.getButton(AlertDialog.BUTTON_POSITIVE).isEnabled = true
                    dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(requireContext(), R.color.title_primary))
                }
            }
        }
    }

    private fun showAdMobReportDialog(item:ConversationModel) {
        if (isAdded && activity != null) {
            // setup the alert builder
            val builder = AlertDialog.Builder(requireContext())

            // add a list
            val list = arrayOf(getString(R.string.hide), getString(R.string.report))
            builder.setItems(list) { _, which ->
                when (which) {
                    0 -> {
                        firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_ADS)

                        requireActivity().openPremiumPaywall(
                            viewPagerStartPosition = BenefitsPremiumAdapter.PremiumPaywallList.AD_FREE_ITEM,
                            eventSourceClevertap = ClevertapEventSourceValues.ADS_REMOVAL_IN_CONVERSATION,
                            placementId = PurchaselyPlacement.ADS_REMOVAL.id,
                            userModel = homeViewModel.userProfile.value
                        )
                    }
                    1 -> {
                        openLinkInBrowser(requireContext(),getString(R.string.support_app))
                        removeAdItem(item)
                    }
                }
            }

            // create and show the alert dialog
            val dialog = builder.create()
            dialog.show()
        }
    }
    private fun openLinkInBrowser(context: Context,url: String) {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = Uri.parse(url)
        if (intent.resolveActivity(context.packageManager) != null) {
            startActivity(intent)
        }
    }
    private fun removeAdItem(item: ConversationModel) {
        matchesTabViewModel.conversations.value?.let {
            val newItems = ArrayList(it)
            newItems.remove(item)
            matchesTabViewModel.setConversations(newItems)
        }
    }

    //function to disable searchView
    private fun enableSearchView(view: View, enabled: Boolean) {
        view.isEnabled = enabled
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val child = view.getChildAt(i)
                enableSearchView(child, enabled)
            }
        }
    }

    private fun setDefaultData() {
        getConversationsAdapter().setMatches(ArrayList(matchesTabViewModel.matches.value!!))
        getConversationsAdapter().setConversations(
            ArrayList(matchesTabViewModel.conversations.value!!),
            rmodItem = homeViewModel.rmodItem.value
        )

        binding.recyclerviewMessages.scrollToPosition(0)
        getMatchesRecyclerView()?.scrollToPosition(0)

        updateMatchesCounter()
        updateUnreadConversationsCounter()
    }

    private fun setSearchData() {
        getConversationsAdapter().setMatches(ArrayList(matchesTabViewModel.matchesSearch.value!!))
        getConversationsAdapter().setConversations(
            ArrayList(matchesTabViewModel.conversationsSearch.value!!),
            rmodItem = homeViewModel.rmodItem.value
        )
    }

    private fun populateLists() {
        if (matchesTabViewModel.getIsSearching())
            getConversationsAdapter().setMatches(ArrayList(matchesTabViewModel.matchesSearch.value!!))
        else
            getConversationsAdapter().setMatches(ArrayList(matchesTabViewModel.matches.value!!))
    }

    fun loadMoreMatches(filter: String?, nextCursor: String?) {
        if(filter==null)
            getMatchesRecyclerView()?.post {
                getMatchesAdapter().addLoadingItem()
                getMatchesRecyclerView()?.scrollToPosition(getMatchesAdapter().itemCount - 1)
            }

        if (!matchesTabViewModel.isMatchesLoading) {
            matchesTabViewModel.loadMoreMatches(filter, nextCursor)
        }
    }

    fun loadMoreConversations(filter: String?, nextCursor: String?) {
        if(filter==null)
                binding.recyclerviewMessages.post {
                    if (viewLifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                        getConversationsAdapter().addLoadingItem()
                        binding.recyclerviewMessages.scrollToPosition(getConversationsAdapter().itemCount - 1)
                    }
                }

        matchesTabViewModel.loadMoreConversations(filter, nextCursor)

    }
    private fun updateDotNotification() {
        matchesTabViewModel.duaSharedPrefs.getDotNotificationSharedPrefs()
                .setBothMatchAndConversationSeenTime(
                        System.currentTimeMillis()
                )
        homeViewModel.showMatchTabDotNotification(false)
    }

    override fun onResume() {
        super.onResume()
        binding.swipeRefresh.isEnabled = true
        updateDotNotification()
        (activity as HomeActivity).setChatButtonClickListener(this)
        firebaseScreenLogEvent(FirebaseAnalyticsParameterName.MATCHES_TAB_SCREEN.value)
    }

    override fun onPause() {
        super.onPause()
        binding.swipeRefresh.isEnabled = false
        (activity as HomeActivity).removeChatButtonClickListener()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        if (_binding != null) {
            searchQuery = getSearchView()?.query.toString()
            outState.putString(SEARCH_KEY, searchQuery)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        LocalBroadcastManager.getInstance(requireContext())
                .unregisterReceiver(premiumBroadcastReceiver)
        matchesTabViewModel.cleanAdListenersOnChatList()
        binding.recyclerviewMessages.adapter = null
        _binding = null
    }

    override fun chatButtonClickListener() {
        scrollToTop()
    }

    private fun scrollToTop() {
        binding.recyclerviewMessages.scrollToPosition(0)
    }


    private fun updateMatchesCounter() {
        binding.recyclerviewMessages.post {
            lifecycleScope.launchWhenResumed {
                val matchesCount = matchesTabViewModel.matchesCount
                val matchesIcon = getMatchesCounter()
                matchesIcon?.let {
                    val unreadMessageCount = matchesIcon.findViewById<TextView>(R.id.unread_message_count)
                    val ninePlus = "9+"

                    when {
                        matchesCount <= 0 -> {
                            matchesIcon.visibility = View.GONE
                        }
                        matchesCount in 1..9 -> {
                            matchesIcon.visibility = View.VISIBLE
                            unreadMessageCount.text = matchesCount.toString()
                        }
                        else -> {
                            matchesIcon.visibility = View.VISIBLE
                            unreadMessageCount.text = ninePlus
                        }
                    }
                }
            }
        }
    }

    private fun updateUnreadConversationsCounter() {
        binding.recyclerviewMessages.post {
            lifecycleScope.launchWhenResumed {

                val unreadMessagesNumber = matchesTabViewModel.unreadConversationCount
                val unreadBadgeId = getUnreadMessageCount()
                unreadBadgeId?.let {
                    val unreadMessageCount =
                            unreadBadgeId.findViewById<TextView>(R.id.unread_message_count)
                    val ninPlus = "9+"

                    when {
                        unreadMessagesNumber <= 0 -> {
                            unreadBadgeId.visibility = View.GONE
                        }
                        unreadMessagesNumber in 1..9 -> {
                            unreadBadgeId.visibility = View.VISIBLE
                            unreadMessageCount.text = unreadMessagesNumber.toString()
                        }
                        else -> {
                            unreadBadgeId.visibility = View.VISIBLE
                            unreadMessageCount.text = ninPlus
                        }
                    }
                }
            }
        }
    }


    /** This internal class is used to debounce API calls onQueryTextChanged of a SearchView.
     * To use on an another module [viewModel] type must be modified according the needs.
     * [debouncePeriod]-set the desired delay value for debouncing
     * */
    internal class DebouncingQueryTextListener(
            lifecycle: Lifecycle,
            private val viewModel: MatchesTabViewModel,
            private val debouncePeriod: Long,
            private val onDebouncingQueryTextChange: (String?) -> Unit
    ) : SearchView.OnQueryTextListener, LifecycleObserver {


        private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Main)

        private var searchJob: Job? = null

        init {
            lifecycle.addObserver(this)
        }

        override fun onQueryTextSubmit(query: String?): Boolean {
            return false
        }

        override fun onQueryTextChange(newText: String?): Boolean {
            if (newText != null && newText.isNotEmpty() && newText.length < 3) {
                val query = newText.lowercase()
                val matchesSearch = viewModel.matches.value!!.filter { it.user.firstName.lowercase().contains(query)}
                val conversationsSearch = viewModel.conversations.value!!.filter { it.name.lowercase().contains(query)}
                viewModel.setSearchMatches(matchesSearch)
                viewModel.setSearchConversations(conversationsSearch)
            }
            searchJob?.cancel()
            searchJob = coroutineScope.launch {
                newText?.let {
                    delay(debouncePeriod)
                    onDebouncingQueryTextChange(newText)
                }
            }
            return false
        }

        @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        private fun destroy() {
            searchJob?.cancel()
        }
    }

    private fun getSearchView(): SearchView? {
        return getMatchesSection()?.findViewById(R.id.search_view_matches)
    }

    private fun getUnreadMessageCount(): FrameLayout? {
        return getMatchesSection()?.findViewById<FrameLayout>(R.id.messages_badge)//?.findViewById(R.id.unread_message_count)
    }

    private fun getMatchesCounter(): FrameLayout? {
        return getMatchesSection()?.findViewById<FrameLayout>(R.id.matches_badge)//?.findViewById(R.id.unread_message_count)
    }

    private fun getMessagesText(): View? {
        return getMatchesSection()?.findViewById(R.id.messages_textView)
    }

    private fun getMatchesText(): View? {
        return getMatchesSection()?.findViewById(R.id.matches_textView)
    }

    private fun showUnreadMessagesIfNotEmpty() {
        val container = getUnreadMessageCount()
        val textView = container?.findViewById<TextView>(R.id.unread_message_count)
        if (!textView?.text.isNullOrEmpty() && textView?.text.toString() == "0") {
            container?.visibility = View.VISIBLE
        }
    }

    private fun showMatchesCounterIfNotEmpty() {
        val container = getMatchesCounter()
        val textView = container?.findViewById<TextView>(R.id.unread_message_count)
        if (!textView?.text.isNullOrEmpty()) {
            container?.visibility = View.VISIBLE
        }

    }

    private fun closeSearch() {
        matchesTabViewModel.setIsSearching(false)
        matchesTabViewModel.clearSearchData()
        matchesTabViewModel.setStatusNoUsers(false)

        getSearchInputView()?.visibility = View.GONE
        getSearchView()?.visibility = View.GONE

        if (!matchesTabViewModel.getIsSearching()) {
            getSearchButtonView()?.visibility = View.VISIBLE
        }
        getSearchView()?.setQuery("", false)
    }

    private fun keepSearchOpen() {
        lifecycleScope.launch {
            delay(250)
            getSearchButtonView()?.performClick()

            matchesTabViewModel.setIsSearching(true)

            if (searchQuery != null) {
                getSearchView()?.setQuery(searchQuery, false)
            }
        }
    }

    private fun getNoUsersView(): View? {
        return getMatchesSection()?.findViewById(R.id.no_users_container)
    }

    private fun getRmodView(): ViewGroup? {
        return getMatchesSection()?.findViewById(R.id.rmod_item)
    }

    private fun updateTimeInRmodItem(timeLeft: String, rmodItem: RmodModel) {
        val rmodItemView = getMatchesSection()?.findViewById<View>(R.id.rmod_item)
        rmodItemView?.let {
            val rmodDescriptionTextView = it.findViewById<TextView>(R.id.rmod_description)
            rmodDescriptionTextView.text = requireContext().getString(R.string.dont_miss_the_opportunity_rmod_desc_an, rmodItem.partner.firstName, timeLeft)
        }
    }

    private fun getSearchInputView(): View? {
        return getMatchesSection()?.findViewById(R.id.search_input_view)
    }

    private fun getSearchButtonView(): View? {
        return getMatchesSection()?.findViewById(R.id.button_search)
    }

    private fun getNewMatchesBackgroundContainer(): View? {
        return getMatchesSection()?.findViewById(R.id.new_matches_container)
    }

    private fun getMatchesSection(): View? {
        return binding.recyclerviewMessages.layoutManager?.findViewByPosition(0)
    }

    private fun getMatchesRecyclerView(): RecyclerView? {
        return getMatchesSection()?.findViewById(R.id.recyclerView_matches)
    }

    private fun getConversationsAdapter(): MessagesAdapter {
        return (binding.recyclerviewMessages.adapter as MessagesAdapter)
    }

    private fun getMatchesAdapter(): MatchesAdapter {
        return (binding.recyclerviewMessages.adapter as MessagesAdapter).matchesAdapter
    }
}