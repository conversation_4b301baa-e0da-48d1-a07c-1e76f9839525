package com.duaag.android.chat.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.api.Result
import com.duaag.android.api.socket.ChatWebSocket
import com.duaag.android.api.socket.model.ErrorChatWebSocketModel
import com.duaag.android.api.socket.model.LikedMessageStateChanged
import com.duaag.android.api.socket.model.NewConversationTokenModel
import com.duaag.android.api.socket.model.UpdateTypingStateChanged
import com.duaag.android.api.socket.model.WebSocketMessageModel
import com.duaag.android.api.socket.model.createTypingStateWebSocket
import com.duaag.android.api.socket.model.mapToMessageModel
import com.duaag.android.api.succeeded
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.models.UserModel
import com.duaag.android.chat.ChatRepository
import com.duaag.android.chat.adapters.InstaChatAdapter
import com.duaag.android.chat.blockspamlinks.domain.SaveAndCheckSpamLinksCountUseCase
import com.duaag.android.chat.blockspamlinks.domain.SendUserAsSuspectLinksUseCase
import com.duaag.android.chat.blockspamlinks.utils.hasLink
import com.duaag.android.chat.model.ConversationListResponseModel
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationType
import com.duaag.android.chat.model.ConversationWebSocketModel
import com.duaag.android.chat.model.EventTrackBody
import com.duaag.android.chat.model.EventTrackEnum
import com.duaag.android.chat.model.IncomingCallsBody
import com.duaag.android.chat.model.LikeMessageModel
import com.duaag.android.chat.model.MessageDraftModel
import com.duaag.android.chat.model.MessageModel
import com.duaag.android.chat.model.MessageModelResponse
import com.duaag.android.chat.model.MessageType
import com.duaag.android.chat.model.RepliesBundleBodyModel
import com.duaag.android.clevertap.ClevertapDeleteInstachatSourceValues
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapUnMatchReasonsValue
import com.duaag.android.clevertap.ClevertapUnmatchTypeValues
import com.duaag.android.clevertap.PremiumBadgeValues
import com.duaag.android.clevertap.RmodRemovedValues
import com.duaag.android.clevertap.RmodViewValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.sendMessageSentClevertapEvents
import com.duaag.android.clevertap.sendRmodRemovedEvent
import com.duaag.android.exceptions.ConversationsException
import com.duaag.android.exceptions.MatchNotFoundException
import com.duaag.android.exceptions.NoConnectivityException
import com.duaag.android.home.models.InteractionBody
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.recommender.domain.model.RmodModel
import com.duaag.android.recommender.domain.use_case.SetRmodSeenUseCase
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.createConversationId
import com.duaag.android.utils.getDateObjectFromString
import com.duaag.android.utils.isSameDay
import com.duaag.android.utils.isSameYear
import com.duaag.android.utils.livedata.SingleLiveData
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import kotlin.collections.set
import java.util.*
import kotlin.collections.ArrayList

enum class LOADEDITEMS { DATABASE_ITEMS, PREVIOUS_ITEMS, NEXT_ITEMS, NEW_MESSAGE, NEW_MESSAGE_LIKE, NEW_MESSAGE_INDICATOR }


class ConversationViewModel @AssistedInject constructor(
    val chatRepository: ChatRepository,
    val userRepository: UserRepository,
    val duaSharedPrefs: DuaSharedPrefs,
    private val saveAndCheckSpamLinksCountUseCase: SaveAndCheckSpamLinksCountUseCase,
    private val sendUserAsSuspectLinksUseCase: SendUserAsSuspectLinksUseCase,
    private val setRmodSeenUseCase: SetRmodSeenUseCase,
    @Assisted var conversationModel: ConversationModel?,
    @Assisted var rmodModel: RmodModel?
) : ViewModel(),ChatWebSocket.ConversationChatWebSocketReceivedInterface {

    companion object {
        private const val requestVerificationForMalesDate = "14-09-2023"
    }

    private var conversationToken: String? = null
    var messagesNextCursor: String? = null

    var unSeenMessageCount: Int = 0
    var scrollTime: Int = 0
    var isMessagesLoading = false

    private var _items: ArrayList<MessageModel> = ArrayList()
    val items get() = _items

    private val _user = userRepository.user
    private val _makeSeen: SingleLiveData<Boolean> = SingleLiveData()
    private val _updateToConversationListForResendFieldMessage: SingleLiveData<MessageModel> =
            SingleLiveData()

    private val _recyclerViewReadyToScroll = SingleLiveData<LOADEDITEMS>()
    val recyclerViewReadyToScroll: LiveData<LOADEDITEMS>
        get() = _recyclerViewReadyToScroll

    private val _messagesLoading = MutableLiveData<Boolean>()
    val messagesLoading: LiveData<Boolean>
        get() = _messagesLoading

    private val _newMessagesIndicator = SingleLiveData<Boolean>()
    val newMessagesIndicator: LiveData<Boolean>
        get() = _newMessagesIndicator

    private val _showTurnOnNotifications: SingleLiveData<Boolean> = SingleLiveData()
    val showTurnOnNotifications: LiveData<Boolean>
        get() = _showTurnOnNotifications

    private var _loadedNextItems: SingleLiveData<Void> = SingleLiveData()
    val loadedNextItems: LiveData<Void>
        get() = _loadedNextItems

    private var _loadedPreviousItems: SingleLiveData<Void> = SingleLiveData()
    val loadedPreviousItems: LiveData<Void>
        get() = _loadedPreviousItems

    private var _loadedDatabseItems: SingleLiveData<Void> = SingleLiveData()
    val loadedDatabseItems: LiveData<Void>
        get() = _loadedDatabseItems

    private var _addNewMessage: SingleLiveData<Void> = SingleLiveData()
    val addNewMessage: LiveData<Void>
        get() = _addNewMessage

    private var _addNewMessageLike: SingleLiveData<Void> = SingleLiveData()
    val addNewMessageLike: LiveData<Void>
        get() = _addNewMessageLike

    private var _updateConversationListAndMatches: SingleLiveData<MessageModel> = SingleLiveData()
    val updateConversationListAndMatches: LiveData<MessageModel>
        get() = _updateConversationListAndMatches

    //Check if is first message to disable sendButton until return response
    private var _isEnableSendMessage: SingleLiveData<Boolean> = SingleLiveData()
    val isEnableSendMessage: LiveData<Boolean>
        get() = _isEnableSendMessage

    private var _expiredTimeInstacahtConversation: SingleLiveData<Boolean> = SingleLiveData()
    val expiredTimeInstacahtConversation: LiveData<Boolean>
        get() = _expiredTimeInstacahtConversation

    private var _convertToDefaultConversation: SingleLiveData<Boolean> = SingleLiveData()
    val convertToDefaultConversation: LiveData<Boolean>
        get() = _convertToDefaultConversation

    private var _successInstaChatDeleted: SingleLiveData<Boolean> = SingleLiveData()
    val successInstaChatDeleted: LiveData<Boolean>
        get() = _successInstaChatDeleted

    private var _draftMessage: SingleLiveData<String> = SingleLiveData()
    val draftMessage: LiveData<String>
        get() = _draftMessage

    private var _updateMessgaType: SingleLiveData<Boolean> = SingleLiveData()
    val updateMessgaType: LiveData<Boolean>
        get() = _updateMessgaType

    private var _userNotPartOfConversations: SingleLiveData<Boolean> = SingleLiveData()
    val userNotPartOfConversations: LiveData<Boolean>
        get() = _userNotPartOfConversations

    private var _addLoadingItem: SingleLiveData<Void> = SingleLiveData()
    val addLoadingItem: LiveData<Void>
        get() = _addLoadingItem

    private var _deleteUserMatchByUserId: SingleLiveData<String> = SingleLiveData()
    val deleteUserMatchByUserId: LiveData<String>
        get() = _deleteUserMatchByUserId

    private var _matchNotFound: SingleLiveData<String> = SingleLiveData()
    val matchNotFound: LiveData<String>
        get() = _matchNotFound

    private var _unMatchSuccessful: SingleLiveData<Void> = SingleLiveData()
    val unMatchSuccessful: LiveData<Void>
        get() = _unMatchSuccessful

    private var _rmodRemovedSuccesfully: SingleLiveData<Void> = SingleLiveData()
    val rmodRemovedSuccesfully: LiveData<Void>
        get() = _rmodRemovedSuccesfully

    private val _updateTypingState = MutableSharedFlow<Boolean>(replay = 0)
    val updateTypingState: SharedFlow<Boolean>
        get() = _updateTypingState

    private val typingCoroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private var receiveTypingJob: Job? = null

    private val typingIndicatorDelayMillis = 20000L

    private var isTyping = false

    private var lastTypingStatusChangedTime: Long = 0

    private var sendTypingJob: Job? = null

    private val typingEventDelayMillis = 5000L // 5 seconds
    private val typingEventRepeatDelayMillis = 20000L // 3 seconds

    private var isSpamLinkSuspicious: Boolean = false

    init {
        Timber.tag("CONVERSATIONID").d("conversationId $conversationModel: ")
        if (!conversationModel!!.isFromMatchView && conversationModel?.id != null) {
            loadLatestMessagesFromDB(conversationModel!!.id!!, System.currentTimeMillis())
        }

        viewModelScope.launch(Dispatchers.IO) {
            conversationModel?.id?.let {
                val model = chatRepository.getConversationToken(it)
                conversationToken = model.values.firstOrNull()?.token
                Timber.tag("TOKENTAG").d("getConversationsWithTokens: $model")
            }


            conversationModel?.let { conversationModel ->
                conversationModel.id?.let { conversationId ->
                    getDraftMessage(conversationId)

                    getNextMessagesFromAPI(conversationModel.id, null, conversationToken)
                            .catch { exception ->
                                withContext(Dispatchers.Main) {
                                    when (exception) {
                                        is ConversationsException -> {
                                            _userNotPartOfConversations.call()
                                        }
                                        else -> {
                                            ToastUtil.toast(R.string.an_error_occurred)
                                            onMessagesLoaded()
                                            logError(ErrorStatus.GET_NEXT_MESSAGES_FROM_API)
                                        }
                                    }
                                }
                            }
                            .collect { response ->
                                when (response) {
                                    is Resource.Success -> {
                                        val messages = response.data.messageList
                                        response.data.conversationToken?.let { conversationToken = it }

                                        setPreviousItems(ArrayList(messages))
                                        onMessagesLoaded()
                                        messagesNextCursor =
                                                if (messages.isEmpty()) null else messages.last().nextCursor
                                        unSeenMessageCount =
                                                messages.filter { it.time > items.firstOrNull()?.time ?: 0 }
                                                        .count()
                                    }
                                    is Resource.Error -> {
                                    }
                                    Resource.Loading -> {
                                        onMessagesLoading()
                                    }
                                }
                            }
                }
            }
        }
        ChatWebSocket.connectWebSocket()
        ChatWebSocket.addListener(this)

        if(conversationModel?.isRMOD == true && conversationModel?.showSeen != true)
            setRmodSeen()
    }

    fun userObserver(): LiveData<UserModel> = _user
    fun updateToConversationListForResendFieldMessageObserver(): LiveData<MessageModel> =
            _updateToConversationListForResendFieldMessage

    fun setNextItems(items: ArrayList<MessageModel>) {
        val start = System.currentTimeMillis()
        val removeDouble =
                items.asSequence().distinctBy { it.id }.sortedByDescending { it.time }.toMutableList()
        Timber.tag("DISTINCT").d("Next ${System.currentTimeMillis() - start}")
        _items = removeDouble as ArrayList<MessageModel>
        _loadedNextItems.postValue(null)
    }

    fun setPreviousItems(items: ArrayList<MessageModel>) {
        val start = System.currentTimeMillis()
        items.addAll(_items.filter { it.hasFailed || it.isPending })
        val removeDouble =
                items.asSequence().distinctBy { it.id }.sortedByDescending { it.time }.toMutableList()
        Timber.tag("DISTINCT").d("Previous ${System.currentTimeMillis() - start}")
        _items = removeDouble as ArrayList<MessageModel>
        _loadedPreviousItems.postValue(null)
    }

    fun loadPreviousItems(){
        _loadedPreviousItems.call()
    }


    fun getNextMessagesFromAPI(
            conversationId: String,
            nextCursor: String?,
            conversationToken: String? = null
    ): Flow<Resource<ConversationListResponseModel>> {
        return chatRepository.getMessages(conversationToken, conversationId, nextCursor, null)
    }

    fun getPreviousMessagesFromAPI(
            conversationId: String,
            previousCursor: String?,
            conversationToken: String?
    ): Flow<Resource<ConversationListResponseModel>> {
        return chatRepository.getMessages(conversationToken, conversationId, null, previousCursor, limit = null)
    }

    fun loadLatestMessagesFromDB(conversationId: String, nextCursor: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val list = async { getNextMessagesFromDB(conversationId, nextCursor) }
            _items = (ArrayList(list.await()))
            messagesNextCursor = _items.lastOrNull()?.nextCursor
            _loadedDatabseItems.postValue(null)
        }
    }

    suspend fun getNextMessagesFromDB(
            conversationId: String,
            nextCursor: Long
    ): List<MessageModel> {
        return chatRepository.getNextMessagesFromDB(conversationId, nextCursor)
    }

    fun loadNextMessages(nextCursor: String?) {
        if (items.isNotEmpty() && !conversationModel!!.isFromMatchView) {
            viewModelScope.launch(Dispatchers.IO) {
                loadMore(nextCursor)
            }
        }
    }

    fun loadPreviousMessages() {
        conversationModel?.let { conversationModel ->
            conversationModel.id?.let { id ->
                //if there are more than 20 items in the list get from that item else get from the last item
                val previousCursor = when {
                    items.isNullOrEmpty() -> null
                    items.size > 20 -> items[20].id
                    else -> items.lastOrNull()?.id
                }

                viewModelScope.launch(Dispatchers.IO) {
                    getPreviousMessagesFromAPI(id, previousCursor, conversationToken)
                            .catch { exception ->
                                withContext(Dispatchers.Main) {
                                    when (exception) {
                                        is ConversationsException -> {
                                            _userNotPartOfConversations.call()
                                        }
                                        else -> {
                                            ToastUtil.toast(R.string.an_error_occurred)
                                            dataLoadedFrom(LOADEDITEMS.PREVIOUS_ITEMS)
                                            logError(ErrorStatus.GET_PREVIOUS_MESSAGES_FROM_API)
                                        }
                                    }
                                }
                            }
                            .collect { response ->
                                when (response) {
                                    is Resource.Success -> {
                                        withContext(Dispatchers.Main) {
                                            response.data.conversationToken?.let { conversationToken = it }
                                            val existingConversations = items
                                            val newConversations = ArrayList(response.data.messageList)
                                            newConversations.addAll(existingConversations)
                                            setPreviousItems(newConversations)
                                        }
                                    }
                                    is Resource.Error -> {
                                    }
                                    is Resource.Loading -> {
                                    }
                                }
                            }
                }
            }
        }
    }

    private suspend fun loadMore(nextCursor: String?) {
        nextCursor?.let {
            withContext(Dispatchers.IO) {
                getNextMessagesFromAPI(conversationModel!!.id!!, nextCursor)
                        .catch { ex ->
                            withContext(Dispatchers.Main) {
                                isMessagesLoading = false
                                when (ex) {
                                    is ConversationsException -> {
                                        _userNotPartOfConversations.call()
                                    }
                                    is NoConnectivityException -> {
                                        ToastUtil.toast(DuaApplication.instance.getString(R.string.an_error_occurred))
                                        logError(ErrorStatus.GET_NEXT_MESSAGES_FROM_API)
                                    }
                                }
                            }
                        }
                        .collect { response ->
                            withContext(Dispatchers.Main) {
                                when (response) {
                                    is Resource.Success -> {
                                        val messages = response.data.messageList
                                        response.data.conversationToken?.let { conversationToken = it }

                                        messagesNextCursor = if (messages.isEmpty()) null else messages.last().nextCursor
                                        val existingConversations = items
                                        existingConversations.addAll(messages)
                                        viewModelScope.launch(Dispatchers.IO) {
                                            val failedMessages = getFailedMessages(
                                                    conversationModel!!.id!!,
                                                messages.lastOrNull()?.time ?: 0,
                                                messages.firstOrNull()?.time ?: 0
                                            )
                                            existingConversations.addAll(failedMessages)
                                            withContext(Dispatchers.Main) {
                                                setNextItems(existingConversations)
                                                isMessagesLoading = false
                                            }
                                        }
                                    }
                                    is Resource.Error -> {
                                    }
                                    Resource.Loading -> {
                                        isMessagesLoading = true
                                        _addLoadingItem.call()
                                    }
                                }
                            }
                        }
            }
        }
    }

    fun updateConversation(id: String) {
        val oldConversation = conversationModel?.copy(id = id)
        oldConversation?.isFromMatchView = false
        conversationModel = oldConversation
    }

    fun makeSeenObserver(): LiveData<Boolean> = _makeSeen

    fun makeSeenMessages() {
        conversationModel?.let { con ->
            if (!con.isFromMatchView) {
                _makeSeen.value = true
            }
        }
    }

    fun makeMatchSeen(userId: Int) {
       viewModelScope.launch {
           chatRepository.setMatchSeen(userId)
       }
    }

    fun makeConversationSeen() {
       _makeSeen.value = true
    }

   private fun checkForSpamLinks(newMessage: MessageModel, randomId: String?, isResendMessage: Boolean = false) {
        viewModelScope.launch {
            saveAndCheckSpamLinksCountUseCase.invoke(
                newMessage.content,
                RemoteConfigUtils.getSuspiciousLinksCount(),
                RemoteConfigUtils.getTimeIntervalHoursForSuspicionLinks()
            ).catch {
                Timber.tag("SPAM").d("checkForSpamLinks: ${it.localizedMessage}")
            }.collect {
                if (it) {
                    sendUserAsSuspectLinksUseCase.invoke(
                        RemoteConfigUtils.getTimeIntervalHoursForSuspicionLinks()
                    ).catch {
                        Timber.tag("SPAM").d("checkForSpamLinks: ${it.localizedMessage}")
                    }.collect {
                        if (it) {
                            addSuspensionLinkItem()
                            updateIsSpamLinkSuspicious(true)
                        }
                    }
                } else {
                    if (isResendMessage) {
                        resendMessage(newMessage, true)
                    } else {
                        sendMessage(newMessage, randomId!!, true)
                    }
                }
            }
        }
    }

    fun updateIsSpamLinkSuspicious(value: Boolean) {
        isSpamLinkSuspicious = value
    }

    fun sendMessage(
        newMessage: MessageModel,
        randomId: String,
        linkChecked: Boolean = false,
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            if(conversationModel?.type == ConversationType.DEFAULT.value &&
                newMessage.content.hasLink() &&
                !linkChecked){
                if (isSpamLinkSuspicious) {
                    addSuspensionLinkItem()
                } else {
                    checkForSpamLinks(newMessage, randomId)
                }
                return@launch
            }

            if (conversationModel != null
                && conversationModel!!.type != ConversationType.INSTANT_CHAT.value
                && !conversationModel!!.isFromMatchView
                && ChatWebSocket.isConnected()
            ) {
                sendWebSocketMessage(newMessage)
            } else {
                try {
                    val response = sendMessageAPI(newMessage, randomId)
                    withContext(Dispatchers.Main) {
                        when (response) {
                            is Resource.Success -> {
                                val messageModel = response.data.messageModel
                                response.data.conversationToken?.let { conversationToken = it }

                                if (conversationModel != null && conversationModel!!.isFromMatchView) {
                                    conversationModel?.userId?.let { userId ->
                                        _deleteUserMatchByUserId.value = userId
                                    }
                                    val items = items
                                    items.find { it.isNewMessage }?.let { items.remove(it) }
                                    updateConversation(messageModel.conversationId)

                                    if(conversationModel != null &&
                                        conversationModel?.type == ConversationType.INSTANT_CHAT.value &&
                                        conversationModel?.isRMOD == true
                                    ) {
                                        conversationModel?.type = ConversationType.DEFAULT.value
                                        conversationModel?.isRMOD = false
                                    }
                                }
                                messageDelivered(randomId, messageModel)
                                firebaseLogEvent(FirebaseAnalyticsEventsName.MESSAGES,
                                    mapOf(FirebaseAnalyticsParameterName.MSG_COUNT.value to 1L))
                            }
                            is Resource.Error -> {
                            }
                            Resource.Loading -> {
                            }
                        }
                    }
                } catch (ex: Exception) {
                    withContext(Dispatchers.Main) {
                        if (ex is MatchNotFoundException) {
                            conversationModel?.userId?.let { userId ->
                                _matchNotFound.value = userId
                            }
                        } else messageFailedToSend(randomId)
                    }
                }

            }
        }
    }

    private fun sendWebSocketMessage(newMessage: MessageModel) {
        val newList = _items
        if (!newList.isNullOrEmpty()) {
            newList.removeAll { it.id == newMessage.id }
            if (newList.isNullOrEmpty()) {
                val items = ArrayList<MessageModel>()
                items.add(0, newMessage)
                _items = items
            } else {
                val firstItem = newList[0].copy()
                    .apply { fullyRounded = this.senderUserId == newMessage.senderUserId }
                newList[0] = firstItem
                newList.add(0, newMessage)
                _items = newList
            }
        } else {
            val items = ArrayList<MessageModel>()
            items.add(0, newMessage)
            _items = items
        }
        _addNewMessage.postValue(null)

        val eventPremiumType = getPremiumTypeEventProperty(userRepository.user.value)
        val isMessageReply = if(conversationModel != null && items.size > 1)
            items[1].senderUserId == conversationModel!!.userId
        else
            false

        sendMessageSentClevertapEvents(eventPremiumType, conversationModel?.id!!, isMessageReply)
        viewModelScope.launch {
            if(isMessageReply) {
                val data = RepliesBundleBodyModel(conversationModel?.id!!)
                chatRepository.trackEvent(EventTrackBody(EventTrackEnum.REPLIES_BUNDLE_RECEIVED.value, data))
            }
        }


        ChatWebSocket.sendMessage(newMessage,conversationToken)
    }

    fun sendGif(newMessage: MessageModel, randomId: String) {
        viewModelScope.launch(Dispatchers.IO) {

            if (conversationModel != null
                && conversationModel!!.type != ConversationType.INSTANT_CHAT.value
                && !conversationModel!!.isFromMatchView
                && ChatWebSocket.isConnected()
            ) {
                sendWebSocketMessage(newMessage)
            } else {
                try {
                    val response = sendMessageAPI(newMessage, randomId)

                    withContext(Dispatchers.Main) {
                        when (response) {
                            is Resource.Success -> {
                                val messageModel = response.data.messageModel
                                response.data.conversationToken?.let { conversationToken = it }

                                if (conversationModel != null && conversationModel!!.isFromMatchView) {
                                    conversationModel?.userId?.let { userId ->
                                        _deleteUserMatchByUserId.value = userId
                                    }
                                    updateConversation(messageModel.conversationId)
                                }
                                messageDelivered(randomId, messageModel)
                                firebaseLogEvent(FirebaseAnalyticsEventsName.MESSAGES,
                                    mapOf(FirebaseAnalyticsParameterName.MSG_COUNT.value to 1L))
                            }
                            is Resource.Error -> {
                            }
                            Resource.Loading -> {
                            }
                        }
                    }


                } catch (ex: Exception) {
                    withContext(Dispatchers.Main) {
                        messageFailedToSend(randomId)
                    }
                }
            }

        }
    }

    fun resendMessage(message: MessageModel, linkChecked: Boolean = false) {

        viewModelScope.launch(Dispatchers.IO) {

            if(message.content.hasLink() && !linkChecked){

                if (isSpamLinkSuspicious) {
                    addSuspensionLinkItem()
                } else {
                    checkForSpamLinks(message, null, true)
                }
                return@launch
            }

            if (conversationModel != null
                && conversationModel!!.type != ConversationType.INSTANT_CHAT.value
                && !conversationModel!!.isFromMatchView
                && ChatWebSocket.isConnected()
            ) {
                sendWebSocketMessage(message)
            } else {
                try {
                    val response = sendMessageAPI(message)
                    withContext(Dispatchers.Main) {
                        when (response) {
                            is Resource.Success -> {
                                response.data.conversationToken?.let { conversationToken = it }
                                val messageModel = response.data.messageModel.apply { localId = message.localId }
                                messageDelivered(message.id, messageModel)
                            }
                            is Resource.Error -> {

                            }
                            is Resource.Loading -> {
                            }
                        }
                    }

                } catch (ex: Exception) {
                    withContext(Dispatchers.Main) {
                        messageFailedToSend(message.id)
                    }
                }
            }
        }
    }

    suspend fun sendMessageAPI(newMessage: MessageModel, randomId: String? = null): Resource<MessageModelResponse> {
        val newList = _items
        if (!newList.isNullOrEmpty()) {
            newList.removeAll { it.id == newMessage.id }
            if (newList.isNullOrEmpty()) {
                val items = ArrayList<MessageModel>()
                items.add(0, newMessage)
                _items = items
            } else {
                val firstItem = newList[0].copy()
                        .apply { fullyRounded = this.senderUserId == newMessage.senderUserId }
                newList[0] = firstItem
                newList.add(0, newMessage)
                _items = newList
            }
        } else {
            val items = ArrayList<MessageModel>()
            items.add(0, newMessage)
            _items = items
        }
        _addNewMessage.postValue(null)

        val param = mutableMapOf("content" to newMessage.content, "type" to newMessage.type)

        if (conversationModel!!.type != ConversationType.INSTANT_CHAT.value) {
            param["eventId"] = newMessage.id
        }

        val eventActivityType = rmodModel?.partner?.activityType
        val isPhotoHidden = _user.value?.profile?.hasBlurredPhotos

        val eventPremiumType = getPremiumTypeEventProperty(userRepository.user.value)
        val isMessageReply = if(conversationModel != null && items.size > 1)
            items[1].senderUserId == conversationModel!!.userId
        else
            false

        val premiumBadge = rmodModel?.let {
            if (it.partner.profile.showPremiumBadge == true)
                PremiumBadgeValues.SHOWN.value
            else
                PremiumBadgeValues.HIDDEN.value
        }
        return if (conversationModel != null && conversationModel!!.type == ConversationType.INSTANT_CHAT.value) {
            Timber.tag("ESAT_LOGG").d("conversationModel $conversationModel | isRMOD: $${conversationModel?.isRMOD}")
            chatRepository.sendInstaChatMessage(
                conversationId = conversationModel!!.id!!,
                param = param,
                eventPremiumType = eventPremiumType,
                eventActivityType = eventActivityType,
                isPhotoHidden = isPhotoHidden,
                isRmod = conversationModel?.isRMOD,
                premiumBadge = premiumBadge
            )
        } else if (conversationModel != null && !conversationModel!!.isFromMatchView) {
            chatRepository.sendMessage(conversationModel!!.id!!, param, conversationToken, eventPremiumType, isMessageReply)
        } else {
            _isEnableSendMessage.postValue(false)
            conversationModel?.userId?.let { userId -> _deleteUserMatchByUserId.postValue(userId)}
            chatRepository.sendMessageForFirstTime(conversationModel!!, newMessage, param,eventPremiumType)
        }
    }

    fun sendRmodMessage(
        interactionBody: InteractionBody,
        newMessage: MessageModel,
        rmodModel: RmodModel
    ) {
        viewModelScope.launch {
            try {
                val newList = _items
                if (!newList.isNullOrEmpty()) {
                    newList.removeAll { it.id == newMessage.id }
                    if (newList.isNullOrEmpty()) {
                        val items = ArrayList<MessageModel>()
                        items.add(0, newMessage)
                        _items = items
                    } else {
                        val firstItem = newList[0].copy()
                            .apply { fullyRounded = this.senderUserId == newMessage.senderUserId }
                        newList[0] = firstItem
                        newList.add(0, newMessage)
                        _items = newList
                    }
                } else {
                    val items = ArrayList<MessageModel>()
                    items.add(0, newMessage)
                    _items = items
                }
                _addNewMessage.postValue(null)

                val conversationId = createConversationId(newMessage.senderUserId, interactionBody.receiverUserId)
                val eventPremiumType = getPremiumTypeEventProperty(userRepository.user.value)
                val response = userRepository.sendRmodInteraction(interactionBody, newMessage, conversationId, rmodModel, eventPremiumType)
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    fun removeRmod(interactionBody: InteractionBody, rmodItem: RmodModel?) {
        viewModelScope.launch {
            try {
                val response = userRepository.removeRmod(interactionBody)
                if(response.succeeded) {
                    val eventPremiumType = getPremiumTypeEventProperty(_user.value)
                    sendRmodRemovedEvent(eventPremiumType, rmodItem?.totalPercentage)
                    _rmodRemovedSuccesfully.call()
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    fun setNewMessageIndicator() {
        val newList = _items
        newList.firstOrNull { it.isNewMessage }?.let {
            newList.remove(it)
        }
        val previewDate: Long = if (unSeenMessageCount >= 0 && newList.size > unSeenMessageCount) newList[unSeenMessageCount].time else System.currentTimeMillis()
        newList.add(unSeenMessageCount, MessageModel.getEmptyMessage().copy(time = previewDate).apply { isNewMessage = true })
        _newMessagesIndicator.postValue(null)
    }

    fun messageDelivered(id: String, newMessage: MessageModel) {
        //Check if is first message
        if (_isEnableSendMessage.value == false) {
            _isEnableSendMessage.value = true
        }
        val items = _items
        if (!items.isNullOrEmpty()) {
            val index = items.indexOfFirst { it.id == id }
            val newItem = items[index].copy(
                    isPending = false,
                    id = newMessage.id,
                    time = newMessage.time,
                    conversationId = newMessage.conversationId
            )
            items[index] = newItem
            items.find { it.isNewMessage }?.let {
                items.remove(it)
            }
            _items = items
            _addNewMessage.postValue(null)
            insertMessage(newItem)
            checkToConvertTypeToDefault()
            updateToConversationListForResendFieldMessage(newItem)
            newItem.localId?.let { deleteMessageByLocalId(it) }
        }

    }

    private fun deleteMessageByLocalId(localId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.deleteMessageByLocalId(localId)
        }
    }

    fun messageFailedToSend(id: String) {
        val items = _items
        if (!items.isNullOrEmpty()) {
            val index = items.indexOfFirst { it.id == id }
            val newItem = items[index].copy(hasFailed = true)
            items[index] = newItem
            items.find { it.isNewMessage }?.let {
                items.remove(it)
            }
            _items = items
            _addNewMessage.postValue(null)
            insertMessage(newItem)
            updateToConversationListForResendFieldMessage(newItem)
        }
    }

   suspend fun addSuspensionLinkItem() {
       val suspiciousLinkItem = MessageModel.getEmptyMessage().copy(time = Date().time, id = System.currentTimeMillis().toString()).apply {
           isSuspiciousLink = true
       }
       _items = if (!_items.isNullOrEmpty()) {
           val items = _items
           items.add(0,suspiciousLinkItem )
           items
       } else {
           val items = ArrayList<MessageModel>()
           items.add(0, suspiciousLinkItem)
           items
       }
       _addNewMessage.postValue(null)
   }

    fun insertMessage(message: MessageModel) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.insertMessage(message)
        }
    }

    fun makeSeen(): LiveData<Result<String>> =
            chatRepository.makeMessageSeen(conversationModel?.id)

    fun unMatch(userId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.unMatchFlow(userId)
                    .catch { ex -> }
                    .collect {
                        withContext(Dispatchers.Main) {
                            when (it) {
                                is Resource.Success -> {
                                    firebaseLogEvent(
                                            FirebaseAnalyticsEventsName.UNMATCH_INSIDE_CONVERSATION,
                                            mapOf(
                                                    FirebaseAnalyticsParameterName.UMATCH_POPUP_COUNT.value to 1L,
                                                    FirebaseAnalyticsParameterName.NO_REASON.value to 1L))

                                    val eventPremiumType = getPremiumTypeEventProperty(userRepository.user.value)

                                    val unmatchsource = ClevertapUnmatchTypeValues.INSIDE_CONVERSATION.value

                                    sendClevertapEvent(
                                        ClevertapEventEnum.UNMATCH, mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                            ClevertapEventPropertyEnum.UNMATCH_SOURCE.propertyName to unmatchsource,
                                            ClevertapEventPropertyEnum.UNMATCH_REASONS.propertyName to ClevertapUnMatchReasonsValue.NO_REASON.value))

                                    _unMatchSuccessful.call()
                                }
                                is Resource.Error -> {
                                }
                                Resource.Loading -> {
                                }
                            }
                        }
                    }
        }
    }

    fun updateToConversationListForResendFieldMessage(messageModel: MessageModel) {
        _updateToConversationListForResendFieldMessage.value = messageModel
    }

    fun onMessageReceives(conversationWebSocketModel: ConversationWebSocketModel) {
        val newMessage: MessageModel = conversationWebSocketModel.data.message
        val newList = ArrayList(_items)
        if (newList.any { it.id == newMessage.id }) return
        if (!newList.isNullOrEmpty()) {
            val firstItem = newList[0].copy()
                    .apply { fullyRounded = this.senderUserId == newMessage.senderUserId }
            newMessage.isSameDay = isSameDay(firstItem.time, newMessage.time)
            newMessage.isSameYear = isSameYear(firstItem.time, newMessage.time)
            newList[0] = firstItem
            newList.add(0, newMessage)
        } else {
            newMessage.isSameDay = false
            newMessage.isSameYear = isSameYear(newMessage.time, System.currentTimeMillis())
            newList.add(0, newMessage)
        }
        _items = newList
        _addNewMessage.postValue(null)
        _makeSeen.value = true

        if (conversationModel != null && conversationModel!!.isFromMatchView) {
            _updateConversationListAndMatches.value = newMessage
            _isEnableSendMessage.value = true
        }
        checkToConvertTypeToDefault()
        checkAllowDisallowCall(newMessage)
        updateToConversationListForResendFieldMessage(newMessage)
        insertMessage(newMessage)
        checkIfIsContentCut(newMessage)
    }

    private fun checkAllowDisallowCall(newMessage: MessageModel) {
        if (newMessage.type == MessageType.CALLS_ALLOWED.value) {
            conversationModel?.areOutgoingCallsAllowed = true
        }
        if (newMessage.type == MessageType.CALLS_DISALLOWED.value) {
            conversationModel?.areOutgoingCallsAllowed = false
        }
        _updateMessgaType.value = true
    }

    // when others like our message
    fun onLikeMessage(likeMessageModel: LikeMessageModel, saveInDB: Boolean = true) {
        val items = _items
        if (!items.isNullOrEmpty()) {
            val index = items.indexOfFirst { it.id == likeMessageModel.messageId && it.isLiked != likeMessageModel.isLiked }
            if (index != -1) {
                val newItem = items[index].copy(isLiked = likeMessageModel.isLiked)
                items[index] = newItem
                _items = items
                _addNewMessageLike.postValue(null)
                if (saveInDB) insertMessage(newItem)

            }
        }
    }

    fun likeMessage(message: MessageModel) {
        onLikeMessage(LikeMessageModel(messageId = message.id, isLiked = true), false)
        if (ChatWebSocket.isConnected()) {
            ChatWebSocket.likeUnlikeMessage(message,true,conversationToken)
        } else {
            viewModelScope.launch(Dispatchers.IO) {
                chatRepository.likeMessage(message, conversationToken).catch {
                }.collect { response ->
                    when (response) {
                        is Resource.Success -> {
                            response.data.conversationToken?.let { conversationToken = it }
                        }
                        is Resource.Error -> {
                        }
                        Resource.Loading -> {
                        }
                    }
                }
            }
        }
    }

    fun deleteLikeMessage(message: MessageModel) {
        onLikeMessage(LikeMessageModel(messageId = message.id, isLiked = false), false)
        if (ChatWebSocket.isConnected()) {
            ChatWebSocket.likeUnlikeMessage(message,false,conversationToken)
        } else {
            viewModelScope.launch(Dispatchers.IO) {
                chatRepository.deleteLikeMessage(message, conversationToken).catch {
                }.collect { response ->
                    when (response) {
                        is Resource.Success -> {
                            response.data.conversationToken?.let { conversationToken = it }
                        }
                        is Resource.Error -> {
                        }
                        Resource.Loading -> {
                        }
                    }
                }
            }
        }
    }

    private fun checkIfIsContentCut(message: MessageModel) {
        message.isContentCut?.let {
            if (it) {
                viewModelScope.launch(Dispatchers.IO) {
                    chatRepository.getMessageById(message.id)
                        .catch { ex ->
                            Timber.tag("getMessageById").e("Error")
                            ex.printStackTrace()
                        }
                        .collect { newMessage ->
                            withContext(Dispatchers.Main) {
                            when (newMessage) {
                                is Resource.Success -> {
                                    replaceCutMessage(newMessage.data.id, newMessage.data)
                                }
                                is Resource.Error -> {
                                }
                                Resource.Loading -> {
                                }
                            }
                            }
                        }
                }
            }
        }
    }

    private fun replaceCutMessage(id: String, newMessage: MessageModel) {
        val items = _items
        if (!items.isNullOrEmpty()) {
            val index = items.indexOfFirst { it.id == id }
            val newItem = items[index].copy(
                    isContentCut = false,
                    id = newMessage.id,
                    time = newMessage.time,
                    conversationId = newMessage.conversationId,
                    content = newMessage.content
            )
            items[index] = newItem
            _items = items
            _addNewMessage.postValue(null)
            insertMessage(newItem)
        }
    }

    fun onMessagesLoaded() {
        _messagesLoading.postValue(false)
    }

    fun onMessagesLoading() {
        _messagesLoading.postValue(true)
    }

    fun dataLoadedFrom(from: LOADEDITEMS) {
        _recyclerViewReadyToScroll.value = from
    }

    fun dismissTurnOnNotifications() {
        _showTurnOnNotifications.value = false
    }

    fun showTurnOnNotifications() {
        _showTurnOnNotifications.value = true
    }

    fun insertDraft(draftModel: MessageDraftModel) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.insertDraft(draftModel)
        }
    }

    fun getDraftMessage(conversationId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val message = chatRepository.getDraft(conversationId)
            _draftMessage.postValue(message?.content ?: "")
        }
    }

    suspend fun getFailedMessages(conversationId: String, start: Long, end: Long): List<MessageModel> {
        return chatRepository.getFailedMessages(conversationId, start, end)
    }

    fun checkExpiredTimeInstachatConversation() {
        _expiredTimeInstacahtConversation.value = false
        if ((conversationModel?.lastMessageTime ?: 0L + InstaChatAdapter.SEVENTYTWOHOURSINMS > System.currentTimeMillis())
                && conversationModel?.type == ConversationType.INSTANT_CHAT.value
        ) {
            _expiredTimeInstacahtConversation.value = true
        }
    }

    fun checkToConvertTypeToDefault() {
        if (conversationModel != null && conversationModel!!.type == ConversationType.INSTANT_CHAT.value) {
            conversationModel!!.type = ConversationType.DEFAULT.value
            _convertToDefaultConversation.value = true
        }
    }

    fun deleteInstaChat() {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.deleteInstaChat(conversationModel?.id!!)
                    .catch { ex -> ex.printStackTrace() }
                    .collect {
                        when (it) {
                            is Resource.Success -> {
                                _successInstaChatDeleted.postValue(true)

                                firebaseLogEvent(
                                        FirebaseAnalyticsEventsName.DELETE_INSTACHAT_FROM_INSIDE_CHAT, mapOf(
                                        FirebaseAnalyticsParameterName.DELETE_INSTACHAT_FROM_INSIDE_CHAT_COUNT.value to 1L))

                                val eventPremiumType = getPremiumTypeEventProperty(userRepository.user.value)
                                val eventSourceValue = ClevertapDeleteInstachatSourceValues.INSIDE_CHAT.value

                                sendClevertapEvent(
                                    ClevertapEventEnum.INSTACHAT_DELETE, mapOf(
                                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                        ClevertapEventPropertyEnum.DELETE_INSTACHAT_SOURCE.propertyName to eventSourceValue))
                            }
                            is Resource.Loading -> {
                            }
                            is Resource.Error -> {
                            }
                        }
                    }
        }
    }


    fun allowOrDisallowIncomingVideoCalls(incomingCallsBody: IncomingCallsBody): LiveData<Result<MessageModel>> = chatRepository.allowOrDisallowIncomingVideoCall(conversationModel?.userId
            ?: "", incomingCallsBody)

    fun messageAllowedDisallowedDelivered(newMessage: MessageModel) {
        //Check if is first message
        if (_isEnableSendMessage.value == false) {
            _isEnableSendMessage.value = true
        }
        newMessage.apply {
            isSameDay = true
            isSameYear = true
        }
        val items = _items
        if (!items.isNullOrEmpty()) {
            items.find { it.isNewMessage }?.let {
                items.remove(it)
            }

            val firstItem = items[0].copy()
                    .apply { fullyRounded = this.senderUserId == newMessage.senderUserId }
            items[0] = firstItem

            items.add(0, newMessage)

        } else {
            items.add(0, newMessage)
        }

        _items = items
        _addNewMessage.postValue(null)
        insertMessage(newMessage)
        checkToConvertTypeToDefault()
        conversationModel?.areIncomingCallsAllowed = newMessage.type == MessageType.CALLS_ALLOWED.value
        updateToConversationListForResendFieldMessage(newMessage)
        _updateMessgaType.value = true
    }

    override fun newConversationToken(newConversationTokenModel: NewConversationTokenModel) {
        if (newConversationTokenModel.payload.conversationId == conversationModel?.id) {
            conversationToken = newConversationTokenModel.payload.data.conversationToken
        }
    }

    override fun messageSent(webSocketMessageModel: WebSocketMessageModel) {
        if (webSocketMessageModel.payload.conversationId != conversationModel?.id) {
            return
        }
        if (conversationModel != null && conversationModel!!.isFromMatchView) {
            conversationModel?.userId?.let { userId ->
                _deleteUserMatchByUserId.value = userId
            }
            val items = items
            items.find { it.isNewMessage }?.let { items.remove(it) }
            updateConversation(webSocketMessageModel.payload.conversationId)
        }

        messageDelivered(webSocketMessageModel.eventId, webSocketMessageModel.mapToMessageModel())
        firebaseLogEvent(FirebaseAnalyticsEventsName.MESSAGES,
            mapOf(FirebaseAnalyticsParameterName.MSG_COUNT.value to 1L))
    }

    override fun newWebSocketMessage(webSocketMessageModel: WebSocketMessageModel) {
        val newMessage = webSocketMessageModel.mapToMessageModel()
        if (newMessage.senderUserId == conversationModel!!.userId) {
            conversationModel?.apply {
                lastMessageText = newMessage.content
                lastMessageSender = newMessage.senderUserId
                lastMessageTime = newMessage.time
                lastMessageType = newMessage.type
                seen = 1
                hasFailed = false
            }

            val newList = ArrayList(_items)
            if (newList.any { it.id == newMessage.id }) return
            if (!newList.isNullOrEmpty()) {
                val firstItem = newList[0].copy()
                    .apply { fullyRounded = this.senderUserId == newMessage.senderUserId }
                newMessage.isSameDay = isSameDay(firstItem.time, newMessage.time)
                newMessage.isSameYear = isSameYear(firstItem.time, newMessage.time)
                newList[0] = firstItem
                newList.add(0, newMessage)
            } else {
                newMessage.isSameDay = false
                newMessage.isSameYear = isSameYear(newMessage.time, System.currentTimeMillis())
                newList.add(0, newMessage)
            }
            _items = newList
            _addNewMessage.postValue(null)
            _makeSeen.value = true

            if (conversationModel != null && conversationModel!!.isFromMatchView) {
                _updateConversationListAndMatches.value = newMessage
                _isEnableSendMessage.value = true
            }
            checkToConvertTypeToDefault()
            checkAllowDisallowCall(newMessage)
            insertMessage(newMessage)

            //We should delay this function because we need to notify ConversationsList
            // after HomeViModel.newWebSocketMessage did it
            viewModelScope.launch(Dispatchers.Main) {
                delay(50)
                updateToConversationListForResendFieldMessage(newMessage)
            }
        }
    }

    override fun likeMessageStateChange(likedMessageStateChanged: LikedMessageStateChanged) {
        if (likedMessageStateChanged.payload.conversationId == conversationModel?.id) {
            onLikeMessage(LikeMessageModel(likedMessageStateChanged.payload.messageId,likedMessageStateChanged.payload.data.isLiked))
        }
    }

    override fun updateTypingStateChanged(updateTypingStateChanged: UpdateTypingStateChanged) {
        viewModelScope.launch {
            if (updateTypingStateChanged.payload.conversationId == conversationModel?.id) {
                val isTyping = updateTypingStateChanged.payload.data.isTyping
                receiveTypingJob?.cancel()
                _updateTypingState.emit(isTyping)
                if (isTyping){
                    receiveTypingJob = typingCoroutineScope.launch {
                        delay(typingIndicatorDelayMillis)
                        ensureActive()
                        _updateTypingState.emit(false)
                    }
                }
            }
        }
    }

    override fun userNotPartOfConversationsError(errorChatWebSocketModel: ErrorChatWebSocketModel) {
        if (errorChatWebSocketModel.payload.conversationId != conversationModel?.id) {
            return
        }
        _userNotPartOfConversations.call()
    }

    override fun validateError(errorChatWebSocketModel: ErrorChatWebSocketModel) {
        if (errorChatWebSocketModel.payload.conversationId != conversationModel?.id) {
            return
        }
        errorChatWebSocketModel.eventId?.let {
            messageFailedToSend(errorChatWebSocketModel.eventId)
        }
    }

    override fun defaultError(errorChatWebSocketModel: ErrorChatWebSocketModel) {
        if (errorChatWebSocketModel.payload.conversationId != conversationModel?.id) {
            return
        }
        errorChatWebSocketModel.eventId?.let {
            messageFailedToSend(errorChatWebSocketModel.eventId)
        }
    }

    fun sendTypingState(hasText: Boolean) {
        if (isTyping.not() && hasText) {
            isTyping = true
            lastTypingStatusChangedTime = System.currentTimeMillis()
            sendTypingJob?.cancel()
            sendIsTypingStatusChanged(isTyping)
        } else if (isTyping && hasText.not()) {
            isTyping = false
            lastTypingStatusChangedTime = System.currentTimeMillis()
            sendTypingJob?.cancel()
            sendIsTypingStatusChanged(isTyping)
        } else if (isTyping && hasText) {
            if (System.currentTimeMillis() - lastTypingStatusChangedTime >= typingEventDelayMillis) {
                isTyping = true
                lastTypingStatusChangedTime = System.currentTimeMillis()
                sendTypingJob?.cancel()
                sendIsTypingStatusChanged(isTyping)
            }
        }

    }

    fun setRmodSeen() {
        viewModelScope.launch {
            setRmodSeenUseCase.invoke()
                .catch {ex ->
                    ex.printStackTrace()
                }
                .collect { response ->
                    Timber.tag("rmod_seen").d("$response")
                }
        }
    }

   private fun sendIsTypingStatusChanged(isTyping:Boolean) {

        if (ChatWebSocket.isConnected()) {
            conversationModel?.id?.let {
                val model = createTypingStateWebSocket(it, isTyping, conversationToken)
                ChatWebSocket.sendIsTypingStatusChanged(model)
            }
        } else {
            this.isTyping = false
            lastTypingStatusChangedTime = System.currentTimeMillis()
            ChatWebSocket.connectWebSocket()
            ChatWebSocket.removeListener(this)
            ChatWebSocket.addListener(this)
        }

       if (isTyping) {
           sendTypingJob = typingCoroutineScope.launch {
               delay(typingEventRepeatDelayMillis)
               ensureActive()
               if (<EMAIL>) {
                   <EMAIL> = false
                   lastTypingStatusChangedTime = System.currentTimeMillis()
                   sendIsTypingStatusChanged(<EMAIL>)
               }
           }
       }

   }
    
    fun hasNoMessages(): Boolean {
        return items.isEmpty()
    }
    fun checkForFirstReply(): Boolean = _items.all { it.senderUserId != _user.value?.cognitoUserId }


    fun hasConversationBeenCreatedAfterX(): Boolean {
        val timestamp = getDateObjectFromString(requestVerificationForMalesDate)?.time
        return (conversationModel?.createdAt != null &&
                timestamp != null &&
                conversationModel?.createdAt!! > timestamp)
    }

    override fun onCleared() {
        super.onCleared()
        ChatWebSocket.removeListener(this)
        typingCoroutineScope.cancel()
    }

}