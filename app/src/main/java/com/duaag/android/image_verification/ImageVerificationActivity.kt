package com.duaag.android.image_verification

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.databinding.ImageVerificationActivityBinding
import com.duaag.android.home.di.HomeComponent
import com.duaag.android.image_verification.di.ImageVerificationComponent
import com.duaag.android.image_verification.viewmodels.ImageVerificationViewModel
import com.duaag.android.user.DuaAccount
import java.io.File
import javax.inject.Inject
import com.uxcam.UXCam

class ImageVerificationActivity : AppCompatActivity() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ImageVerificationViewModel> { viewModelFactory }
    private  var _binding: ImageVerificationActivityBinding? = null
    private  val binding get() = _binding!!

    lateinit var homeComponent: HomeComponent

    lateinit var imageVerificationComponent: ImageVerificationComponent


    override fun onCreate(savedInstanceState: Bundle?) {
        homeComponent = (application as DuaApplication).appComponent.homeComponent().create()
        homeComponent.inject(this)

        imageVerificationComponent = (application as DuaApplication).appComponent.imageVerificationComponent().create()
        imageVerificationComponent.inject(this)

        super.onCreate(savedInstanceState)
        viewModel.generateRandomImage(intent.getStringExtra(USER_GENDER) ?: DuaAccount.user?.gender ?: "F")
        viewModel.itsOpenFromSignUpActivity = intent.getBooleanExtra(ITS_OPEN_FROM_SIGN_UP, false)

        _binding = ImageVerificationActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    companion object {
        const val ITS_OPEN_FROM_SIGN_UP = "its_open_from_sign_up"
        const val USER_GENDER = "user_gender"
        const val VERIFY_PROFILE_FROM_SIGN_UP_REQUEST_CODE = 177
        const val USER_SHADOW_BANNED = "user_shadow_banned"

        const val EVENT_SOURCE_VALUE = "event_source_value"
        const val REQUEST_CODE_IMAGE_VERIFICATION = 85

        /** Use external media if it is available, our app's file directory otherwise */
        fun getOutputDirectory(context: Context): File {
            val appContext = context.applicationContext
            val mediaDir = context.externalMediaDirs.firstOrNull()?.let {
                File(it, appContext.resources.getString(R.string.app_name)).apply { mkdirs() }
            }
            return if (mediaDir != null && mediaDir.exists())
                mediaDir else appContext.filesDir
        }
    }

    fun finishImageVerification(){
        val finishIntent = Intent()
        setResult(Activity.RESULT_OK, finishIntent)
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }
}