package com.duaag.android.image_verification.fragments

import android.content.Context
import android.graphics.Paint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentImageDeniedBinding
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.fragment.guidelines.DialogClickListener
import com.duaag.android.signup.fragment.guidelines.GuidelinesDialogFragment
import com.duaag.android.signup.models.InvalidProfileImageReason
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.getS3Url
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import javax.inject.Inject


class ImageDeniedFragment : Fragment(), DialogClickListener {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedSignUpViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity }) { viewModelFactory }
    private val choosePicturesViewModel by viewModels<ChoosePictureViewModel>({ if (activity is ManagePicturesActivity) activity as ManagePicturesActivity else activity as SignUpActivity }) { viewModelFactory }

    private var _binding: FragmentImageDeniedBinding? = null
    private val binding get() = _binding!!

    private var invalidReason: String? = null
    private var invalidImageKey: String? = null
    private var authMethod: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            invalidReason = it.getString(ARG_REASON)
            invalidImageKey = it.getString(ARG_IMAGE_KEY)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        if (activity is SignUpActivity) {
            (requireActivity() as SignUpActivity).signUpComponent.inject(this)
        } else if (activity is ManagePicturesActivity)
            (requireActivity() as ManagePicturesActivity).managePicturesComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentImageDeniedBinding.inflate(inflater, container, false)

        binding.guidelinesTextView.paintFlags =
            binding.guidelinesTextView.paintFlags or Paint.UNDERLINE_TEXT_FLAG

        authMethod =
            if (activity is SignUpActivity) sharedSignUpViewModel.authMethod?.methodName else null

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bindReasonSection(requireContext(), binding, invalidReason)

        bindUserInvalidImage(binding.image, invalidImageKey)

        binding.guidelinesTextView.setOnSingleClickListener {
            val dialog = GuidelinesDialogFragment.newInstance(
                methodName = authMethod,
                premiumTypeEventProperty = getPremiumTypeEventProperty(choosePicturesViewModel.user.value)
            )
            dialog.show(childFragmentManager, "dialog")
        }

        binding.replacePhotoBtn.setOnSingleClickListener {
            if (activity is SignUpActivity) {
                sendClevertapEvent(ClevertapEventEnum.REPLACE_PHOTO,
                    mapOf(
                        ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod,
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null
                    )
                )
                sendUxCamEvent(ClevertapEventEnum.REPLACE_PHOTO,
                    mapOf(
                        ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod,
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null
                    )
                )
            }
            findNavController().popBackStack()
        }

        if (activity is SignUpActivity) {
            sharedSignUpViewModel.gender.observe(viewLifecycleOwner) {
                bindImageGuideline(binding.imageGuideline, it)
            }
        } else {
            val gender =
                if (choosePicturesViewModel.userProfileNU.value?.gender == GenderType.MAN.value) GenderType.MAN else GenderType.WOMAN
            bindImageGuideline(binding.imageGuideline, gender)
        }
        sendClevertapImageDeniedEvent()
    }

    private fun sendClevertapImageDeniedEvent() {
        val premiumType = getPremiumTypeEventProperty(choosePicturesViewModel.user.value)
        sendClevertapEvent(
            ClevertapEventEnum.IMAGE_UPLOAD_DENIED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod,
                ClevertapEventPropertyEnum.IMAGE_DENIED_REASON.propertyName to invalidReason,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType
            )
        )
        sendUxCamEvent(
            ClevertapEventEnum.IMAGE_UPLOAD_DENIED,
            mapOf(
                ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to authMethod,
                ClevertapEventPropertyEnum.IMAGE_DENIED_REASON.propertyName to invalidReason
            )
        )
    }

    private fun bindReasonSection(
        context: Context,
        binding: FragmentImageDeniedBinding,
        invalidReason: String?,
    ) {
        val (titleRes, descRes) = when (InvalidProfileImageReason.fromValue(invalidReason)) {
            InvalidProfileImageReason.NO_FACE_DETECTED -> Pair(
                R.string.first_page_title,
                R.string.first_page_guideline
            )

            InvalidProfileImageReason.EXPLICIT_NUDITY_DETECTED -> Pair(
                R.string.third_page_title,
                R.string.third_guidelines
            )

            InvalidProfileImageReason.VIOLENCE_DETECTED -> Pair(
                R.string.second_page_title,
                R.string.second_page_guideline
            )

            InvalidProfileImageReason.VISUALLY_DISTURBING_DETECTED -> Pair(
                R.string.visual_disturbing_title,
                R.string.visual_disturbing_desc
            )

            InvalidProfileImageReason.CHILD_PICTURE_DETECTED -> Pair(
                R.string.fifth_page_title,
                R.string.fifth_guideline
            )

            null -> Pair(R.string.fifth_page_title, R.string.fifth_guideline)
        }

        binding.reasonTitle.text = context.getString(titleRes)
        binding.reasonDescription.text = context.getString(descRes)
    }

    private fun bindUserInvalidImage(imageView: ImageView, invalidImageKey: String?) {
        invalidImageKey?.let {
            val circularProgressDrawable = CircularProgressDrawable(imageView.context)
            circularProgressDrawable.strokeWidth = 5f
            circularProgressDrawable.centerRadius = 30f
            circularProgressDrawable.start()
            val roundedCorners = convertDpToPixel(16f, requireContext())
            val imageUrl = getS3Url(it)
            Glide.with(imageView.context)
                .load(imageUrl)
                .transform(
                    MultiTransformation(
                        CenterCrop(),
                        GranularRoundedCorners(
                            roundedCorners,
                            roundedCorners,
                            roundedCorners,
                            roundedCorners
                        )
                    )
                )
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .apply(
                    RequestOptions()
                        .placeholder(circularProgressDrawable)
                        .error(R.drawable.ic_broken_image)
                )
                .into(imageView)
        }
    }

    private fun bindImageGuideline(image: ImageView, genderType: GenderType?) {
        val drawableRes = when (genderType) {
            GenderType.MAN -> R.drawable.guideline_man_template
            GenderType.WOMAN -> R.drawable.guideline_woman_template
            null -> R.drawable.guideline_man_template
        }

        val roundedCorners = convertDpToPixel(16f, requireContext())
        Glide.with(image.context)
            .load(drawableRes)
            .transform(
                MultiTransformation(
                    CenterCrop(),
                    GranularRoundedCorners(
                        roundedCorners,
                        roundedCorners,
                        roundedCorners,
                        roundedCorners
                    )
                )
            )
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(image)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onDestroy() {
        super.onDestroy()
        invalidReason = null
        invalidImageKey = null
    }

    companion object {
        const val ARG_REASON = "arg_reason"
        const val ARG_IMAGE_KEY = "arg_image_key"
    }

    override fun onButtonClicked() {
        binding.replacePhotoBtn.performClick()
    }
}