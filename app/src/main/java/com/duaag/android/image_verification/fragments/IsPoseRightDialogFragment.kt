package com.duaag.android.image_verification.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.duaag.android.R
import com.duaag.android.clevertap.sendSentImageVerificationEvent
import com.duaag.android.clevertap.sendTryPoseAgainImageVerificationEvent
import com.duaag.android.databinding.IsPoseRightDialogBinding
import com.duaag.android.image_verification.ImageVerificationActivity
import com.duaag.android.image_verification.viewmodels.ImageVerificationViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.updateLocale
import javax.inject.Inject


class IsPoseRightDialogFragment : DialogFragment() {

    companion object {
        const val POSE_URI = "pose_uri"
        const val CAMERA_IMAGE_URI = "camera_image_uri"


        fun newInstance(poseUri: String, pictureUri: String): IsPoseRightDialogFragment {
            val fragment = IsPoseRightDialogFragment()
            val args = Bundle()
            args.putString(POSE_URI, poseUri)
            args.putString(CAMERA_IMAGE_URI, pictureUri)
            fragment.arguments = args
            return fragment
        }
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ImageVerificationViewModel>({ activity as ImageVerificationActivity }) { viewModelFactory }

    private  var _binding: IsPoseRightDialogBinding? = null
    private  val binding get() = _binding!!
    private lateinit var cameraUri: String
    private lateinit var poseUri: String


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as ImageVerificationActivity).imageVerificationComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        cameraUri = arguments?.getString(CAMERA_IMAGE_URI) ?: ""
        poseUri = arguments?.getString(POSE_URI) ?: ""

        isCancelable = false
        dialog?.setCanceledOnTouchOutside(false)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = IsPoseRightDialogBinding.inflate(layoutInflater)

        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.bottom_sheet_rounded)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }

        val roundedCorners = convertDpToPixel(20f, requireContext())
        Glide.with(requireContext())
                .load(poseUri)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .transform(MultiTransformation(CenterCrop(),GranularRoundedCorners(roundedCorners, 0f, 0f, roundedCorners)))
                .into(binding.poseImage)

        Glide.with(requireContext())
                .load(cameraUri)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .transform(MultiTransformation(CenterCrop(),GranularRoundedCorners(0f, roundedCorners, roundedCorners, 0f)))
                .into(binding.cameraImage)

        binding.continueButton.setOnClickListener {
            dismissAllowingStateLoss()
            (parentFragment as CameraImageVerificationFragment).continueWithImage(cameraUri, poseUri)

            val eventSource = requireActivity().intent.getStringExtra(ImageVerificationActivity.EVENT_SOURCE_VALUE)
            sendSentImageVerificationEvent(viewModel.userProfile.value, eventSource ?: "")

            firebaseLogEvent(FirebaseAnalyticsEventsName.SENT_PICTURES_VERIFICATION)
        }

        binding.tryAgain.setOnClickListener {
            (parentFragment as CameraImageVerificationFragment).tryAgain()
            dismissAllowingStateLoss()

            val eventSource = requireActivity().intent.getStringExtra(ImageVerificationActivity.EVENT_SOURCE_VALUE)
            sendTryPoseAgainImageVerificationEvent(viewModel.userProfile.value, eventSource ?: "")

            firebaseLogEvent(FirebaseAnalyticsEventsName.TRY_POSE_AGAIN)
        }

        return binding.root
    }

    interface PoseRightInterface {
        fun continueWithImage(image: String, pose: String)
        fun tryAgain()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}