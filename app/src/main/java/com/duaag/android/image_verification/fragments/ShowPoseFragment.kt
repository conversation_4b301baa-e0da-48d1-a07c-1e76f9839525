package com.duaag.android.image_verification.fragments

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.FragmentNavigatorExtras
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import androidx.transition.TransitionInflater
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.duaag.android.R
import com.duaag.android.clevertap.*
import com.duaag.android.databinding.ShowPoseFragmentBinding
import com.duaag.android.home.fragments.ShadowBannedDialog
import com.duaag.android.image_verification.ImageVerificationActivity
import com.duaag.android.image_verification.ImageVerificationActivity.Companion.EVENT_SOURCE_VALUE
import com.duaag.android.image_verification.viewmodels.ImageVerificationViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.NEW_IMAGES_REQUEST
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.OPENED_FROM
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.UPLOAD_IMAGE_SOURCE
import com.duaag.android.signup.fragment.guidelines.DialogClickListener
import com.duaag.android.signup.fragment.guidelines.GuidelinesDialogFragment
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import timber.log.Timber
import javax.inject.Inject

class ShowPoseFragment : Fragment(), DialogClickListener, ShadowBannedDialog.ShadowBannedDialogListener {

    companion object {
        fun newInstance() = ShowPoseFragment()
        private val REQUEST_CODE_PERMISSIONS = 103

        private val permissions = listOf(Manifest.permission.CAMERA)
        var showGuidelines = false
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ImageVerificationViewModel>({ activity as ImageVerificationActivity }) { viewModelFactory }
    private  var _binding: ShowPoseFragmentBinding? = null
    private  val binding get() = _binding!!


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        sharedElementReturnTransition = TransitionInflater.from(requireContext()).inflateTransition(android.R.transition.move)

        val callback: OnBackPressedCallback =
            object : OnBackPressedCallback(true /* enabled by default */) {
                override fun handleOnBackPressed() {
                    (requireActivity() as ImageVerificationActivity).finishImageVerification()
                }
            }
        requireActivity().onBackPressedDispatcher.addCallback(this, callback)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        _binding = ShowPoseFragmentBinding.inflate(inflater)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val circularProgressDrawable = CircularProgressDrawable(requireContext())
        circularProgressDrawable.setColorSchemeColors(Color.WHITE)
        circularProgressDrawable.strokeWidth = 5f
        circularProgressDrawable.centerRadius = 30f
        circularProgressDrawable.start()


        Glide.with(requireContext())
                .load(viewModel.randomImage)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .centerCrop()
                .apply(RequestOptions()
                        .placeholder(circularProgressDrawable)
                        .error(R.drawable.ic_broken_image))
                .into(binding.poseImage)


        binding.continueButton.setOnSingleClickListener(700L) {
            if(viewModel.userProfile.value?.profile?.isShadowBanned == true){
                val dialog = ShadowBannedDialog.newInstance(
                    profileUrl = viewModel.userProfile.value?.profile?.pictureUrl ?: "",
                    premiumType = getPremiumTypeEventProperty(viewModel.userProfile.value),
                    isFromFragment = true,
                    isUserTryingToVerify = true
                )
                dialog.show(childFragmentManager, "ShadowBannedDialog")
                return@setOnSingleClickListener
            }

            checkCameraStoragePermissions()

            val eventSource = requireActivity().intent.getStringExtra(EVENT_SOURCE_VALUE)
            sendUploadImageVerificationEvent(viewModel.userProfile.value, eventSource ?: "")

            firebaseLogEvent(FirebaseAnalyticsEventsName.UPLOAD_IMAGE_VERIFICATION)
        }

        if (viewModel.itsOpenFromSignUpActivity) {
            binding.showPoseDsc.setText(R.string.pose_as_shown_in_image)
        } else {
            binding.showPoseDsc.setText(R.string.pose_as_shown_in_the_image_above)
        }

        binding.closeButton.setOnSingleClickListener {
            (requireActivity() as ImageVerificationActivity).finishImageVerification()
        }

        binding.guidelines.setOnClickListener {

            val gender = (activity as ImageVerificationActivity).intent.extras?.getString(ImageVerificationActivity.USER_GENDER)

            GuidelinesDialogFragment.newInstance(isForImageVerification = true, userGender = gender
                    ?: "M", premiumTypeEventProperty = getPremiumTypeEventProperty(viewModel.userProfile.value)).show(childFragmentManager, "guidelinesDialog")

            val eventSource = requireActivity().intent.getStringExtra(EVENT_SOURCE_VALUE)
            sendSeeGuidelinesVerificationEvent(viewModel.userProfile.value, eventSource ?: "")

            firebaseLogEvent(FirebaseAnalyticsEventsName.SEE_GUIDELINES_VERIFICATION)
        }

        if (showGuidelines) {
            showGuidelines = false
            binding.guidelines.performClick()
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as ImageVerificationActivity).imageVerificationComponent.inject(this)
    }

    override fun onButtonClicked() {
        binding.continueButton.performClick()
    }

    private fun checkCameraStoragePermissions() {
        if (!checkAllPermissions()) {
            requestPermissions(permissions.toTypedArray(), REQUEST_CODE_PERMISSIONS)
        } else {
            goToCameraFragment()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int,
                                            permissions: Array<String>,
                                            grantResults: IntArray) {

        when (requestCode) {
            REQUEST_CODE_PERMISSIONS -> {
                // If request is cancelled, the result arrays are empty.
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED && grantResults[1] == PackageManager.PERMISSION_GRANTED &&((Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) || (grantResults[2] == PackageManager.PERMISSION_GRANTED))) {
                    Timber.tag("debug").d("Permission Granted")
                    goToCameraFragment()
                } else {
                    Timber.tag("debug").d("Permission Denied")
                    ToastUtil.toast(R.string.u_must_allow_permissions, Toast.LENGTH_LONG)
                }
                return
            }

            else -> {
            }
        }
    }

    private fun goToCameraFragment() {
        val extras = FragmentNavigatorExtras(binding.poseImage to "poseTransition")

        findNavController().navigate(
            R.id.action_showPoseFragment_to_cameraImageVerificationFragment,
            null,
            null,
            extras)

    }

    /** Permission Checking  */
    private fun checkAllPermissions(): Boolean {
        var hasPermissions = true
        for (permission in permissions) {
            hasPermissions = hasPermissions and isPermissionGranted(permission)
        }
        return hasPermissions
    }

    private fun isPermissionGranted(permission: String) = context?.let { ContextCompat.checkSelfPermission(it, permission) } == PackageManager.PERMISSION_GRANTED

    override fun changePhoto() {
        val intent = Intent(context, ManagePicturesActivity::class.java)
        intent.putExtra(UPLOAD_IMAGE_SOURCE, arguments?.getString(OPENED_FROM) ?: UploadImageSourceValues.PROFILE.value)
        startActivityForResult(intent, NEW_IMAGES_REQUEST)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}