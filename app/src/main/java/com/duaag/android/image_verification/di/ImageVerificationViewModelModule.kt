package com.duaag.android.image_verification.di

import androidx.lifecycle.ViewModel
import com.duaag.android.di.ViewModelKey
import com.duaag.android.image_verification.viewmodels.ImageVerificationViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class ImageVerificationViewModelModule {

    @Binds
    @IntoMap
    @ViewModelKey(ImageVerificationViewModel::class)
    abstract fun bindImageVerificationViewModel(myViewModel: ImageVerificationViewModel): ViewModel
}