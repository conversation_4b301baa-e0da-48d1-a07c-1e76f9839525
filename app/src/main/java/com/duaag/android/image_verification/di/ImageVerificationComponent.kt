package com.duaag.android.image_verification.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.image_verification.ImageVerificationActivity
import com.duaag.android.image_verification.fragments.CameraImageSubmitDialogFragment
import com.duaag.android.image_verification.fragments.CameraImageVerificationFragment
import com.duaag.android.image_verification.fragments.IsPoseRightDialogFragment
import com.duaag.android.image_verification.fragments.ShowPoseFragment
import dagger.Subcomponent

@ActivityScope
@Subcomponent(modules = [ImageVerificationViewModelModule::class])
interface ImageVerificationComponent {

    // Factory to create instances of RegistrationComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): ImageVerificationComponent
    }

    // Classes that can be injected by this Component
    fun inject(activity: ImageVerificationActivity)
    fun inject(fragment: ShowPoseFragment)
    fun inject(fragment: CameraImageSubmitDialogFragment)
    fun inject(fragment: IsPoseRightDialogFragment)
    fun inject(fragment: CameraImageVerificationFragment)
}