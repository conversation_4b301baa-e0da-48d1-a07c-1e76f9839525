package com.duaag.android.image_verification.fragments

import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.hardware.display.DisplayManager
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.*
import android.view.Surface.ROTATION_0
import android.webkit.MimeTypeMap
import androidx.camera.core.*
import androidx.camera.core.AspectRatio.RATIO_16_9
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.core.net.toFile
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.transition.TransitionInflater
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.duaag.android.R
import com.duaag.android.clevertap.sendSeeGuidelinesVerificationEvent
import com.duaag.android.databinding.CameraImageVerificationFragmentBinding
import com.duaag.android.image_verification.ImageVerificationActivity
import com.duaag.android.image_verification.viewmodels.ImageVerificationViewModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.SpinningCircleDialog
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import javax.inject.Inject

class CameraImageVerificationFragment : Fragment(), IsPoseRightDialogFragment.PoseRightInterface, CameraImageSubmitDialogFragment.ImageSubmitted {

    companion object {
        fun newInstance() = CameraImageVerificationFragment()
        private const val TAG = "CameraImageVerification"

        private const val FILENAME = "yyyy-MM-dd-HH-mm-ss-SSS"
        private const val PHOTO_EXTENSION = ".jpg"

        /** Helper function used to create a timestamped file */
        private fun createFile(baseFolder: File, format: String, extension: String) =
                File(baseFolder, SimpleDateFormat(format, Locale.US)
                        .format(System.currentTimeMillis()) + extension)
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ImageVerificationViewModel>({ activity as ImageVerificationActivity }) { viewModelFactory }
    private  var _binding: CameraImageVerificationFragmentBinding? = null
    private  val binding get() = _binding!!


    private var spinningCircleDialog: SpinningCircleDialog? = null

    private var lensFacing: Int = CameraSelector.LENS_FACING_FRONT
    var preview: Preview? = null
    private var camera: Camera? = null
    private var cameraProvider: ProcessCameraProvider? = null
    private var imageCapture: ImageCapture? = null
    private var flashEnabled: Boolean = false
    private lateinit var outputDirectory: File

    /** Blocking camera operations are performed using this executor */
    private lateinit var cameraExecutor: ExecutorService

    private val displayManager by lazy {
        requireContext().getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
    }

    /**
     * We need a display listener for orientation changes that do not trigger a configuration
     * change, for example if we choose to override config change in manifest or for 180-degree
     * orientation changes.
     */
    private val displayListener = object : DisplayManager.DisplayListener {
        override fun onDisplayAdded(displayId: Int) = Unit
        override fun onDisplayRemoved(displayId: Int) = Unit
        override fun onDisplayChanged(displayId: Int) = view?.let { view ->
            if (displayId == displayId) {
                Timber.tag(TAG).d("Rotation changed: ${view.display?.rotation}")
                view.display?.rotation?.let {
                    imageCapture?.targetRotation = it
                }
            }
        } ?: Unit
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        postponeEnterTransition()

        sharedElementEnterTransition = TransitionInflater.from(requireContext()).inflateTransition(android.R.transition.move)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        _binding = CameraImageVerificationFragmentBinding.inflate(inflater)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        spinningCircleDialog = context?.let { it1 -> SpinningCircleDialog(it1) }

        // Initialize our background executor
        cameraExecutor = Executors.newSingleThreadExecutor()

        // Every time the orientation of device changes, update rotation for use cases
        displayManager.registerDisplayListener(displayListener, null)

        // Determine the output directory
        outputDirectory = ImageVerificationActivity.getOutputDirectory(requireContext())


        Glide.with(requireContext())
                .load(viewModel.randomImage)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: com.bumptech.glide.request.target.Target<Drawable>?,
                            isFirstResource: Boolean
                    ): Boolean {
                        view.post { startPostponedEnterTransition() }
                        return false
                    }

                    override fun onResourceReady(
                            resource: Drawable?,
                            model: Any?,
                            target: com.bumptech.glide.request.target.Target<Drawable>?,
                            dataSource: DataSource?,
                            isFirstResource: Boolean
                    ): Boolean {
                        view.post { startPostponedEnterTransition() }
                        return false
                    }
                })
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .transform(MultiTransformation(CenterCrop(), RoundedCorners(convertDpToPixel(8f, requireContext()).toInt())))
                .into(binding.poseImage)

        // Set up the camera and its use cases
        setUpCamera()

        viewModel.uploadProgress.observe(viewLifecycleOwner, {
            if (it == -1)
                spinningCircleDialog?.dismiss()
        })

        viewModel.imageVerified.observe(viewLifecycleOwner) {
            it?.let { response ->
                val eventSource =
                    requireActivity().intent.getStringExtra(ImageVerificationActivity.EVENT_SOURCE_VALUE)
                sendSeeGuidelinesVerificationEvent(viewModel.userProfile.value, eventSource ?: "")

                spinningCircleDialog?.dismiss()
                val imageSubmittedDialog =
                    viewModel.userProfile.value?.profile?.pictureUrl?.let { it1 ->
                        CameraImageSubmitDialogFragment.newInstance(
                            it1, response.image, response.status == Badge2Status.PROCESSING.status)
                    }
                imageSubmittedDialog?.show(childFragmentManager, "imageSubmittedDialog")
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as ImageVerificationActivity).imageVerificationComponent.inject(this)
        Timber.tag("IMAGEVIEWMODEL").d("CameraFragment $viewModel")
    }

    /** Initialize CameraX, and prepare to bind the camera use cases  */
    private fun setUpCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(requireContext())
        cameraProviderFuture.addListener(Runnable {

            // CameraProvider
            cameraProvider = cameraProviderFuture.get()


            setCameraListeners()

            // Build and bind the camera use cases
            bindCameraUseCases()
        }, ContextCompat.getMainExecutor(requireContext()))
    }

    /** Declare and bind preview, capture and analysis use cases */
    private fun bindCameraUseCases() {

        // CameraProvider
        val cameraProvider = cameraProvider
                ?: throw IllegalStateException("Camera initialization failed.")

        // CameraSelector
        val cameraSelector = CameraSelector.Builder().requireLensFacing(lensFacing).build()

        // Preview
        preview = Preview.Builder()
                // We request aspect ratio but no resolution
                .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                // Set initial target rotation
                .setTargetRotation(Surface.ROTATION_0)
                .build()

        // ImageCapture
        imageCapture = ImageCapture.Builder()
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                // We request aspect ratio but no resolution to match preview config, but letting
                // CameraX optimize for whatever specific resolution best fits our use cases
                .setTargetAspectRatio(RATIO_16_9)
                // Set initial target rotation, we will have to call this again if rotation changes
                // during the lifecycle of this use case
                .setTargetRotation(ROTATION_0)
                .build()


        // Must unbind the use-cases before rebinding them
        cameraProvider.unbindAll()

        try {
            // A variable number of use-cases can be passed here -
            // camera provides access to CameraControl & CameraInfo
            camera = cameraProvider.bindToLifecycle(
                    this, cameraSelector, preview, imageCapture)

            // Attach the viewfinder's surface provider to preview use case
            preview?.setSurfaceProvider(binding.viewFinder.surfaceProvider)
        } catch (exc: Exception) {
            Timber.tag(TAG).e(exc, "Use case binding failed")
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setCameraListeners() {

        binding.closeButton.setOnClickListener {
            (requireActivity() as ImageVerificationActivity).finishImageVerification()
        }


        binding.flashButton.setOnClickListener {
            flashEnabled = !flashEnabled
            binding.flashButton.setImageResource(if (flashEnabled) R.drawable.ic_flash_active else R.drawable.ic_flash_disabled)
            camera?.cameraControl?.enableTorch(flashEnabled)
        }

        // Listener for button used to capture photo
        binding.cameraCaptureButton.setOnClickListener {

            animateCameraButton()

            // Get a stable reference of the modifiable image capture use case
            imageCapture?.let { imageCapture ->

                // Create output file to hold the image
                val photoFile = createFile(outputDirectory, FILENAME, PHOTO_EXTENSION)

                // Setup image capture metadata
                val metadata = ImageCapture.Metadata().apply {

                    // Mirror image when using the front camera
                    isReversedHorizontal = lensFacing == CameraSelector.LENS_FACING_FRONT
                }

                // Create output options object which contains file + metadata
                val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile)
                        .setMetadata(metadata)
                        .build()

                val layout: WindowManager.LayoutParams = requireActivity().window.attributes
                val layoutBrightness = layout.screenBrightness
                if (flashEnabled) {
                    layout.screenBrightness = 1f
                    requireActivity().window.attributes = layout
                    binding.whiteForeground.visibility = View.VISIBLE
                }

                // Setup image capture listener which is triggered after photo has been taken
                imageCapture.takePicture(
                        outputOptions, cameraExecutor, object : ImageCapture.OnImageSavedCallback {
                    override fun onError(exc: ImageCaptureException) {
                        Timber.tag(TAG).e(exc, "Photo capture failed: ${exc.message}")

                        if (flashEnabled) {
                            binding.whiteForeground.visibility = View.GONE
                            layout.screenBrightness = layoutBrightness
                            requireActivity().window.attributes = layout
                        }
                    }

                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {

                        if (flashEnabled) {
                            Handler(Looper.getMainLooper()).post {
                                binding.whiteForeground.visibility = View.GONE
                                val layout: WindowManager.LayoutParams = requireActivity().window.attributes
                                layout.screenBrightness = layoutBrightness
                                requireActivity().window.attributes = layout
                            }
                        }

                        val savedUri = output.savedUri ?: Uri.fromFile(photoFile)
                        Timber.tag(TAG).d("Photo capture succeeded: $savedUri")

                        Handler(Looper.getMainLooper()).post {
                            // load image in glide
                            binding.capturedImage.visibility = View.VISIBLE
                            Glide.with(requireContext())
                                    .load(savedUri)
                                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                    .centerCrop()
                                    .into(binding.capturedImage)
                        }

                        val poseRightDialog = IsPoseRightDialogFragment.newInstance(
                                viewModel.randomImage,
                                savedUri.toString()
                        )
                        poseRightDialog.show(childFragmentManager, "iPoseRight")


                        // Implicit broadcasts will be ignored for devices running API level >= 24
                        // so if you only target API level 24+ you can remove this statement
                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
                            requireActivity().sendBroadcast(
                                    Intent(android.hardware.Camera.ACTION_NEW_PICTURE, savedUri)
                            )
                        }

                        // If the folder selected is an external media directory, this is
                        // unnecessary but otherwise other apps will not be able to access our
                        // images unless we scan them using [MediaScannerConnection]
                        val mimeType = MimeTypeMap.getSingleton()
                                .getMimeTypeFromExtension(savedUri.toFile().extension)
                        MediaScannerConnection.scanFile(
                                context,
                                arrayOf(savedUri.toFile().absolutePath),
                                arrayOf(mimeType)
                        ) { _, uri ->
                            Timber.tag(TAG).d("Image capture scanned into media store: $uri")
                        }
                    }
                })

                // We can only change the foreground Drawable using API level 23+ API
                /*if (!flashEnabled) {
                    // Display flash animation to indicate that photo was captured
                    binding.root.postDelayed({
                        binding.root.foreground = ColorDrawable(Color.WHITE)
                        binding.root.postDelayed(
                                { binding.root.foreground = null }, 100L)
                    }, 20L)
                }*/
            }
        }

    }

    private fun animateCameraButton() {
        val scaleX = PropertyValuesHolder.ofFloat(View.SCALE_X, 1.1f)
        val scaleY = PropertyValuesHolder.ofFloat(View.SCALE_Y, 1.1f)
        val animator = ObjectAnimator.ofPropertyValuesHolder(binding.cameraCaptureButton, scaleX, scaleY)
        animator.repeatCount = 1
        animator.repeatMode = ObjectAnimator.REVERSE
        animator.duration = 50
        animator.start()
    }

    override fun continueWithImage(image: String, pose: String) {
        viewModel.uploadImage(image)
        spinningCircleDialog?.show()
    }

    override fun done() {
        viewModel.setUserProcessing()
        (requireActivity() as ImageVerificationActivity).finishImageVerification()
    }

    override fun tryAgain() {
        hideCapturedImage()
    }

    override fun goToGuidelines() {
        parentFragmentManager.popBackStack()
    }

    private fun hideCapturedImage() {
        binding.capturedImage.setImageBitmap(null)
        binding.capturedImage.visibility = View.GONE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        displayManager.unregisterDisplayListener(displayListener)

        spinningCircleDialog = null

        preview = null
        camera = null
        cameraProvider = null
        imageCapture = null

        _binding = null
    }
}
