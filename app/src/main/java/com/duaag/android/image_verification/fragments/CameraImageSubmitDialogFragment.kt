package com.duaag.android.image_verification.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.duaag.android.R
import com.duaag.android.clevertap.*
import com.duaag.android.databinding.VerificationSubmittedDialogBinding
import com.duaag.android.image_verification.ImageVerificationActivity
import com.duaag.android.image_verification.viewmodels.ImageVerificationViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.getS3Url
import javax.inject.Inject


class CameraImageSubmitDialogFragment : DialogFragment() {

    companion object {
        const val POSE_URI = "pose_uri"
        const val CAMERA_IMAGE_URI = "camera_image_uri"
        const val VERIFICATION_SUBMITTED = "verification_submitted"

        fun newInstance(poseUri: String, pictureUri: String?, verificationSubmitted: Boolean): CameraImageSubmitDialogFragment {
            val fragment = CameraImageSubmitDialogFragment()
            val args = Bundle()
            args.putString(POSE_URI, poseUri)
            args.putString(CAMERA_IMAGE_URI, pictureUri)
            args.putBoolean(VERIFICATION_SUBMITTED, verificationSubmitted)
            fragment.arguments = args
            return fragment
        }
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ImageVerificationViewModel>({ activity as ImageVerificationActivity }) { viewModelFactory }


    private  var _binding: VerificationSubmittedDialogBinding? = null
    private  val binding get() = _binding!!

    private lateinit var cameraUri: String
    private lateinit var poseUri: String
    private var verificationSubmitted: Boolean = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as ImageVerificationActivity).imageVerificationComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        cameraUri = arguments?.getString(CAMERA_IMAGE_URI) ?: ""
        poseUri = arguments?.getString(POSE_URI) ?: ""
        verificationSubmitted = arguments?.getBoolean(VERIFICATION_SUBMITTED) ?: false

        isCancelable = false
        dialog?.setCanceledOnTouchOutside(false)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = VerificationSubmittedDialogBinding.inflate(layoutInflater)

        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.bottom_sheet_rounded)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }

        val roundedCorners = convertDpToPixel(20f, requireContext())

        if(verificationSubmitted){
            setSuggestionsVisibility(View.GONE)
            binding.continueButton.text = getString(R.string.done_f)
            binding.title.text = getString(R.string.image_sent)
            binding.description.text = getString(R.string.thanks_your_photo_will_be_reviewed_by_dua_we_will_get_back_to_you_as_soon_as_possible_happy_connecting,getString(R.string.app_name))

            binding.continueButton.setOnClickListener {
                dismissAllowingStateLoss()
                (parentFragment as CameraImageVerificationFragment).done()

                val eventSource = requireActivity().intent.getStringExtra(ImageVerificationActivity.EVENT_SOURCE_VALUE)
                sendImageSentSuccessfullyEvent(viewModel.userProfile.value, eventSource ?: "")

                firebaseLogEvent(FirebaseAnalyticsEventsName.IMAGE_SENT_SUCCESSFULLY)
            }

            Glide.with(requireContext())
                    .load(cameraUri)
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    .transform(MultiTransformation(CenterCrop(),RoundedCorners(roundedCorners.toInt())))
                    .into(binding.image)

        } else {
            val eventPremiumType = getPremiumTypeEventProperty(viewModel.userProfile.value)

            firebaseLogEvent(FirebaseAnalyticsEventsName.VERIFICATION_DENIED, mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
            ))

            sendClevertapEvent(
                ClevertapEventEnum.VERIFICATION_DENIED, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType
            ))

            setSuggestionsVisibility(View.VISIBLE)
            binding.continueButton.text = getString(R.string.try_again)
            binding.title.text = getString(R.string.verification_denied)
            binding.description.text = getString(R.string.verification_denied_subtitle)
           
            binding.suggestionOneTextView.text = getString(R.string.verification_denied_suggestion1)
            binding.suggestionTwoTextView.text = getString(R.string.verification_denied_suggestion2)

            binding.continueButton.setOnClickListener {

                firebaseLogEvent(FirebaseAnalyticsEventsName.TRY_POSE_AGAIN)

                val eventSource = requireActivity().intent.getStringExtra(ImageVerificationActivity.EVENT_SOURCE_VALUE)
                sendTryPoseAgainImageVerificationEvent(viewModel.userProfile.value, eventSource ?: "")

                ShowPoseFragment.showGuidelines = true
                (parentFragment as CameraImageVerificationFragment).goToGuidelines()
            }

             val image3Url = getS3Url(poseUri)
            Glide.with(requireContext())
                    .load(image3Url)
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    .transform(MultiTransformation(CenterCrop(),GranularRoundedCorners(roundedCorners, 0f, 0f, roundedCorners)))
                    .into(binding.poseImage)

            Glide.with(requireContext())
                    .load(cameraUri)
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    .transform(MultiTransformation(CenterCrop(),GranularRoundedCorners(0f, roundedCorners, roundedCorners, 0f)))
                    .into(binding.cameraImage)
        }


        return binding.root
    }

    private fun setSuggestionsVisibility(visibility: Int) {
        binding.suggestionOne.visibility = visibility
        binding.suggestionTwo.visibility = visibility
        binding.titleSuggestions.visibility = visibility
    }

    interface ImageSubmitted {
        fun done()
        fun goToGuidelines()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}