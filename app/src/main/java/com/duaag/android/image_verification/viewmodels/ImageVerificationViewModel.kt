package com.duaag.android.image_verification.viewmodels

import androidx.exifinterface.media.ExifInterface
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.api.UserService
import com.duaag.android.api.Resource
import com.duaag.android.application.DuaApplication
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.di.ActivityScope
import com.duaag.android.image_verification.models.ImageVerificationSubmitResponse
import com.duaag.android.image_verification.models.SubmitImageModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.*
import com.duaag.android.utils.livedata.SingleLiveData
import id.zelory.compressor.Compressor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.util.*
import javax.inject.Inject
import javax.inject.Named

@ActivityScope
class ImageVerificationViewModel @Inject constructor(@Named("private") val userService: UserService,
                                                     val userRepository: UserRepository) : ViewModel() {


    companion object {
        const val TAG = "ImageVerificationViewModel"
    }

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    val userProfile = userRepository.user

    var itsOpenFromSignUpActivity :Boolean = false

    lateinit var randomPose: String
    lateinit var randomImage: String

    private val _uploadProgress = MutableLiveData<Int>()
    val uploadProgress: LiveData<Int>
        get() = _uploadProgress

    private var _imageVerified = SingleLiveData<ImageVerificationSubmitResponse>()
    val imageVerified: LiveData<ImageVerificationSubmitResponse>
        get() = _imageVerified

    var maleImages = listOf("gestures/male_1.png",
            "gestures/male_2.png",
            "gestures/male_3.png")

    var femaleImages = listOf("gestures/female_1.png",
            "gestures/female_2.png",
            "gestures/female_3.png")


    fun generateRandomImage(gender: String) {
        randomPose = if (gender == GenderType.WOMAN.value) {
            femaleImages.random()
        } else {
            maleImages.random()
        }
        val date = Calendar.getInstance()
        val timeQuery = "?time=" + date.get(Calendar.DAY_OF_MONTH) + date.get(Calendar.MONTH) + date.get(Calendar.YEAR)
        randomImage = RemoteConfigUtils.getCdnDomain() + randomPose + timeQuery
    }


    fun uploadImage(imagePath: String) {
        val image = File(imagePath.substringAfter(":"))

        viewModelScope.launch(Dispatchers.IO) {
            try {
                val compressedImageFile = Compressor.compress(DuaApplication.instance, image)
                try {
                    copyExif(image.absolutePath, compressedImageFile.absolutePath)
                } catch (e: IOException) {
                    e.printStackTrace()
                }

                Timber.tag("COMPRESS").d("Size: ${getFileSize(compressedImageFile.length())}")

                val fileName = AWSInteractor.getResourceUrl(UUID.randomUUID().toString(), compressedImageFile.extension)

                removeMetadataFromImage(compressedImageFile.absolutePath)

                AWSInteractor.uploadFile(compressedImageFile, fileName, object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState) {
                        if (TransferState.COMPLETED === state) {
                            Timber.tag(TAG).d("State: ${state.name}")
                            _uploadProgress.postValue(100)

                            val model = SubmitImageModel(randomPose, fileName)
                            submitForVerification(model, imagePath)
                        } else if (TransferState.FAILED === state) {
                            _uploadProgress.postValue(-1)
                            ToastUtil.toast("Upload Failed!")
                        }
                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                        val percentDone = (bytesCurrent.toFloat() / bytesTotal.toFloat() * 100).toInt()
                        _uploadProgress.postValue(percentDone)
                        Timber.tag(TAG).d("ID:$id|bytesCurrent: $bytesCurrent|bytesTotal: $bytesTotal|$percentDone%")
                    }

                    override fun onError(id: Int, ex: Exception) {
                        ex.printStackTrace()
                        _uploadProgress.postValue(-1)
                        ToastUtil.toast("Upload Failed!")
                        ex.printStackTrace()
                    }

                })
            } catch (e: Exception) {
                _uploadProgress.postValue(-1)
                e.printStackTrace()
            }
        }

    }

    fun submitForVerification(model: SubmitImageModel, imagePath: String) {
        viewModelScope.launch(Dispatchers.IO) {
            Timber.tag(TAG).d("Key: $model")
            userRepository.submitImageVerification(model)
                    .catch { ex ->
                        _uploadProgress.postValue(-1)
                        ToastUtil.toast(R.string.an_error_occurred)
                        ex.printStackTrace()
                        logError(ErrorStatus.SUBMIT_IMAGE_VERIFICATION)
                    }
                    .collect {
                        when (it) {
                            is Resource.Loading -> {
                            }
                            is Resource.Success -> {
                                val data = it.data
                                data.image = imagePath
                                duaSharedPrefs.setVerifyProfileBadge2DialogModel(null)
                                _imageVerified.postValue(it.data)
                            }
                            else -> {
                            }
                        }
                    }
        }
    }

    fun setUserProcessing() {
        viewModelScope.launch(Dispatchers.IO) {
            val user = userProfile.value
            user?.let {
                it.badge2 = Badge2Status.PROCESSING.status
                userRepository.updateUser(it)
            }
        }
    }

    @Throws(IOException::class)
    fun copyExif(oldPath: String, newPath: String) {
        val oldExif = ExifInterface(oldPath)
        val attributes = arrayOf<String>(
                ExifInterface.TAG_DATETIME,
                ExifInterface.TAG_DATETIME_DIGITIZED,
                ExifInterface.TAG_ORIENTATION
        )
        val newExif = ExifInterface(newPath)
        for (i in attributes.indices) {
            val value: String? = oldExif.getAttribute(attributes[i])
            if (value != null) newExif.setAttribute(attributes[i], value)
        }
        newExif.saveAttributes()
    }
}