package com.duaag.android.profile_builder.adapter

import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.ItemTagOptionLoadingBinding
import com.duaag.android.databinding.ItemTagOptionMultiBinding
import com.duaag.android.databinding.ItemTagOptionSingleBinding
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import com.duaag.android.utils.setOnSingleClickListener

/**
 * Adapter for displaying tag options in a RecyclerView.
 * Supports single-select, multi-select, and loading view types.
 *
 * @param questionType The type of question (determines the view type).
 * @param onTagSelected Callback invoked when a tag is selected.
 */
class TagOptionsAdapter(
    var questionType: ProfileQuestionType,
    private val onTagSelected: (ProfileTagOptionModel.Item) -> Unit
) : ListAdapter<ProfileTagOptionModel, RecyclerView.ViewHolder>(TagDiffCallback()) {

    companion object {
        const val VIEW_TYPE_SINGLE_SELECT = 1
        const val VIEW_TYPE_MULTI_SELECT = 2
        const val VIEW_TYPE_LOADING = 3
    }

    /**
     * Determines the view type for a given position.
     *
     * @param position The position of the item in the list.
     * @return The view type constant.
     */
    override fun getItemViewType(position: Int): Int {
        val item = getItem(position)

        return when (item) {
            is ProfileTagOptionModel.Loading -> VIEW_TYPE_LOADING
            is ProfileTagOptionModel.Item -> {
                if (questionType == ProfileQuestionType.LANGUAGES) {
                    VIEW_TYPE_MULTI_SELECT
                } else {
                    VIEW_TYPE_SINGLE_SELECT
                }
            }
            else -> throw IllegalArgumentException("Unknown item type")
        }
    }

    /**
     * Creates a new ViewHolder for the given view type.
     *
     * @param parent The parent ViewGroup.
     * @param viewType The type of the view.
     * @return A new ViewHolder instance.
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            VIEW_TYPE_SINGLE_SELECT -> {
                val binding = ItemTagOptionSingleBinding.inflate(inflater, parent, false)
                SingleSelectViewHolder(binding, onTagSelected)
            }
            VIEW_TYPE_MULTI_SELECT -> {
                val binding = ItemTagOptionMultiBinding.inflate(inflater, parent, false)
                MultiSelectViewHolder(binding, onTagSelected)
            }
            VIEW_TYPE_LOADING -> {
                val binding = ItemTagOptionLoadingBinding.inflate(inflater, parent, false)
                LoadingViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    /**
     * Binds data to the ViewHolder, with support for partial updates using payloads.
     *
     * @param holder The ViewHolder to bind data to.
     * @param position The position of the item in the list.
     * @param payloads A list of payloads for partial updates.
     */
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty()) {
            val item = getItem(position)

            if (holder is SingleSelectViewHolder && item is ProfileTagOptionModel.Item) {
                val isSelected = payloads.firstOrNull() as? Boolean ?: item.isSelected
                holder.updateSelectionState(isSelected)
                return
            } else if (holder is MultiSelectViewHolder && item is ProfileTagOptionModel.Item) {
                val isSelected = payloads.firstOrNull() as? Boolean ?: item.isSelected
                holder.updateSelectionState(isSelected)
                return
            }
        }

        // Fallback to full bind if no payload or different holder type
        super.onBindViewHolder(holder, position, payloads)
    }

    /**
     * Binds data to the ViewHolder.
     *
     * @param holder The ViewHolder to bind data to.
     * @param position The position of the item in the list.
     */
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position)

        when (holder) {
            is SingleSelectViewHolder -> {
                if (item is ProfileTagOptionModel.Item) {
                    holder.bind(item)
                }
            }
            is MultiSelectViewHolder -> {
                if (item is ProfileTagOptionModel.Item) {
                    holder.bind(item)
                }
            }
            is LoadingViewHolder -> {
                // Loading holder needs no binding
            }
        }
    }

    /**
     * ViewHolder for single-select tag options.
     *
     * @param binding The binding for the single-select layout.
     * @param onTagSelected Callback invoked when a tag is selected.
     */
    class SingleSelectViewHolder(
        private val binding: ItemTagOptionSingleBinding,
        private val onTagSelected: (ProfileTagOptionModel.Item) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.tagRadioButton.isClickable = false

            binding.root.setOnSingleClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val adapter = bindingAdapter as? TagOptionsAdapter
                    if (adapter != null && position < adapter.itemCount) {
                        val item = adapter.getItem(position) as? ProfileTagOptionModel.Item
                        item?.let(onTagSelected)
                    }
                }
            }
        }

        /**
         * Binds a single-select tag option to the ViewHolder.
         *
         * @param item The tag option to bind.
         */
        fun bind(item: ProfileTagOptionModel.Item) {
            binding.tagText.text = item.name
            updateSelectionState(item.isSelected)
        }

        /**
         * Updates the selection state of the tag option.
         *
         * @param isSelected Whether the tag option is selected.
         */
        fun updateSelectionState(isSelected: Boolean) {
            binding.tagRadioButton.setImageResource(
                if (isSelected) R.drawable.radio_selected_pink
                else R.drawable.radio_unselected
            )
            binding.root.setBackgroundResource(
                if (isSelected) R.drawable.border_background_thick_16_dp
                else R.drawable.border_background_16_dp
            )
        }
    }

    /**
     * ViewHolder for multi-select tag options.
     *
     * @param binding The binding for the multi-select layout.
     * @param onTagSelected Callback invoked when a tag is selected.
     */
    class MultiSelectViewHolder(
        private val binding: ItemTagOptionMultiBinding,
        private val onTagSelected: (ProfileTagOptionModel.Item) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.tagCheckbox.isClickable = false

            binding.root.setOnClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val adapter = bindingAdapter as? TagOptionsAdapter
                    if (adapter != null && position < adapter.itemCount) {
                        val item = adapter.getItem(position) as? ProfileTagOptionModel.Item
                        item?.let(onTagSelected)
                    }
                }
            }
        }

        /**
         * Binds a multi-select tag option to the ViewHolder.
         *
         * @param item The tag option to bind.
         */
        fun bind(item: ProfileTagOptionModel.Item) {
            binding.tagText.text = item.name
            updateSelectionState(item.isSelected)
        }

        /**
         * Updates the selection state of the tag option.
         *
         * @param isSelected Whether the tag option is selected.
         */
        fun updateSelectionState(isSelected: Boolean) {
            binding.tagCheckbox.isChecked = isSelected
            binding.tagCheckbox.buttonTintList = ColorStateList.valueOf(
                ContextCompat.getColor(binding.root.context,
                    if (isSelected) R.color.red_500 else R.color.stroke_secondary)
            )
            binding.root.setBackgroundResource(
                if (isSelected) R.drawable.border_background_thick_16_dp
                else R.drawable.border_background_16_dp
            )
        }
    }

    /**
     * ViewHolder for loading tag options.
     *
     * @param binding The binding for the loading layout.
     */
    class LoadingViewHolder(
        binding: ItemTagOptionLoadingBinding
    ) : RecyclerView.ViewHolder(binding.root)

    /**
     * DiffUtil callback for calculating the differences between two lists of tag options.
     */
    class TagDiffCallback : DiffUtil.ItemCallback<ProfileTagOptionModel>() {

        /**
         * Checks if two items represent the same tag option.
         *
         * @param oldItem The old item.
         * @param newItem The new item.
         * @return True if the items are the same, false otherwise.
         */
        override fun areItemsTheSame(oldItem: ProfileTagOptionModel, newItem: ProfileTagOptionModel): Boolean {
            return oldItem.id == newItem.id
        }

        /**
         * Checks if the contents of two items are the same.
         *
         * @param oldItem The old item.
         * @param newItem The new item.
         * @return True if the contents are the same, false otherwise.
         */
        override fun areContentsTheSame(oldItem: ProfileTagOptionModel, newItem: ProfileTagOptionModel): Boolean {
            return oldItem == newItem
        }

        /**
         * Determines the payload for partial updates.
         *
         * @param oldItem The old item.
         * @param newItem The new item.
         * @return The payload for partial updates, or null if a full rebind is needed.
         */
        override fun getChangePayload(oldItem: ProfileTagOptionModel, newItem: ProfileTagOptionModel): Any? {
            if (oldItem is ProfileTagOptionModel.Item && newItem is ProfileTagOptionModel.Item) {
                if (oldItem.isSelected != newItem.isSelected &&
                    oldItem.tagId == newItem.tagId &&
                    oldItem.name == newItem.name) {
                    return newItem.isSelected
                }
            }
            return null
        }
    }

    /**
     * Submits a list of items to the adapter, handling loading states.
     *
     * @param list The list of items to submit.
     * @param commitCallback Optional callback to be called when the update is committed.
     */
    override fun submitList(list: List<ProfileTagOptionModel>?, commitCallback: Runnable?) {
        // If list is null or empty, show loading state
        if (list.isNullOrEmpty()) {
            val loadingItems = List(3) { ProfileTagOptionModel.Loading() }
            super.submitList(loadingItems, commitCallback)
            return
        }
        
        // Force a new list instance to ensure DiffUtil detects changes
        val newList = list.toList()
        super.submitList(newList, commitCallback)
    }
}