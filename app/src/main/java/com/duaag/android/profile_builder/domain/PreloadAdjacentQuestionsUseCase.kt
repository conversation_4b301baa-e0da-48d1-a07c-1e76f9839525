package com.duaag.android.profile_builder.domain

import com.duaag.android.profile_builder.data.repository.ProfileOptionsRepository
import com.duaag.android.profile_builder.models.ProfileQuestionType
import javax.inject.Inject

/**
 * Use case for preloading adjacent questions to improve performance.
 */
class PreloadAdjacentQuestionsUseCase @Inject constructor(
    private val repository: ProfileOptionsRepository
) {
    /**
     * Preload options for questions adjacent to the current one.
     */
    suspend operator fun invoke(
        currentType: ProfileQuestionType,
        getSelections: (ProfileQuestionType) -> Set<Int>
    ) {
        repository.preloadAdjacentQuestions(currentType, getSelections)
    }
}
