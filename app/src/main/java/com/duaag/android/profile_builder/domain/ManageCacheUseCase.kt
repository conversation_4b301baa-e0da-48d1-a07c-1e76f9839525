package com.duaag.android.profile_builder.domain

import com.duaag.android.profile_builder.data.repository.ProfileOptionsRepository
import com.duaag.android.profile_builder.models.ProfileQuestionType
import javax.inject.Inject

/**
 * Use case for managing cache operations.
 */
class ManageCacheUseCase @Inject constructor(
    private val repository: ProfileOptionsRepository
) {
    /**
     * Update the current position for better cache management.
     */
    suspend fun updateCurrentPosition(position: Int) {
        repository.updateCurrentPosition(position)
    }
    
    /**
     * Clear the cache.
     */
    suspend fun clearCache() {
        repository.clearCache()
    }
    
    /**
     * Release resources.
     */
    suspend fun release() {
        repository.release()
    }
    
    /**
     * Handle low memory situations.
     */
    suspend fun handleLowMemory(currentQuestionType: ProfileQuestionType?) {
        repository.handleLowMemory(currentQuestionType)
    }
}
