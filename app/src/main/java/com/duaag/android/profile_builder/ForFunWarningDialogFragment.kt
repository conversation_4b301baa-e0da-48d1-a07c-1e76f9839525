package com.duaag.android.profile_builder

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.databinding.DialogForFunWarningBinding
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.setOnSingleClickListener

class ForFunWarningDialogFragment : DialogFragment() {
    private var _binding: DialogForFunWarningBinding? = null
    private val binding get() = _binding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = DialogForFunWarningBinding.inflate(inflater, container, false)
        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        val gender = arguments?.getString(ARG_GENDER) ?: GenderType.MAN.value
        
        // Set illustration based on gender
        val illustrationResId = if (gender == GenderType.WOMAN.value) {
            R.drawable.looking_for_stop_f_illustration
        } else {
            R.drawable.looking_for_stop_m_illustration
        }
        
        binding?.illustration?.setImageResource(illustrationResId)
        
        binding?.btnOk?.setOnSingleClickListener {
            dismissAllowingStateLoss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        const val TAG = "ForFunWarningDialog"
        private const val ARG_GENDER = "arg_gender"
        
        fun newInstance(gender: String): ForFunWarningDialogFragment {
            val fragment = ForFunWarningDialogFragment()
            val args = Bundle()
            args.putString(ARG_GENDER, gender)
            fragment.arguments = args
            return fragment
        }
    }
}