package com.duaag.android.profile_builder.models

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.duaag.android.R
import com.duaag.android.utils.GenderType

/**
 * Handles UI resources for profile questions.
 * This class centralizes all UI-related resources for profile questions
 * and provides a clean interface for accessing them.
 */
object ProfileQuestionResources {
    @StringRes
    fun getQuestionTitle(questionType: ProfileQuestionType): Int {
        return when (questionType) {
            ProfileQuestionType.BE_YOURSELF -> R.string.welcome
            ProfileQuestionType.ALL_READY -> R.string.welcome
            ProfileQuestionType.LANGUAGES -> R.string.onboarding_profilebuilder_language_heading
            ProfileQuestionType.CHILDREN_WANT -> R.string.onboarding_profilebuilder_wantchildren_heading
            ProfileQuestionType.CHILDREN_HAVE -> R.string.onboarding_profilebuilder_havechildren_heading
            ProfileQuestionType.SMOKING -> R.string.onboarding_profilebuilder_smoke_heading
            ProfileQuestionType.RELIGION -> R.string.onboarding_profilebuilder_religion_heading
            ProfileQuestionType.LOOKING_FOR -> R.string.onboarding_profilebuilder_lookingfor_heading
        }
    }

    /**
     * Returns the appropriate illustration resource for a given question type and user gender.
     *
     * @param questionType The type of profile question being displayed
     * @param userGender The gender of the user from [GenderType]
     * @return The drawable resource ID for the appropriate gender-specific illustration
     */
    @DrawableRes
    fun getIllustrationForQuestionType(questionType: ProfileQuestionType, userGender: String): Int {
        val isWoman = userGender == GenderType.WOMAN.value
        
        return when (questionType) {
            ProfileQuestionType.BE_YOURSELF -> if (isWoman) R.drawable.be_yourself_f_illustration else R.drawable.be_yourself_m_illustration
            ProfileQuestionType.ALL_READY -> if (isWoman) R.drawable.be_yourself_f_illustration else R.drawable.be_yourself_m_illustration
            ProfileQuestionType.CHILDREN_HAVE -> if (isWoman) R.drawable.have_children_f_illustration else R.drawable.have_children_m_illustration
            ProfileQuestionType.CHILDREN_WANT -> if (isWoman) R.drawable.want_children_f_illustration else R.drawable.want_children_m_illustration
            ProfileQuestionType.SMOKING -> if (isWoman) R.drawable.smoke_f_illustration else R.drawable.smoke_m_illustration
            ProfileQuestionType.RELIGION -> if (isWoman) R.drawable.religion_f_illustration else R.drawable.religion_m_illustration
            ProfileQuestionType.LOOKING_FOR -> if (isWoman) R.drawable.looking_for_f_illustration else R.drawable.looking_for_m_illustration
            ProfileQuestionType.LANGUAGES -> if (isWoman) R.drawable.languages_f_illustration else R.drawable.languages_m_illustration
        }
    }
}