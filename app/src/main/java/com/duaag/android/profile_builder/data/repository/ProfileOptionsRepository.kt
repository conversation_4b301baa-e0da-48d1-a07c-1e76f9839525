package com.duaag.android.profile_builder.data.repository

import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for managing profile options.
 * Abstracts the data operations from the ViewModel.
 */
interface ProfileOptionsRepository {
    /**
     * Gets options for a specific question type.
     * @param questionType The profile question type
     * @param forceRefresh Whether to force a refresh from data source
     * @return Flow of profile tag options for the question
     */
    fun getOptionsForQuestion(
        questionType: ProfileQuestionType,
        forceRefresh: Boolean = false
    ): Flow<List<ProfileTagOptionModel>?>

    /**
     * Updates the selection state of a single option.
     * @param questionType The profile question type
     * @param tagId The ID of the tag to update
     * @param isSelected The new selection state
     */
    suspend fun updateItemSelection(
        questionType: ProfileQuestionType,
        tagId: Int,
        isSelected: Boolean
    )

    /**
     * Updates the selection state of multiple options.
     * @param questionType The profile question type
     * @param selections Set of selected tag IDs
     */
    suspend fun updateMultipleSelections(
        questionType: ProfileQuestionType,
        selections: Set<Int>
    )

    /**
     * Updates the current position for optimizing cache management.
     * @param position The current position in the question flow
     */
    suspend fun updateCurrentPosition(position: Int)

    /**
     * Preloads adjacent questions for better performance.
     * @param currentType The current question type
     * @param getSelections Function to get current selections for a question type
     */
    suspend fun preloadAdjacentQuestions(
        currentType: ProfileQuestionType,
        getSelections: (ProfileQuestionType) -> Set<Int>
    )

    /**
     * Clears the cache completely.
     */
    suspend fun clearCache()

    /**
     * Release resources when they are no longer needed.
     */
    suspend fun release()

    /**
     * Handles low memory situations.
     * @param currentQuestionType The question type currently being viewed
     */
    suspend fun handleLowMemory(currentQuestionType: ProfileQuestionType?)

    /**
     * Sets all available options from backend response.
     * @param options Map of profile question types to their corresponding options
     */
    fun setAllAvailableOptions(options: Map<ProfileQuestionType, List<ProfileTagOptionModel.Item>>)
}