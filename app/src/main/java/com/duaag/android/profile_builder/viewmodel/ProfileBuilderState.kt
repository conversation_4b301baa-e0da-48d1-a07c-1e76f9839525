package com.duaag.android.profile_builder.viewmodel

import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel

data class ProfileBuilderState(
    val isLoading: Boolean = false,
    val currentStepIndex: Int = ProfileBuilderViewModel.BE_YOURSELF_SCREEN_INDEX,
    val selections: Map<ProfileQuestionType, Set<Int>> = emptyMap(),
    val currentQuestionType: ProfileQuestionType? = null,
    val error: String? = null,
    val isSubmitting: Boolean = false,
    val isComplete: Boolean = false,
    val progressValue: Int = 0,
    val currentQuestionOptions: List<ProfileTagOptionModel>? = null,
    val allQuestionOptions: Map<ProfileQuestionType, List<ProfileTagOptionModel>> = emptyMap()
) {
    fun isLandingScreen(): Boolean = currentStepIndex == ProfileBuilderViewModel.BE_YOURSELF_SCREEN_INDEX

    fun isAllReadyToSubmitScreen(): Boolean {
        return currentStepIndex ==  ProfileQuestionType.orderedQuestionTypes.size + 1
    }
}