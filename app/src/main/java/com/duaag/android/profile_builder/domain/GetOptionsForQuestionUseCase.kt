package com.duaag.android.profile_builder.domain

import com.duaag.android.profile_builder.data.repository.ProfileOptionsRepository
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for retrieving options for a specific profile question.
 */
class GetOptionsForQuestionUseCase @Inject constructor(
    private val repository: ProfileOptionsRepository
) {
    /**
     * Get options for a specific question type.
     */
    operator fun invoke(
        questionType: ProfileQuestionType,
        forceRefresh: Boolean = false
    ): Flow<List<ProfileTagOptionModel>?> {
        return repository.getOptionsForQuestion(questionType, forceRefresh)
    }
}
