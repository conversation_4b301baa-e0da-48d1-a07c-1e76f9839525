package com.duaag.android.profile_builder.data.source

import com.duaag.android.di.ActivityScope
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import javax.inject.Inject

/**
 * Remote data source for profile options. Handles retrieving options from backend.
 * In this implementation, it works with a cached set of all available options.
 */
@ActivityScope
class ProfileOptionsRemoteDataSource @Inject constructor() {
    
    private var allAvailableOptions: Map<ProfileQuestionType, List<ProfileTagOptionModel.Item>> = emptyMap()
    
    /**
     * Sets all available options from backend response.
     * Called after tags are loaded from the backend.
     */
    fun setAllAvailableOptions(options: Map<ProfileQuestionType, List<ProfileTagOptionModel.Item>>) {
        allAvailableOptions = options
    }
    
    /**
     * Gets options for a specific question type.
     */
    fun getOptionsForQuestion(questionType: ProfileQuestionType): List<ProfileTagOptionModel>? {
        return allAvailableOptions[questionType]
    }

    /**
     * Clears all stored options.
     */
    fun clear() {
        allAvailableOptions = emptyMap()
    }
}
