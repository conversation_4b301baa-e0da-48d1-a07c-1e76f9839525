package com.duaag.android.profile_builder.models


sealed class ProfileTagOptionModel {
    abstract val id : Int?
    data class Item(
        val tagId: Int,
        val tagTypeId: Int,
        val name: String,
        val isSelected: Boolean = false,
        override val id: Int = tagId,
        val limit: Int

    ) : ProfileTagOptionModel()


    data class Loading(override val id: Int = Int.MIN_VALUE) : ProfileTagOptionModel()
} 