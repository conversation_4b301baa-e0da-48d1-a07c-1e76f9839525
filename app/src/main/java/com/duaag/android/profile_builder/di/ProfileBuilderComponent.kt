package com.duaag.android.profile_builder.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.profile_builder.ProfileBuilderActivity
import dagger.Subcomponent

@ActivityScope
@Subcomponent(modules = [ProfileBuilderModule::class])
interface ProfileBuilderComponent {

    @Subcomponent.Factory
    interface Factory {
        fun create(): ProfileBuilderComponent
    }

    // Inject methods for activities/fragments that use this subcomponent
    fun inject(activity: ProfileBuilderActivity)

} 