package com.duaag.android.profile_builder.data.repository

import com.duaag.android.di.ActivityScope
import com.duaag.android.profile_builder.data.source.ProfileOptionsLocalDataSource
import com.duaag.android.profile_builder.data.source.ProfileOptionsRemoteDataSource
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber
import javax.inject.Inject

/**
 * Implementation of [ProfileOptionsRepository] that manages profile options data.
 *
 * This implementation follows a cache-first strategy, fetching data from local cache
 * when available and falling back to remote sources when necessary. It also handles
 * preloading adjacent questions to improve user experience.
 *
 * @property localDataSource Source for locally cached profile options data
 * @property remoteDataSource Source for remote profile options data
 */
@ActivityScope
class ProfileOptionsRepositoryImpl @Inject constructor(
    private val localDataSource: ProfileOptionsLocalDataSource,
    private val remoteDataSource: ProfileOptionsRemoteDataSource
) : ProfileOptionsRepository {

    companion object {
        private const val TAG = "OptionsRepository"
    }

    /**
     * Retrieves options for a specific profile question.
     *
     * @param questionType The type of profile question to get options for
     * @param forceRefresh If true, bypasses cache and fetches fresh data from remote source
     * @return Flow emitting list of profile tag options or null if not available
     */
    override fun getOptionsForQuestion(
        questionType: ProfileQuestionType,
        forceRefresh: Boolean
    ): Flow<List<ProfileTagOptionModel>?> = flow {
        if (!forceRefresh) {
            val cachedOptions = localDataSource.getOptionsFromCache(questionType)
            if (cachedOptions != null) {
                emit(cachedOptions)
                return@flow
            }
        }

        val options = remoteDataSource.getOptionsForQuestion(questionType)
        if (options != null) {
            localDataSource.putToCache(questionType, options)
            emit(options)
        } else {
            emit(null)
        }
    }

    /**
     * Updates the selection state of a single profile option item in cache.
     *
     * @param questionType The profile question type
     * @param tagId The ID of the tag to update
     * @param isSelected The new selection state
     */
    override suspend fun updateItemSelection(
        questionType: ProfileQuestionType,
        tagId: Int,
        isSelected: Boolean
    ) {
        localDataSource.updateItemInCache(questionType, tagId, isSelected)
    }

    /**
     * Updates multiple tag selections for a question type.
     *
     * @param questionType The profile question type
     * @param selections Set of tag IDs that are selected
     */
    override suspend fun updateMultipleSelections(
        questionType: ProfileQuestionType,
        selections: Set<Int>
    ) {
        localDataSource.updateItemsInCache(questionType, selections)
    }

    /**
     * Updates the current position in the profile builder flow.
     *
     * @param position The current position index
     */
    override suspend fun updateCurrentPosition(position: Int) {
        localDataSource.updateCurrentPosition(position)
    }

    /**
     * Preloads options for questions adjacent to the current question.
     *
     * This helps improve UX by fetching data for likely next questions before the user navigates to them.
     *
     * @param currentType The current question type
     * @param getSelections Function to get selected tags for a question type
     */
    override suspend fun preloadAdjacentQuestions(
        currentType: ProfileQuestionType,
        getSelections: (ProfileQuestionType) -> Set<Int>
    ) {
        try {
            val orderedTypes = ProfileQuestionType.orderedQuestionTypes
            val currentIndex = orderedTypes.indexOf(currentType)

            if (currentIndex == -1) {
                Timber.tag(TAG).w("Current type $currentType not found in ordered types")
                return
            }

            // Preload previous
            if (currentIndex > 0) {
                val prevType = orderedTypes[currentIndex - 1]
                val selections = getSelections(prevType)
                val options = remoteDataSource.getOptionsForQuestion(prevType)
                if (options != null) {
                    localDataSource.updateItemsInCache(prevType, selections)
                }
            }

            // Preload next
            if (currentIndex < orderedTypes.size - 1) {
                val nextType = orderedTypes[currentIndex + 1]
                val selections = getSelections(nextType)
                val options = remoteDataSource.getOptionsForQuestion(nextType)
                if (options != null) {
                    localDataSource.updateItemsInCache(nextType, selections)
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error preloading adjacent questions")
        }
    }

    /**
     * Clears all cached options data.
     */
    override suspend fun clearCache() {
        localDataSource.clearCache()
    }

    /**
     * Releases resources held by the repository.
     */
    override suspend fun release() {
        localDataSource.release()
    }

    /**
     * Handles low memory situations by clearing all cache except for the current question data.
     *
     * @param currentQuestionType The currently active question type, if any
     */
    override suspend fun handleLowMemory(currentQuestionType: ProfileQuestionType?) {
        val currentOptions = currentQuestionType?.let {
            remoteDataSource.getOptionsForQuestion(it)
        }

        localDataSource.clearCache()

        if (currentQuestionType != null && currentOptions != null) {
            localDataSource.putToCache(currentQuestionType, currentOptions)
        }
    }

    /**
     * Sets all available options from backend response.
     * Updates the remote data source with all options and caches them locally.
     *
     * @param options Map of profile question types to their corresponding options
     */
    override fun setAllAvailableOptions(options: Map<ProfileQuestionType, List<ProfileTagOptionModel.Item>>) {
        remoteDataSource.setAllAvailableOptions(options)
        
//        // Store options in local cache
//        options.forEach { (questionType, itemList) ->
//            localDataSource.putToCache(questionType, itemList)
//        }
    }
}