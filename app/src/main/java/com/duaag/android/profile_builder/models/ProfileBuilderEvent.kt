package com.duaag.android.profile_builder.models

sealed class ProfileBuilderEvent {
    data class ShowError(val message: String) : ProfileBuilderEvent()
    data class NavigateToPage(val position: Int, val smoothScroll: Boolean = true) : ProfileBuilderEvent()
    object Complete : ProfileBuilderEvent()
    object Success : ProfileBuilderEvent()
    object TagsLoaded : ProfileBuilderEvent()
    object ShowFunWarning : ProfileBuilderEvent()
}