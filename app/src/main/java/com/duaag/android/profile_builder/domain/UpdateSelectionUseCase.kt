package com.duaag.android.profile_builder.domain

import com.duaag.android.profile_builder.data.repository.ProfileOptionsRepository
import com.duaag.android.profile_builder.models.ProfileQuestionType
import javax.inject.Inject

/**
 * Use case for updating selection state of options.
 */
class UpdateSelectionUseCase @Inject constructor(
    private val repository: ProfileOptionsRepository
) {

    /**
     * Update multiple tags' selection states.
     */
    suspend fun updateMultipleSelections(
        questionType: ProfileQuestionType,
        selections: Set<Int>
    ) {
        repository.updateMultipleSelections(questionType, selections)
    }
}
