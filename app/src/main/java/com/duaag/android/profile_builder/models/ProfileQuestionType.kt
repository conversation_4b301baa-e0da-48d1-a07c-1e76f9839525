package com.duaag.android.profile_builder.models

import com.duaag.android.signup.signup_persist.domain.models.SignUpPersistStepsEnum

enum class ProfileQuestionType {
    CHILDREN_HAVE,
    CHILDREN_WANT,
    SMOKING,
    LANGUAGES,
    RELIGION,
    LOOKING_FOR,
    BE_YOURSELF,
    ALL_READY;
    // INTERESTS;

    companion object {
        val orderedQuestionTypes: List<ProfileQuestionType> = entries
            .filter { it != BE_YOURSELF && it != ALL_READY}
            .sortedBy { it.ordinal }
    }

}

/**
 * Extension function to check if a question type allows multiple selections.
 */
private fun ProfileQuestionType.isMultiSelect(): <PERSON><PERSON><PERSON> {
    return this == ProfileQuestionType.LANGUAGES
    // Add other multi-select question types here
    false
}

// Mappings from QuestionType to backend TagTypeID
 val questionTypeToTagTypeId = mapOf(
    ProfileQuestionType.CHILDREN_HAVE to TagTypeIds.CHIL<PERSON>EN_HAVE,
    ProfileQuestionType.CHILDREN_WANT to TagTypeIds.CHILDREN_WANT,
    ProfileQuestionType.SMOKING to TagTypeIds.SMOKING,
    ProfileQuestionType.LANGUAGES to TagTypeIds.LANGUAGES,
    ProfileQuestionType.RELIGION to TagTypeIds.RELIGION,
    ProfileQuestionType.LOOKING_FOR to TagTypeIds.LOOKING_FOR
).filterValues { it > 0 }

 val questionTypeToSignUpPersistStepsEnum = mapOf(
    ProfileQuestionType.CHILDREN_HAVE to SignUpPersistStepsEnum.PROFILE_BUILDER_CHILDREN_HAVE,
    ProfileQuestionType.CHILDREN_WANT to SignUpPersistStepsEnum.PROFILE_BUILDER_CHILDREN_WANT,
    ProfileQuestionType.SMOKING to SignUpPersistStepsEnum.PROFILE_BUILDER_SMOKING,
    ProfileQuestionType.LANGUAGES to SignUpPersistStepsEnum.PROFILE_BUILDER_LANGUAGES,
    ProfileQuestionType.RELIGION to SignUpPersistStepsEnum.PROFILE_BUILDER_RELIGION,
    ProfileQuestionType.LOOKING_FOR to SignUpPersistStepsEnum.PROFILE_BUILDER_LOOKING_FOR
)