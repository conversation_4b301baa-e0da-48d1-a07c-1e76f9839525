package com.duaag.android.profile_builder.data.source

import com.duaag.android.di.ActivityScope
import com.duaag.android.profile_builder.cache.ProfileOptionsCacheManager
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import javax.inject.Inject

/**
 * Local data source for profile options, backed by in-memory cache.
 */
@ActivityScope
class ProfileOptionsLocalDataSource @Inject constructor(
    private val cacheManager: ProfileOptionsCacheManager
) {
    /**
     * Gets options from cache for a question type.
     */
    fun getOptionsFromCache(questionType: ProfileQuestionType?): List<ProfileTagOptionModel>? {
        return cacheManager.getFromCache(questionType)
    }

    /**
     * Stores options in the cache.
     */
    fun putToCache(questionType: ProfileQuestionType?, value: List<ProfileTagOptionModel>) {
        cacheManager.putToCache(questionType, value)
    }

    /**
     * Updates the current position.
     */
    fun updateCurrentPosition(position: Int) {
        cacheManager.updateCurrentPosition(position)
    }

    /**
     * Updates a single item's selection state.
     */
    fun updateItemInCache(
        questionType: ProfileQuestionType?,
        tagId: Int,
        isSelected: Boolean
    ) {
        cacheManager.updateItemInCache(questionType, tagId, isSelected)
    }

    /**
     * Updates multiple items' selection states.
     */
    fun updateItemsInCache(
        questionType: ProfileQuestionType?,
        selections: Set<Int>
    ) {
        cacheManager.updateItemsInCache(questionType, selections)
    }

    /**
     * Clears the cache.
     */
    fun clearCache() {
        cacheManager.clearCache()
    }

    /**
     * Releases resources.
     */
    fun release() {
        cacheManager.release()
    }
}
