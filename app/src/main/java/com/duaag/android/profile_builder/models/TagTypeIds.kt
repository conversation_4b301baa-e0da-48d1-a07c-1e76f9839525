package com.duaag.android.profile_builder.models

// Backend Tag Type IDs
object TagTypeIds {
    const val CHILDREN_HAVE = 8
    const val CHILDREN_WANT = 9
    const val SMOKING = 7
    const val LANGUAGES = 2
    const val RELIGION = 4
    const val LOOKING_FOR = 6
    // Add IDs for INTERESTS etc.
    // const val INTERESTS = ...
}

const val FOR_FUN_TAG_ITEM_ID = 9999  // Custom ID for "Fun" option in LOOKING_FOR question
