package com.duaag.android.profile_builder.cache

import androidx.collection.SparseArrayCompat
import com.duaag.android.di.ActivityScope
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileQuestionType.Companion.orderedQuestionTypes
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

/**
 * Manages a cache of profile tag options for the profile builder.
 * 
 * This class provides a caching mechanism for profile options to improve
 * performance and user experience when navigating through profile questions.
 * It implements an LRU-like cache strategy to keep memory usage controlled.
 */
@ActivityScope
class ProfileOptionsCacheManager @Inject constructor() {
    
    /**
     * Cache of profile tag options, keyed by question type.
     */
    private val optionsCache = SparseArrayCompat<List<ProfileTagOptionModel>>()
    
    /**
     * Flag indicating whether the cache has been cleaned up.
     */
    private var isCleanedUp = false
    
    /**
     * Maximum number of items to keep in the cache.
     */
    private val maxCacheSize = 3
    
    /**
     * Current position being viewed in the profile builder.
     */
    private var currentPosition = 0
    
    companion object {
        private const val TAG = "OptionsCache"
    }
    
    /**
     * Gets cached options for a question type.
     * Returns null if not cached or if cache is cleaned up.
     * 
     * @param questionType The question type to retrieve options for
     * @return Cached options for the question type, or null
     */
    fun getFromCache(questionType: ProfileQuestionType?): List<ProfileTagOptionModel>? = synchronized(optionsCache) {
        if (checkCleanedUpState("get from cache")) return null
        return questionType?.let { optionsCache[getCacheKeyForType(it)] }
    }
    
    /**
     * Stores options in the cache for a question type.
     * No-op if cache is cleaned up.
     * 
     * @param questionType The question type to store options for
     * @param value The options to store
     */
    fun putToCache(questionType: ProfileQuestionType?, value: List<ProfileTagOptionModel>): Unit = synchronized(optionsCache) {
        if (checkCleanedUpState("put to cache")) return
        questionType?.let { optionsCache.put(getCacheKeyForType(it), value) }
    }
    
    /**
     * Updates the current position being viewed in the profile builder.
     * Triggers cache trimming if needed.
     * 
     * @param position The new current position
     */
    fun updateCurrentPosition(position: Int): Unit = synchronized(optionsCache) {
        if (checkCleanedUpState("update position")) return
        
        currentPosition = position
        trimCacheIfNeeded()
    }
    
    /**
     * Updates a single item's selection state in the cache.
     * No-op if item doesn't exist or cache is cleaned up.
     * 
     * @param questionType The question type containing the item
     * @param tagId The ID of the tag to update
     * @param isSelected The new selection state
     */
    fun updateItemInCache(
        questionType: ProfileQuestionType?,
        tagId: Int,
        isSelected: Boolean
    ): Unit = synchronized(optionsCache) {
        if (checkCleanedUpState("update item")) return

        questionType?.let {
            val cacheKey = getCacheKeyForType(it)
            val currentOptions = optionsCache.get(cacheKey) ?: return@synchronized
            val updatedOptions = updateSingleOptionSelection(currentOptions, tagId, isSelected)
            optionsCache.put(cacheKey, updatedOptions)
        }
    }
    
    /**
     * Updates multiple items' selection states in the cache.
     * No-op if cache is cleaned up.
     * 
     * @param questionType The question type containing the items
     * @param selections Set of selected tag IDs
     */
    fun updateItemsInCache(
        questionType: ProfileQuestionType?,
        selections: Set<Int>
    ): Unit = synchronized(optionsCache) {
        if (checkCleanedUpState("update items")) return

        questionType?.let {
            val cacheKey = getCacheKeyForType(it)
            val currentOptions = optionsCache.get(cacheKey) ?: return@synchronized
            val updatedOptions = updateMultipleOptionsSelection(currentOptions, selections)
            optionsCache.put(cacheKey, updatedOptions)
        }
    }
    
    /**
     * Clears the cache and resets the cleaned up state.
     * Used when initializing with a new user or loading new tags.
     */
    fun clearCache(): Unit = synchronized(optionsCache) {
        Timber.tag(TAG).i("Clearing options cache")
        optionsCache.clear()
        isCleanedUp = false
    }
    
    /**
     * Releases all resources and marks the cache as cleaned up.
     * Should be called when the host activity is being destroyed.
     */
    fun release(): Unit = synchronized(optionsCache) {
        Timber.tag(TAG).i("Releasing all cache resources")
        optionsCache.clear()
        isCleanedUp = true
    }
    
    
    /**
     * Checks if the cache has been cleaned up and logs an appropriate message.
     * 
     * @param operation Description of the attempted operation
     * @return True if cache is cleaned up, false otherwise
     */
    private fun checkCleanedUpState(operation: String): Boolean {
        if (isCleanedUp) {
            Timber.tag(TAG).e("Attempted to $operation in cleaned up cache")
            return true
        }
        return false
    }
    
    /**
     * Gets a unique cache key for a question type.
     * 
     * @param questionType The question type to get a key for
     * @return Integer key for storage in SparseArrayCompat
     */
    private fun getCacheKeyForType(questionType: ProfileQuestionType): Int {
        return orderedQuestionTypes.indexOf(questionType)
    }
    
    /**
     * Maps a ViewPager position to its corresponding question type.
     * Accounts for the BeYourself screen at position 0.
     * 
     * @param position The position in the ViewPager
     * @return The question type at that position, or null for BeYourself screen
     */
    private fun getQuestionTypeForPosition(position: Int): ProfileQuestionType? {
        if (position <= 0) {
            return null
        }
        
        val index = position - 1 // Adjust for Be Yourself screen
        return orderedQuestionTypes.getOrNull(index)
    }
    
    /**
     * Updates the selection state of a single option in a list.
     * 
     * @param options The list of options to update
     * @param tagId The ID of the tag to update
     * @param isSelected The new selection state
     * @return Updated list with the selection state changed
     */
    private fun updateSingleOptionSelection(
        options: List<ProfileTagOptionModel>,
        tagId: Int,
        isSelected: Boolean
    ): List<ProfileTagOptionModel> {
        return options.map { option ->
            if (option is ProfileTagOptionModel.Item && option.tagId == tagId) {
                option.copy(isSelected = isSelected)
            } else {
                option
            }
        }
    }
    
    /**
     * Updates the selection state of multiple options based on a set of selected IDs.
     * 
     * @param options The list of options to update
     * @param selections Set of selected tag IDs
     * @return Updated list with selection states changed
     */
    private fun updateMultipleOptionsSelection(
        options: List<ProfileTagOptionModel>,
        selections: Set<Int>
    ): List<ProfileTagOptionModel> {
        return options.map { option ->
            if (option is ProfileTagOptionModel.Item) {
                option.copy(isSelected = option.tagId in selections)
            } else {
                option
            }
        }
    }
    
    
    
    
    /**
     * Trims the cache when it exceeds the maximum size.
     * Keeps only the most important items (current and adjacent).
     */
    private fun trimCacheIfNeeded(): Unit = synchronized(optionsCache) {
        if (optionsCache.size() <= maxCacheSize) {
            return
        }
        
        Timber.tag(TAG).d("Trimming cache from size ${optionsCache.size()}")
        
        val keysToKeep = determineKeysToKeep()
        val keysToRemove = findKeysToRemove(keysToKeep)
        
        removeKeysFromCache(keysToRemove)
    }
    
    /**
     * Determines which keys should be kept during cache trimming.
     * Keeps the current position and its adjacent positions.
     * 
     * @return Set of cache keys to keep
     */
    private fun determineKeysToKeep(): Set<Int> {
        val keysToKeep = mutableSetOf<Int>()
        
        val currentType = getQuestionTypeForPosition(currentPosition)
        val prevType = if (currentPosition > 0) getQuestionTypeForPosition(currentPosition - 1) else null
        val nextType = getQuestionTypeForPosition(currentPosition + 1)
        
        currentType?.let { keysToKeep.add(getCacheKeyForType(it)) }
        prevType?.let { keysToKeep.add(getCacheKeyForType(it)) }
        nextType?.let { keysToKeep.add(getCacheKeyForType(it)) }
        
        return keysToKeep
    }
    
    /**
     * Identifies which cache keys should be removed during trimming.
     * 
     * @param keysToKeep Set of keys that should be preserved
     * @return List of keys to remove from the cache
     */
    private fun findKeysToRemove(keysToKeep: Set<Int>): List<Int> {
        val keysToRemove = mutableListOf<Int>()
        
        for (i in 0 until optionsCache.size()) {
            val key = optionsCache.keyAt(i)
            if (key !in keysToKeep && key >= 0) {
                keysToRemove.add(key)
            }
        }
        
        return keysToRemove
    }
    
    /**
     * Removes the specified keys from the cache.
     * 
     * @param keysToRemove List of keys to remove
     */
    private fun removeKeysFromCache(keysToRemove: List<Int>) {
        keysToRemove.forEach {
            Timber.tag(TAG).v("Removing cache key $it")
            optionsCache.remove(it)
        }
    }
}