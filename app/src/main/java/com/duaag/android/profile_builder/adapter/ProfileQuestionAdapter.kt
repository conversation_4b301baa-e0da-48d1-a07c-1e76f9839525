package com.duaag.android.profile_builder.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.LayoutAllReadyItemBinding
import com.duaag.android.databinding.LayoutBeYourselfItemBinding
import com.duaag.android.databinding.LayoutProfileQuestionBinding
import com.duaag.android.profile_builder.models.ProfileQuestionResources
import com.duaag.android.profile_builder.models.ProfileQuestionType
import com.duaag.android.profile_builder.models.ProfileTagOptionModel
import com.duaag.android.profile_builder.viewmodel.ProfileBuilderViewModel
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.setOnSingleClickListener
import timber.log.Timber

/**
 * Adapter for managing and displaying profile questions in a RecyclerView.
 * Supports three types of items: a "Be Yourself" landing screen, profile questions, and an "All Ready" screen.
 *
 * @param onTagSelected Callback invoked when a tag is selected.
 * @param onStartQuestionsClicked Callback invoked when the "Start Questions" button is clicked.
 * @param onProfileReadyClicked Callback invoked when the  button is clicked.
 */
class ProfileQuestionAdapter(
    private val onTagSelected: (ProfileTagOptionModel.Item) -> Unit,
    private val onStartQuestionsClicked: () -> Unit,
    private val onProfileReadyClicked: () -> Unit,
) : ListAdapter<ProfileQuestionAdapter.ProfileBuilderItem, RecyclerView.ViewHolder>(ProfileBuilderItemDiffCallback()) {

    companion object {
        private const val VIEW_TYPE_BE_YOURSELF = ProfileBuilderViewModel.BE_YOURSELF_SCREEN_INDEX
        private const val VIEW_TYPE_QUESTION = 1
        private const val VIEW_TYPE_ALL_READY = 2

        // Constants for recycled view pool setup
        private const val SINGLE_SELECT_POOL_SIZE = 40 // Increased for larger question sets
        private const val MULTI_SELECT_POOL_SIZE = 40 // Increased for larger question sets
        private const val OPTIONS_CHANGED_PAYLOAD = "options_changed"
        private const val ALL_READY_STATE_CHANGED_PAYLOAD = "all_ready_state_changed"
    }

    // Shared RecyclerView pools for different types of tag selections
    private val singleSelectPool = RecyclerView.RecycledViewPool().apply {
        setMaxRecycledViews(TagOptionsAdapter.VIEW_TYPE_SINGLE_SELECT, SINGLE_SELECT_POOL_SIZE)
    }

    private val multiSelectPool = RecyclerView.RecycledViewPool().apply {
        setMaxRecycledViews(TagOptionsAdapter.VIEW_TYPE_MULTI_SELECT, MULTI_SELECT_POOL_SIZE)
    }

    /**
     * Represents the different types of items in the adapter.
     */
    sealed class ProfileBuilderItem {
        abstract val id: Int

        data class BeYourselfLandingItem(
            val userGender: String?
        ) : ProfileBuilderItem() {
            override val id: Int
                get() = Int.MIN_VALUE
        }

        data class AllReadyItem(
            override val id: Int = Int.MAX_VALUE,
            val userGender: String?,
            val userName: String?,
            val isSubmitting: Boolean = false,
            val isComplete: Boolean = false
        ) : ProfileBuilderItem()

        /**
         * Represents a profile question item.
         *
         * @param questionType The type of the question.
         * @param questions The list of tag options for the question.
         * @param id Unique identifier for the question.
         */
        data class Question(
            val questionType: ProfileQuestionType,
            val questions: List<ProfileTagOptionModel>?,
            val userGender: String?,
            override val id: Int = questionType.ordinal
        ) : ProfileBuilderItem()
    }

    /**
     * Determines the view type for a given position.
     *
     * @param position The position of the item in the list.
     * @return The view type constant.
     */
    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is ProfileBuilderItem.BeYourselfLandingItem -> VIEW_TYPE_BE_YOURSELF
            is ProfileBuilderItem.Question -> VIEW_TYPE_QUESTION
            is ProfileBuilderItem.AllReadyItem -> VIEW_TYPE_ALL_READY
        }
    }

    /**
     * Creates a new ViewHolder for the given view type.
     *
     * @param parent The parent ViewGroup.
     * @param viewType The type of the view.
     * @return A new ViewHolder instance.
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            VIEW_TYPE_BE_YOURSELF -> {
                val binding = LayoutBeYourselfItemBinding.inflate(
                    inflater, parent, false)
                BeYourselfViewHolder(binding, onStartQuestionsClicked)
            }
            VIEW_TYPE_QUESTION -> {
                val binding = LayoutProfileQuestionBinding.inflate(
                    inflater, parent, false)
                QuestionViewHolder(binding, onTagSelected)
            }
            VIEW_TYPE_ALL_READY -> {
                val binding = LayoutAllReadyItemBinding.inflate(
                    inflater, parent, false)
                AllReadyViewHolder(binding, onProfileReadyClicked)
            }
            else -> throw IllegalArgumentException("Invalid view type: $viewType")
        }
    }

    /**
     * Binds data to the ViewHolder, with support for partial updates using payloads.
     *
     * @param holder The ViewHolder to bind data to.
     * @param position The position of the item in the list.
     * @param payloads A list of payloads for partial updates.
     */
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.contains(OPTIONS_CHANGED_PAYLOAD) && holder is QuestionViewHolder && getItem(position) is ProfileBuilderItem.Question) {
            val item = getItem(position) as ProfileBuilderItem.Question
            item.questions?.let { holder.updateOptions(it) }
        } else if (payloads.contains(ALL_READY_STATE_CHANGED_PAYLOAD) && holder is AllReadyViewHolder && getItem(position) is ProfileBuilderItem.AllReadyItem) {
            val item = getItem(position) as ProfileBuilderItem.AllReadyItem
            holder.updateState(item)
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    /**
     * Binds data to the ViewHolder.
     *
     * @param holder The ViewHolder to bind data to.
     * @param position The position of the item in the list.
     */
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        try {
            when (val item = getItem(position)) {
                is ProfileBuilderItem.BeYourselfLandingItem -> (holder as BeYourselfViewHolder).bind(item)
                is ProfileBuilderItem.Question -> (holder as QuestionViewHolder).bind(item)
                is ProfileBuilderItem.AllReadyItem -> (holder as AllReadyViewHolder).bind(item)
            }
        } catch (e: Exception) {
            Timber.e(e, "Error binding view holder at position $position: ${e.message}")
        }
    }

    /**
     * Cleans up resources when a ViewHolder is recycled.
     *
     * @param holder The ViewHolder being recycled.
     */
    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        super.onViewRecycled(holder)

        if (holder is QuestionViewHolder) {
            holder.cleanUp()
        }
    }

    /**
     * ViewHolder for the "Be Yourself" landing screen.
     *
     * @param binding The binding for the layout.
     * @param onStartQuestionsClicked Callback invoked when the "Start Questions" button is clicked.
     */
    inner class BeYourselfViewHolder(
        private val binding: LayoutBeYourselfItemBinding,
        private val onStartQuestionsClicked: () -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: ProfileBuilderItem.BeYourselfLandingItem) {
            binding.landingItemImage.setImageResource(
                when (item.userGender) {
                    GenderType.MAN.value-> R.drawable.be_yourself_m_illustration
                    GenderType.WOMAN.value -> R.drawable.be_yourself_f_illustration
                    else -> R.drawable.loading_img
                }
            )
            binding.welcomeItemStartButton.setOnSingleClickListener {
                onStartQuestionsClicked()
            }
        }
    }

    /**
     * ViewHolder for the "All Ready" screen.
     *
     * @param binding The binding for the layout.
     * @param onProfileReadyClicked Callback invoked when the  button is clicked.
     */
    inner class AllReadyViewHolder(
        private val binding: LayoutAllReadyItemBinding,
        private val onProfileReadyClicked: () -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ProfileBuilderItem.AllReadyItem) {

            binding.allReadyItemTitle.text = binding.root.context.getString(R.string.onboarding_allset_heading) + " ${item.userName}"
            binding.allReadyItemButton.text = binding.root.context.getString(R.string.continue_button_reward)
            binding.allReadyItemImage.setImageResource(if(item.userGender == GenderType.MAN.value) R.drawable.all_set_m_illustration else R.drawable.all_set_f_illustration)
            updateState(item)

            binding.allReadyItemButton.setOnSingleClickListener {
                onProfileReadyClicked()
            }
        }
        
        fun updateState(item: ProfileBuilderItem.AllReadyItem) {
            binding.allReadyItemButton.visibility = if(!item.isSubmitting || item.isComplete) View.VISIBLE else View.INVISIBLE
            binding.submitProgressBar.visibility = if (item.isSubmitting && !item.isComplete) {
                View.VISIBLE
            } else View.GONE
        }
    }

    /**
     * ViewHolder for profile question items.
     *
     * @param binding The binding for the layout.
     * @param onTagSelected Callback invoked when a tag is selected.
     */
    inner class QuestionViewHolder(
        private val binding: LayoutProfileQuestionBinding,
        private val onTagSelected: (ProfileTagOptionModel.Item) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        private val tagOptionsAdapter = TagOptionsAdapter(ProfileQuestionType.CHILDREN_HAVE, onTagSelected)

        init {
            binding.optionsRecyclerView.apply {
                layoutManager = LinearLayoutManager(context)
                adapter = tagOptionsAdapter
                // Disable item animations to prevent flickering
                itemAnimator = null
                setRecycledViewPool(
                    if (tagOptionsAdapter.questionType == ProfileQuestionType.LANGUAGES) {
                        multiSelectPool
                    } else {
                        singleSelectPool
                    }
                )
            }
        }

        /**
         * Binds a profile question item to the ViewHolder.
         *
         * @param item The profile question item to bind.
         */
        fun bind(item: ProfileBuilderItem.Question) {
            binding.questionTitle.setText(ProfileQuestionResources.getQuestionTitle(item.questionType))
            binding.questionImage.setImageResource(
                ProfileQuestionResources.getIllustrationForQuestionType(
                    item.questionType,
                    item.userGender ?: GenderType.MAN.value
                )
            )

            tagOptionsAdapter.questionType = item.questionType

            val options = item.questions
            tagOptionsAdapter.submitList(options, null)
            
            // Handle selection count text visibility and text update
            if (item.questionType == ProfileQuestionType.LANGUAGES) {
                binding.questionSelectionCountText.visibility = View.VISIBLE
                updateSelectionCountText(options,(options?.firstOrNull() as? ProfileTagOptionModel.Item)?.limit)
            } else {
                binding.questionSelectionCountText.visibility = View.GONE
            }
        }

        /**
         * Updates the selection count text showing how many options are selected.
         *
         * @param options The list of tag options.
         */
        private fun updateSelectionCountText(options: List<ProfileTagOptionModel>?, limit: Int?) {
            options?.let {
                val selectedCount = it.count { option ->
                    when(option) {
                        is ProfileTagOptionModel.Item -> option.isSelected
                        else -> false
                    }
                }
                binding.questionSelectionCountText.text = binding.root.context.getString(
                    R.string.onboarding_profilebuilder_language_from_full_an, "$selectedCount", "$limit"
                )
            }
        }

        /**
         * Cleans up resources when the ViewHolder is recycled.
         */
        fun cleanUp() {
            tagOptionsAdapter.submitList(emptyList(), null)
        }

        /**
         * Updates the tag options for the question.
         *
         * @param questions The new list of tag options.
         */
        fun updateOptions(questions: List<ProfileTagOptionModel>) {
            // Use the submitList with a callback to ensure the change is applied
            tagOptionsAdapter.submitList(questions) {
                // Force the RecyclerView to redraw if necessary
                binding.optionsRecyclerView.invalidate()
                
                // Update the selection count text for multi-select questions
                if (tagOptionsAdapter.questionType == ProfileQuestionType.LANGUAGES) {
                    updateSelectionCountText(
                        questions,
                        (questions.firstOrNull() as? ProfileTagOptionModel.Item)?.limit
                    )
                }
            }
        }
    }

    /**
     * DiffUtil callback for calculating the differences between two lists of items.
     */
    private class ProfileBuilderItemDiffCallback : DiffUtil.ItemCallback<ProfileBuilderItem>() {
        override fun areItemsTheSame(oldItem: ProfileBuilderItem, newItem: ProfileBuilderItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: ProfileBuilderItem, newItem: ProfileBuilderItem): Boolean {
            return oldItem == newItem
        }

        /**
         * Determines the payload for partial updates.
         *
         * @param oldItem The old item.
         * @param newItem The new item.
         * @return The payload for partial updates, or null if a full rebind is needed.
         */
        override fun getChangePayload(oldItem: ProfileBuilderItem, newItem: ProfileBuilderItem): Any? {
            if (oldItem is ProfileBuilderItem.Question && newItem is ProfileBuilderItem.Question) {
                if (oldItem.questionType == newItem.questionType) {
                    if (oldItem.questions?.size == newItem.questions?.size &&
                        !areContentsTheSame(oldItem, newItem)) {
                        return OPTIONS_CHANGED_PAYLOAD
                    }
                }
            } else if (oldItem is ProfileBuilderItem.AllReadyItem && newItem is ProfileBuilderItem.AllReadyItem) {
                if (oldItem.isSubmitting != newItem.isSubmitting || oldItem.isComplete != newItem.isComplete) {
                    return ALL_READY_STATE_CHANGED_PAYLOAD
                }
            }
            return null
        }
    }
}