package com.duaag.android.profile_builder.di

import androidx.lifecycle.ViewModel
import com.duaag.android.di.ViewModelKey
import com.duaag.android.profile_builder.data.repository.ProfileOptionsRepository
import com.duaag.android.profile_builder.data.repository.ProfileOptionsRepositoryImpl
import com.duaag.android.profile_builder.viewmodel.ProfileBuilderViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class ProfileBuilderModule {

    @Binds
    @IntoMap
    @ViewModelKey(ProfileBuilderViewModel::class)
    abstract fun bindProfileBuilderViewModel(viewModel: ProfileBuilderViewModel): ViewModel

    @Binds
    abstract fun bindProfileOptionsRepository(
        repository: ProfileOptionsRepositoryImpl
    ): ProfileOptionsRepository
}
