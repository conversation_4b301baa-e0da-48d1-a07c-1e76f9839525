package com.duaag.android.onboarding_permissions

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapOnboardingTypeValues
import com.duaag.android.clevertap.ClevertapPremiumTypeValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentNotificationPermissionStepBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.NotificationsSourceValues
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.isPostNotificationsPermissionEnabled
import com.duaag.android.utils.requestPushNotificationPermissionWithLauncher
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.shouldShowLocationPermissionRationale
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import javax.inject.Inject


class NotificationPermissionStepFragment : Fragment() {

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs
    @Inject
    lateinit var duaAccount: DuaAccount
    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    private val signUpViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity } ) { viewModelFactory }

    private var _binding: FragmentNotificationPermissionStepBinding? = null
    private val binding get() = _binding!!


    private val requestPushNotificationPermissionLauncher  =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { granted: Boolean ->
            sendNotificationPopUpEvents()

            if(granted){
                sendNotificationAllowedEvents()
            }

            navigateBasedOnPermissions(requireContext(), findNavController())

//            val action = NotificationPermissionStepFragmentDirections.actionNotificationPermissionStepFragmentToLoveStoriesSignUpFragment()
//            findNavController().navigate(action)
        }


    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateLocale(context)
        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }


    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentNotificationPermissionStepBinding.inflate(inflater)

        binding.continueBtn.setOnSingleClickListener{
            if(requireContext().isPostNotificationsPermissionEnabled()) {
                if(findNavController().currentDestination?.id == R.id.notificationPermissionStepFragment) {
                    navigateBasedOnPermissions(requireContext(), findNavController())
                }
            } else {
                requestPushNotificationPermissionWithLauncher(requestPushNotificationPermissionLauncher)
            }
        }

        return binding.root
    }

    private fun setIllustration() {
        val language = ModifiedLingver.getInstance().getLanguage()
        val gender = signUpViewModel.gender.value

        val drawableResId = when {
            // Albanian language
            language == "sq" && gender == GenderType.MAN -> R.drawable.notification_male_al
            language == "sq" && gender == GenderType.WOMAN -> R.drawable.notification_female_al

            // German language
            language == "de" && gender == GenderType.MAN -> R.drawable.notification_male_de
            language == "de" && gender == GenderType.WOMAN -> R.drawable.notification_female_de

            // Turkish language
            language == "tr" && gender == GenderType.MAN -> R.drawable.notification_male_tr
            language == "tr" && gender == GenderType.WOMAN -> R.drawable.notification_female_tr

            // Italian language
            language == "it" && gender == GenderType.MAN -> R.drawable.notification_male_it
            language == "it" && gender == GenderType.WOMAN -> R.drawable.notification_female_it

            // French language
            language == "fr" && gender == GenderType.MAN -> R.drawable.notification_male_fr
            language == "fr" && gender == GenderType.WOMAN -> R.drawable.notification_female_fr

            // Arabic language
            (language == "ar" || language == "ar_MA") && gender == GenderType.MAN -> R.drawable.notification_male_ar
            (language == "ar" || language == "ar_MA") && gender == GenderType.WOMAN -> R.drawable.notification_female_ar

            // English language (default)
            gender == GenderType.MAN -> R.drawable.notification_male_en
            gender == GenderType.WOMAN -> R.drawable.notification_female_en

            // Fallback if gender is not set
            else -> R.drawable.notification_male_en
        }

        binding.illustration.setImageResource(drawableResId)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setIllustration()
        sendScreenViewEvents()

        signUpViewModel.isCreatingProfile.observe(viewLifecycleOwner) {
            if(it == true) {
                toggleContinueButton(false)
                binding.continueBtn.visibility = View.INVISIBLE
                binding.progressBar.visibility = View.VISIBLE
            } else  {
                toggleContinueButton(true)
                binding.continueBtn.visibility = View.VISIBLE
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun navigateBasedOnPermissions(context: Context, navController: NavController) {
        // Use extension function to check if we should show rationale for location permissions
        val shouldShowLocationRationale = shouldShowLocationPermissionRationale()

        val action = when {
            // Check for location permission, considering shouldShowRequestRationale
            RemoteConfigUtils.isNewLocationOnboardingEnabled() &&
                    !context.isLocationPermissionEnabled() &&
                    !shouldShowLocationRationale ->
                NotificationPermissionStepFragmentDirections.actionNotificationPermissionStepFragmentToLocationPermissionStepFragment2()
            else -> null
        }

        if (findNavController().currentDestination?.id == R.id.notificationPermissionStepFragment) {
            if (action != null) {
                navController.navigate(action)
            } else {
                navController.navigate(NotificationPermissionStepFragmentDirections.actionNotificationPermissionStepFragmentToLoveStoriesSignUpFragment())
            }
        }
        (requireActivity() as SignUpActivity).removeGraphStep(R.id.notificationPermissionStepFragment)
    }

    private fun toggleContinueButton(enable: Boolean) {
        binding.continueBtn.isEnabled = enable
    }

    private fun sendNotificationAllowedEvents() {
        val eventSourceGA = NotificationsSourceValues.ONBOARDING_AFTER_COMMUNITY.value
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        val params = mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.ONBOARDING_AFTER_BIRTHDAY.value
        )

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_ALLOWED, params)
        sendUxCamEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_ALLOWED, params)

        val premiumType = ClevertapPremiumTypeValues.FREEMIUM.value
        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_NOTIFICATIONS_ALLOWED,
            mapOf(FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA))

    }
    private fun sendNotificationPopUpEvents() {
        val eventSourceGA = NotificationsSourceValues.ONBOARDING_AFTER_COMMUNITY.value
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        val params = mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.ONBOARDING_AFTER_BIRTHDAY.value
        )

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_POPUP, params)
        sendUxCamEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_POPUP, params)

        val premiumType = ClevertapPremiumTypeValues.FREEMIUM.value
        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_NOTIFICATIONS_POPUP,
            mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA
            )
        )
    }

    private fun sendScreenViewEvents() {
        val eventSourceGA = NotificationsSourceValues.ONBOARDING_AFTER_COMMUNITY.value
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        val params = mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.ONBOARDING_AFTER_BIRTHDAY.value
        )

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_SCREEN_VIEW, params)
        sendUxCamEvent(ClevertapEventEnum.PERMISSION_NOTIFICATIONS_SCREEN_VIEW, params)

        val premiumType = ClevertapPremiumTypeValues.FREEMIUM.value
        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_NOTIFICATIONS_SCREEN_VIEW,
            mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}