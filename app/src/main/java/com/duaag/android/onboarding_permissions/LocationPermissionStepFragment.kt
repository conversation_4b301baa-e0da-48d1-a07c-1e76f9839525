package com.duaag.android.onboarding_permissions

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapOnboardingTypeValues
import com.duaag.android.clevertap.ClevertapPremiumTypeValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentLocationPermissionStepBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.NotificationsSourceValues
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.signup.SignUpActivity
import com.duaag.android.signup.viewmodel.SharedSignUpViewModel
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.requestLocationPermissionsWithLauncher
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import javax.inject.Inject

class LocationPermissionStepFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    private var _binding: FragmentLocationPermissionStepBinding? = null
    private val binding get() = _binding!!

    private val signUpViewModel by viewModels<SharedSignUpViewModel>({ activity as SignUpActivity } ) { viewModelFactory }

    private val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            sendNotificationPopUpEvents()
            val granted = permissions.all { it.value }
            if(granted){
                sendPermissionAllowedEvents()
                signUpViewModel.getCurrentLocation()
            }
           navigate()
        }


    private fun navigate() {
        val action = LocationPermissionStepFragmentDirections.actionLocationPermissionStepFragmentToLoveStoriesSignUpFragment()
        findNavController().navigate(action)

        (requireActivity() as SignUpActivity).removeGraphStep(R.id.locationPermissionStepFragment)
    }

    private fun sendPermissionAllowedEvents() {
        val eventSourceGA = NotificationsSourceValues.ONBOARDING_AFTER_COMMUNITY.value
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        val params = mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.ONBOARDING_AFTER_BIRTHDAY.value
        )

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_ALLOWED, params)
        sendUxCamEvent(ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_ALLOWED, params)

        val premiumType = ClevertapPremiumTypeValues.FREEMIUM.value
        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_LOCATION_TO_USE_ALLOWED,
            mapOf(FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA))
    }

    private fun sendNotificationPopUpEvents() {
        val eventSourceGA = NotificationsSourceValues.ONBOARDING_AFTER_COMMUNITY.value
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        val params = mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.ONBOARDING_AFTER_BIRTHDAY.value
        )

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_POPUP, params)
        sendUxCamEvent(ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_POPUP, params)

        val premiumType = ClevertapPremiumTypeValues.FREEMIUM.value
        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_LOCATION_TO_USE_POPUP,
            mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA
            )
        )
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)
        (requireActivity() as SignUpActivity).signUpComponent.inject(this)
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLocationPermissionStepBinding.inflate(inflater)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.allowLocationBtn.setOnSingleClickListener{
            if(requireContext().isLocationPermissionEnabled())
                navigate()
            else
                requestLocationPermissionsWithLauncher(requestPermissionLauncher)
        }

        signUpViewModel.isCreatingProfile.observe(viewLifecycleOwner) {
            if(it == true) {
                toggleContinueButton(false)
                binding.allowLocationBtn.visibility = View.INVISIBLE
                binding.progressBar.visibility = View.VISIBLE
            } else  {
                toggleContinueButton(true)
                binding.allowLocationBtn.visibility = View.VISIBLE
                binding.progressBar.visibility = View.GONE
            }
        }

        sendScreenViewEvents()
    }

    private fun toggleContinueButton(enable: Boolean) {
        binding.allowLocationBtn.isEnabled = enable
    }

    private fun sendScreenViewEvents() {
        val eventSourceGA = NotificationsSourceValues.ONBOARDING_AFTER_COMMUNITY.value
        val signUpOrSignInmMediumValue = signUpViewModel.getClevertapAuthMethodValue()

        val params = mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to null,
            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMediumValue,
            ClevertapEventPropertyEnum.ONBOARDING_TYPE.propertyName to ClevertapOnboardingTypeValues.ONBOARDING_AFTER_BIRTHDAY.value
        )

        sendClevertapEvent(ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_SCREEN_VIEW, params)
        sendUxCamEvent(ClevertapEventEnum.PERMISSION_LOCATION_TO_USE_SCREEN_VIEW, params)

        val premiumType = ClevertapPremiumTypeValues.FREEMIUM.value
        firebaseLogEvent(FirebaseAnalyticsEventsName.PERMISSION_LOCATION_TO_USE_SCREEN_VIEW,
            mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType,
                FirebaseAnalyticsParameterName.PERMISSION_SOURCE_SCREEN.value to eventSourceGA
            )
        )

    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}