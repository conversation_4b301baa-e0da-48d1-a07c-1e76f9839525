package com.duaag.android.user_guide.adapters


import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.UserGuideTextItemBinding
import com.duaag.android.user_guide.models.UserGuideTextItem


class UserGuideTextAdapter(private val items:List<UserGuideTextItem>) : RecyclerView.Adapter<UserGuideTextAdapter.UserGuideTextViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserGuideTextViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: UserGuideTextItemBinding = UserGuideTextItemBinding.inflate(layoutInflater, parent, false)
        return UserGuideTextViewHolder(binding)
    }

    override fun onBindViewHolder(holder: UserGuideTextViewHolder, position: Int) {
        items[position].let { holder.bind(it) }
    }
    override fun getItemCount() = items.size
    inner class UserGuideTextViewHolder(private var binding: UserGuideTextItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: UserGuideTextItem) {
            binding.title.setText(item.title)
            binding.description.setText(item.description)
        }
    }
}

class GuidelinesDiffCallback : DiffUtil.ItemCallback<UserGuideTextItem>() {
    override fun areItemsTheSame(oldItem: UserGuideTextItem, newItem: UserGuideTextItem): Boolean {
        return oldItem.title == newItem.title
    }

    override fun areContentsTheSame(oldItem: UserGuideTextItem, newItem: UserGuideTextItem): Boolean {
        return oldItem == newItem
    }
}
