package com.duaag.android.user_guide.fragments

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.R
import com.duaag.android.databinding.NewProfileUserGuideBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.fragments.FilterOptionsFragment
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user_guide.adapters.UserGuideTextAdapter
import com.duaag.android.user_guide.models.UserGuideTextItem
import com.duaag.android.utils.convertDpToPixel
import com.google.android.material.tabs.TabLayoutMediator
import timber.log.Timber
import javax.inject.Inject

class NewProfileGuideDialog :DialogFragment() {


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    private var _binding: NewProfileUserGuideBinding? = null
    private val binding get() = _binding!!
    private var tabLayoutMediator: TabLayoutMediator? = null

    var filterBtnX = 0f
    var filterBtnY = 0f

    var editBtnX = 0f
    var editBtnY = 0f

    var photoBtnX = 0f
    var photoBtnY = 0f

    var locationY = 0f
    var locationHeight = 0
    var guideStep = 0


    companion object {
        const val USER_GUIDE_STEP = "userGuideStep"

        const val BTN_FILTER_COORDINATES_X = "filterBtnPositionX"
        const val BTN_FILTER_COORDINATES_Y = "filterBtnPositionY"

        const val BTN_EDIT_COORDINATES_Y = "editBtnPositionY"
        const val BTN_EDIT_COORDINATES_X = "editBtnPositionX"

        const val BTN_PHOTO_COORDINATES_Y = "photoBtnPositionY"
        const val BTN_PHOTO_COORDINATES_X = "photoBtnPositionX"

        const val LOCATION_COORDINATES_Y = "locationPositionY"
        const val LOCATION_HEIGHT = "locationHeight"
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDayNightAppTheme)

        filterBtnX = arguments?.getFloat(BTN_FILTER_COORDINATES_X, 0f) ?: 0f
        filterBtnY = arguments?.getFloat(BTN_FILTER_COORDINATES_Y, 0f) ?: 0f

        editBtnX = arguments?.getFloat(BTN_EDIT_COORDINATES_X, 0f) ?: 0f
        editBtnY = arguments?.getFloat(BTN_EDIT_COORDINATES_Y, 0f) ?: 0f

        photoBtnX = arguments?.getFloat(BTN_PHOTO_COORDINATES_X, 0f) ?: 0f
        photoBtnY = arguments?.getFloat(BTN_PHOTO_COORDINATES_Y, 0f) ?: 0f

        locationY = arguments?.getFloat(LOCATION_COORDINATES_Y, 0f) ?: 0f
        locationHeight = arguments?.getInt(LOCATION_HEIGHT, 0) ?: 0
        guideStep = arguments?.getInt(USER_GUIDE_STEP, 0) ?: 0

        Timber.tag("CAMERAPOSITION").d("USERGUIDE cameraImagebtn.y: $photoBtnY")
        Timber.tag("CAMERAPOSITION").d("USERGUIDE editProfileButton: $editBtnY")

        isCancelable = false
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)

        dialog.window!!.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        dialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)

        return dialog
    }

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        _binding = NewProfileUserGuideBinding.inflate(inflater)

        val itmes = listOf(
                UserGuideTextItem(
                        R.string.filter_f,
                        R.string.to_change_the_location_age_range_and_radius_click_on_this_icon
                ),
                UserGuideTextItem(
                        R.string.flying,
                        R.string.if_you_want_to_search_profiles_in_other_cities_you_can_simply_fly_to_another_location_by_changing_it_here
                ),
                UserGuideTextItem(
                        R.string.photo,
                        R.string.you_can_upload_up_to_6_photos_on_your_profile_the_better_your_profile_the_more_matches_you_get
                ),
                UserGuideTextItem(
                        R.string.your_profile,
                        R.string.job_hobbies_town_and_more_information_can_be_added_on_your_profile_make_sure_to_have_your_profile_completed_in_order_to_get_more_matches
                )
        )


        binding.nextButton.setOnClickListener {
            when (binding.userGuideViewPager.currentItem) {
                0 -> {
                    homeViewModel.openFilterGuide()
                    dismissAllowingStateLoss()
                }
                1 -> {
                    (parentFragment as FilterOptionsFragment).popBackStack()
                    homeViewModel.continueUserGuide()
                    dismissAllowingStateLoss()
                }
                2 -> {
                    binding.userGuideViewPager.currentItem = 3
                }
                3 -> {
                    duaSharedPrefs.setHasShownUserGuideInProfile(true)
                    dismissAllowingStateLoss()
                }
            }
        }

        val adapter = UserGuideTextAdapter(itmes)
        binding.userGuideViewPager.adapter = adapter
        binding.userGuideViewPager.isUserInputEnabled = false
        setupViewPager(adapter, binding.userGuideViewPager)

        binding.userGuideViewPager.registerOnPageChangeCallback(object :
                ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                updateStep(binding.userGuideViewPager.currentItem)
            }
        })

        binding.btnEdit.setOnClickListener { binding.nextButton.performClick() }
        binding.btnFilter.setOnClickListener { binding.nextButton.performClick() }
        binding.cameraImagebtn.setOnClickListener { binding.nextButton.performClick() }
        binding.locationContainer.setOnClickListener { binding.nextButton.performClick() }

        binding.userGuideViewPager.currentItem = guideStep
        updateStep(guideStep)

        binding.root.viewTreeObserver?.addOnGlobalLayoutListener(
                object : ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        binding.root.viewTreeObserver!!.removeOnGlobalLayoutListener(this)

                        //TODO logic when to show this
                        //set offscreen coordinates
                        val displayMetrics = DisplayMetrics()
                        requireActivity().windowManager.defaultDisplay.getMetrics(displayMetrics)
                        val screenHeight = displayMetrics.heightPixels.toFloat()
                        val screenWidth = displayMetrics.widthPixels.toFloat()

                        Timber.tag("USERGUIDE").d("onGlobalLayout: screenHeight: $screenHeight  screenWidth: $screenWidth locationContainer: ${binding.locationContainer.y}")
                    }
                })

        return binding.root
    }

    fun updateStep(step: Int){
        when (step) {
            0 -> {
                showFilterGuide()
            }
            1 -> {
                showLocationGuide()
            }
            2 -> {
                showPhotoGuide()
            }
            3 -> {
                hidePhotoGuide()
                showEditGuide()
            }
        }
    }

    private fun setupViewPager(adapter: UserGuideTextAdapter, pager: ViewPager2) {
        pager.adapter = adapter
        binding.tabLayout.let {
            pager.let { it1 ->
                tabLayoutMediator = TabLayoutMediator(it, it1) { _, _ -> }
                tabLayoutMediator?.attach()
            }
        }
    }

    fun showFilterGuide() {
        binding.viewPagerContainer.visibility = View.VISIBLE
        binding.userGuideViewPager.currentItem = 0

        binding.filterBtnContainer.visibility = View.VISIBLE
        binding.filterBtnContainer.x = filterBtnX - convertDpToPixel(4f, requireContext())
        binding.filterBtnContainer.y = filterBtnY - convertDpToPixel(4f, requireContext())

        binding.viewPagerContainer.y = filterBtnY + convertDpToPixel(58f, requireContext())
    }

    fun hideFilterGuide() {
        binding.filterBtnContainer.visibility = View.GONE
    }

    fun showEditGuide() {
        binding.editBtnContainer.visibility = View.VISIBLE
        binding.editBtnContainer.y = editBtnY - convertDpToPixel(4f, requireContext())

        binding.viewPagerContainer.visibility = View.VISIBLE
        binding.viewPagerContainer.y = editBtnY + convertDpToPixel(46f, requireContext())

    }

    fun hideEditGuide() {
        binding.editBtnContainer.visibility = View.GONE
    }

    fun showPhotoGuide() {
        binding.cameraBtnContainer.visibility = View.VISIBLE
        binding.cameraBtnContainer.y = photoBtnY - convertDpToPixel(4f, requireContext())
        binding.cameraBtnContainer.x = photoBtnX - convertDpToPixel(4f, requireContext())

        binding.viewPagerContainer.visibility = View.VISIBLE
        binding.viewPagerContainer.y = photoBtnY + convertDpToPixel(52f, requireContext())
    }

    fun showLocationGuide() {
        binding.locationContainer.visibility = View.VISIBLE
        binding.dottedLineBottom.visibility = View.VISIBLE
        binding.dottedLineTop.visibility = View.VISIBLE

        val params = binding.locationContainer.layoutParams
        params.height = locationHeight
        binding.locationContainer.layoutParams = params

        binding.locationContainer.y = locationY.toFloat()
        binding.dottedLineTop.y = locationY.toFloat() - convertDpToPixel(5.5f, requireContext())
        binding.dottedLineBottom.y = locationY.toFloat() + locationHeight + convertDpToPixel(2f, requireContext())

        binding.viewPagerContainer.visibility = View.VISIBLE
        binding.viewPagerContainer.y = locationY + locationHeight + convertDpToPixel(24.5f, requireContext())
    }

    fun hidePhotoGuide() {
        binding.cameraBtnContainer.visibility = View.GONE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.userGuideViewPager.adapter = null
        _binding = null
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
    }
}