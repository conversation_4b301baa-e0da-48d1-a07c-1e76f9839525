package com.duaag.android.premium_subscription.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.databinding.PremiumBenefitsItemBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.premium_subscription.models.PremiumBenefitsDataClass
import com.duaag.android.premium_subscription.models.premiumBenefitsItemsFemale
import com.duaag.android.premium_subscription.models.premiumBenefitsItemsMale
import com.duaag.android.utils.GenderType

class BenefitsPremiumAdapter(homeViewModel: HomeViewModel) : RecyclerView.Adapter<BenefitsPremiumAdapter.BenefitsPremiumAdapterViewHolder>() {

    enum class PremiumPaywallList {
        BOOST_ITEM,
        IMPRESSIONS_ITEM,
        INSTACHATS_ITEM,
        REVEAL_INSTACHATS,
        FLIGHTS_ITEM,
        GHOST_ITEM,
        UNDO_ITEM,
        UNBLURRED_ITEM,
        AD_FREE_ITEM,
        UPLOAD_MORE_PICTURES,
        VIDEO_CALLS_ITEM
    }

    private var user = homeViewModel.userProfile.value?.gender

    private val items
        get() = if (user == GenderType.WOMAN.value) {
            premiumBenefitsItemsFemale.map { it.apply { descriptionTxt = descriptionTxt ?: DuaApplication.instance.getString(description) } }
        } else {
            premiumBenefitsItemsMale.map { it.apply { descriptionTxt = descriptionTxt ?: DuaApplication.instance.getString(description) } }
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BenefitsPremiumAdapterViewHolder {
        val binding: PremiumBenefitsItemBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), R.layout.premium_benefits_item, parent, false)
        return BenefitsPremiumAdapterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: BenefitsPremiumAdapterViewHolder, position: Int) {
        holder.binding.premiumBenefitsList = items[position]
    }

    override fun getItemCount(): Int = items.size

    class BenefitsPremiumAdapterViewHolder(val binding: PremiumBenefitsItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(premiumBenefitsDataClass: PremiumBenefitsDataClass) {
            binding.premiumBenefitsList = premiumBenefitsDataClass
            binding.executePendingBindings()
        }
    }
}