package com.duaag.android.premium_subscription.models

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize
import com.google.gson.annotations.SerializedName


@Keep
@Parcelize
data class SpecialOfferDataModel(
    @SerializedName("endTime")
    val endTime: Long,
    @SerializedName("offer_type")
    val offerType: String,
    @SerializedName("product_Id")
    val productId: String,
    @SerializedName("startTime")
    val startTime: Long
): Parcelable