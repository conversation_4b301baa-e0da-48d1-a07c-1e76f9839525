package com.duaag.android.premium_subscription.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.fragments.PremiumFragment
import com.duaag.android.premium_subscription.fragments.SpecialOfferPremiumFragment
import dagger.Subcomponent

// Definition of a Dagger subcomponent
@ActivityScope
@Subcomponent(modules = [PremiumViewModelModule::class])
interface PremiumComponent {

    // Factory to create instances of PremiumComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): PremiumComponent
    }

    // Classes that can be injected by this Component
    fun inject(activity: PremiumActivity)
    fun inject(fragment: PremiumFragment)
//    fun inject(fragment: SpecialOfferPremiumFragment)
}