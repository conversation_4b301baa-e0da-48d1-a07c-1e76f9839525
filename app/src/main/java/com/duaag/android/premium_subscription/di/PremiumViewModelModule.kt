package com.duaag.android.premium_subscription.di

import androidx.lifecycle.ViewModel
import com.duaag.android.chat.viewmodel.LikedYouViewModel
import com.duaag.android.di.ViewModelKey
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.home.viewmodels.InAppPackagesViewModel
import com.duaag.android.premium_subscription.viewmodels.PremiumViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class PremiumViewModelModule {

    @Binds
    @IntoMap
    @ViewModelKey(PremiumViewModel::class)
    abstract fun bindViewModel(myViewModel: PremiumViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(HomeViewModel::class)
    abstract fun bindHomeViewModel(myViewModel: HomeViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(LikedYouViewModel::class)
    abstract fun likedYouViewModel(myViewModel: LikedYouViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(InAppPackagesViewModel::class)
    abstract fun inAppPackagesViewModel(inAppPackagesViewModel: InAppPackagesViewModel): ViewModel

}