package com.duaag.android.premium_subscription

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.FrameLayout
import android.widget.ProgressBar
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.NavHostFragment
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.android.billingclient.api.queryPurchasesAsync
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.sendConsumableScreenViewEvent
import com.duaag.android.clevertap.sendGoPremiumInitiatedEvent
import com.duaag.android.clevertap.sendGoPremiumScreenViewEvent
import com.duaag.android.home.models.PaymentType
import com.duaag.android.home.models.PaymentVerifyErrorType
import com.duaag.android.home.models.VerifyPaymentModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.di.PremiumComponent
import com.duaag.android.premium_subscription.viewmodels.PremiumViewModel
import com.duaag.android.settings.fragments.SubscriptionAlreadyExistsBottomSheet
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.utils.getPackageTypeForId
import com.duaag.android.utils.getProductTypeForId
import com.duaag.android.views.SpinningCircleDialog
import io.purchasely.ext.PLYPresentation
import io.purchasely.ext.PLYPresentationAction
import io.purchasely.ext.PLYPresentationProperties
import io.purchasely.ext.PLYPresentationType
import io.purchasely.ext.Purchasely
import io.purchasely.views.presentation.PLYPresentationView
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class PremiumActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "PremiumActivity"
        const val VIEW_PAGER_START_POSITION = "viewPagerStartPosition"
        const val PREMIUM_REQUEST_CODE = 164
        const val PREMIUM_INTENT_BROADCAST = "broadcast_premium"
        const val PREMIUM_INTENT = "premium_intent"
        const val BILLING_AVAILABLE_INTENT = "billing_available"
        const val PREMIUM_OFFER_INTENT = "premium_offer_intent"
        const val HAS_PREMIUM_OFFER_INTENT = "premium_offer"
        const val PREMIUM_OFFER_ID = "premiumOfferId"
        const val UPGRADED_PREMIUM = "upgraded_premium"
        const val VIDEO_CALL_COUNTER_RESET_TIME = "VIDEO_CALL_COUNTER_RESET_TIME"
        const val VIDEO_CALL_NAME = "VIDEO_CALL_NAME"
        const val ANALYTICS_EVENT_FROM = "ANALYTICS_EVENT_FROM"
        const val PLACEMENT_ID = "placement_id"
        const val PLACEMENT = "placement"
        const val IS_FROM_IMPRESSIONS = "is_from_impressions"
        const val PROMO_CODE = "promo_code"

        const val PURCHASELY_TAG = "Purchasely"

        const val SPECIAL_DAYS_OFFER_TAG = "special-days-offer"

        const val EVENT_SOURCE_VALUE = "event_source_value"
        const val PURCHASE_TYPE = "purchase_type"
    }

    public enum class InAppPurchase {
        SUBSCRIPTION,
        CONSUMABLE
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<PremiumViewModel> { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel> { viewModelFactory }

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    lateinit var premiumComponent: PremiumComponent
    private var spinningCircleDialog: SpinningCircleDialog? = null
    private var billingClient: BillingClient? = null

    var presentation: PLYPresentation? = null
    var plyPresentationView: PLYPresentationView? = null

    var isFromImpressions: Boolean = false
    var promoCode: String? = null

    private var purchasesUpdatedListener: PurchasesUpdatedListener = PurchasesUpdatedListener { billingResult, purchases ->
        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
            Timber.tag("BILLING").d("purchases: $purchases")
            if (!purchases.isNullOrEmpty()) {
                Timber.tag("BILLING").d("purchases: $purchases")
                purchases.forEach { purchase ->
                    purchase?.let {
                        Timber.tag("BILLING").d("purchaseState: ${purchase.purchaseState}")

                        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {

                            Timber.tag("BILLING").d("purchaseState: ${purchase.purchaseState}")
                            val purchaseType = getPackageTypeForId(it.skus[0])

                            val eventSourceValue = intent.getStringExtra(EVENT_SOURCE_VALUE)

                            //This listener was being called even when the purchase was being
                            //made from PremiumActivity.
                            if (purchaseType != PaymentType.PREMIUM.type) {
                                val model = VerifyPaymentModel(
                                    "play-store",
                                    it.skus[0],
                                    it.orderId!!,
                                    it.purchaseTime.toString(),
                                    it.purchaseToken,
                                    purchaseType,
                                    promoCode
                                )
                                homeViewModel.verifyPayment(
                                    model,
                                    purchase,
                                    eventSourceValue,
                                    presentation?.placementId
                                )
                            } else {
                                val model = VerifyPaymentModel(
                                    "play-store",
                                    it.skus.first(),
                                    it.orderId!!,
                                    it.purchaseTime.toString(),
                                    it.purchaseToken,
                                    getPackageTypeForId(it.skus.first()),
                                    promoCode
                                )

                                homeViewModel.verifySubscription(
                                    model = model,
                                    purchase = purchase,
                                    isRestoring = false,
                                    eventSource = eventSourceValue,
                                    placementId = presentation?.placementId
                                )

                                sendClevertapEvent(
                                    ClevertapEventEnum.PREMIUM_PURCHASE_WAITING_FOR_VERIFICATION,
                                    mapOf(
                                        ClevertapEventPropertyEnum.COMMUNITY.propertyName to homeViewModel.userProfile.value?.communityInfo?.id,
                                        ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSourceValue,
                                    )
                                )

                            }
                        }
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        premiumComponent = (application as DuaApplication).appComponent.premiumComponent().create()
        premiumComponent.inject(this)


       if(homeViewModel.anyOfferAvailable())
           setTheme(R.style.DayNightAppTheme)

        Timber.tag("VIEWMODEL").d(premiumComponent.toString())

        super.onCreate(savedInstanceState)
        setContentView(R.layout.premium_activity)

        initPurchasely()

        initBillingClient()

        spinningCircleDialog = SpinningCircleDialog(this)

        homeViewModel.showSpinningDialog.observe(this) { show ->
            if (show)
                spinningCircleDialog?.show()
            else {
                spinningCircleDialog?.dismiss()
            }
        }

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                homeViewModel.premiumBoughtSuccessfully.collect { event ->
                    val returnIntent = Intent()
                    setResult(Activity.RESULT_OK, returnIntent)
                    returnIntent.putExtras(bundleOf( PURCHASE_TYPE to InAppPurchase.SUBSCRIPTION))
                    finish()
                }
            }
        }


        homeViewModel.consumePurchase.observe(this) {
            consumeProduct(it)
        }

        viewModel.buyPackage.observe(this) { playPackage ->
            playPackage?.let { buyPremiumItem(it, "") }
        }

        homeViewModel.subscriptionValidationError.observe(this) {
            if(it.type == PaymentVerifyErrorType.SUBSCRIPTION_CONFLICT.value) {
                val conflictBottomSheet = SubscriptionAlreadyExistsBottomSheet.newInstance(it, false)
                conflictBottomSheet.show(supportFragmentManager, "SubscriptionAlreadyExistsBottomSheet")
            }
        }

        isFromImpressions = intent.getBooleanExtra(IS_FROM_IMPRESSIONS, false)
        promoCode = intent.getStringExtra(PROMO_CODE)
    }

    private fun initPurchasely() {
        presentation = intent.getParcelableExtra<PLYPresentation>(PLACEMENT)
        findViewById<FrameLayout>(R.id.paywallFrame).isVisible = true

        //if presentation is not null load it
        presentation?.let {
            handlePresentation(it)
            return
        }

        //fetch presentation
        val placementId = intent.getStringExtra(PLACEMENT_ID)!!
        findViewById<ProgressBar>(R.id.progress_bar).isVisible = true
        Timber.tag(PURCHASELY_TAG).d("fetching presentation $placementId")
        Purchasely.fetchPresentation(placementId = placementId) { presentation, error ->
            findViewById<ProgressBar>(R.id.progress_bar).isVisible = false
            if(error != null) {
                Timber.tag(PURCHASELY_TAG).e("${error.message }")
                error.printStackTrace()
            }

            Timber.tag(PURCHASELY_TAG).d("presentation fetched $placementId")
            Timber.tag(PURCHASELY_TAG).e("error $error")

            this.presentation = presentation

            if(presentation == null) {
                closeActivityWithNoPayment()
                return@fetchPresentation
            }

            handlePresentation(presentation)
            PurchaselyManager.cachePresentation(presentation)
        }
    }

    private fun handlePresentation(presentation: PLYPresentation) {
        when(presentation.type) {
            PLYPresentationType.NORMAL,
            PLYPresentationType.FALLBACK -> {

                plyPresentationView = presentation.buildView(this@PremiumActivity)

                if(plyPresentationView == null) Timber.tag(PURCHASELY_TAG).d("Error with view")

                findViewById<FrameLayout>(R.id.paywallFrame).addView(plyPresentationView)
                val eventSource = intent.getStringExtra(EVENT_SOURCE_VALUE)

                if(presentation.plans.firstOrNull()?.storeProductId?.contains(PaymentType.PREMIUM.type) == true) {
                    sendGoPremiumScreenViewEvent(eventSource ?: "", presentation.placementId, promoCode)
                } else {
                    val productType = getProductTypeForId(presentation.plans.firstOrNull()?.storeProductId ?: "") ?: ""
                    sendConsumableScreenViewEvent(eventSource ?: "", productType, presentation.placementId)
                }
            }
            PLYPresentationType.DEACTIVATED -> {
                closeActivityWithNoPayment()
            }
            PLYPresentationType.CLIENT -> {
                findViewById<FrameLayout>(R.id.paywallFrame).isVisible = false

                setupNavController()
            }
            else -> {}
        }

        setPaywallInterceptor()
    }


    private fun setPaywallInterceptor() {
        Purchasely.setPaywallActionsInterceptor { info, action, parameters, processAction ->
            when(action) {
                PLYPresentationAction.PURCHASE -> {
                    Timber.tag(PURCHASELY_TAG).d("PLYPresentationAction.PURCHASE")

                    if(parameters.plan?.getProductId()?.contains(PaymentType.PREMIUM.type) == true) {

                        parameters.plan?.getProductId()?.let {
                            val eventSource = intent.getStringExtra(EVENT_SOURCE_VALUE)
                            sendGoPremiumInitiatedEvent(it, eventSource, presentation?.placementId, promoCode)

                            val productId = parameters.plan?.store_product_id
                            val offerToken = parameters.subscriptionOffer?.offerToken.orEmpty()

                            buyPremiumItem(productId!!, offerToken)
                        }

                    } else {
                        parameters.plan?.getProductId()?.let {
                            //send analytics events

                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            val productIdEvent = it.substringAfter("${BuildConfig.APPLICATION_ID.substringBeforeLast(".")}.").replace(".", "_")

                            sendClevertapEvent(
                                ClevertapEventEnum.IN_APP_PURCHASE_INITIATED,
                                mapOf(
                                        ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to  intent.getStringExtra(EVENT_SOURCE_VALUE),
                                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                        ClevertapEventPropertyEnum.IN_APP_PRODUCT.propertyName to getPackageTypeForId(it),
                                        ClevertapEventPropertyEnum.PLACEMENT_ID.propertyName to presentation?.placementId,
                                        ClevertapEventPropertyEnum.PRODUCT_ID.propertyName to productIdEvent
                                )
                            )

                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.IN_APP_PURCHASE_INITIATED,
                                mapOf(
                                        FirebaseAnalyticsParameterName.EVENT_SOURCE.value to  intent.getStringExtra(EVENT_SOURCE_VALUE),
                                        FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                        FirebaseAnalyticsParameterName.IN_APP_PRODUCT.value to getPackageTypeForId(it),
                                        FirebaseAnalyticsParameterName.PLACEMENT_ID.value to presentation?.placementId,
                                        FirebaseAnalyticsParameterName.PRODUCT_ID.value to productIdEvent
                                )
                            )


                            buyConsumableItem(it)
                        }

                    }

                    processAction(false)
                }
                PLYPresentationAction.CLOSE -> {
                    Timber.tag(PURCHASELY_TAG).d("PLYPresentationAction.CLOSE")

                    processAction(true)

                    closeActivityWithNoPayment()
                }
                PLYPresentationAction.RESTORE -> {
                    Timber.tag(PURCHASELY_TAG).d("PLYPresentationAction.RESTORE")

                    consumePurchasedGoods()

                    processAction(false)
                }
                PLYPresentationAction.OPEN_PRESENTATION -> {
                    findViewById<FrameLayout>(R.id.paywallFrame).removeAllViews()
                    plyPresentationView = Purchasely.presentationView(
                            context = this,
                            properties = PLYPresentationProperties(
                                    presentationId = parameters.presentation,
                                    onClose = {
                                        findViewById<FrameLayout>(R.id.paywallFrame).removeAllViews()
                                    })
                    )
                    findViewById<FrameLayout>(R.id.paywallFrame).addView(plyPresentationView)
                    processAction(false)
                }
                else -> {
                    processAction(true)
                }
            }
        }
    }


    private fun setupNavController(){
        val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_settings) as NavHostFragment
        val navController = navHostFragment.navController

            val navGraph = navController.navInflater.inflate(R.navigation.nav_graph_premium)
            navGraph.setStartDestination(R.id.premiumFragment)

            val startPagerItem: BenefitsPremiumAdapter.PremiumPaywallList =
                (intent.getSerializableExtra(VIEW_PAGER_START_POSITION) as? BenefitsPremiumAdapter.PremiumPaywallList)
                    ?: BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM

            navController.setGraph(navGraph, bundleOf(VIEW_PAGER_START_POSITION to startPagerItem))
    }

    private fun buyConsumableItem(productId: String) {
        queryConsumableDetails(productId)
    }

    private fun buyPremiumItem(productId: String, offerId: String) {
        queryPremiumProductsDetails(productId, offerId)
    }

    private fun queryPremiumProductsDetails(productId: String, offerToken: String) {
        ensureBillingClientConnected {
            val productList = listOf(
                QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(productId)
                    .setProductType(BillingClient.ProductType.SUBS)
                    .build()
            )

            val params = QueryProductDetailsParams.newBuilder().setProductList(productList)

            billingClient?.queryProductDetailsAsync(params.build()) { billingResult, productDetailsList ->
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && productDetailsList.isNotEmpty()) {
                    val productDetails = productDetailsList.first()

                    val productDetailsParamsList = listOf(
                        BillingFlowParams.ProductDetailsParams.newBuilder()
                            .setProductDetails(productDetails)
                            .setOfferToken(offerToken)
                            .build()
                    )

                    val billingFlowParams = BillingFlowParams.newBuilder()
                        .setProductDetailsParamsList(productDetailsParamsList)
                        .build()

                    val billingResult = billingClient?.launchBillingFlow(this, billingFlowParams)
                    Timber.tag(TAG).d("launchBillingFlow: responseCode: ${billingResult?.responseCode}")
                } else {
                    Timber.tag(TAG).e("Failed to fetch premium product details: ${billingResult.debugMessage}")
                }
            }
        }
    }

    private fun queryConsumableDetails(productId: String) {
        ensureBillingClientConnected {
            val params = QueryProductDetailsParams.newBuilder()
            val productList = QueryProductDetailsParams.Product.newBuilder()
                .setProductType(BillingClient.ProductType.INAPP)
                .setProductId(productId)
                .build()
            params.setProductList(listOf(productList))

            billingClient?.queryProductDetailsAsync(params.build()) { billingResult, productDetails ->
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && productDetails.isNotEmpty()) {
                    val billingFlowParams = BillingFlowParams.newBuilder()
                        .setProductDetailsParamsList(
                            listOf(
                                BillingFlowParams.ProductDetailsParams.newBuilder()
                                    .setProductDetails(productDetails.first())
                                    .build()
                            )
                        )
                        .build()

                    billingClient?.launchBillingFlow(this@PremiumActivity, billingFlowParams)
                } else {
                    Timber.tag("BILLING").e("Failed to fetch product details: ${billingResult.debugMessage}")
                }
            }
        }
    }

    private fun ensureBillingClientConnected(action: () -> Unit) {
        if (billingClient?.isReady == true) {
            action()
        } else {
            Timber.tag("BILLING").e("BillingClient not ready. Reconnecting...")
            connectToBillingService(action)
        }
    }


    private fun consumeProduct(purchaseToken: String) {
        ensureBillingClientConnected {
            val params = ConsumeParams.newBuilder()
                .setPurchaseToken(purchaseToken)
                .build()

            billingClient?.consumeAsync(params) { billingResult, outToken ->
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    Timber.tag("BILLING").d("CONSUMED: $outToken")
                    // Handle the success of the consume operation.

                    spinningCircleDialog?.dismiss()

                    val returnIntent = Intent()
                    returnIntent.putExtras(bundleOf( PURCHASE_TYPE to InAppPurchase.CONSUMABLE))
                            setResult(Activity.RESULT_OK, returnIntent)
                            finish()

                } else {
                    Timber.tag("BILLING").e("Failed to consume purchase: ${billingResult.debugMessage}")
                }
            }
        }
    }

    private fun initBillingClient() {
        billingClient = BillingClient.newBuilder(applicationContext)
            .setListener(purchasesUpdatedListener)
            .enablePendingPurchases()
            .build()
    }

    private fun connectToBillingService(action: () -> Unit) {
        if (billingClient?.isReady == true) return // Prevent multiple connections

        billingClient?.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    Timber.tag("BILLING").d("Billing Client connected successfully.")
                    action()
                } else {
                    Timber.tag("BILLING").e("Billing Client connection failed: ${billingResult.debugMessage}")
                }
            }

            override fun onBillingServiceDisconnected() {
                Timber.tag("BILLING").e("Billing Service Disconnected. Retrying...")
                reconnectBillingService()
            }
        })
    }

    private fun reconnectBillingService() {
        lifecycleScope.launch {
            delay(2000) // Wait for 2 seconds before retrying
            connectToBillingService({})
        }
    }

    fun closeActivityWithNoPayment() {
        checkIsFromImpressions()
        finish()
    }

    private fun checkIsFromImpressions() {
     if (isFromImpressions) {
            val returnIntent = Intent()
            returnIntent.putExtra(IS_FROM_IMPRESSIONS, true)
            setResult(Activity.RESULT_CANCELED, returnIntent)
        }
    }

    private fun consumePurchasedGoods() {
        // Connect to Google Play
        ensureBillingClientConnected {
            val params = QueryPurchasesParams.newBuilder()
                .setProductType(BillingClient.ProductType.SUBS)
                .build()

            lifecycleScope.launch {
                val results = billingClient?.queryPurchasesAsync(params)

                if (results?.billingResult?.responseCode == BillingClient.BillingResponseCode.OK) {
                    val purchases = results.purchasesList
                    if (!purchases.isNullOrEmpty()) {
                        Timber.tag("BILLING").d("Subscription Purchases: $purchases")

                        for (purchase in purchases) {
                            if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                                val model = VerifyPaymentModel(
                                    "play-store",
                                    purchase.skus.first(),
                                    purchase.orderId!!,
                                    purchase.purchaseTime.toString(),
                                    purchase.purchaseToken,
                                    getPackageTypeForId(purchase.skus.first()),
                                    promoCode
                                )

                                val eventSource = intent.getStringExtra(EVENT_SOURCE_VALUE)
                                homeViewModel.verifySubscription(
                                    model = model,
                                    purchase = purchase,
                                    isRestoring = true,
                                    eventSource = eventSource,
                                    placementId = presentation?.placementId
                                )
                            }
                        }
                    }
                } else {
                    Timber.tag("BILLING").e("Failed to fetch purchases: ${results?.billingResult?.debugMessage}")
                }
            }
        }
    }

    override fun onBackPressed() {
        checkIsFromImpressions()

        super.onBackPressed()
    }

    override fun onDestroy() {
        super.onDestroy()

        Purchasely.setPaywallActionsInterceptor(null)
        findViewById<FrameLayout>(R.id.paywallFrame).removeAllViews()
        plyPresentationView = null
        presentation = null

        spinningCircleDialog?.dismiss()
        spinningCircleDialog = null

        billingClient?.endConnection()
        billingClient = null
    }
}