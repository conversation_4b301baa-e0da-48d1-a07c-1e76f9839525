package com.duaag.android.premium_subscription.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.PremiumBenefitsLayoutBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import javax.inject.Inject

class PremiumBenefitsDialogFragment : DialogFragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private var _binding: PremiumBenefitsLayoutBinding? = null
    private val binding get() = _binding!!


    override fun onAttach(context: Context) {
        super.onAttach(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDayNightAppTheme)

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = PremiumBenefitsLayoutBinding.inflate(inflater)

        sendScreenViewEvent()

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initializeRecyclerView()

        binding.closeButton.setOnClickListener(){
            dismiss()
        }
    }

    private fun sendScreenViewEvent() {
        sendClevertapEvent(ClevertapEventEnum.PREMIUM_OVERVIEW, mapOf(
            ClevertapEventPropertyEnum.COMMUNITY.propertyName to homeViewModel.userProfile.value?.communityInfo?.id,
        ))
    }

    fun initializeRecyclerView() {
        binding.premiumBenefitsRecyclerView.layoutManager = LinearLayoutManager(context)
        val adapter = BenefitsPremiumAdapter(homeViewModel)
        binding.premiumBenefitsRecyclerView.adapter = adapter
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}