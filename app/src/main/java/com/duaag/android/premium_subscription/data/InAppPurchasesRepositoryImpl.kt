package com.duaag.android.premium_subscription.data

import com.duaag.android.api.InteractionErrorBody
import com.duaag.android.api.PurchasesService
import com.duaag.android.api.Resource
import com.duaag.android.premium_subscription.data.dto.RedeemCodeErrorType
import com.duaag.android.premium_subscription.domain.InAppPurchasesRepository
import com.duaag.android.premium_subscription.domain.RedeemCodeException
import com.duaag.android.premium_subscription.models.RedeemCodeResponse
import com.duaag.android.utils.isAlphanumeric
import com.google.gson.Gson
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Named

class InAppPurchasesRepositoryImpl @Inject constructor(
    @Named("purchases") private val inAppPurchasesService: PurchasesService
) : InAppPurchasesRepository {

    override fun redeemCode(code: String): Flow<Resource<RedeemCodeResponse>> = flow {
        emit(Resource.Loading)
        try {
            if(!code.isAlphanumeric)
                throw RedeemCodeException(RedeemCodeErrorType.PROMO_CODE_NOT_FOUND_ERROR)

            val response = inAppPurchasesService.getPaywallFromCode(code)
            if (response.isSuccessful) {
                val body = response.body()!!
                emit(Resource.Success(body))
            }
            else {
                val error = response.errorBody()!!.string()

                when(response.code()){
                    400 -> {
                        val errorObject = Gson().fromJson<InteractionErrorBody>(
                            error,
                            InteractionErrorBody::class.java
                        )
                        when (errorObject.type) {
                            RedeemCodeErrorType.PROMO_CODE_EXPIRED_ERROR.value -> {
                                throw RedeemCodeException(RedeemCodeErrorType.PROMO_CODE_EXPIRED_ERROR)
                            }
                            RedeemCodeErrorType.PROMO_CODE_REDEEM_LIMIT_REACHED_ERROR.value -> {
                                throw RedeemCodeException(RedeemCodeErrorType.PROMO_CODE_REDEEM_LIMIT_REACHED_ERROR)
                            }
                            RedeemCodeErrorType.PROMO_CODE_NOT_FOUND_ERROR.value -> {
                                throw RedeemCodeException(RedeemCodeErrorType.PROMO_CODE_NOT_FOUND_ERROR)
                            }
                            else -> throw Exception("Error")
                        }
                    }
                    else -> throw Exception(error)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }
}