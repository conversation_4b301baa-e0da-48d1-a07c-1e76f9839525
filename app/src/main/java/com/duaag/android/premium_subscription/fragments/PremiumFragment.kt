package com.duaag.android.premium_subscription.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.PurchasesUpdatedListener
import com.bumptech.glide.Glide
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.chat.viewmodel.LikedYouViewModel
import com.duaag.android.clevertap.sendGoPremiumInitiatedEvent
import com.duaag.android.clevertap.sendGoPremiumScreenViewEvent
import com.duaag.android.counters.domain.CalculateIntervalTimeInHourUseCase
import com.duaag.android.databinding.PremiumFragmentBinding
import com.duaag.android.home.adapter.GetInteractionsAdapter
import com.duaag.android.home.adapter.GetInteractionsAdapter.Companion.PREMIUM
import com.duaag.android.home.models.GetInteractionsModel
import com.duaag.android.home.models.GooglePlayPackage
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.home.viewmodels.InAppPackagesViewModel
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.PremiumActivity.Companion.EVENT_SOURCE_VALUE
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.viewmodels.PremiumViewModel
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.autoScroll
import com.duaag.android.utils.getStringPlaceHolder
import com.duaag.android.utils.getStringResourceByName
import com.duaag.android.utils.getTimeStampAfterXHours
import com.duaag.android.utils.makeLinks
import com.duaag.android.utils.openWebView
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.views.SpinningCircleDialog
import com.google.android.material.tabs.TabLayoutMediator
import io.purchasely.ext.PLYPresentation
import io.purchasely.ext.PLYPresentationType
import io.purchasely.ext.Purchasely
import timber.log.Timber
import java.lang.String.format
import java.util.Locale
import javax.inject.Inject

class PremiumFragment : Fragment() {

    companion object {
        fun newInstance(startPosition: Int): PremiumFragment {
            val fragment = PremiumFragment()
            val args = Bundle()
            args.putSerializable(PremiumActivity.VIEW_PAGER_START_POSITION, startPosition)
            fragment.arguments = args
            return fragment
        }
    }
    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<PremiumViewModel>({ activity as PremiumActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as PremiumActivity }) { viewModelFactory }
    private val likedYouViewModel by viewModels<LikedYouViewModel>({ activity as PremiumActivity }) { viewModelFactory }

    val inAppPackageViewModel: InAppPackagesViewModel by viewModels { viewModelFactory }

    private var _binding: PremiumFragmentBinding? = null
    private val binding get() = _binding!!

    private lateinit var firstPackage: GooglePlayPackage
    private lateinit var secondPackage: GooglePlayPackage
    private lateinit var thirdPackage: GooglePlayPackage

    private lateinit var links: Map<String?, String>
    private var pagerStartItem:BenefitsPremiumAdapter.PremiumPaywallList = BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM
    private var videoCallCounterResetTime: Long? = null
    private var videoCallName: String? = null

    private var billingClient: BillingClient? = null
    private var billingStateListener :BillingClientStateListener? = null
    private var tabLayoutMediator: TabLayoutMediator? = null

    private var spinningCircleDialog: SpinningCircleDialog? = null

    private var presentation: PLYPresentation? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        (requireActivity() as PremiumActivity).premiumComponent.inject(this)
        Timber.tag("VIEWMODEL").d(viewModel.toString())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        pagerStartItem =
            (arguments?.getSerializable(PremiumActivity.VIEW_PAGER_START_POSITION) as? BenefitsPremiumAdapter.PremiumPaywallList)
                ?: BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM

        videoCallCounterResetTime =
            activity?.intent?.getLongExtra(PremiumActivity.VIDEO_CALL_COUNTER_RESET_TIME, -1)

        videoCallName = activity?.intent?.getStringExtra(PremiumActivity.VIDEO_CALL_NAME)

        links = mapOf(
            getString(R.string.terms_of_service) to requireContext().getStringResourceByName(BuildConfig.TERMS_AND_CONDITIONS_LINK_KEY),
            getString(R.string.privacy_policy) to requireContext().getStringResourceByName(BuildConfig.PRIVACY_POLICY_LINK_KEY))

        initializePackages()

        val callback: OnBackPressedCallback = object : OnBackPressedCallback(true /* enabled by default */) {
            override fun handleOnBackPressed() { // Handle the back button event
                if (viewModel.getIsCloseTimerShowed()){
                    activity?.finish()
                }
            }
        }
        requireActivity().onBackPressedDispatcher.addCallback(this, callback)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PremiumFragmentBinding.inflate(layoutInflater)

        presentation = (activity as PremiumActivity).presentation
        if(presentation != null) {
            Timber.tag("Purchasely").d("abTestId ${presentation!!.plans}")

            if(presentation!!.type == PLYPresentationType.CLIENT) {
                //Show our paywall
                Purchasely.clientPresentationDisplayed(presentation!!)
                 inAppPackageViewModel.getPLYPlans(presentation!!.plans.map { it.planVendorId!! })
                Timber.tag("Purchasely").d("${presentation!!.plans}")

            } else {
                if(presentation!!.type == PLYPresentationType.DEACTIVATED) {
                    Timber.tag("Purchasely").d("Paywall is deactivated")
                } else {
                    requireActivity().supportFinishAfterTransition()
                }
            }
        } else {
            requireActivity().finish()
        }

        spinningCircleDialog = SpinningCircleDialog(requireContext())
        spinningCircleDialog?.show()

        viewModel.setCloseTimer()

        billingClient = BillingClient.newBuilder(requireContext())
            .setListener(PurchasesUpdatedListener { billingResult, purchases -> })
            .enablePendingPurchases()
            .build()

        binding.premiumTitle.text =  getStringPlaceHolder(getString(R.string.dua_premium_1,getString(R.string.app_name)),getString(R.string.dua_premium_2_an),"", R.color.white,requireContext(),R.font.tt_norms_pro_bold)
        binding.freeTrialText.text = getString(R.string.three_day_free_trial,BuildConfig.DAYS_OF_TRIAL)

        binding.closeBtn.setOnClickListener {
            (requireActivity() as PremiumActivity).closeActivityWithNoPayment()
        }

        binding.premiumBtn.setOnSingleClickListener {

            viewModel.selectedPackage.value?.let {
                val eventSource = requireActivity().intent.getStringExtra(EVENT_SOURCE_VALUE)
                val promoCode = (requireActivity() as? PremiumActivity)?.promoCode

                sendGoPremiumInitiatedEvent(it.productId, eventSource, presentation?.placementId, promoCode)

                viewModel.buyPackage(it.productId)
            }
        }

        makeLinksClickable()

        homeViewModel.userProfileNU.observe(viewLifecycleOwner) { userModel ->
            val isAllowedNewLimits = RemoteConfigUtils.isAllowedToUseNewPremiumLimits()

            val list = mutableListOf<GetInteractionsModel>()
            if (DuaApplication.instance.getBillingAvailable()) {
                //SEE WHO LIKED YOU
                list.add(
                    if (userModel.gender == GenderType.WOMAN.value) {
                        GetInteractionsModel(
                            R.drawable.blurred_liked_you_female,
                            R.string.unblurred_liked_you,
                            R.string.unblurred_liked_you_description,
                            getExpirationTimeOfUnblur(),
                            id = BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM)
                    } else {
                        GetInteractionsModel(
                            R.drawable.blurred_liked_you_male,
                            R.string.unblurred_liked_you,
                            R.string.unblurred_liked_you_description,
                            id = BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM
                        )
                    }
                )

                //IMPRESSIONS
                list.add(
                    GetInteractionsModel(
                        R.drawable.ic_premium_wall_likes,
                        if (userModel.gender == GenderType.MAN.value && isAllowedNewLimits.not()) R.string.impressions_premium_500 else R.string.unlimited_impressions_premium,
                        R.string.unlimited_impressions_premium_description,
                        getExpirationTimeOf(InteractionType.LIKE),
                        id = BenefitsPremiumAdapter.PremiumPaywallList.IMPRESSIONS_ITEM
                    )
                )

                //INSTACHATS
                list.add(
                    GetInteractionsModel(
                        R.drawable.premium_paywall_ic_instachat,
                        if (userModel.gender == GenderType.MAN.value) R.string.five_instachats_daily_premium else R.string.unlimited_instachats_premium,
                        R.string.unlimited_instachats_premium_description,
                        getExpirationTimeOf(InteractionType.INSTA_CHAT),
                        id = BenefitsPremiumAdapter.PremiumPaywallList.INSTACHATS_ITEM
                    ).apply {
                        if (userModel.gender == GenderType.MAN.value) {
                            titleText = getString(R.string.five_instachats_daily_premium, if (isAllowedNewLimits) 1 else 5)
                        }
                    }
                )

                //GHOST MODE
                if (userModel.gender == GenderType.WOMAN.value) {
                    list.add(
                        GetInteractionsModel(
                            R.drawable.ic_reveal_ghost,
                            R.string.ghost_mode,
                            R.string.ghost_mode_benefit_desc,
                            id = BenefitsPremiumAdapter.PremiumPaywallList.GHOST_ITEM
                        )
                    )
                }

                //BOOST
                list.add(
                    GetInteractionsModel(
                        R.drawable.premium_wall_boost,
                        R.string.one_boost_per_month,
                        R.string.boost_desc,
                        id = BenefitsPremiumAdapter.PremiumPaywallList.BOOST_ITEM
                    ).apply {
                        titleText = getString(R.string.one_boost_per_month, if (isAllowedNewLimits) 2 else 1)
                    }
                )

                //FLYING
                list.add(
                    GetInteractionsModel(
                        R.drawable.premium_paywall_ic_airplane_54,
                        R.string.unlimited_flights_premium,
                        R.string.flights_premium_description,
                        getExpirationTimeOf(InteractionType.IS_FLYING),
                        id = BenefitsPremiumAdapter.PremiumPaywallList.FLIGHTS_ITEM
                    )
                )

                //REVEAL INSTACHATS MALE
                if (userModel.gender == GenderType.MAN.value) {
                    list.add(
                        GetInteractionsModel(
                            R.drawable.ic_reveal_instachats,
                            R.string.reveal_instachats_premium,
                            R.string.reveal_instachats_desc,
                            id = BenefitsPremiumAdapter.PremiumPaywallList.REVEAL_INSTACHATS
                        )
                    )
                }


                //UNDO
                list.add(
                    GetInteractionsModel(
                        R.drawable.premium_paywall_ic_undo,
                        R.string.unlimited_undo_premium,
                        R.string.unlimited_undo_premium_description,
                        id = BenefitsPremiumAdapter.PremiumPaywallList.UNDO_ITEM
                    )
                )


                //VIDEO CALL
                list.add(
                    GetInteractionsModel(
                        R.drawable.ic_premium_video_call,
                        if (videoCallCounterResetTime == -1L) R.string.unlimited_calls_premium else R.string.out_of_credits_for_x,
                        R.string.go_premium_for_ten_hours_calls,
                        videoCallCounterResetTime,
                        id = BenefitsPremiumAdapter.PremiumPaywallList.VIDEO_CALLS_ITEM
                    ).apply {
                        if (videoCallCounterResetTime != -1L) titleText =
                            getString(R.string.out_of_credits_for_x, videoCallName)
                    }
                )

                //AD FREE
                if (RemoteConfigUtils.isAdsEnabled()) {
                    list.add(
                        GetInteractionsModel(
                            R.drawable.premium_paywall_ic_ads_free,
                            R.string.ad_free,
                            R.string.ad_free_description,
                            id = BenefitsPremiumAdapter.PremiumPaywallList.AD_FREE_ITEM
                        )
                    )
                }
            }

            binding.pager.adapter = GetInteractionsAdapter(list, PREMIUM, homeViewModel)
            binding.tabLayout.let { tabLayout ->
                binding.pager.let { viewPager ->
                    tabLayoutMediator = TabLayoutMediator(tabLayout, viewPager) { _, _ -> }
                    tabLayoutMediator?.attach()
                }
            }

            var indexPosition = list.indexOfFirst { it.id == pagerStartItem }
            if (indexPosition == -1) indexPosition = 0

            binding.pager.setCurrentItem(indexPosition, false)
            binding.pager.autoScroll(lifecycleScope, 5000)
        }
        setupPayments()

        viewModel.elapsedTime.observe(viewLifecycleOwner){
            binding.closeBtn.isVisible = false
            binding.closeTimerTxt.isVisible = true
            binding.closeTimerTxt.text = it
        }

        viewModel.onFinishCloseTimer.observe(viewLifecycleOwner){
            if (it) {
                binding.closeBtn.isVisible = true
                binding.closeTimerTxt.isVisible = false
            }
        }

        binding.closeBtn.isVisible = viewModel.getIsCloseTimerShowed()
        binding.closeTimerTxt.isVisible = !viewModel.getIsCloseTimerShowed()

        val eventSource = requireActivity().intent.getStringExtra(EVENT_SOURCE_VALUE)
        val promoCode = (requireActivity() as? PremiumActivity)?.promoCode
        sendGoPremiumScreenViewEvent(eventSource ?: "", presentation?.placementId, promoCode)

        inAppPackageViewModel.planPrices.observe(viewLifecycleOwner) {
            setPrices(dismissProgressBar = true)
        }

        setPrices()

        return binding.root
    }

    private fun setPrices(dismissProgressBar: Boolean = false) {
        val arePlansNull = inAppPackageViewModel.firstPackage?.plan == null ||
                inAppPackageViewModel.secondPackage?.plan == null ||
                inAppPackageViewModel.thirdPackage?.plan == null

        val arePricesZero = inAppPackageViewModel.firstPackage?.plan?.amount() == 0.0 ||
                inAppPackageViewModel.secondPackage?.plan?.amount() == 0.0 ||
                inAppPackageViewModel.thirdPackage?.plan?.amount() == 0.0

        if(arePlansNull || arePricesZero) {

            if(dismissProgressBar)
                spinningCircleDialog?.dismiss()

            return
        }

        try {
            binding.package1Quantity.text = inAppPackageViewModel.firstPackage?.plan?.durationInMonths()?.toInt().toString()
            binding.package2Quantity.text = inAppPackageViewModel.secondPackage?.plan?.durationInMonths()?.toInt().toString()
            binding.package3Quantity.text = inAppPackageViewModel.thirdPackage?.plan?.durationInMonths()?.toInt().toString()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }


        val showPricePerMonthPremiumPaywall = RemoteConfigUtils.getShowPricePerMonthPremiumPaywall()

        val pricePerMonth1 = "${
            getPricePerMonth(
                inAppPackageViewModel.firstPackage?.plan?.localizedPrice()!!,
                inAppPackageViewModel.firstPackage?.plan?.amount()!! * 1_000_000L,
                inAppPackageViewModel.firstPackage?.plan?.durationInMonths()?.toInt()!!
            )
        }/mo"

        val smallTextPrice1 =
            if (showPricePerMonthPremiumPaywall) inAppPackageViewModel.firstPackage?.plan?.localizedPrice() else pricePerMonth1
        val bigTextPrice1 =
            if (showPricePerMonthPremiumPaywall) pricePerMonth1 else inAppPackageViewModel.firstPackage?.plan?.localizedPrice()
        binding.smallTextPrice1.text = smallTextPrice1
        binding.bigTextPrice1.text = bigTextPrice1

        val pricePerMonth2 = "${
            getPricePerMonth(
                inAppPackageViewModel.secondPackage?.plan?.localizedPrice()!!,
                inAppPackageViewModel.secondPackage?.plan?.amount()!! * 1_000_000L,
                inAppPackageViewModel.secondPackage?.plan?.durationInMonths()?.toInt()!!
            )
        }/mo"

        val smallTextPrice2 =
            if (showPricePerMonthPremiumPaywall) inAppPackageViewModel.secondPackage?.plan?.localizedPrice() else pricePerMonth2
        val bigTextPrice2 =
            if (showPricePerMonthPremiumPaywall) pricePerMonth2 else inAppPackageViewModel.secondPackage?.plan?.localizedPrice()
        binding.smallTextPrice2.text = smallTextPrice2
        binding.bigTextPrice2.text = bigTextPrice2


        val pricePerMonth3 = "${
            getPricePerMonth(
                inAppPackageViewModel.thirdPackage?.plan?.localizedPrice()!!,
                inAppPackageViewModel.thirdPackage?.plan?.amount()!! * 1_000_000L,
                inAppPackageViewModel.thirdPackage?.plan?.durationInMonths()?.toInt()!!
            )
        }/mo"

        val smallTextPrice3 =
            if (showPricePerMonthPremiumPaywall) inAppPackageViewModel.thirdPackage?.plan?.localizedPrice() else pricePerMonth3
        val bigTextPrice3 =
            if (showPricePerMonthPremiumPaywall) pricePerMonth3 else inAppPackageViewModel.thirdPackage?.plan?.localizedPrice()
        binding.smallTextPrice3.text = smallTextPrice3
        binding.bigTextPrice3.text = bigTextPrice3


        spinningCircleDialog?.dismiss()
    }

    private fun setupPayments() {
        viewModel.setSelectedPackage(secondPackage)

        binding.package1.setOnSingleClickListener {
            if (viewModel.selectedPackage.value == firstPackage)
                viewModel.buyPackage(firstPackage.productId)
            else
                viewModel.setSelectedPackage(firstPackage)
        }
        binding.package2.setOnSingleClickListener {
            if (viewModel.selectedPackage.value == secondPackage)
                viewModel.buyPackage(secondPackage.productId)
            else
                viewModel.setSelectedPackage(secondPackage)
        }
        binding.package3.setOnSingleClickListener {
            if (viewModel.selectedPackage.value == thirdPackage)
                viewModel.buyPackage(thirdPackage.productId)
            else
                viewModel.setSelectedPackage(thirdPackage)
        }

        viewModel.selectedPackage.observe(viewLifecycleOwner, Observer {
            when (it) {
                firstPackage -> {
                    setActiveStateUI(0)
                }
                secondPackage -> {
                    setActiveStateUI(1)
                }
                thirdPackage -> {
                    setActiveStateUI(2)
                }
                else -> {}
            }
        })
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

                Glide.with(requireContext())
                        .load(R.drawable.premium_background)
                        .into(binding.backgroundImg)
    }


    private fun setActiveStateUI(active: Int) {
        binding.package1.setBackgroundResource(if (active == 0) R.drawable.premium_rounded_rect_active else R.drawable.buy_rounded_rect_darker)
        binding.package2.setBackgroundResource(if (active == 1) R.drawable.premium_rounded_rect_active else R.drawable.buy_rounded_rect_darker)
        binding.package3.setBackgroundResource(if (active == 2) R.drawable.premium_rounded_rect_active else R.drawable.buy_rounded_rect_darker)

        binding.view10.setBackgroundResource(if (active == 1) R.color.pink_500 else R.color.border)
        binding.view111.setBackgroundResource(if (active == 2) R.color.pink_500 else R.color.border)

        binding.popular.setTextColor(if (active == 1) resources.getColor(R.color.pink_500) else resources.getColor(R.color.title_primary))
        binding.bestDeal.setTextColor(if (active == 2) resources.getColor(R.color.pink_500) else resources.getColor(R.color.title_primary))
    }

    private fun initializePackages() {
        firstPackage = GooglePlayPackage.PREMIUM_PACKAGE_1_MONTH
        secondPackage = GooglePlayPackage.PREMIUM_PACKAGE_3_MONTHS
        thirdPackage = GooglePlayPackage.PREMIUM_PACKAGE_6_MONTHS

        Timber.tag("Packages").d("initializePackages: firstPackage : ${firstPackage.productId}")
        Timber.tag("Packages").d("initializePackages: secondPackage : ${secondPackage.productId}")
        Timber.tag("Packages").d("initializePackages: thirdPackage : ${thirdPackage.productId}")
    }


    fun getPricePerMonth(price: String, priceAmountMicros: Double, months: Int): String {
        val ppm = format(Locale.getDefault(), "%.2f", (priceAmountMicros / 1_000_000f) / months);

        val regex = "\\d{1,4}[,.]?(\\d{1,2})?".toRegex()
        val pricePerMonth = price.replace(regex, ppm.toString())

        return pricePerMonth
    }

    private fun getExpirationTimeOf(interactionType: InteractionType): Long? {
        val counterConfigurationNames = homeViewModel.userProfile.value?.counterConfigurationNames
        return when (interactionType) {
            InteractionType.LIKE -> {
                if (!homeViewModel.remainingLikes()) {
                    val counter = homeViewModel.userCounters?.firstOrNull { it.configurationName == counterConfigurationNames?.likeCounterCN }
                    counter?.counter?.resetTime
                        ?: (if (counter != null ) CalculateIntervalTimeInHourUseCase.invoke(counter) else getTimeStampAfterXHours(12)) } else null
            }
            InteractionType.DISLIKE -> {
                if (!homeViewModel.remainingDislikes()) {
                    val counter = homeViewModel.userCounters?.firstOrNull { it.configurationName == counterConfigurationNames?.dislikeCounterCN }
                    counter?.counter?.resetTime
                        ?: (if (counter != null ) CalculateIntervalTimeInHourUseCase.invoke(counter) else getTimeStampAfterXHours(12)) } else null
            }
            InteractionType.INSTA_CHAT -> {
                if (!homeViewModel.remainingInstaChatInteractions()) {
                    val counter = homeViewModel.userCounters?.firstOrNull { it.configurationName == counterConfigurationNames?.instachatCounterCN }
                    counter?.counter?.resetTime
                        ?: (if (counter != null ) CalculateIntervalTimeInHourUseCase.invoke(counter) else getTimeStampAfterXHours(12)) } else null
            }

            InteractionType.IS_FLYING -> {
                if (!homeViewModel.hasRemainingFlights()) {
                    val counter = homeViewModel.userCounters?.firstOrNull { it.configurationName == counterConfigurationNames?.flyCounterCN }
                    counter?.counter?.resetTime
                        ?: (if (counter != null ) CalculateIntervalTimeInHourUseCase.invoke(counter) else getTimeStampAfterXHours(12)) } else null
            }
            else -> null
        }
    }

    private fun getExpirationTimeOfUnblur() : Long? {
        return if (!likedYouViewModel.hasRemainingUnblur())
            likedYouViewModel.unbluredCounterEntity?.counter?.resetTime
                ?: if (likedYouViewModel.unbluredCounterEntity != null) CalculateIntervalTimeInHourUseCase.invoke(likedYouViewModel.unbluredCounterEntity!!) else getTimeStampAfterXHours( 12)
        else
            null
    }

    private fun makeLinksClickable(){
        val firstPairTitle = getString(R.string.terms_of_service)
        val secondPairTitle = getString(R.string.privacy_policy)
        val termsLink = links[firstPairTitle].orEmpty()
        val privacyLink = links[secondPairTitle].orEmpty()
        binding.policyText.makeLinks(null,
            Pair(
                first = firstPairTitle,
                second = View.OnClickListener {
                    context?.openWebView(termsLink, firstPairTitle, requireActivity())
                }
            ),
            Pair(
                first = secondPairTitle,
                second = View.OnClickListener {
                    context?.openWebView(privacyLink, secondPairTitle, requireActivity())
                }
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()

        binding.pager.adapter = null
        tabLayoutMediator?.detach()
        tabLayoutMediator = null

        spinningCircleDialog?.dismiss()
        spinningCircleDialog = null

        presentation?.let { Purchasely.clientPresentationClosed(it) }
        presentation = null

        billingStateListener = null
        billingClient?.endConnection()
        billingClient = null

        _binding = null
    }
}