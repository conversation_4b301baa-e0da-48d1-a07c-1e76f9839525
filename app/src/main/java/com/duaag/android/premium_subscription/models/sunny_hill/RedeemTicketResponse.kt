package com.duaag.android.premium_subscription.models.sunny_hill

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class RedeemTicketResponse(
    @SerializedName("vendor") val vendor: String? = null,
    @SerializedName("identifier") val identifier: String? = null,
    @SerializedName("isRedeemed") val isRedeemed: Boolean? = null,
    @SerializedName("redeemedBy") val redeemedBy: String? = null
)