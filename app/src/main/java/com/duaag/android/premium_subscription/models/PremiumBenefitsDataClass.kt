package com.duaag.android.premium_subscription.models

import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.utils.RemoteConfigUtils

data class PremiumBenefitsDataClass(val image: Int, val title: Int, val description: Int, var descriptionTxt: String? = null)

val premiumBenefitsItemsFemale: List<PremiumBenefitsDataClass>
get() {
        val isAllowedNewLimits = RemoteConfigUtils.isAllowedToUseNewPremiumLimits()

        return mutableListOf(
                PremiumBenefitsDataClass(R.drawable.pose_img_one, R.string.liked_you, R.string.all_revealed),
                PremiumBenefitsDataClass(R.drawable.ic_like_benefit, R.string.impressions, R.string.unlimited),
                PremiumBenefitsDataClass(R.drawable.ic_instachat_icon, R.string.instachats_string, R.string.unlimited),
                PremiumBenefitsDataClass(R.drawable.ic_icon_boost, R.string.boost, R.string.one_per_month).apply { descriptionTxt = DuaApplication.instance.getString(R.string.one_per_month, if (isAllowedNewLimits) 2 else 1) },
                PremiumBenefitsDataClass(R.drawable.ic_flights_icon, R.string.flights, R.string.unlimited),
                PremiumBenefitsDataClass(R.drawable.ic_incognito,R.string.ghost_mode,R.string.ghost_mode_benefit_paywall),
                PremiumBenefitsDataClass(R.drawable.ic_undo_icon, R.string.undo, R.string.unlimited),
                PremiumBenefitsDataClass(R.drawable.ic_unlimited_calls_icon, R.string.calls_string, if (isAllowedNewLimits) R.string.unlimited else R.string.ten_hours_per_month)
            ).apply {
                if (RemoteConfigUtils.isAdsEnabled()){
                    add(PremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.ads, R.string.no_ads))
                }
        }
    }

val premiumBenefitsBigItemsFemale: List<PremiumBenefitsDataClass>
    get() {
        val isAllowedNewLimits = RemoteConfigUtils.isAllowedToUseNewPremiumLimits()
        return  mutableListOf(
            PremiumBenefitsDataClass(R.drawable.pose_img_one, R.string.liked_you, R.string.all_revealed),
            PremiumBenefitsDataClass(R.drawable.ic_like_benefit, R.string.impressions, R.string.unlimited),
            PremiumBenefitsDataClass(R.drawable.ic_instachat_icon, R.string.instachats_string, R.string.unlimited),
            PremiumBenefitsDataClass(R.drawable.ic_icon_boost_background, R.string.boost, R.string.one_per_month).apply { descriptionTxt = DuaApplication.instance.getString(R.string.one_per_month, if (isAllowedNewLimits) 2 else 1) },
            PremiumBenefitsDataClass(R.drawable.ic_flights_icon, R.string.flights, R.string.unlimited),
            PremiumBenefitsDataClass(R.drawable.ic_undo_icon, R.string.undo, R.string.unlimited),
            PremiumBenefitsDataClass(R.drawable.ic_unlimited_calls_icon, R.string.calls_string, if (isAllowedNewLimits) R.string.unlimited else R.string.ten_hours_per_month),
           // PremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.ads, R.string.no_ads),
        ).apply {
            if (RemoteConfigUtils.isAdsEnabled()){
                add(PremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.ads, R.string.no_ads))
            }
        }
    }

val premiumBenefitsItemsMale: List<PremiumBenefitsDataClass>
    get() {
        val isAllowedNewLimits = RemoteConfigUtils.isAllowedToUseNewPremiumLimits()

        return  mutableListOf(
            PremiumBenefitsDataClass(R.drawable.pose_img_one, R.string.liked_you, R.string.all_revealed),
            PremiumBenefitsDataClass(R.drawable.ic_like_benefit, R.string.impressions, if (isAllowedNewLimits) R.string.unlimited else R.string.five_hundred_daily),
            PremiumBenefitsDataClass(R.drawable.ic_instachat_icon, R.string.instachats_string, R.string.five_daily).apply { descriptionTxt = DuaApplication.instance.getString(R.string.five_daily,if (isAllowedNewLimits) 1 else 5) },
            PremiumBenefitsDataClass(R.drawable.ic_icon_boost, R.string.boost, R.string.one_per_month).apply { descriptionTxt = DuaApplication.instance.getString(R.string.one_per_month,
                    if (isAllowedNewLimits) 2 else 1) },
            PremiumBenefitsDataClass(R.drawable.ic_flights_icon, R.string.flights, R.string.unlimited),
            PremiumBenefitsDataClass(R.drawable.ic_undo_icon, R.string.undo, R.string.unlimited),
            PremiumBenefitsDataClass(R.drawable.ic_unlimited_calls_icon, R.string.calls_string, if (isAllowedNewLimits) R.string.unlimited else R.string.ten_hours_per_month),
            //PremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.ads, R.string.no_ads)
        ).apply {
            if (RemoteConfigUtils.isAdsEnabled()){
                add(PremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.ads, R.string.no_ads))
            }
        }
    }

val premiumBenefitsBigItemsMale: List<PremiumBenefitsDataClass>
    get() {
        val isAllowedNewLimits = RemoteConfigUtils.isAllowedToUseNewPremiumLimits()

        return mutableListOf(
            PremiumBenefitsDataClass(R.drawable.pose_img_one, R.string.liked_you, R.string.all_revealed),
            PremiumBenefitsDataClass(R.drawable.ic_like_benefit, R.string.impressions, if (isAllowedNewLimits) R.string.unlimited else R.string.five_hundred_daily),
            PremiumBenefitsDataClass(R.drawable.ic_instachat_icon, R.string.instachats_string, R.string.five_daily).apply { descriptionTxt = DuaApplication.instance.getString(R.string.five_daily,if (isAllowedNewLimits) 1 else 5) },
            PremiumBenefitsDataClass(R.drawable.ic_icon_boost_background, R.string.boost, R.string.one_per_month).apply { descriptionTxt = DuaApplication.instance.getString(R.string.one_per_month,
                    if (isAllowedNewLimits) 2 else 1) },
            PremiumBenefitsDataClass(R.drawable.ic_flights_icon, R.string.flights, R.string.unlimited),
            PremiumBenefitsDataClass(R.drawable.ic_undo_icon, R.string.undo, R.string.unlimited),
            PremiumBenefitsDataClass(R.drawable.ic_unlimited_calls_icon, R.string.calls_string, if (isAllowedNewLimits) R.string.unlimited else R.string.ten_hours_per_month),
          //  PremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.ads, R.string.no_ads)
        ).apply {
            if (RemoteConfigUtils.isAdsEnabled()){
                add(PremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.ads, R.string.no_ads))
            }
        }
    }
