package com.duaag.android.premium_subscription.models

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize


@Keep
@Parcelize
data class PremiumOfferModel(
    @SerializedName("eligibleFrom")
    val eligibleFrom: Long,
    @SerializedName("eligibleUntil")
    val eligibleUntil: Long,
    @SerializedName("isJourney")
    val isJourney: Boolean,
    @SerializedName("lengthInHours")
    val lengthInHours: Int,
    @SerializedName("offerId")
    val offerId: String,
    @SerializedName("offerType")
    val offerType: String,
    @SerializedName("productId")
    val productId: String
) : Parcelable