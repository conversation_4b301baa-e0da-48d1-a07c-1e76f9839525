package com.duaag.android.premium_subscription.di

import com.duaag.android.premium_subscription.data.InAppPurchasesRepositoryImpl
import com.duaag.android.premium_subscription.domain.InAppPurchasesRepository
import dagger.Module
import dagger.Provides
import javax.inject.Singleton

@Module
class InAppPurchasesModule {

    @Provides
    @Singleton
    fun provideInAppPurchasesRepository(inAppPurchasesRepositoryImpl: InAppPurchasesRepositoryImpl): InAppPurchasesRepository {
        return inAppPurchasesRepositoryImpl
    }
}