package com.duaag.android.premium_subscription.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.android.billingclient.api.*
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.clevertap.sendGoPremiumScreenViewEvent
import com.duaag.android.databinding.SpecialOfferPremiumFragmentBinding
import com.duaag.android.home.models.PremiumOfferTypes
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.PremiumActivity.Companion.ANALYTICS_EVENT_FROM
import com.duaag.android.premium_subscription.models.InAppPurchaseItemInfoModel
import com.duaag.android.premium_subscription.viewmodels.PremiumViewModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.*
import com.duaag.android.views.SpinningCircleDialog
import timber.log.Timber
import javax.inject.Inject

class SpecialOfferPremiumFragment : Fragment() {

    companion object {
        const val OFFER_PACKAGE = "offerPackage"

        fun newInstance(): SpecialOfferPremiumFragment {
            val fragment = SpecialOfferPremiumFragment()
            return fragment
        }
    }
/*
    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<PremiumViewModel>({ activity as PremiumActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as PremiumActivity }) { viewModelFactory }

    private var _binding: SpecialOfferPremiumFragmentBinding? = null
    private val binding get() = _binding!!

    private lateinit var offerPackage: String

    private lateinit var links: Map<String?, String>

    private var billingClient: BillingClient? = null
    private var billingStateListener :BillingClientStateListener? = null

    private var spinningCircleDialog: SpinningCircleDialog? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        (requireActivity() as PremiumActivity).premiumComponent.inject(this)
        Timber.tag("VIEWMODEL").d(viewModel.toString())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)


        offerPackage = homeViewModel.getAvailableOffer()
        Timber.tag("OFFER_PACKAGE").d("offerPackage: $offerPackage")

        when(homeViewModel.getOfferType()) {
            PremiumOfferTypes.DONT_LET_GO.offerType -> {
                firebaseLogEvent(FirebaseAnalyticsEventsName.REDEEM_OFFER_VIEW_POPUP)
            }
            PremiumOfferTypes.PAY_1_GET_1.offerType -> {
                firebaseLogEvent(FirebaseAnalyticsEventsName.GET_2FOR1_PAYWALL)
            }
        }

        links = mapOf(
            getString(R.string.terms_of_service) to requireContext().getStringResourceByName(BuildConfig.TERMS_AND_CONDITIONS_LINK_KEY),
            getString(R.string.privacy_policy) to requireContext().getStringResourceByName(BuildConfig.PRIVACY_POLICY_LINK_KEY))

        val callback: OnBackPressedCallback = object : OnBackPressedCallback(true *//* enabled by default *//*) {
            override fun handleOnBackPressed() { // Handle the back button event
                if (viewModel.getIsCloseTimerShowed()){
                    firebaseLogEvent(FirebaseAnalyticsEventsName.SPECIAL_OFFER_POPUP_DISMISSED)
                    activity?.finish()
                }
            }
        }
        requireActivity().onBackPressedDispatcher.addCallback(this, callback)

        duaSharedPrefs.setDontLetGoOfferShown(true)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SpecialOfferPremiumFragmentBinding.inflate(layoutInflater)

        spinningCircleDialog = SpinningCircleDialog(requireContext())
        spinningCircleDialog?.show()

        when(homeViewModel.getOfferType()) {
            PremiumOfferTypes.DONT_LET_GO.offerType, PremiumOfferTypes.SPECIAL_DAYS.offerType -> {
                binding.diamondContainer.visibility = View.INVISIBLE
                binding.heartsContainer.visibility = View.VISIBLE
            }
            PremiumOfferTypes.PAY_1_GET_1.offerType -> {
                binding.diamondContainer.visibility = View.VISIBLE
                binding.heartsContainer.visibility = View.INVISIBLE

                val benefitsContainer: ConstraintLayout = binding.benefitsContainer.parent as ConstraintLayout
                val benefitsConstraints = ConstraintSet()
                benefitsConstraints.clone(benefitsContainer)
                benefitsConstraints.connect(R.id.benefits_container, ConstraintSet.TOP, R.id.hearts_container, ConstraintSet.BOTTOM, convertDpToPixel(30f, requireContext()).toInt())
                benefitsConstraints.applyTo(benefitsContainer)
            }
            else -> {}
        }

        viewModel.setCloseTimer()

        billingClient = BillingClient.newBuilder(requireContext())
            .setListener(PurchasesUpdatedListener { billingResult, purchases -> })
            .enablePendingPurchases()
            .build()


        binding.closeBtn.setOnClickListener {
            if (homeViewModel.getOfferType() == PremiumOfferTypes.PAY_1_GET_1.offerType) {
                firebaseLogEvent(FirebaseAnalyticsEventsName.GET_2FOR1_POPUP_DISMISSED)
            } else {
                firebaseLogEvent(FirebaseAnalyticsEventsName.SPECIAL_OFFER_POPUP_DISMISSED)
            }
            requireActivity().finish()
        }

        binding.premiumBtn.setOnSingleClickListener {
            //log where did user come from
            if (requireActivity().intent.hasExtra(ANALYTICS_EVENT_FROM)) {
                when (val event = requireActivity().intent.getStringExtra(ANALYTICS_EVENT_FROM)){
                    FirebaseAnalyticsEventsName.REDEEM_OFFER_INITIATED_POPUP.value -> firebaseLogEvent(event)
                    FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_GET_2FOR1.value -> firebaseLogEvent(event)
                    else -> firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_SPECIAL_OFFER)
                }
            }

            viewModel.buyPackage(offerPackage)
        }

        getPrice(offerPackage)

        homeViewModel.userProfile.observe(viewLifecycleOwner) {
            if(it != null) {
                setBenefits(it.gender)
            } else {
                setBenefits(DuaAccount.user?.gender ?: GenderType.MAN.value)
            }
        }
        viewModel.elapsedTime.observe(viewLifecycleOwner){
            binding.closeBtn.isVisible = false
            binding.closeTimerTxt.isVisible = true
            binding.closeTimerTxt.text = it
        }

        viewModel.onFinishCloseTimer.observe(viewLifecycleOwner){
            if (it) {
                binding.closeBtn.isVisible = true
                binding.closeTimerTxt.isVisible = false
            }
        }

        binding.closeBtn.isVisible = viewModel.getIsCloseTimerShowed()
        binding.closeTimerTxt.isVisible = !viewModel.getIsCloseTimerShowed()

        val eventSource = requireActivity().intent.getStringExtra(PremiumActivity.EVENT_SOURCE_VALUE)
        sendGoPremiumScreenViewEvent(eventSource ?: "")

        return binding.root
    }

    private fun setBenefits(genderType: String){
        val isAllowedNewLimits = RemoteConfigUtils.isAllowedToUseNewPremiumLimits()
        when(genderType){
            GenderType.MAN.value -> {
                binding.benefit0.text = getString(R.string.one_boost_per_month,if (isAllowedNewLimits) 2 else 1)
                binding.benefit1.setText(R.string.impressions_premium_500)
                binding.benefit2.text = getString(R.string.five_instachats_daily_premium,if (isAllowedNewLimits) 1 else 5)
                binding.benefit3.setText(R.string.unlimited_flights_premium)
                binding.benefit4.setText(R.string.unblurred_liked_you)

                showAllTicks()
            }
            GenderType.WOMAN.value -> {
                binding.benefit0.text =  getString(R.string.one_boost_per_month,if (isAllowedNewLimits) 2 else 1)
                binding.benefit1.setText(R.string.unlimited_impressions_premium)
                binding.benefit2.setText(R.string.unlimited_instachats_premium)
                binding.benefit3.setText(R.string.unlimited_flights_premium)
                binding.benefit4.setText(R.string.unblurred_liked_you)

                showAllTicks()
            }
        }
    }

    private fun showAllTicks() {
        binding.benefitImage0.visibility = View.VISIBLE
        binding.benefitImage1.visibility = View.VISIBLE
        binding.benefitImage2.visibility = View.VISIBLE
        binding.benefitImage3.visibility = View.VISIBLE
        binding.benefitImage4.visibility = View.VISIBLE
    }

    private fun getPrice(productId: String) {
        billingStateListener = object:BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    val productList = listOf(
                        QueryProductDetailsParams.Product.newBuilder()
                        .setProductId(productId)
                        .setProductType(BillingClient.ProductType.SUBS)
                        .build()
                    )



                    val params = QueryProductDetailsParams.newBuilder().setProductList(productList)

                    billingClient?.queryProductDetailsAsync(params.build()) {
                            billingResult,
                            productDetailsList ->
                        Timber.tag("BILLING").d("listSize: ${productDetailsList.size} billingResult: $billingResult productDetailsList: $productDetailsList")
                        lifecycleScope.launchWhenResumed {

                            // Process the result
                            productDetailsList.forEach { productDetails ->
                                val offer = productDetails.subscriptionOfferDetails?.get(0)?.pricingPhases?.pricingPhaseList

                                val offerPriceMicros = offer?.get(0)?.priceAmountMicros
                                val regularPriceMicros = offer?.get(1)?.priceAmountMicros

                                val offerPrice = offer?.get(0)?.formattedPrice
                                val regularPrice = offer?.get(1)?.formattedPrice
                                homeViewModel.addInAppPurchaseItem(productDetails.productId,
                                    InAppPurchaseItemInfoModel(productDetails.productId,
                                        ((offerPriceMicros?:0L) / 1_000_000f).toString(),
                                        (offer?.get(0)?.priceCurrencyCode?:"")))

                                if (regularPriceMicros != null && offerPriceMicros != null) {
                                    val discountPercentage = "${((offerPriceMicros.toDouble() / regularPriceMicros) * 100).toInt()}%"

                                    coloredText(
                                        textView = binding.offerDiscountTitle,
                                        baseText = getString(R.string.special_offer_profile_view, discountPercentage),
                                        coloredText = discountPercentage,
                                        targetColor = R.color.pink_500
                                    )

                                }
                                offerPrice?.let { binding.offerPrice.text = it }
                                regularPrice?.let { binding.regularPrice.text = "$it / ${getString(R.string.regular_price)}" }

                                binding.duration.text =
                                    if(homeViewModel.getOfferType() == PremiumOfferTypes.PAY_1_GET_1.offerType)
                                        "/ ${getString(R.string.first_2_months)}"
                                    else
                                        "/ ${getString(R.string.first_month)}"

                                val policyText = when(homeViewModel.getOfferType()) {
                                    PremiumOfferTypes.PAY_1_GET_1.offerType -> getString(R.string.pay_1_get_2_privacy_policy_android, offerPrice, regularPrice)
                                    else -> getString(R.string.special_offers_privacy_policy_android, offerPrice, regularPrice)
                                }

                                binding.policyText.text = policyText
                                makeLinksClickable()
                            }
                            spinningCircleDialog?.dismiss()
                        }
                    }
                } else if (billingResult.responseCode == BillingClient.BillingResponseCode.BILLING_UNAVAILABLE
                    || billingResult.responseCode == BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE) {
                    ToastUtil.toast("Billing not available")
                    spinningCircleDialog?.dismiss()
                    requireActivity().finish()
                }
            }

            override fun onBillingServiceDisconnected() {
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
                spinningCircleDialog?.dismiss()
            }
        }
        billingStateListener?.let { billingClient?.startConnection(it) }

    }

    private fun makeLinksClickable(){
        val firstPairTitle = getString(R.string.terms_of_service)
        val secondPairTitle = getString(R.string.privacy_policy)
        val termsLink = links[firstPairTitle].orEmpty()
        val privacyLink = links[secondPairTitle].orEmpty()
        binding.policyText.makeLinks(
            Pair(
                first = firstPairTitle,
                second = View.OnClickListener {
                    context?.openWebView(termsLink, firstPairTitle, requireActivity())
                }
            ),
            Pair(
                first = secondPairTitle,
                second = View.OnClickListener {
                    context?.openWebView(privacyLink, secondPairTitle, requireActivity())
                }
            ),
            textColor = R.color.title_primary
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        billingStateListener = null
        billingClient?.endConnection()
        billingClient = null
        _binding = null
        spinningCircleDialog = null
    }*/
}