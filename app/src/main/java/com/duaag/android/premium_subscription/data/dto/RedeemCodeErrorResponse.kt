package com.duaag.android.premium_subscription.data.dto

import com.duaag.android.api.ResponseError

enum class RedeemCodeErrorType(override val value: String) : ResponseError {
	PROMO_CODE_NOT_FOUND_ERROR("promo_code_not_found_error"),
	PROMO_CODE_REDEEM_LIMIT_REACHED_ERROR("promo_code_redeem_limit_reached_error"),
	PROMO_CODE_EXPIRED_ERROR("promo_code_expired_error"),
	NO_NETWORK_CONNECTION("no_network_connection"),
	AN_ERROR_OCCURRED("an_error_occurred")
}