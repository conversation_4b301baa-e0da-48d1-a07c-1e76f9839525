package com.duaag.android.premium_subscription.viewmodels

import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.duaag.android.BuildConfig
import com.duaag.android.di.ActivityScope
import com.duaag.android.home.models.GooglePlayPackage
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.getPackageTypeForId
import com.duaag.android.utils.livedata.SingleLiveData
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@ActivityScope
class PremiumViewModel @Inject constructor(private val locationsRepository: DuaSharedPrefs,
                                           val userRepository: UserRepository) : ViewModel() {


    private val _selectedPackage: SingleLiveData<GooglePlayPackage> = SingleLiveData()
    val selectedPackage: LiveData<GooglePlayPackage>
        get() = _selectedPackage

    private val _buyPackage: SingleLiveData<String> = SingleLiveData()
    val buyPackage: LiveData<String>
        get() = _buyPackage

    fun buyPackage(item: String) {
        _buyPackage.value = item

        val eventName = "Get_${getPackageTypeForId(item)}_Initiate"
        val propertyName = item.substringAfter("${BuildConfig.APPLICATION_ID.substringBeforeLast(".")}.").replace(".", "_")
        firebaseLogEvent(eventName, mapOf(propertyName to 1L, FirebaseAnalyticsParameterName.PRODUCT_ID.value to propertyName))
    }

    fun setSelectedPackage(type: GooglePlayPackage) {
        _selectedPackage.value = type
    }


    private var timer: CountDownTimer? = null
    private var isCloseTimerShowed = false
    private val _elapsedTime: MutableLiveData<String> = MutableLiveData("3")
    val elapsedTime: LiveData<String>
        get() = _elapsedTime
    private val _onFinishCloseTimer: MutableLiveData<Boolean> = MutableLiveData()
    val onFinishCloseTimer: LiveData<Boolean>
        get() = _onFinishCloseTimer

    fun setCloseTimer() {
        if (isCloseTimerShowed)
            return
        else {
            createTimer()
        }
    }

    private fun createTimer() {
        val triggerTime = System.currentTimeMillis() + 3000
        timer = object : CountDownTimer(triggerTime - System.currentTimeMillis(), 1000) {
            override fun onTick(p0: Long) {
                val seconds = TimeUnit.MILLISECONDS.toSeconds(p0)
                _elapsedTime.value = (seconds + 1).toString()
            }

            override fun onFinish() {
                onFinishCloseTime()
            }


        }
        timer?.start()
    }

    private fun onFinishCloseTime() {
        timer?.cancel()
        isCloseTimerShowed = true
        _onFinishCloseTimer.value = true
    }

    fun getIsCloseTimerShowed(): Boolean = isCloseTimerShowed

}