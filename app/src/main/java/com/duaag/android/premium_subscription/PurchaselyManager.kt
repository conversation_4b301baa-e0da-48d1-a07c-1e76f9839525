package com.duaag.android.premium_subscription

import android.app.Activity
import android.content.Intent
import android.view.View
import androidx.core.content.ContextCompat
import com.appsflyer.AppsFlyerLib
import com.clevertap.android.sdk.CleverTapAPI
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.offers.getPlacementForOffer
import com.duaag.android.premium_subscription.PremiumActivity.Companion.PURCHASELY_TAG
import com.duaag.android.premium_subscription.PurchaselyManager.BIRTHDATE
import com.duaag.android.premium_subscription.PurchaselyManager.COMMUNITY_ID
import com.duaag.android.premium_subscription.PurchaselyManager.GENDER
import com.duaag.android.premium_subscription.PurchaselyManager.IS_DARK_MODE_ENABLED
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.PresentationCacheEntry
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getDateObjectFromString
import com.duaag.android.utils.haveMoreThanXMinutesPassedFrom
import com.duaag.android.utils.isDarkModeEnabled
import com.google.firebase.analytics.FirebaseAnalytics
import io.purchasely.ext.Attribute
import io.purchasely.ext.LogLevel
import io.purchasely.ext.PLYAlertMessage
import io.purchasely.ext.PLYEvent
import io.purchasely.ext.PLYPresentation
import io.purchasely.ext.PLYPresentationType
import io.purchasely.ext.PLYRunningMode
import io.purchasely.ext.PLYUIHandler
import io.purchasely.ext.PLYUIViewType
import io.purchasely.ext.Purchasely
import io.purchasely.google.GoogleStore
import timber.log.Timber
import java.util.Date
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

object PurchaselyManager {


    //region User Attributes
    const val GENDER = "gender"
    const val COMMUNITY_ID = "community_id"
    const val IS_SUBSCRIPTION_ENDING = "IsSubscriptionEnding"
    const val BIRTHDATE = "birthDate"
    const val PROFILE_CREATED_AT = "profileCreatedAt"
    const val LIKES_ROUGHLY_COUNT = "likesRoughlyCount"
    const val HAS_USER_ALREADY_BOUGHT_DONT_LET_GO = "hasUserAlreadyBoughtDontLetGo"
    const val SUBSCRIPTION_END_DATE = "subscriptionEndDate"
    const val IS_DARK_MODE_ENABLED = "isDarkModeEnabled"
    const val MATCH_COUNT = "matchCount"
    const val COMPLETED_PROFILE_PERCENTAGE = "completedProfilePercentage"
    const val INITIATED_INSTACHAT_COUNT = "initiatedInstachatCount"
    const val RECEIVED_INSTACHAT_COUNT = "receivedInstachatCount"
    //endregion
    val PREFETCH_PRESENTATION_LIST = listOf(
        PurchaselyPlacement.SEE_WHO_LIKED_YOU.id,
        PurchaselyPlacement.PROFILE_GO_PREMIUM_BUTTON.id,
        PurchaselyPlacement.OUT_OF_IMPRESSIONS.id,
        PurchaselyPlacement.DYNAMIC_BOOST.id,
        PurchaselyPlacement.DYNAMIC_INSTACHAT.id,
        PurchaselyPlacement.PREMIUM_SUBSCRIPTION_PREMIUM_BADGE.id,
        PurchaselyPlacement.SCROLLED_TO_BLURRED_FEATURED_PROFILE.id
    )
    const val PRESENTATION_CACHE_EXPIRE_TIME_MINUTES = 60L


    private val presentationCache: ConcurrentHashMap<String, PresentationCacheEntry> = ConcurrentHashMap()

    fun cachePresentation(presentation: PLYPresentation) {
        presentation.placementId?.let {
            val currentTime = System.currentTimeMillis()
            presentationCache[it] = PresentationCacheEntry(presentation, currentTime)
            Timber.tag(PURCHASELY_TAG).d("presentationCached ${presentation.placementId}")
        }
    }

    fun getCachedPresentation(placementId: String): PresentationCacheEntry? {
        return presentationCache[placementId]?.takeIf { !hasCacheExpired(placementId) }
    }

    fun getCachedTime(placementId: String): Long? {
        return presentationCache[placementId]?.cachedTime
    }

    fun getOldestCacheTime(): Long? {
        return presentationCache.values.minByOrNull { it.cachedTime }?.cachedTime
    }

    fun hasCacheExpired(placementId: String): Boolean {
        val cachedTime = getCachedTime(placementId)
        val durationInMillis = TimeUnit.MINUTES.toMillis(PRESENTATION_CACHE_EXPIRE_TIME_MINUTES)
        return cachedTime == null || cachedTime + durationInMillis < System.currentTimeMillis()
    }

    private fun preFetchPresentations(placementIds: List<String>) {
        placementIds.forEach { preFetchPresentation(it) }
    }

    fun reFetchPaywallsIfExpired() {
        val oldestCacheTime = getOldestCacheTime()
        if(oldestCacheTime == null || haveMoreThanXMinutesPassedFrom(oldestCacheTime, PRESENTATION_CACHE_EXPIRE_TIME_MINUTES)) {
            preFetchPresentations(PREFETCH_PRESENTATION_LIST)
        }
    }

    private fun preFetchPresentation(placementId: String) {
        Timber.tag(PURCHASELY_TAG).d("fetching presentation $placementId")
        Purchasely.fetchPresentation(placementId) { presentation, error ->
            if(error != null) {
                Timber.tag(PURCHASELY_TAG).e("${error.message}")
                return@fetchPresentation
            }
            if (presentation == null) return@fetchPresentation

            Timber.tag(PURCHASELY_TAG).d("presentation fetched $placementId")

            when(presentation.type) {
                PLYPresentationType.NORMAL,
                PLYPresentationType.FALLBACK -> {
                    cachePresentation(presentation)
                }
                else -> {
                    //No Screen, it means an error was triggered
                }
            }
        }
    }

    val eventKeysToInclude = listOf(
        "ab_test_variant_id",
        "audience",
        "audience_id",
        "cumulated_revenues_in_eur",
        "device_type",
        "displayed_presentation",
        "environment",
        "error_message",
        "event_name",
        "is_fallback_presentation",
        "placement",
        "placement_id",
        "plan",
        "presentation",
        "previous_offer_type",
        "product",
        "purchase_type",
        "selected_plan",
        "store",
        "store_country",
        "store_product_id",
        "subscription_status",
        "ab_test_variant")

    private val eventListener by lazy {
        object : io.purchasely.ext.EventListener {
            override fun onEvent(event: PLYEvent) {
                when (event) {
                    //Paywall
                    is PLYEvent.PresentationViewed,
                    is PLYEvent.PlanSelected,
                    is PLYEvent.PurchaseTapped,
                    is PLYEvent.PurchaseCancelled,
                        //Payment
                    is PLYEvent.InAppPurchased,
                    is PLYEvent.InAppPurchasing,
                    is PLYEvent.InAppPurchaseFailed,
                    is PLYEvent.InAppDeferred,
                    is PLYEvent.PurchaseCancelledByApp,
                        //Restore purchase
                    is PLYEvent.RestoreFailed,
                    is PLYEvent.RestoreStarted,
                    is PLYEvent.RestoreSucceeded,
                        //Cancellation
                    is PLYEvent.CancellationReasonPublished,
                    is PLYEvent.SubscriptionCancelTapped,
                    is PLYEvent.SubscriptionDetailsViewed,
                    is PLYEvent.SubscriptionPlanTapped,
                    is PLYEvent.SubscriptionListViewed,
                    is PLYEvent.PresentationClosed -> {
                        val eventProperties = event.properties.toMap()
                        val filteredProperties = eventProperties?.filterKeys { eventKey->
                            eventKey in eventKeysToInclude
                        }
                        if(!filteredProperties.isNullOrEmpty()) {
                            CleverTapAPI.getDefaultInstance(DuaApplication.instance)
                                ?.pushEvent(event.name, filteredProperties)
                        }
                    }

                    else -> {
                        /* not used */
                    }
                }
            }
        }
    }

    fun startPurchasely(userModel: UserModel? = DuaAccount.user, onStart: () -> Unit) {
        Purchasely.start { configured, error ->
            userModel?.let {
                Purchasely.userLogin(userModel.cognitoUserId)
                setPurchaselyUserAttributes(userModel)

                if (userModel.premiumType == null)
                    preFetchPresentations(PREFETCH_PRESENTATION_LIST)
            }

            onStart()
            Timber.tag("START_PURCHASELY")
                .d("Purchasely is configured: $configured - error: $error readyToOpenDeepLinks: ${Purchasely.readyToOpenDeeplink}")
        }

        setPurchaselyReadyToPurchase()
    }

    fun setupPurchasely(userId: String? = null) {

        val appContext = DuaApplication.instance.applicationContext

        // Initialize Purchasely SDK
        val purchaselyBuilder = Purchasely.Builder(appContext)
            .apiKey(appContext.getString(R.string.purchasely_key))
            .logLevel(LogLevel.INFO) // set to warning or error for release
            .runningMode(PLYRunningMode.PaywallObserver)
            .stores(listOf(GoogleStore()))

        userId?.let {
            purchaselyBuilder.userId(it)
        }
        purchaselyBuilder.build()

        Purchasely.eventListener = eventListener

        Purchasely.uiHandler = object: PLYUIHandler {
            override fun onPresentation(presentation: PLYPresentation, proceed: () -> Unit) {
                super.onPresentation(presentation, proceed)
            }

            override fun onAlert(
                alert: PLYAlertMessage,
                purchaselyView: View,
                activity: Activity?,
                proceed: () -> Unit
            ) {
                super.onAlert(alert, purchaselyView, activity, proceed)
                when(alert) {
                    else -> {}
                }
            }
        }

        Purchasely.start { configured, error ->
            Timber.tag("Purchasely").d("Purchasely setup started: configured=$configured | error=$error")
        }

        setAnalyticsProviderIds()
    }

    private fun setAnalyticsProviderIds() {
        val appContext = DuaApplication.instance.applicationContext

        val cleverTap = CleverTapAPI.getDefaultInstance(appContext)
        cleverTap?.cleverTapID?.let {
            Purchasely.setAttribute(Attribute.CLEVER_TAP_ID, it)
        }

        AppsFlyerLib.getInstance().getAppsFlyerUID(appContext)?.let {
            Purchasely.setAttribute(Attribute.APPSFLYER_ID, it)
        }

        FirebaseAnalytics.getInstance(appContext).appInstanceId.addOnSuccessListener { appInstanceId ->
            appInstanceId?.let {
                Purchasely.setAttribute(Attribute.FIREBASE_APP_INSTANCE_ID, it)
            }
        }
    }

    private fun setPurchaselyReadyToPurchase() {
        Purchasely.readyToOpenDeeplink = true
    }

    fun setPurchaselyUserAttributes(
        userModel: UserModel,
        isSubscriptionEnding: Boolean = false
    ) {
        val birthdayObject = getDateObjectFromString(userModel.birthDate)
        val createdProfileAt = userModel.profile.createdAtTime

        Purchasely.language = Locale(userModel.language)

        Purchasely.setUserAttribute(IS_DARK_MODE_ENABLED, isDarkModeEnabled(DuaApplication.instance.applicationContext))
        Purchasely.setUserAttribute(GENDER, if (userModel.gender == GenderType.MAN.value) "male" else "female")
        Purchasely.setUserAttribute(COMMUNITY_ID, userModel.communityInfo?.id ?: "")
        Purchasely.setUserAttribute(IS_SUBSCRIPTION_ENDING, isSubscriptionEnding)
        Purchasely.setUserAttribute(COMPLETED_PROFILE_PERCENTAGE, userModel.profilePercentage)
        birthdayObject?.let { Purchasely.setUserAttribute(BIRTHDATE, it) }
        createdProfileAt?.let { Purchasely.setUserAttribute(PROFILE_CREATED_AT, Date(it * 1000)) }
    }

    fun setTotalMatchesCount(totalMatches: Int) {
        Purchasely.setUserAttribute(MATCH_COUNT, totalMatches)
    }

    fun setInstachatCount(initiatedCount: Int, receivedCount: Int) {
        Purchasely.setUserAttribute(INITIATED_INSTACHAT_COUNT, initiatedCount)
        Purchasely.setUserAttribute(RECEIVED_INSTACHAT_COUNT, receivedCount)
    }

    fun logOut() {
        Purchasely.userLogout()
    }

    fun changeLanguage(language: String) {
        Purchasely.language = Locale(language)
    }

    fun setDontLetGoOfferData(subscriptionEndDate: Date?, hasUserAlreadyBoughtDontLetGo: Boolean) {
        Purchasely.setUserAttribute(
            HAS_USER_ALREADY_BOUGHT_DONT_LET_GO,
            hasUserAlreadyBoughtDontLetGo
        )
        subscriptionEndDate?.let { Purchasely.setUserAttribute(SUBSCRIPTION_END_DATE, it) }
    }

    fun clearCache() {
        presentationCache.clear()
    }
}

fun Activity.openPremiumPaywall(
    viewPagerStartPosition: BenefitsPremiumAdapter.PremiumPaywallList = BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM,
    eventSourceClevertap: ClevertapEventSourceValues?,
    placementId: String,
    userModel: UserModel? = null,
    purchaselyIntent: Intent = Intent(this, PremiumActivity::class.java),
    isFromImpressions: Boolean = false,
    promoCode: String? = null
) {
    val overriddenPlacement = getOverriddenPlacement(placementId)

    if(!NetworkChecker.isNetworkConnected(this)) {
        ToastUtil.toast(R.string.no_internet_connection)
        return
    }

    if(DuaApplication.instance.getBillingAvailable().not()) {
        Timber.tag(PURCHASELY_TAG).e("BILLING NOT AVAILABLE")
        return
    }


    fun startActivity() {
        val cachedPresentation = PurchaselyManager.getCachedPresentation(overriddenPlacement)
        cachedPresentation?.presentation?.placementId?.let {
            if(!PurchaselyManager.hasCacheExpired(it))
                purchaselyIntent.putExtra(PremiumActivity.PLACEMENT, cachedPresentation.presentation)
        }
        purchaselyIntent.putExtra(PremiumActivity.VIEW_PAGER_START_POSITION, viewPagerStartPosition)
        eventSourceClevertap?.let { purchaselyIntent.putExtra(PremiumActivity.EVENT_SOURCE_VALUE, eventSourceClevertap.value) }
        purchaselyIntent.putExtra(PremiumActivity.PLACEMENT_ID, overriddenPlacement)
        purchaselyIntent.putExtra(PremiumActivity.IS_FROM_IMPRESSIONS, isFromImpressions)
        purchaselyIntent.putExtra(PremiumActivity.PROMO_CODE, promoCode)
        purchaselyIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivityForResult(purchaselyIntent, PremiumActivity.PREMIUM_REQUEST_CODE)
    }

    if(Purchasely.userAttribute(GENDER) == null ||
        Purchasely.userAttribute(BIRTHDATE) == null ||
        Purchasely.userAttribute(COMMUNITY_ID) == null ||
        Purchasely.userAttribute(IS_DARK_MODE_ENABLED) == null) {

        PurchaselyManager.startPurchasely(userModel = userModel) {
            startActivity()
        }
    } else {
        startActivity()
    }
}

fun Activity.openPremiumPaywallAsync(
    viewPagerStartPosition: BenefitsPremiumAdapter.PremiumPaywallList = BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM,
    eventSourceClevertap: ClevertapEventSourceValues?,
    placementId: String,
    userModel: UserModel? = null,
    purchaselyIntent: Intent = Intent(this, PremiumActivity::class.java),
    isFromImpressions: Boolean = false,
    promoCode: String? = null
) {
    val overriddenPlacement = getOverriddenPlacement(placementId)

    if(!NetworkChecker.isNetworkConnected(this)) {
        ToastUtil.toast(R.string.no_internet_connection)
        return
    }

    if(DuaApplication.instance.getBillingAvailable().not()) {
        Timber.tag(PURCHASELY_TAG).e("BILLING NOT AVAILABLE")
        return
    }


    fun startActivity() {
        purchaselyIntent.putExtra(PremiumActivity.VIEW_PAGER_START_POSITION, viewPagerStartPosition)
        eventSourceClevertap?.let { purchaselyIntent.putExtra(PremiumActivity.EVENT_SOURCE_VALUE, eventSourceClevertap.value) }
        purchaselyIntent.putExtra(PremiumActivity.PLACEMENT_ID, overriddenPlacement)
        purchaselyIntent.putExtra(PremiumActivity.IS_FROM_IMPRESSIONS, isFromImpressions)
        purchaselyIntent.putExtra(PremiumActivity.PROMO_CODE, promoCode)
        purchaselyIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)

        val cachedPresentation = PurchaselyManager.getCachedPresentation(overriddenPlacement)
        if (cachedPresentation != null) {
            purchaselyIntent.putExtra(PremiumActivity.PLACEMENT, cachedPresentation.presentation)
            startActivityForResult(purchaselyIntent, PremiumActivity.PREMIUM_REQUEST_CODE)
        } else {
            Timber.tag(PURCHASELY_TAG).d("fetching presentation $placementId")
            Purchasely.fetchPresentation(placementId = overriddenPlacement) { presentation, error ->
                if (error != null) {
                    Timber.tag(PURCHASELY_TAG).e("${error.message}")
                    error.printStackTrace()
                    return@fetchPresentation
                }

                if (presentation == null) return@fetchPresentation
                Timber.tag(PURCHASELY_TAG).d("presentation fetched $placementId")

                PurchaselyManager.cachePresentation(presentation)
                Timber.tag(PURCHASELY_TAG).d("plans ${presentation.plans}")

                if (presentation.type == PLYPresentationType.DEACTIVATED) {
                    Timber.tag(PURCHASELY_TAG).d("Paywall is deactivated")
                } else {
                    purchaselyIntent.putExtra(PremiumActivity.PLACEMENT, presentation)
                    startActivityForResult(purchaselyIntent, PremiumActivity.PREMIUM_REQUEST_CODE)
                }
            }
        }
    }

    if(Purchasely.userAttribute(GENDER) == null ||
        Purchasely.userAttribute(BIRTHDATE) == null ||
        Purchasely.userAttribute(COMMUNITY_ID) == null ||
        Purchasely.userAttribute(IS_DARK_MODE_ENABLED) == null) {

        PurchaselyManager.startPurchasely(userModel = userModel) {
            startActivity()
        }
    } else {
        startActivity()
    }
}

fun Activity.showBillingNotAvailable(title:String,message:String=this.getString(R.string.billings_not_available_desc_an)) {
    val builder = androidx.appcompat.app.AlertDialog.Builder(this)

    builder.apply {

        setTitle(title)
            .setMessage(message)
            .setNegativeButton(resources.getString(R.string.cancel)) { dialog, which ->
                dialog.cancel()
            }
            .setPositiveButton(resources.getString(R.string.i_understand)) { dialog, which ->
                dialog.cancel()
            }
    }
    return builder.create().run {
        setOnShowListener {
            getButton(androidx.appcompat.app.AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.title_primary))
            getButton(androidx.appcompat.app.AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(context, R.color.title_primary))
        }
        show()
    }
}

fun getOverriddenPlacement(placementId: String): String {
    val sharedPrefs = DuaSharedPrefs(DuaApplication.instance)
    val realTimeOffer = when(placementId) {
        PurchaselyPlacement.DYNAMIC_BOOST.id -> sharedPrefs.getRealTimeClevertapBoostOffer() ?: sharedPrefs.getRealTimeClevertapPremiumOffer()
        PurchaselyPlacement.DYNAMIC_INSTACHAT.id -> sharedPrefs.getRealTimeClevertapInstachatOffer() ?: sharedPrefs.getRealTimeClevertapPremiumOffer()
        PurchaselyPlacement.DYNAMIC_IMPRESSIONS.id -> sharedPrefs.getRealTimeClevertapImpressionsOffer() ?: sharedPrefs.getRealTimeClevertapPremiumOffer()
        PurchaselyPlacement.DYNAMIC_UNDO.id -> sharedPrefs.getRealTimeClevertapUndoOffer() ?: sharedPrefs.getRealTimeClevertapPremiumOffer()
        PurchaselyPlacement.DYNAMIC_FLIGHT.id -> sharedPrefs.getRealTimeClevertapFlightOffer() ?: sharedPrefs.getRealTimeClevertapPremiumOffer()
        else -> sharedPrefs.getRealTimeClevertapPremiumOffer()
    }
    val overriddenPlacement = getPlacementForOffer(realTimeOffer, placementId)

    return overriddenPlacement
}