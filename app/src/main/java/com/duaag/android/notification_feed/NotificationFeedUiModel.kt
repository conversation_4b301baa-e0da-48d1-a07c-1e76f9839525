package com.duaag.android.notification_feed

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.duaag.android.firebase.NotificationType

data class NotificationFeedUiModel(val id: String, val title: String, val type: NotificationType, val data: Any?, @StringRes val actionTitleResId: Int?, @DrawableRes val notificationImage: Int, val timeAgo: String, val createdAt: Long, val hasProfilePicture: Boolean = false, val read: Boolean=false, val alreadyVisited: <PERSON>olean= false)

