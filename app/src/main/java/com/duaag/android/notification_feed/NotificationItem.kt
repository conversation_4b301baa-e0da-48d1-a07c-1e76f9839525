package com.duaag.android.notification_feed

sealed class NotificationItem {
    abstract val id: String

    data class Section(val title: String,override val id: String = title.hashCode().toString()
        ) : NotificationItem()
    data class Row(
        val notificationFeedModel: NotificationFeedUiModel, override val id: String= notificationFeedModel.id, val hasProfilePicture: Boolean, val isPremium: Boolean
    ) : NotificationItem()

    object Shimmer : NotificationItem() {
        override val id: String
            get() = hashCode().toString()
    }

    object Empty : NotificationItem() {
        override val id: String
            get() = hashCode().toString()
    }
}