package com.duaag.android.notification_feed

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.duaag.android.R
import com.duaag.android.chat.model.ConversationData
import com.duaag.android.clevertap.CrossPathCreatedNotificationModel
import com.duaag.android.clevertap.ProfileVisitedModel
import com.duaag.android.databinding.NotificationFeedEmptyItemBinding
import com.duaag.android.databinding.NotificationFeedRowItemBinding
import com.duaag.android.databinding.NotificationFeedRowPictureItemBinding
import com.duaag.android.databinding.NotificationFeedSectionItemBinding
import com.duaag.android.databinding.NotificationFeedShimmerItemBinding
import com.duaag.android.firebase.NotificationType
import com.duaag.android.firebase.model.UserLikedYouNotificationResponse
import com.duaag.android.firebase.model.UserMatchNotificationResponse
import com.duaag.android.utils.CircleBorderTransformation
import com.duaag.android.utils.getS3Url
import com.duaag.android.utils.imageUrlCircle
import com.duaag.android.utils.newTypeFace
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setTextColorRes
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date


class NotificationFeedAdapter(
    private val notificationActionClickListener: (NotificationFeedUiModel)-> Unit,
    private val onNotificationSeen: (NotificationFeedUiModel) -> Unit
    ) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val differ: AsyncListDiffer<NotificationItem> = AsyncListDiffer(this, object : DiffUtil.ItemCallback<NotificationItem>() {
        override fun areItemsTheSame(oldItem: NotificationItem, newItem: NotificationItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: NotificationItem, newItem: NotificationItem): Boolean {
            return oldItem == newItem
        }
    })

    private val adapterScope = CoroutineScope(Dispatchers.Default)
    fun submitData(context: Context, state: NotificationFeedUiState) {
        adapterScope.launch {
            val items = when {
                state.isLoading && state.paginationData.nextCursor.isNullOrEmpty() -> listOf(NotificationItem.Shimmer)
                state.items.isEmpty() -> listOf(NotificationItem.Empty)
                else -> {
                    val categorizedItems = mutableListOf<NotificationItem>()
                    val list = state.items
                    val dayInMillis = (1000 * 60 * 60 * 24)
                    val currentDate = Date()
                    val groupedItems = list.groupBy {
                        val dateCreated = Date(it.createdAt)
                        val timeDifference = currentDate.time - dateCreated.time
                        val days = (timeDifference / dayInMillis).toInt()
                        when {
                            days < 1 -> context.getString(R.string.today_section)
                            days == 1 -> context.getString(R.string.yesterday_section)
                            days in 2..7 -> context.getString(R.string.this_week_section)
                            else -> context.getString(R.string.this_month_section)
                        }
                    }

                    groupedItems.forEach { (sectionTitle, items) ->
                        categorizedItems.add(NotificationItem.Section(sectionTitle))
                        categorizedItems.addAll(items.map { item ->
                            NotificationItem.Row(
                                item,
                                hasProfilePicture = item.hasProfilePicture,
                                isPremium = state.isCurrentUserPremium
                            )
                        }.distinctBy { it.id })
                    }
                    categorizedItems
                }
            }
            withContext(Dispatchers.Main) {
                differ.submitList(items)
            }
        }
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            SECTION_VIEW -> SectionViewHolder(NotificationFeedSectionItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            ITEM_VIEW -> RowViewHolder(NotificationFeedRowItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            ITEM_VIEW_PICTURE -> RowPictureViewHolder(NotificationFeedRowPictureItemBinding.inflate(LayoutInflater.from(parent.context),parent,false))
            ITEM_VIEW_SHIMMER -> NotificationFeedShimmerViewHolder(NotificationFeedShimmerItemBinding.inflate(LayoutInflater.from(parent.context),parent,false))
            ITEM_VIEW_EMPTY -> NotificationFeedEmptyViewHolder(NotificationFeedEmptyItemBinding.inflate(LayoutInflater.from(parent.context),parent,false))

            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = differ.currentList[position]) {
            is NotificationItem.Section -> (holder as SectionViewHolder).bind(item)
            is NotificationItem.Row ->if(item.hasProfilePicture) (holder as RowPictureViewHolder).bind (item,notificationActionClickListener,onNotificationSeen) else (holder as RowViewHolder).bind(item,notificationActionClickListener,onNotificationSeen)
           else -> {}
        }
    }

    override fun getItemViewType(position: Int): Int {
        val item = differ.currentList[position]
        return when (differ.currentList[position]) {
            is NotificationItem.Section -> SECTION_VIEW
            is NotificationItem.Row -> {
                if((item as NotificationItem.Row).hasProfilePicture) ITEM_VIEW_PICTURE else ITEM_VIEW
            }
            is NotificationItem.Shimmer -> ITEM_VIEW_SHIMMER
            is NotificationItem.Empty -> ITEM_VIEW_EMPTY
        }
    }

    override fun getItemCount(): Int = differ.currentList.size

    inner class SectionViewHolder(private val binding: NotificationFeedSectionItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(section: NotificationItem.Section) {
            binding.sectionTitle.text = section.title
        }
    }

    inner class NotificationFeedShimmerViewHolder constructor(val binding: NotificationFeedShimmerItemBinding) :
        RecyclerView.ViewHolder(binding.root)

    inner class NotificationFeedEmptyViewHolder constructor(val binding: NotificationFeedEmptyItemBinding): RecyclerView.ViewHolder(binding.root)

    inner class RowViewHolder(private val binding: NotificationFeedRowItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(
            notification: NotificationItem,
            notificationActionClickListener: (NotificationFeedUiModel) -> Unit,
            onNotificationSeen: (NotificationFeedUiModel) -> Unit
        ) {
            val item = (notification as NotificationItem.Row).notificationFeedModel
            val actionTitle = item.actionTitleResId?.let { binding.root.context.getString(it) }
            binding.rowTitle.apply {
                text = item.title
                newTypeFace(if (item.read) R.font.tt_norms_pro_normal else R.font.tt_norms_pro_medium)
                setTextColorRes(if (item.read) R.color.description_primary else R.color.title_primary)
            }
            binding.rowDescription.text = timeAgo(binding.root.context,item.createdAt)
            binding.actionButton.visibility = if(item.actionTitleResId == null) View.GONE else View.VISIBLE
            binding.actionButton.text = actionTitle ?: ""
            if(!item.alreadyVisited && actionTitle.isNullOrEmpty() && !item.read) {
                onNotificationSeen(item)
            }
            binding.actionButton.setOnSingleClickListener {
                notificationActionClickListener(item)
            }

            if(item.type == NotificationType.SETUP_ACCOUNT_CREDENTIALS) {
                binding.image.setBackgroundResource(item.notificationImage)
                binding.image.setImageDrawable(null)
            } else {
                binding.image.setBackgroundResource(R.drawable.circular_stroke)
                binding.image.setImageDrawable(ContextCompat.getDrawable(binding.root.context,item.notificationImage))
            }
        }
    }

    inner class RowPictureViewHolder(private val binding: NotificationFeedRowPictureItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(
            notification: NotificationItem,
            notificationClickListener: (NotificationFeedUiModel) -> Unit,
            onNotificationSeen: (NotificationFeedUiModel) -> Unit
        ) {
            val item = (notification as? NotificationItem.Row)?.notificationFeedModel ?: return

            with(binding) {
                val actionTitle = item.actionTitleResId?.let { root.context.getString(it) }
                rowPictureTitle.apply {
                    text = item.title
                    newTypeFace(if (item.read) R.font.tt_norms_pro_normal else R.font.tt_norms_pro_medium)
                    setTextColorRes(if (item.read) R.color.description_primary else R.color.title_primary)
                }
                if(!item.alreadyVisited && actionTitle.isNullOrEmpty() && !item.read) {
                    onNotificationSeen(item)
                }
                rowPictureDescription.text = timeAgo(root.context,item.createdAt)

                actionPictureButton.apply {
                    visibility = if (actionTitle.isNullOrEmpty()) View.GONE else View.VISIBLE
                    text = actionTitle ?: ""
                    setOnSingleClickListener{
                        notificationClickListener(item)
                    }
                }

                val borderColor = ContextCompat.getColor(root.context, R.color.background)

               when (item.type) {
                   NotificationType.CROSS_PATH_CREATED -> {
                     val(currentUserImageUrl, counterPartUserImageUrl) = extractCrossPathImageUrls(item.data as? CrossPathCreatedNotificationModel)
                       updateUI(
                           this,
                           this@RowPictureViewHolder,
                           currentUserImageUrl,
                           borderColor,
                           counterPartUserImageUrl
                       )
                   }
                    NotificationType.MATCH -> {
                        val (imageUrl, initiatorImageUrl) = extractMatchImageUrls(item.data as? UserMatchNotificationResponse)
                        updateUI(
                            this,
                            this@RowPictureViewHolder,
                            imageUrl,
                            borderColor,
                            initiatorImageUrl
                        )
                    }
                    NotificationType.MESSAGE -> {
                        val imageUrl = extractMessageNotificationImageUrl(item.data as? ConversationData,notification.isPremium)
                        doubleImageWrapper.visibility = View.GONE
                        singleImage.visibility = View.VISIBLE
                         imageUrlCircle(binding.singleImage,imageUrl)
                    }
                    NotificationType.NEW_LIKE_RECEIVED -> {
                        val imageUrl = extractLikedYouNotificationImageUrl(item.data as? UserLikedYouNotificationResponse,notification.isPremium)
                        doubleImageWrapper.visibility = View.GONE
                        singleImage.visibility = View.VISIBLE
                        imageUrlCircle(binding.singleImage,imageUrl)
                    }

                   NotificationType.NEW_PROFILE_VISIT_RECEIVED -> {
                       val imageUrl = extractProfileVisitNotificationImageUrl((item.data as? ProfileVisitedModel),notification.isPremium)
                       doubleImageWrapper.visibility = View.GONE
                       singleImage.visibility = View.VISIBLE
                       imageUrlCircle(binding.singleImage,imageUrl)
                   }
                   else -> {}
                }

            }
        }

        private fun extractCrossPathImageUrls(data: CrossPathCreatedNotificationModel?): Pair<String?, String?> {
            val currentUser = data?.currentUser
            val counterPartUser = data?.counterPartUser

            val currentUserImageUrl = currentUser?.let {
                it.thumbnailUrl ?: it.pictureUrl
            }

            val counterPartUserImageUrl = counterPartUser?.let {
                it.thumbnailUrl ?: it.pictureUrl
            }

            return currentUserImageUrl to counterPartUserImageUrl
        }
        private fun extractMatchImageUrls(data: UserMatchNotificationResponse?): Pair<String?, String?> {
            val profile = data?.data?.user?.profile
            val initiatorProfile = data?.initiatorData?.user?.profile

            val imageUrl = profile?.let {
               it.thumbnailUrl ?: it.pictureUrl
            }

            val initiatorImageUrl = initiatorProfile?.let {
               it.thumbnailUrl ?: it.pictureUrl
            }

            return imageUrl to initiatorImageUrl
        }
        private fun extractMessageNotificationImageUrl(
            data: ConversationData?,
            premium: Boolean
        ): String? {
            val member = data?.conversationMember
            return when (premium) {
                true -> {
                    member?.thumbnailUrl ?: member?.pictureUrl
                }
                false -> {
                    member?.bluredThumbnailUrl
                }
            }

        }
        private fun extractLikedYouNotificationImageUrl(
            data: UserLikedYouNotificationResponse?,
            premium: Boolean
        ): String? {
            val profile = data?.data?.user?.profile
            return when (premium) {
                true -> {
                    profile?.thumbnailUrl ?: profile?.pictureUrl
                }
                false -> {
                    profile?.bluredThumbnailUrl
                }
            }

        }

        private fun extractProfileVisitNotificationImageUrl(
            data: ProfileVisitedModel?,
            premium: Boolean,
        ): String? {
            // TODO: TBD if we use blurredThumbnailUrl when the user is freemium
            return  data?.thumbnailUrl
        }


        private fun loadImageIntoView(imageView: ImageView, imageUrl: String?, borderColor: Int) {
            val image = imageUrl?.let { getS3Url(it) }

            Glide.with(imageView.context)
                .load(image)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .override(250, 250)
                .transform(CircleBorderTransformation(imageView.context, 5f, borderColor))
                .into(imageView)
        }

        private fun updateUI(
            notificationFeedRowPictureItemBinding: NotificationFeedRowPictureItemBinding,
            rowPictureViewHolder: RowPictureViewHolder,
            imageUrl: String?,
            borderColor: Int,
            initiatorImageUrl: String?
        ) {
            notificationFeedRowPictureItemBinding.doubleImageWrapper.visibility = View.VISIBLE
            notificationFeedRowPictureItemBinding.singleImage.visibility = View.GONE
            rowPictureViewHolder.loadImageIntoView(
                notificationFeedRowPictureItemBinding.imageView2,
                imageUrl,
                borderColor
            )
            rowPictureViewHolder.loadImageIntoView(
                notificationFeedRowPictureItemBinding.imageView1,
                initiatorImageUrl,
                borderColor
            )
        }
    }



    companion object {
        private const val SECTION_VIEW = 0
        private const val ITEM_VIEW = 1
        private const val ITEM_VIEW_PICTURE = 2
        private const val ITEM_VIEW_SHIMMER = 3
        private const val ITEM_VIEW_EMPTY = 4
    }
}
