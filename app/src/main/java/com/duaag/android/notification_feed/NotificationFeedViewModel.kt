package com.duaag.android.notification_feed

import androidx.annotation.Keep
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.Resource
import com.duaag.android.di.ActivityScope
import com.duaag.android.di.IoDispatcher
import com.duaag.android.di.MainDispatcher
import com.duaag.android.firebase.NotificationRepository
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@ActivityScope
class NotificationFeedViewModel @Inject constructor(
    private val notificationRepository: NotificationRepository,
    private val userRepository: UserRepository,
    @IoDispatcher val ioDispatcher: CoroutineDispatcher,
    @MainDispatcher val mainDispatcher: CoroutineDispatcher
) : ViewModel() {

    val user = userRepository.user

    private val _notificationFeedUiState: MutableStateFlow<NotificationFeedUiState> = MutableStateFlow(NotificationFeedUiState())
    val notificationFeedUiState : LiveData<NotificationFeedUiState>
        get() = _notificationFeedUiState.asLiveData()

    var language: String? = null
    init {
        viewModelScope.launch {
            userRepository.userFlow()
                .filterNotNull()
                .map { it.language }
                .firstOrNull()
                .let { language ->
                    <EMAIL> = language
                    getNotificationFeedData()
                }
        }
    }

    /**
     * Determines if more data should be loaded based on the current UI state and scroll position.
     *
     * @param visibleItemCount The number of visible items.
     * @param totalItemCount The total number of items.
     * @param firstVisibleItemPosition The position of the first visible item.
     * @return True if more data should be loaded, false otherwise.
     */
    fun shouldLoadMoreData(
        visibleItemCount: Int,
        totalItemCount: Int,
        firstVisibleItemPosition: Int
    ): Boolean {
        val isLoading = _notificationFeedUiState.value.isLoading
        val paginationLimit = _notificationFeedUiState.value.paginationData.limit
        val nextCursor = _notificationFeedUiState.value.paginationData.nextCursor
        return nextCursor != null && totalItemCount > 0 && !isLoading &&
                (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 5 &&
                firstVisibleItemPosition >= 0 &&
                totalItemCount >= paginationLimit
    }

    /**
     * Retrieves notification feed data and updates the UI state accordingly.
     *
     * @param refresh Flag indicating whether to refresh the data.
     *                Defaults to false.
     */
     fun getNotificationFeedData(refresh:Boolean = false) {
        if (_notificationFeedUiState.value.isLoading) return
        viewModelScope.launch(ioDispatcher) {
            language?.let {language->
                notificationRepository
                    .getNotificationFeedData(language = language, nextCursor = if (!refresh) _notificationFeedUiState.value.paginationData.nextCursor else null)
                    .catch {e->
                        e.printStackTrace()
                        withContext(mainDispatcher){
                            _notificationFeedUiState.update {
                                it.copy(isLoading = false)
                            }
                        }
                    }
                    .collect{result->
                        when(result){
                            Resource.Loading -> {
                                _notificationFeedUiState.update {
                                    it.copy(isLoading = true)
                                }
                            }

                            is Resource.Success -> {
                                updateNotificationFeedRefreshTime()
                                val uiModel = result.data.result.map { it.mapToUiModel() }
                                val items = if(refresh) uiModel else  _notificationFeedUiState.value.items.plus(uiModel)
                                withContext(mainDispatcher){
                                    _notificationFeedUiState.update {
                                        result.data.nextCursor
                                            .let { nextCursor -> it.paginationData.copy(nextCursor = nextCursor) }
                                            .let { paginationData -> it.copy(isLoading = false, items = items, isCurrentUserPremium = !user.value?.premiumType.isNullOrEmpty(), paginationData = paginationData) }
                                    }
                                }
                            }
                            else -> {
                                _notificationFeedUiState.update {
                                    it.copy(isLoading = false)
                                }
                            }
                        }
                    }
            }
        }
    }


    /**
     * Checks and updates the notification state by marking it as read.
     *
     * @param notification The notification to be checked and updated.
     */
    fun checkAndUpdateNotification(notification: NotificationFeedUiModel) {
        val notificationId = notification.id
         markNotificationAsRead(notificationId)
    }
    /**
     * Marks a notification as read and updates its state in the UI.
     *
     * @param notificationId The ID of the notification to mark as read.
     */
    private fun markNotificationAsRead(notificationId: String) {
        viewModelScope.launch(ioDispatcher) {
            notificationRepository.markNotificationAsRead(notificationId)
                .catch { e->
                    e.printStackTrace()
                }
                .collect{
                    when(it){
                       is  Resource.Success -> {
                           val notifications = _notificationFeedUiState.value.items.toMutableList()
                           if(notifications.isEmpty()) return@collect
                           val itemToUpdateIndex = notifications.indexOfFirst { it.id == notificationId }
                           if (itemToUpdateIndex != -1) {
                               val updatedItem = notifications[itemToUpdateIndex].copy(read = !notifications[itemToUpdateIndex].read)
                               notifications[itemToUpdateIndex] = updatedItem
                               withContext(mainDispatcher){
                                   _notificationFeedUiState.update {
                                       it.copy(items =notifications)
                                   }
                               }
                           }
                        } else -> {/*not used*/}
                    }
                }
        }


    }

    /**
     * Handles the visibility state change of a notification and updates its state in the UI.
     *
     * @param notification The notification whose visibility state needs to be updated.
     */
    fun onNotificationSeen(notification: NotificationFeedUiModel) {
        val notificationId = notification.id
        val notifications = _notificationFeedUiState.value.items.toMutableList()
        if(notifications.isEmpty()) return
        val itemToUpdateIndex = notifications.indexOfFirst { it.id == notificationId }
        if (itemToUpdateIndex != -1) {
            val updatedItem = notifications[itemToUpdateIndex].copy(alreadyVisited = !notifications[itemToUpdateIndex].alreadyVisited)

            notifications[itemToUpdateIndex] = updatedItem

            _notificationFeedUiState.update {
                it.copy(items = notifications)
            }

        }
    }

    /**
     * Marks the notification feed as visited in the UI state.
     * This function sets the flag indicating that the notification feed has been visited.
     */
    fun markNotificationFeedAsVisited() {
        _notificationFeedUiState.update {
            it.copy(notificationFeedAlreadyVisited = true)
        }
    }

    /**
     * Updates the read status for visited notifications in the UI.
     * Identifies notifications that have been visited but not marked as read,
     * and marks them as read if they satisfy certain conditions.
     */
    fun updateReadStatusForVisitedNotifications() {
        val notifications = _notificationFeedUiState.value.items
        if(notifications.isNotEmpty()) {
       notifications
            .filter {it.actionTitleResId == null && !it.read && it.alreadyVisited }
            .forEach { notification ->
                if(notification.read && notification.alreadyVisited) return@forEach
              checkAndUpdateNotification(notification)
            }
    } }

    fun updateLastReceivedNotificationTime(lastReceivedNotificationTime: Long) {
        val updatedValue = if(lastReceivedNotificationTime > 0) lastReceivedNotificationTime else null
        _notificationFeedUiState.update {
            it.copy(refreshNotificationFeedData = it.refreshNotificationFeedData.copy(lastReceivedNotificationTime=updatedValue))
        }
    }

    private fun updateNotificationFeedRefreshTime() {
        val now = System.currentTimeMillis()
        _notificationFeedUiState.update {
            it.copy(refreshNotificationFeedData = it.refreshNotificationFeedData.copy(notificationFeedRefreshTime=now))
        }
    }

    fun getLastRefreshTime(): Long = _notificationFeedUiState.value.refreshNotificationFeedData.notificationFeedRefreshTime ?: 0
    fun showTurnNotificationMessage(show:Boolean) {
        _notificationFeedUiState.update {
            it.copy(showTurnNotificationMessage = show)
        }
    }

}
private fun shouldRefreshData(refreshData: RefreshNotificationFeedData, isLoading: Boolean): Boolean {
    return !isLoading &&
            refreshData.notificationFeedRefreshTime != null &&
            refreshData.lastReceivedNotificationTime != null &&
            refreshData.lastReceivedNotificationTime > refreshData.notificationFeedRefreshTime
}
@Keep
data class NotificationFeedUiState(
    val items: List<NotificationFeedUiModel> = emptyList(),
    val isLoading: Boolean = false,
    val error: Exception? = null,
    val paginationData: PaginationData = PaginationData(),
    val notificationFeedAlreadyVisited: Boolean = false,
    val isCurrentUserPremium: Boolean = false,
    val isFragmentVisible: Boolean = false,
    val showTurnNotificationMessage: Boolean = false,
    val refreshNotificationFeedData: RefreshNotificationFeedData= RefreshNotificationFeedData(),
) {
    val refreshNotificationFeed: Boolean = shouldRefreshData(refreshNotificationFeedData, isLoading)
}

data class RefreshNotificationFeedData(
    val notificationFeedRefreshTime: Long? = null,
    val lastReceivedNotificationTime: Long? = null,
)

data class PaginationData(
    val nextCursor: String? = null,
    val limit: Int = 20
)