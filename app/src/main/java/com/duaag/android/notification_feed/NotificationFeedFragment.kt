package com.duaag.android.notification_feed

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.api.ProfileVisitSourceEnum
import com.duaag.android.boost.fragments.BoostResultDialog
import com.duaag.android.boost.models.BoostResultModel
import com.duaag.android.chat.fragments.ConversationFragment
import com.duaag.android.chat.model.ConversationData
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationType
import com.duaag.android.chat.model.MessageType
import com.duaag.android.chat.model.UserLikesModel
import com.duaag.android.chat.viewmodel.LikedYouViewModel
import com.duaag.android.chat.viewmodel.MatchesTabViewModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.PremiumBadgeValues
import com.duaag.android.clevertap.ProfileVisitedModel
import com.duaag.android.clevertap.PurchaselyPaywalllModel
import com.duaag.android.clevertap.SwipeSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.offers.ClevertapOfferTypeEnum
import com.duaag.android.clevertap.offers.RealTimeClevertapOfferModel
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentNotificationFeedBinding
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.firebase.NotificationType
import com.duaag.android.firebase.model.Badge2ApprovalModel
import com.duaag.android.firebase.model.ConsumableRewardGivenModel
import com.duaag.android.firebase.model.PushBadge2VerificationModel
import com.duaag.android.firebase.model.UserLikedYouNotificationResponse
import com.duaag.android.firebase.model.UserMatchNotificationResponse
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.fragments.InstaChatDialogFragment
import com.duaag.android.home.fragments.UserProfileFragment
import com.duaag.android.home.fragments.VerificationInProgressDialog
import com.duaag.android.home.fragments.VerifyYourProfileDialog
import com.duaag.android.home.models.CrossPath
import com.duaag.android.home.models.HomeScreenType
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.models.LimitReachedModel
import com.duaag.android.home.models.LimitReachedScreenSource
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.premium_subscription.openPremiumPaywallAsync
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationBody
import com.duaag.android.settings.fragments.notifications.pushnotification.models.PushNotificationType
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.DebounceChannel
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.checkBackgroundLocationPermission
import com.duaag.android.utils.getLocationServicesStatus
import com.duaag.android.utils.getNotificationsOnClevertapValue
import com.duaag.android.utils.isFragmentDialogShowing
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.yuyakaido.android.cardstackview.Direction
import kotlinx.coroutines.launch
import sendVerifyYourProfilePopupAnalyticsEvent
import javax.inject.Inject


class NotificationFeedFragment : Fragment(), UserProfileFragment.UserProfileInteract {

    private var _binding: FragmentNotificationFeedBinding? = null
    private val binding get() = _binding

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs
    @Inject
    lateinit var duaAccount: DuaAccount

    private val notificationFeedViewModel by viewModels<NotificationFeedViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val matchesTabViewModel by viewModels<MatchesTabViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private val likeYouViewModel by viewModels<LikedYouViewModel> { viewModelFactory }

    private var notificationFeedAdapter : NotificationFeedAdapter? = null

    private var debounceChannel: DebounceChannel? = null


    private val paginationScrollListener by lazy {
        object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val layoutManager = recyclerView.layoutManager as? LinearLayoutManager
                layoutManager ?: return

                val (visibleItemCount, totalItemCount, firstVisibleItemPosition) = with(layoutManager) {
                    Triple(childCount, itemCount, findFirstVisibleItemPosition())
                }

                if (notificationFeedViewModel.shouldLoadMoreData(
                        visibleItemCount,
                        totalItemCount,
                        firstVisibleItemPosition
                    )
                ) {
                    notificationFeedViewModel.getNotificationFeedData()
                }
            }
        }
    }
    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentNotificationFeedBinding.inflate(inflater, container, false)
         debounceChannel = DebounceChannel(
             scope = viewLifecycleOwner.lifecycleScope,
             delayMillis = REFRESH_TIMEOUT,
             skipFirstDebounce = true) {
             notificationFeedViewModel.getNotificationFeedData(refresh = true)
         }
        notificationFeedAdapter = NotificationFeedAdapter(
            notificationActionClickListener = { notification ->
                handleNotificationActions(notification,duaSharedPrefs, requireActivity() as? HomeActivity,matchesTabViewModel)
            },
            onNotificationSeen = {notification->
                notificationFeedViewModel.onNotificationSeen(notification)
            })

        return binding?.root
    }

    private fun sendScreenViewEvents() {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
        val areNotificationsOnEventProperty =
            getNotificationsOnClevertapValue(requireContext(), duaSharedPrefs)

        sendClevertapEvent(ClevertapEventEnum.NOTIFICATION_FEED_SCREEN_VIEW,
            mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.ARE_NOTIFICATIONS_ON.propertyName to areNotificationsOnEventProperty
            )
        )
        firebaseLogEvent(FirebaseAnalyticsEventsName.NOTIFICATION_FEED_SCREEN_VIEW)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.notificationFeedRv?.layoutManager = LinearLayoutManager(requireContext())
        binding?.notificationFeedRv?.adapter = notificationFeedAdapter
        notificationFeedViewModel.notificationFeedUiState.observe(viewLifecycleOwner) {uiState->
            notificationFeedAdapter?.submitData(requireContext(), uiState)
            if (uiState.refreshNotificationFeed) {
                 viewLifecycleOwner.lifecycleScope.launch{
                      debounceChannel?.debounce()
                  }
            }
            binding?.includedLayout?.root?.visibility = if(uiState.showTurnNotificationMessage) View.VISIBLE else View.GONE
        }

        binding?.notificationFeedRv?.addOnScrollListener(paginationScrollListener)
        binding?.backBtn?.setOnSingleClickListener{
            (requireActivity() as? HomeActivity)?.navController?.popBackStack()
        }
        binding?.includedLayout?.notifButton?.setOnSingleClickListener{
            showDeviceNotificationScreen(duaSharedPrefs,duaAccount)
            sendPermissionNotificationsInitiatedEvent()
            notificationFeedViewModel.showTurnNotificationMessage(false)
        }
        binding?.includedLayout?.dismissImg?.setOnSingleClickListener{
            notificationFeedViewModel.showTurnNotificationMessage(false)
        }

        sendScreenViewEvents()
    }

    private fun sendPermissionNotificationsInitiatedEvent() {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
        val eventSourceScreen = ClevertapEventSourceValues.NOTIFICATION_FEED.value
        sendClevertapEvent(
            ClevertapEventEnum.PERMISSION_NOTIFICATIONS_INITIATED,
            mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.PERMISSION_SOURCE_SCREEN.propertyName to eventSourceScreen
            )
        )
    }

    private fun showDeviceNotificationScreen(duaSharedPrefs: DuaSharedPrefs,duaAccount: DuaAccount) {
        val isDeviceNotificationOn =
            NotificationHelper.isMessageNotificationsEnables(requireContext())
        val isSettingsNotificationOn = duaSharedPrefs.isPushMessageNotificationsEnables()
        if (!isSettingsNotificationOn) {
            val notification = duaSharedPrefs.getPushNotifications()
            for (pushNotificationModel in notification) {
                pushNotificationModel.isChecked = true
            }
            duaAccount.setNotifications(
                PushNotificationType.NEW_MESSAGE,
                PushNotificationBody.convertToPushNotificationBody(
                    notification
                )
            )
        }

        if (!isDeviceNotificationOn) {
            val intent = Intent()
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    intent.action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                    intent.putExtra(Settings.EXTRA_APP_PACKAGE, requireActivity().packageName)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    intent.action = "android.settings.APP_NOTIFICATION_SETTINGS"
                    intent.putExtra("app_package", requireActivity().packageName)
                    intent.putExtra("app_uid", requireActivity().applicationInfo.uid)
                }
                else -> {
                    intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                    intent.addCategory(Intent.CATEGORY_DEFAULT)
                    intent.data = Uri.parse("package:" + requireActivity().packageName)
                }
            }
            startActivity(intent)
        }
    }
    private fun checkIfNotificationsAreOn(duaSharedPrefs: DuaSharedPrefs,viewModel: NotificationFeedViewModel) {
        val isDeviceNotificationOn =
            NotificationHelper.isMessageNotificationsEnables(requireContext())
        val isSettingsNotificationOn = duaSharedPrefs.isPushMessageNotificationsEnables()
        if (!(isDeviceNotificationOn && isSettingsNotificationOn)) {
            viewModel.showTurnNotificationMessage(true)
        }
    }
    private fun handleNotificationActions(
        notification: NotificationFeedUiModel,
        duaSharedPrefs: DuaSharedPrefs,
        homeActivity: HomeActivity?,
        matchesTabViewModel: MatchesTabViewModel
    ) {
        markActionNotificationAsRead(notification)
        when(notification.type){
            NotificationType.NEW_LIKE_RECEIVED -> handleNewLikeReceivedAction(notification,homeActivity)
            NotificationType.MESSAGE ->           handleMessageAction(notification,duaSharedPrefs,homeActivity,matchesTabViewModel)
            NotificationType.MATCH -> handleMatchAction(notification,homeActivity,matchesTabViewModel)
            NotificationType.BOOST_SUCCESS -> handleBoostSuccessAction(notification,homeActivity)
            NotificationType.BOOST_FAILED -> handleBoostFailedAction(notification,homeActivity)
            NotificationType.FEATURED_USERS_RESET -> handleFeaturedUsersResetAction(homeActivity)
            NotificationType.COUNTER_RESET -> handleCounterResetAction(homeActivity)
            NotificationType.VERIFY_YOUR_IMAGE -> handleImageVerificationAction()
            NotificationType.NEW_PROFILE_VISIT_RECEIVED -> handleProfileVisitAction(notification, homeActivity)
            NotificationType.PUSH_BADGE2_VERIFICATION-> handlePushBadge2VerificationAction(notification,
                homeActivity
            )
            NotificationType.CONSUMABLE_REWARD_GIVEN -> handleRewardGivenAction(notification,homeActivity)
            NotificationType.PURCHASELY_PAYWALL-> handleOffersAction(notification)
            NotificationType.FORCE_PURCHASELY_PAYWALL-> handleOffersAction(notification, true)
            NotificationType.PURCHASELY_BOOST_PAYWALL -> handlePurchaselyBoostPaywall(
                notification,
                homeActivity
            )
            NotificationType.SHOW_INSTACHAT_PAYWALL -> handleShowInstachatPaywallAction(notification,homeActivity)
            NotificationType.SHOW_BOOST_PAYWALL -> handleShowInstachatPaywallAction(notification,homeActivity)
            NotificationType.SETUP_ACCOUNT_CREDENTIALS -> homeActivity?.checkShowSetupAccountCredentials()
            NotificationType.BADGE_2 -> handleBadge2Action(notification, homeActivity)
            NotificationType.NEW_GROUPED_LIKES -> handleNewGroupedLikesAction(homeActivity)
            NotificationType.REAL_TIME_PURCHASELY_OFFER -> handleRealTimeOfferAction(notification, homeActivity)
            NotificationType.CROSS_PATH_CREATED -> handleCrossPathCreatedAction(homeActivity)

            else -> {}
        }
    }

    private fun handleCrossPathCreatedAction(
        homeActivity: HomeActivity?
    ) {
        homeActivity?.navController?.navigateUp()
        homeActivity?.navigateTo(R.id.nav_graph_cross_path)
    }

    private fun handleNewGroupedLikesAction(homeActivity: HomeActivity?) {
        likeYouViewModel.initData()
        homeViewModel.setNavigateToLikedYou()
        homeActivity?.navController?.navigateUp()
        homeActivity?.navigateTo(R.id.nav_graph_likes)

    }

    private fun  handleRealTimeOfferAction(notification: NotificationFeedUiModel, homeActivity: HomeActivity?) {
        val model = notification.data as? RealTimeClevertapOfferModel
        if(model != null) {
            val offer = when(model.type) {
                ClevertapOfferTypeEnum.PREMIUM.value -> {
                    duaSharedPrefs.getRealTimeClevertapPremiumOffer()
                }
                ClevertapOfferTypeEnum.BOOST.value -> {
                    duaSharedPrefs.getRealTimeClevertapBoostOffer()
                }
                ClevertapOfferTypeEnum.INSTACHATS.value -> {
                    duaSharedPrefs.getRealTimeClevertapInstachatOffer()
                }
                ClevertapOfferTypeEnum.IMPRESSIONS.value -> {
                    duaSharedPrefs.getRealTimeClevertapImpressionsOffer()
                }
                ClevertapOfferTypeEnum.FLIGHTS.value -> {
                    duaSharedPrefs.getRealTimeClevertapFlightOffer()
                }
                ClevertapOfferTypeEnum.UNDO.value -> {
                    duaSharedPrefs.getRealTimeClevertapUndoOffer()
                }
                else -> {
                    null
                }
            }

            if(offer?.activeUntil != null && System.currentTimeMillis() < offer.activeUntil) {
                homeActivity?.openPremiumPaywallAsync(
                        eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                        placementId = offer.placementId,
                )
            }
        }
    }

    private fun handleBadge2Action(notification: NotificationFeedUiModel,homeActivity: HomeActivity?) {
        val badgeData = notification.data as? Badge2ApprovalModel
        if(badgeData?.badge2 == Badge2Status.NOT_APPROVED.status) {
            handleImageVerificationAction()
        } else if(badgeData?.badge2 == Badge2Status.APPROVED.status){
            handlePushBadge2VerificationAction(notification, homeActivity)
        }
    }
    private fun handleShowInstachatPaywallAction(
        notification: NotificationFeedUiModel,
        homeActivity: HomeActivity?
    ) {
        homeActivity?.checkForPaymentTypeDialog()
    }

    private fun handlePurchaselyBoostPaywall(
        notification: NotificationFeedUiModel,
        homeActivity: HomeActivity?
    ) {
        val placementId: String? =
            (notification.data as? PurchaselyPaywalllModel)?.placementId
        if(!placementId.isNullOrEmpty()) {
                homeActivity?.showBoostOfferPurchasely(placementId)
        }

    }

    private fun handleOffersAction(notification: NotificationFeedUiModel, forceOpen: Boolean = false) {
        if(homeViewModel.userProfile.value?.premiumType == null || forceOpen) {
            val data = notification.data as? PurchaselyPaywalllModel
            data?.placementId?.let { placementId ->
                requireActivity().openPremiumPaywall(
                    eventSourceClevertap = ClevertapEventSourceValues.CL_NOTIFICATION_OPEN_PAYWALL,
                    placementId = placementId,
                    userModel = homeViewModel.userProfile.value
                )
            }
        }
    }

    private fun handlePushBadge2VerificationAction(
        notification: NotificationFeedUiModel,
        homeActivity: HomeActivity?
    ) {
       val data = notification.data as? PushBadge2VerificationModel
        if(data?.isBadge2Needed == true && homeViewModel.badge2 != Badge2Status.APPROVED){
            if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                VerificationInProgressDialog.showVerificationInProgressDialog(
                    childFragmentManager
                )
                return
            }
            homeActivity?.badge2VerificationPopUp(
                isFromNotification = true,
                eventSource = ClevertapVerificationSourceValues.RETENTION_JOURNEY
            )
        }
    }

    private fun handleRewardGivenAction(notification: NotificationFeedUiModel,homeActivity: HomeActivity?) {
        val notificationData = notification.data as? ConsumableRewardGivenModel
        notificationData?.let {
            homeActivity?.showRewardDialog(it)
        }
    }

    private fun handleProfileVisitAction(
        notification: NotificationFeedUiModel,
        homeActivity: HomeActivity?
    ) {
        val notificationData = notification.data as? ProfileVisitedModel
        notificationData?.cognitoUserId?.let { cognitoId ->
            showProfileVisited(cognitoId, homeActivity)
        }
    }
    private fun showProfileVisited(
        cognitoId: String,
        homeActivity: HomeActivity?
    ) {
        val userProfileFragment = UserProfileFragment.newInstance(
            RecommendedUserModel.getEmptyRecommendedUserModel().copy(cognitoUserId = cognitoId),
            _isFromHome = true,
            _isFeaturedProfile = true,
            _shouldIncludeInteraction = true,
            _profileVisitSource = ProfileVisitSourceEnum.PROFILE_VISIT.source
        )
        userProfileFragment.setPictureChangeListener(object :
            UserProfileFragment.PictureChangeListener {
            override fun onRightSideClick(index: Int, crossPath: CrossPath?, recommendedUserModel: RecommendedUserModel?) {

                val hasCurrentUserOnlyOnePicture = homeViewModel.userProfile.value?.hasOnlyOnePicture() == true
                if (recommendedUserModel != null) {
                    sendProfileVisitOtherPhotosEvent(hasCurrentUserOnlyOnePicture,recommendedUserModel, ClevertapEventSourceValues.FEATURED_PROFILE.value ?: "")
                }
            }

            override fun onLeftSideClick(index: Int, crossPath: CrossPath?, recommendedUserModel: RecommendedUserModel?) {

                val hasCurrentUserOnlyOnePicture = homeViewModel.userProfile.value?.hasOnlyOnePicture() == true
                if(index <= 1) recommendedUserModel?.let { it1 ->
                    sendProfileVisitOtherPhotosEvent(hasCurrentUserOnlyOnePicture,
                        it1, ClevertapEventSourceValues.FEATURED_PROFILE.value ?: "")
                }
            }

        })
        val fm = homeActivity?.supportFragmentManager
        if (fm != null && fm.findFragmentByTag("userProfile") == null) {
            userProfileFragment.show(fm, "userProfile")
        }
    }
    fun sendProfileVisitOtherPhotosEvent(
        arePhotosLocked: Boolean,
        recommendedUserModel: RecommendedUserModel,
        eventSource: String
    ) {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userRepository.user.value)
        val recommSource = "dua"
        val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
        val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
        val interactedBefore = recommendedUserModel.existingInteractionType ?: ""

        firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_VISIT_OTHER_PHOTOS, mapOf(
            FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
            FirebaseAnalyticsParameterName.ARE_PHOTOS_LOCKED.value to arePhotosLocked,
        ))

        sendClevertapEvent(
            ClevertapEventEnum.PROFILE_VISIT_OTHER_PHOTOS, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                ClevertapEventPropertyEnum.ARE_PHOTOS_LOCKED.propertyName to arePhotosLocked,
                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                ClevertapEventPropertyEnum.INTERACTED_BEFORE.propertyName to interactedBefore

            ))


    }
    private fun handleImageVerificationAction() {
        if(homeViewModel.badge2 == Badge2Status.APPROVED) return

        if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
            VerificationInProgressDialog.showVerificationInProgressDialog(
                childFragmentManager
            )
            return
        }

        findNavController().navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,
            bundleOf(EVENT_SOURCE to ClevertapVerificationSourceValues.RETENTION_JOURNEY.value)
        )
    }

    private fun handleCounterResetAction(homeActivity: HomeActivity?) {
        homeViewModel.userProfile.value?.let {
            homeViewModel.getUserCounters(it)
            homeViewModel.setHomeScreenType(HomeScreenType.CARDS)
        }
        homeActivity?.navController?.navigateUp()
        homeActivity?.navigateTo(R.id.nav_graph_home)
    }

    private fun handleFeaturedUsersResetAction(homeActivity: HomeActivity?) {
        homeViewModel.apply {
            homeViewModel.getCardsAndFeaturedProfilesFromAPi(fromTouch = false, applyCardsImmediately = true)
            homeViewModel.setHomeScreenType(HomeScreenType.FEATURED_PROFILE)
        }
        homeActivity?.navController?.navigateUp()
        homeActivity?.navigateTo(R.id.nav_graph_home)
    }


    private fun handleBoostFailedAction(
        notification: NotificationFeedUiModel,
        homeActivity: HomeActivity?
    ) {
        val boostResult = notification.data as? BoostResultModel
        homeViewModel.apply {
            homeViewModel.decreaseBoost()
            homeViewModel.setUserBoostFinishes()
            homeViewModel.setBoostStatusChanged()
        }
      boostResult?.let {
          BoostResultDialog.newInstance(it).show(childFragmentManager, "BoostResultDialog")
          if (isFragmentDialogShowing(homeActivity?.boostResultDialog)) {
              homeActivity?.boostResultDialog?.dismissAllowingStateLoss()
          }
      }

    }

    private fun handleBoostSuccessAction(
        notification: NotificationFeedUiModel,
        homeActivity: HomeActivity?
    ) {
        val boostResult = notification.data as? BoostResultModel

        homeViewModel.apply {
            setUserBoostFinishes()
            setBoostStatusChanged()
        }

        boostResult?.let {
            BoostResultDialog.newInstance(it).show(childFragmentManager, "BoostResultDialog")

            if (isFragmentDialogShowing(homeActivity?.boostResultDialog)) {
                homeActivity?.boostResultDialog?.dismissAllowingStateLoss()
            }
        }
    }

    private fun handleMatchAction(
        notification: NotificationFeedUiModel,
        homeActivity: HomeActivity?,
        matchesTabViewModel: MatchesTabViewModel,
    ) {
        val matchesModelNotification = (notification.data as? UserMatchNotificationResponse)?.data

        matchesModelNotification?.let {matchNotification->
           matchesTabViewModel.matches.value?.firstOrNull { it.user.cognitoUserId == matchesModelNotification.user.cognitoUserId }
              ?: return@let
            val conversationModel = ConversationModel(
                null,
                matchNotification.user.cognitoUserId,
                "",
                "",
                "",
                matchNotification.time,
                matchNotification.user.firstName,
                "",
                matchNotification.user.profile.pictureUrl,
                null,
                null,
                1,
                if (matchNotification.receivedSuperMatch) 1 else 0,
                ConversationType.DEFAULT.value,
                matchNotification.user.hasBadge1,
                matchNotification.user.badge2,
                false,
                false,
                false,
                0,
                false,
                0
            )
            conversationModel.isFromMatchView = true
            homeViewModel.setNavigateToChat(conversationModel)
        }
        homeActivity?.navController?.navigateUp()
        homeActivity?.navigateTo(R.id.nav_graph_chat)
    }
    private fun handleMessageAction(
        notification: NotificationFeedUiModel,
        duaSharedPrefs: DuaSharedPrefs,
        homeActivity: HomeActivity?,
        matchesTabViewModel: MatchesTabViewModel
    ) {
        if (homeViewModel.userProfile.value?.isDisabled == true) {
            return
        }
        val conversationData = (notification.data as? ConversationData)
        conversationData?.let { conversation ->
            val model = conversation.conversationMember
            val messageType = conversation.message.type

            val isMaleNonPremium = model.gender != GenderType.MAN.value &&
                    !duaSharedPrefs.isLoggedInUserPremium()

            if (isMaleNonPremium) {
                when (messageType) {
                    MessageType.CALLS_ALLOWED.value -> model.areOutgoingCallsAllowed = true
                    MessageType.CALLS_DISALLOWED.value -> model.areOutgoingCallsAllowed = false
                }
            }

            if (!(ConversationFragment.isInConversationFragment && ConversationFragment.isChattingWithId == model.userId)) {
                when {
                    model.type != ConversationType.INSTANT_CHAT.value -> navigateToChat(model,matchesTabViewModel)
                    isMaleNonPremium && messageType == MessageType.CALLS_ALLOWED.value -> navigateToInstaChatList()
                    else -> navigateToInstaChat(model)
                }
            }
        }
        homeActivity?.navController?.navigateUp()
        homeActivity?.navigateTo(R.id.nav_graph_chat)
    }

    private fun handleNewLikeReceivedAction(
        notification: NotificationFeedUiModel,
        homeActivity: HomeActivity?
    ) {
        val userLikesModel = UserLikesModel.convertToUserLikesModel(
            (notification.data as UserLikedYouNotificationResponse).data
        )
        userLikesModel?.let {
            homeViewModel.setNavigateToLikedYou(
                userLikesModel
            )
        }
        homeActivity?.navController?.navigateUp()
        homeActivity?.navigateTo(R.id.nav_graph_likes)

    }

    private fun navigateToChat(model: ConversationModel, matchesTabViewModel: MatchesTabViewModel) {
        val conversations = matchesTabViewModel.conversations.value
         conversations?.let {
             if(it.contains(model)) {
                 homeViewModel.setOnNewPushNotificationsClick()
                 homeViewModel.setNavigateToChat(model)
             }
         }
    }

    private fun navigateToInstaChatList() {
        homeViewModel.setOnNewPushNotificationsClick()
        homeViewModel.setNavigateToInstaChatList(true)
    }

    private fun navigateToInstaChat(model: ConversationModel) {
        homeViewModel.setOnNewPushNotificationsClick()
        homeViewModel.setNavigateToInstaChat(model)
    }
    private fun markActionNotificationAsRead(notification: NotificationFeedUiModel) {
        if (notification.actionTitleResId != null && !notification.read) {
            notificationFeedViewModel.checkAndUpdateNotification(notification)
        }
    }

    override fun onResume() {
        super.onResume()
        homeViewModel.showNotificationFeedTabDotNotification(false)
        checkIfNotificationsAreOn(duaSharedPrefs,notificationFeedViewModel)
        if (notificationFeedViewModel.notificationFeedUiState.value?.notificationFeedAlreadyVisited == false) {
            notificationFeedViewModel.markNotificationFeedAsVisited()
        } else {
            notificationFeedViewModel.updateReadStatusForVisitedNotifications()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        notificationFeedAdapter = null
        debounceChannel?.close()
        debounceChannel = null
        binding?.notificationFeedRv?.removeOnScrollListener(paginationScrollListener)
        _binding = null
    }

    companion object{
        private const val REFRESH_TIMEOUT = 1000L

    }

    override fun onUserInteracted(
        direction: Direction,
        user: RecommendedUserModel,
        isFeaturedProfile: Boolean
    ) {
        val interactionType = when(direction){
            Direction.Right -> InteractionType.LIKE
            Direction.Left -> InteractionType.DISLIKE
            Direction.Top -> InteractionType.INSTA_CHAT
            else -> null
        }
            interactionType?.let { interactFeaturedUser(it, user) }
    }

    fun interactFeaturedUser(
        interactionType: InteractionType,
        recommendedUserModel: RecommendedUserModel
    ) {
        when (interactionType) {
            InteractionType.LIKE -> {
                if (homeViewModel.remainingLikes()) {
                    val eventActivityType = recommendedUserModel.activityType

                    homeViewModel.swipeCard(recommendedUserModel, InteractionType.LIKE)
                    firebaseLogEvent(
                        FirebaseAnalyticsEventsName.SWIPE_RIGHT,
                        mapOf(
                            FirebaseAnalyticsParameterName.SWIPE_RIGHT_COUNT.value to 1L,
                            FirebaseAnalyticsParameterName.SOURCE.value to SwipeSourceValues.POPULAR.value,
                            FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                        )

                    )
                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                    val user = homeViewModel.userProfile.value
                    val locationAccess = getLocationServicesStatus(
                        requireContext().isLocationPermissionEnabled(),
                        requireContext().checkBackgroundLocationPermission()
                    )
                    val areNotificationsOn = getNotificationsOnClevertapValue(requireContext(), duaSharedPrefs)
                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
                    sendClevertapEvent(
                        ClevertapEventEnum.SWIPE_RIGHT, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.DISTANCE.propertyName to recommendedUserModel.profile.distance?.toInt(),
                            ClevertapEventPropertyEnum.SOURCE.propertyName to SwipeSourceValues.POPULAR.value,
                            ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to user?.profilePercentage,
                            ClevertapEventPropertyEnum.IS_BADGE2_VERIFIED.propertyName to (user?.badge2 == Badge2Status.APPROVED.status),
                            ClevertapEventPropertyEnum.LOCATION_ACCESS.propertyName to locationAccess,
                            ClevertapEventPropertyEnum.ARE_NOTIFICATIONS_ON.propertyName to areNotificationsOn,
                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge
                        )
                    )


                } else {
                    // rewindCard(direction)
                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.LIKE, LimitReachedScreenSource.POPULAR))
                }

            }

            InteractionType.DISLIKE -> {
                if(!homeViewModel.remainingDislikes()){
                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.DISLIKE, LimitReachedScreenSource.POPULAR))
                    return
                }
                val eventActivityType = recommendedUserModel.activityType

                homeViewModel.swipeCard(recommendedUserModel, InteractionType.DISLIKE)
                    firebaseLogEvent(
                        FirebaseAnalyticsEventsName.SWIPE_LEFT,
                        mapOf(
                            FirebaseAnalyticsParameterName.SWIPE_LEFT_COUNT.value to 1L,
                            FirebaseAnalyticsParameterName.SOURCE.value to SwipeSourceValues.POPULAR.value,
                            FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                        )

                    )

                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                    val gender =
                        if (homeViewModel.userProfile.value?.gender == GenderType.WOMAN.value) {
                            "female"
                        } else {
                            "male"
                        }
                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                sendClevertapEvent(
                        ClevertapEventEnum.SWIPE_LEFT, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.DISTANCE.propertyName to recommendedUserModel.profile.distance?.toInt(),
                            ClevertapEventPropertyEnum.GENDER.propertyName to gender,
                            ClevertapEventPropertyEnum.SOURCE.propertyName to SwipeSourceValues.POPULAR.value,
                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge
                        )
                    )

            }

            InteractionType.INSTA_CHAT -> {

                recommendedUserModel.let {
                    if (recommendedUserModel.cardUserGuideType == null) {
                        if (homeViewModel.badge2 == Badge2Status.APPROVED) {
                            if (!it.isMobAd && !it.isProfileInfo && !it.isProfileActivities) {
                                if (homeViewModel.remainingInstaChatInteractions()) {
                                    val dialog = getInstaChatDialogFragment(
                                        recommendedUserModel,
                                        isFeaturedProfile = true
                                    )
                                    dialog.show(childFragmentManager, "InstaChatDialogFragment")

                                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                                    val eventSourceValue = SwipeSourceValues.LAST_ACTIVE
                                    val eventActivityType = recommendedUserModel.activityType
                                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                                    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                                    firebaseLogEvent(
                                        FirebaseAnalyticsEventsName.INITIATE_INSTACHAT, mapOf(
                                            FirebaseAnalyticsParameterName.INITIATE_INSTACHAT_COUNT.value to 1L,
                                            FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                            FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                        )
                                    )
                                    sendClevertapEvent(
                                        ClevertapEventEnum.INSTACHAT_INITIATED,
                                        mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                            ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                                            ClevertapEventPropertyEnum.VERIFICATION_STATUS.propertyName to homeViewModel.badge2.status
                                        )
                                    )

                                } else {
                                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.INSTA_CHAT, LimitReachedScreenSource.POPULAR))
                                }
                            } else {

                                if (homeViewModel.badge2 == Badge2Status.APPROVED) {
                                    if (homeViewModel.remainingInstaChatInteractions()) {
                                        val dialog = getInstaChatDialogFragment(
                                            recommendedUserModel,
                                            isFeaturedProfile = true
                                        )
                                        dialog.show(childFragmentManager, "InstaChatDialogFragment")


                                    } else {
                                        homeViewModel.setLimitReached(LimitReachedModel(InteractionType.INSTA_CHAT, LimitReachedScreenSource.POPULAR))
                                    }
                                } else {
                                    if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                                        VerificationInProgressDialog.showVerificationInProgressDialog(
                                            childFragmentManager
                                        )
                                    } else {
                                        val eventPremiumType =
                                            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                        sendVerifyYourProfilePopupAnalyticsEvent(
                                            eventPremiumType,
                                            ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value
                                        )

                                        VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                            childFragmentManager,
                                            VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT
                                        )
                                    }
                                }

                            }
                        } else {
                            if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                                VerificationInProgressDialog.showVerificationInProgressDialog(
                                    childFragmentManager
                                )
                            } else {
                                val eventPremiumType =
                                    getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                sendVerifyYourProfilePopupAnalyticsEvent(
                                    eventPremiumType,
                                    ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value
                                )

                                VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                    childFragmentManager,
                                    VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT
                                )
                            }

                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            val eventSourceValue = SwipeSourceValues.LAST_ACTIVE
                            val eventActivityType = recommendedUserModel.activityType
                            val recommSource = recommendedUserModel.recommSource ?: "dua"
                            val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                            val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.INITIATE_INSTACHAT, mapOf(
                                    FirebaseAnalyticsParameterName.INITIATE_INSTACHAT_COUNT.value to 1L,
                                    FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                    FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                )
                            )
                            sendClevertapEvent(
                                ClevertapEventEnum.INSTACHAT_INITIATED,
                                mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                    ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                    ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                    ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                    ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                                    ClevertapEventPropertyEnum.VERIFICATION_STATUS.propertyName to homeViewModel.badge2.status
                                )
                            )
                        }
                    } else {
                        val dialog = getInstaChatDialogFragment(
                            recommendedUserModel,
                            isFeaturedProfile = true
                        )
                        dialog.isCancelable = false
                        dialog.show(childFragmentManager, "InstaChatDialogFragment")
                    }
                }
            }

            else -> {}
        }
    }
    private fun getInstaChatDialogFragment(recommendedUserModel: RecommendedUserModel, isFeaturedProfile: Boolean = false): InstaChatDialogFragment {
        val dialog = InstaChatDialogFragment.newInstance()
        val arguments = Bundle()
        arguments.putString(InstaChatDialogFragment.USER_NAME, recommendedUserModel.firstName)
        if (recommendedUserModel.cardUserGuideType != null) {
            arguments.putString(
                InstaChatDialogFragment.USER_GUIDE_TEXT,
                getString(R.string.instachat_user_guide_text,"72")
            )
        }
        dialog.arguments = arguments
        dialog.listener = object : InstaChatDialogFragment.InstaChatDialogListener {
            override fun onSendButtonClicked(dialog: InstaChatDialogFragment, message: String?) {
                dialog.dismissAllowingStateLoss()
                if (recommendedUserModel.cardUserGuideType == null) {
                    homeViewModel.swipeCard(
                        recommendedUserModel,
                        InteractionType.INSTA_CHAT,
                        message
                    )
                }
            }
        }
        return dialog
    }


}