package com.duaag.android.notification_feed

import android.content.Context
import androidx.annotation.Keep
import com.duaag.android.R
import com.duaag.android.ads.likedyou.models.NotificationRewardModel
import com.duaag.android.boost.models.BoostResultModel
import com.duaag.android.chat.model.ConversationData
import com.duaag.android.chat.model.LikeMessageModel
import com.duaag.android.clevertap.CrossPathCreatedNotificationModel
import com.duaag.android.clevertap.PremiumIdModel
import com.duaag.android.clevertap.ProfileVisitedModel
import com.duaag.android.clevertap.PurchaselyPaywalllModel
import com.duaag.android.clevertap.offers.RealTimeClevertapOfferModel
import com.duaag.android.firebase.NotificationType
import com.duaag.android.firebase.model.Badge2ApprovalModel
import com.duaag.android.firebase.model.CallModel
import com.duaag.android.firebase.model.ConsumableRewardGivenModel
import com.duaag.android.firebase.model.CounterResetDataModel
import com.duaag.android.firebase.model.CounterResetDataModelType
import com.duaag.android.firebase.model.DisableStateChanged
import com.duaag.android.firebase.model.DislikeInstaChatModel
import com.duaag.android.firebase.model.DontLetGoOfferNotificationModel
import com.duaag.android.firebase.model.EndVideoCallModel
import com.duaag.android.firebase.model.LastHourInstachatModel
import com.duaag.android.firebase.model.NewLocalSubscriptionModel
import com.duaag.android.firebase.model.PushBadge2VerificationModel
import com.duaag.android.firebase.model.UserDeletedModel
import com.duaag.android.firebase.model.UserLikedYouNotificationResponse
import com.duaag.android.firebase.model.UserMatchNotificationResponse
import com.duaag.android.firebase.model.UserUnMatchedModel
import com.duaag.android.firebase.model.VideoCallAcceptedModel
import com.duaag.android.home.models.ImpressionsRewarded
import com.duaag.android.premium_subscription.models.SpecialOfferDataModel
import com.duaag.android.settings.fragments.Badge2Status
import com.google.gson.Gson
import com.google.gson.JsonElement
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Date

@Keep
data class NotificationFeedResponse (
         @SerializedName("result")
        val result: List<NotificationFeedModel>,
        @SerializedName("nextCursor")
        val nextCursor: String?,
        @SerializedName("showRedDotIndicator")
        val showRedDotIndicator: Boolean
) {
        data class NotificationFeedModel(
                @SerializedName("id")
                val id: String,
                @SerializedName("title")
                val title: String,
                @SerializedName("action")
                val action: String,
                @SerializedName("jsonData")
                val jsonData: JsonElement?,
                @SerializedName("read")
                val read: Boolean,
                @SerializedName("createdAt")
                val createdAt: Long
        )
}

val actionTitleMap = mapOf(
        NotificationType.MESSAGE to R.string.reply,
        NotificationType.NEW_LIKE_RECEIVED to R.string.view_notification_button,
        NotificationType.SHOW_BOOST_PAYWALL to R.string.view_notification_button,
        NotificationType.BOOST_FAILED to R.string.view_notification_button,
        NotificationType.BOOST_SUCCESS to R.string.view_notification_button,
        NotificationType.EXTRA_SWIPES_REWARDED_MALE to R.string.view_notification_button,
        NotificationType.FEATURED_USERS_RESET to R.string.view_notification_button,
        NotificationType.CONSUMABLE_REWARD_GIVEN to R.string.view_notification_button,
        NotificationType.NEW_PROFILE_VISIT_RECEIVED to R.string.view_notification_button,
        NotificationType.VERIFY_YOUR_IMAGE  to R.string.view_notification_button,
        NotificationType.BADGE_2 to R.string.view_notification_button,
        NotificationType.PREMIUM_OFFER to R.string.view_notification_button,
        NotificationType.PREMIUM_SPECIAL_OFFER to R.string.view_notification_button,
        NotificationType.PUSH_BADGE2_VERIFICATION to R.string.view_notification_button,
        NotificationType.SETUP_ACCOUNT_CREDENTIALS to R.string.view_notification_button,
        NotificationType.PURCHASELY_BOOST_PAYWALL to R.string.view_notification_button,
        NotificationType.SHOW_INSTACHAT_PAYWALL to R.string.view_notification_button,
        NotificationType.PURCHASELY_PAYWALL to R.string.view_notification_button,
        NotificationType.FORCE_PURCHASELY_PAYWALL to R.string.view_notification_button,
        NotificationType.NEW_GROUPED_LIKES to  R.string.view_notification_button,
        NotificationType.MATCH to R.string.message_notification_button,
        NotificationType.REAL_TIME_PURCHASELY_OFFER to R.string.view_notification_button,
        NotificationType.CROSS_PATH_CREATED to R.string.view_notification_button

)
val actionTypeMap : Map<String, NotificationType> by lazy {
        mapOf(
                "chat.newMessage" to NotificationType.MESSAGE,
                "users.newMatch" to NotificationType.MATCH,
                "users.newLikeReceived" to NotificationType.NEW_LIKE_RECEIVED,
                "users.unmatched" to NotificationType.UN_MATCHED,
                "counters.counterReset" to NotificationType.COUNTER_RESET,
                "users.deleted" to NotificationType.USER_DELETED,
                "users.profileCompletionReminder" to NotificationType.PROFILE_COMPLETION_REMINDER,
                "users.verifyUserAttribute" to NotificationType.VERIFY_USER_ATTRIBUTE,
                "users.newReferralReward" to NotificationType.NEW_REFERRAL_REWARD,
                "chat.isLikedMessageStateChanged" to NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED,
                "updateRemoteConfig" to NotificationType.UPDATE_REMOTE_CONFIG,
                "users.dislikeInstachat" to NotificationType.DISLIKE_INSTACHAT,
                "users.badge2Updated" to NotificationType.BADGE_2,
                "videoCalls.newVideoCall" to NotificationType.VIDEOCALLS_NEWVIDEOCALL,
                "videoCalls.endVideoCall" to NotificationType.END_VIDEO_CALL,
                "videoCalls.videoCallAccepted" to NotificationType.VIDEO_CALL_ACCEPTED,
                "users.verifyImage" to NotificationType.VERIFY_YOUR_IMAGE,
                "in-app-purchases.newLocalSubscription" to NotificationType.NEW_LOCAL_SUBSCRIPTION,
                "download-data.userDataRequestProcessed" to NotificationType.DOWNLOAD_DATA,
                "users.impressionsRewarded" to NotificationType.IMPRESSIONS_REWARDED,
                "users.disabledStateChanged" to NotificationType.DISABLED_STATE_CHANGED,
                "openPremiumPaywall" to NotificationType.OPEN_PREMIUM_PAYWALL,
                "premiumSpecialOffer" to NotificationType.PREMIUM_SPECIAL_OFFER,
                "premiumPay1Get1Offer" to NotificationType.PREMIUM_PAY_1_GET_1,
                "users.boostSuccess" to NotificationType.BOOST_SUCCESS,
                "users.boostFailed" to NotificationType.BOOST_FAILED,
                "users.push_badge2_verification" to NotificationType.PUSH_BADGE2_VERIFICATION,
                "users.instachatLastHour" to NotificationType.INSTACHAT_LASTHOUR,
                "users.RMODLastHour" to NotificationType.RMOD_LASTHOUR,
                "in-app-purchases.showDontLetGoOffer" to NotificationType.SHOW_DONT_LET_GO_OFFER,
                "users.newGroupedLikes" to NotificationType.NEW_GROUPED_LIKES,
                "premiumOffer" to NotificationType.PREMIUM_OFFER,
                "setupAccountCredentials" to NotificationType.SETUP_ACCOUNT_CREDENTIALS,
                "purchaselyPaywall" to NotificationType.PURCHASELY_PAYWALL,
                "forcePurchaselyPaywall" to NotificationType.FORCE_PURCHASELY_PAYWALL,
                "purchaselyBoostOffer" to NotificationType.PURCHASELY_BOOST_PAYWALL,
                "users.rewardMaleUsersWithExtraInteractions" to NotificationType.EXTRA_SWIPES_REWARDED_MALE,
                "users.rewardVideos" to NotificationType.REWARD_VIDEOS,
                "users.featuredUsersReset" to NotificationType.FEATURED_USERS_RESET,
                "users.consumableRewardGiven" to NotificationType.CONSUMABLE_REWARD_GIVEN,
                "users.newProfileVisitReceived" to NotificationType.NEW_PROFILE_VISIT_RECEIVED,
                "showInstachatPaywall" to NotificationType.SHOW_INSTACHAT_PAYWALL,
                "showBoostPaywall" to NotificationType.SHOW_BOOST_PAYWALL,
                "users.premiumRemovedDueToGenderChange" to NotificationType.PREMIUM_REMOVED_DUE_TO_GENDER_CHANGE,
                "recommender.RMODGenerated" to NotificationType.RMOD_GENERATED,
                "recommender.RMODRemoved" to NotificationType.RMOD_REMOVED,
                "realTimePurchaselyOffer" to NotificationType.REAL_TIME_PURCHASELY_OFFER,
                "users.crossPathCreated" to NotificationType.CROSS_PATH_CREATED
        )
}



 fun convertJsonDataToModel(type: NotificationType, jsonData: JsonElement): Any? {

        val jsonData =
                (if(jsonData.isJsonObject) jsonData.asJsonObject else if(jsonData.isJsonArray) jsonData.asJsonArray else null)
                        ?: return null
        return when (type) {
                NotificationType.MESSAGE -> Gson().fromJson(jsonData, ConversationData::class.java)
                NotificationType.MATCH -> {
                     val listType =   TypeToken.getParameterized(ArrayList::class.java, UserMatchNotificationResponse::class.java).type
                      Gson().fromJson<ArrayList<UserMatchNotificationResponse>>(jsonData, listType).firstOrNull()
                }
                NotificationType.NEW_LIKE_RECEIVED -> Gson().fromJson(jsonData,UserLikedYouNotificationResponse::class.java)
                NotificationType.UN_MATCHED -> Gson().fromJson(jsonData, UserUnMatchedModel::class.java)
                NotificationType.COUNTER_RESET -> Gson().fromJson(jsonData, CounterResetDataModel::class.java)
                NotificationType.USER_DELETED ->  Gson().fromJson(jsonData, UserDeletedModel::class.java)
                NotificationType.PROFILE_COMPLETION_REMINDER ->  "PROFILE_COMPLETION_REMINDER"
                NotificationType.VERIFY_USER_ATTRIBUTE -> "VERIFY_USER_ATTRIBUTE"
                NotificationType.NEW_REFERRAL_REWARD -> "NEW_REFERRAL_REWARD"
                NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED -> Gson().fromJson(jsonData, LikeMessageModel::class.java)
                NotificationType.UPDATE_REMOTE_CONFIG -> "UPDATE_REMOTE_CONFIG"
                NotificationType.DISLIKE_INSTACHAT -> Gson().fromJson(jsonData, DislikeInstaChatModel::class.java)
                NotificationType.BADGE_2 -> Gson().fromJson(jsonData, Badge2ApprovalModel::class.java)
                NotificationType.VIDEOCALLS_NEWVIDEOCALL -> Gson().fromJson(jsonData, CallModel::class.java)
                NotificationType.END_VIDEO_CALL -> Gson().fromJson(jsonData, EndVideoCallModel::class.java)
                NotificationType.VIDEO_CALL_ACCEPTED -> Gson().fromJson(jsonData, VideoCallAcceptedModel::class.java)
                NotificationType.VERIFY_YOUR_IMAGE -> "VERIFY_YOUR_IMAGE"
                NotificationType.DOWNLOAD_DATA -> "DOWNLOAD_DATA"
                NotificationType.NEW_LOCAL_SUBSCRIPTION -> Gson().fromJson(jsonData, NewLocalSubscriptionModel::class.java)
                NotificationType.IMPRESSIONS_REWARDED -> Gson().fromJson(jsonData, ImpressionsRewarded::class.java)
                NotificationType.DISABLED_STATE_CHANGED -> Gson().fromJson(jsonData, DisableStateChanged::class.java)
                NotificationType.OPEN_PREMIUM_PAYWALL -> "openPremiumPaywall"
                NotificationType.PREMIUM_SPECIAL_OFFER -> Gson().fromJson(jsonData, SpecialOfferDataModel::class.java)
                NotificationType.PREMIUM_PAY_1_GET_1 -> Gson().fromJson(jsonData, SpecialOfferDataModel::class.java)
                NotificationType.BOOST_SUCCESS -> Gson().fromJson(jsonData, BoostResultModel::class.java)
                NotificationType.BOOST_FAILED -> Gson().fromJson(jsonData, BoostResultModel::class.java)
                NotificationType.PUSH_BADGE2_VERIFICATION -> Gson().fromJson(jsonData, PushBadge2VerificationModel::class.java)
                NotificationType.INSTACHAT_LASTHOUR ->  Gson().fromJson(jsonData, LastHourInstachatModel::class.java)
                NotificationType.RMOD_LASTHOUR ->  "users.RMODLastHour"
                NotificationType.SHOW_DONT_LET_GO_OFFER -> Gson().fromJson(jsonData,
                        DontLetGoOfferNotificationModel::class.java)
                NotificationType.NEW_GROUPED_LIKES -> "users.newGroupedLikes"
                NotificationType.PREMIUM_OFFER -> Gson().fromJson(jsonData,
                        PremiumIdModel::class.java)
                NotificationType.SETUP_ACCOUNT_CREDENTIALS -> "setupAccountCredentials"
                NotificationType.PURCHASELY_PAYWALL -> Gson().fromJson(jsonData, PurchaselyPaywalllModel::class.java)
                NotificationType.FORCE_PURCHASELY_PAYWALL -> Gson().fromJson(jsonData, PurchaselyPaywalllModel::class.java)
                NotificationType.PURCHASELY_BOOST_PAYWALL -> Gson().fromJson(jsonData, PurchaselyPaywalllModel::class.java)
                NotificationType.EXTRA_SWIPES_REWARDED_MALE -> "users.rewardMaleUsersWithExtraInteractions"
                NotificationType.REWARD_VIDEOS -> Gson().fromJson(jsonData, NotificationRewardModel::class.java)
                NotificationType.FEATURED_USERS_RESET -> "users.featuredUsersReset"
                NotificationType.CONSUMABLE_REWARD_GIVEN -> Gson().fromJson(jsonData, ConsumableRewardGivenModel::class.java)
                NotificationType.NEW_PROFILE_VISIT_RECEIVED -> Gson().fromJson(jsonData,ProfileVisitedModel::class.java)
                NotificationType.SHOW_INSTACHAT_PAYWALL -> "showInstachatPaywall"
                NotificationType.SHOW_BOOST_PAYWALL -> "showBoostPaywall"
                NotificationType.PREMIUM_REMOVED_DUE_TO_GENDER_CHANGE -> "users.premiumRemovedDueToGenderChange"
                NotificationType.RMOD_GENERATED -> "recommender.RMODGenerated"
                NotificationType.RMOD_REMOVED -> "recommender.RMODRemoved"
                NotificationType.REAL_TIME_PURCHASELY_OFFER ->  Gson().fromJson(jsonData, RealTimeClevertapOfferModel::class.java)
                NotificationType.CROSS_PATH_CREATED -> Gson().fromJson(jsonData,CrossPathCreatedNotificationModel::class.java)
                NotificationType.NONE -> "NONE"
                else -> null
        }
}

fun mapNotificationImage(type: NotificationType, data: Any?): Int {
        return when (type) {
                NotificationType.PUSH_BADGE2_VERIFICATION -> R.drawable.ic_notification_verified
                NotificationType.NEW_LIKE_RECEIVED,NotificationType.NEW_GROUPED_LIKES -> R.drawable.ic_notification_like
                NotificationType.BOOST_SUCCESS, NotificationType.BOOST_FAILED -> R.drawable.ic_notification_boost
                NotificationType.FEATURED_USERS_RESET-> R.drawable.ic_notification_refresh
                NotificationType.IMPRESSIONS_REWARDED,
                NotificationType.EXTRA_SWIPES_REWARDED_MALE,
                NotificationType.CONSUMABLE_REWARD_GIVEN-> R.drawable.notification_feed_reward_illustration
                NotificationType.PREMIUM_OFFER,
                NotificationType.PREMIUM_SPECIAL_OFFER,
                NotificationType.SHOW_DONT_LET_GO_OFFER,
                NotificationType.PREMIUM_PAY_1_GET_1,
                NotificationType.PURCHASELY_BOOST_PAYWALL,
                NotificationType.SHOW_BOOST_PAYWALL,
                NotificationType.PURCHASELY_PAYWALL-> R.drawable.notification_feed_offers_illustration
                NotificationType.FORCE_PURCHASELY_PAYWALL-> R.drawable.notification_feed_offers_illustration
                NotificationType.INSTACHAT_LASTHOUR -> R.drawable.ic_notification_instachat
                NotificationType.RMOD_LASTHOUR -> R.drawable.rmod_icon
                NotificationType.VERIFY_YOUR_IMAGE -> R.drawable.ic_notification_verified
                NotificationType.COUNTER_RESET -> {
                        when((data as CounterResetDataModel).type){
                                CounterResetDataModelType.INTERACTION.value -> R.drawable.ic_notification_like
                                CounterResetDataModelType.INSTA_CHAT.value -> R.drawable.ic_notification_instachat
                                else -> R.drawable.ic_notification_feed_default
                        }
                }
                NotificationType.NEW_LOCAL_SUBSCRIPTION,NotificationType.PREMIUM_REMOVED_DUE_TO_GENDER_CHANGE -> R.drawable.ic_notification_premium
                NotificationType.DISABLED_STATE_CHANGED -> if((data as DisableStateChanged).isDisabled) R.drawable.ic_user_disabled else R.drawable.ic_user_enabled
                NotificationType.BADGE_2 -> when((data as Badge2ApprovalModel).badge2) {
                        Badge2Status.APPROVED.status -> R.drawable.ic_notification_verification_approved
                        Badge2Status.NOT_APPROVED.status -> R.drawable.ic_notification_verification_denied
                        else -> R.drawable.ic_notification_verified
                }
                NotificationType.SETUP_ACCOUNT_CREDENTIALS -> R.drawable.notification_feed_spotted_illustration
                NotificationType.REAL_TIME_PURCHASELY_OFFER -> R.drawable.ic_notification_premium
                else -> R.drawable.ic_notification_feed_default
        }
}

suspend fun  NotificationFeedResponse.NotificationFeedModel.mapToUiModel(): NotificationFeedUiModel {
        val type = actionTypeMap[action] ?: NotificationType.NONE
        val data: Any? = withContext(Dispatchers.IO)  {jsonData?.let { convertJsonDataToModel(type, it) } }
        var actionTitle : Int? = actionTitleMap[type]
        if (type == NotificationType.BADGE_2 && (data as? Badge2ApprovalModel)?.badge2 != Badge2Status.NOT_APPROVED.status) {
                actionTitle = null
        }

        val notificationImage = mapNotificationImage(type, data)

        val hasProfilePicture = type == NotificationType.MESSAGE || type == NotificationType.MATCH || type == NotificationType.NEW_LIKE_RECEIVED || type == NotificationType.NEW_PROFILE_VISIT_RECEIVED

        return NotificationFeedUiModel(
                id = id,
                title = title,
                type = type,
                data = data,
                actionTitleResId = actionTitle,
                notificationImage = notificationImage,
                timeAgo = "",
                createdAt = createdAt,
                hasProfilePicture = hasProfilePicture,
                read = read
        )
}

fun timeAgo(context: Context,createdAt: Long): String {
        val currentDate = Date()
        val dateCreated = Date(createdAt)

        val timeDifference = currentDate.time - dateCreated.time
        val seconds = (timeDifference / 1000).toInt()
        val minutes = seconds / 60
        val hours = minutes / 60
        val days = hours / 24

        return when {
                days >= 30 -> {
                        val months = days / 30
                        context.getString(R.string.received_month_ago, months)

                }
                days >= 1 -> {
                        context.getString(if(days == 1) R.string.received_day_ago else R.string.received_days_ago, days)
                }
                hours >= 1 -> {
                        context.getString(if(hours == 1) R.string.received_hour_ago else R.string.received_hours_ago, hours)
                }
                minutes >= 1 -> {
                        context.getString(if(minutes == 1) R.string.received_minute_ago else R.string.received_minutes_ago, minutes)
                }
                else -> {
                        context.getString(R.string.received_minute_ago,days)
                }
        }
}


