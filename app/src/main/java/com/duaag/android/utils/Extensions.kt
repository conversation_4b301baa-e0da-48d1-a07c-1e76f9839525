package com.duaag.android.utils

import android.animation.ValueAnimator
import android.app.Activity
import android.app.ActivityManager
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Context.ACTIVITY_SERVICE
import android.content.Intent
import android.content.Intent.CATEGORY_DEFAULT
import android.content.Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.content.Intent.FLAG_ACTIVITY_NO_HISTORY
import android.content.pm.PackageManager
import android.content.pm.PackageManager.GET_META_DATA
import android.content.res.ColorStateList
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.location.Location
import android.net.Uri
import android.os.*
import android.provider.Settings
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.util.DisplayMetrics
import android.util.Patterns
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.webkit.URLUtil
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.IdRes
import androidx.appcompat.app.AlertDialog
import androidx.browser.customtabs.CustomTabsIntent
import androidx.browser.customtabs.CustomTabsService
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.withStateAtLeast
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.duaag.android.views.BlurScrollListener
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapLocationServiceStatusValues
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.HomeActivity.Companion.IS_SIGNING_UP
import com.duaag.android.home.HomeActivity.Companion.IS_SPOTTED_USER
import com.duaag.android.home.fragments.HomeFragment.Companion.ANIMATION_DOT_DURATION
import com.duaag.android.home.models.AnalyticsInAppProductType
import com.duaag.android.home.models.GoogleBillingPackageType
import com.duaag.android.home.models.PaymentType
import com.duaag.android.profile_builder.ProfileBuilderActivity
import com.duaag.android.settings.SettingsWebViewActivity
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.SignUpActivity
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.snackbar.Snackbar
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import net.swiftzer.semver.SemVer
import timber.log.Timber
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext
import kotlin.math.log10
import kotlin.math.pow
import kotlin.math.sqrt
import kotlin.reflect.KFunction1


//Calendar Extension
/** explaining pattern for this extension
 * pattern: EEEE is for day of week ex: Monday
 * pattern: EEE is also for short day of week ex: Mon
 * pattern: dd is for day of month ex: 01 to 31
 * pattern: MM is for month ex: 01 to 12
 * pattern: MMM is for month ex: Jan
 * pattern: MMMM is for month ex: January
 * pattern: yyyy is for year ex: 2019
 * pattern: HH is for hour ex: 07
 * pattern: mm is for minute ex: 48
 * pattern: ss is for second ex: 22 */

fun Long.toReadableDateUTC(pattern: String): String {
    val calendar = Calendar.getInstance()
    val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    sdf.timeZone = TimeZone.getTimeZone("UTC")
    val data = Date()
    data.time = this
    calendar.time = data

    val weekDay: String
    val dayFormat = SimpleDateFormat(pattern, Locale.getDefault())

    weekDay = dayFormat.format(calendar.time)

    return weekDay
}


fun isEmailValid(emailAddress: String): Boolean {
    if (emailAddress.isBlank()) return false

    return Patterns.EMAIL_ADDRESS.matcher(emailAddress).matches()
}

fun isPasswordValid(password: String): Boolean =
        password.matches("^(?=\\D*\\d)[^ ]{6,}\$".toRegex())


fun TextView.newTypeFace(font: Int) {
    this.typeface = ResourcesCompat.getFont(this.context, font)
}

fun TextView.setTextColorRes(@ColorRes colorRes: Int) {
    val color = ContextCompat.getColor(context, colorRes)
    setTextColor(color)
}

fun showKeyboardFrom(context: Context) {
    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
    imm!!.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0)
}

fun EditText.showKeyboard() {
    if (requestFocus()) {
        val imgr = this.context?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imgr.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT)
    }
}

/**
 * This method converts dp unit to equivalent pixels, depending on device density.
 *
 * @param dp A value in dp (density independent pixels) unit. Which we need to convert into pixels
 * @param context Context to get resources and device specific display metrics
 * @return A float value to represent px equivalent to dp depending on device density
 */
fun convertDpToPixel(dp: Float, context: Context): Float {
    return dp * (context.resources.displayMetrics.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT)
}

/**
 * This method converts device specific pixels to density independent pixels.
 *
 * @param px A value in px (pixels) unit. Which we need to convert into db
 * @param context Context to get resources and device specific display metrics
 * @return A float value to represent dp equivalent to px value
 */
fun convertPixelsToDp(px: Float, context: Context): Float {
    return px / (context.resources.displayMetrics.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT)
}

fun Double.roundToDecimals(numDecimalPlaces: Int): Double {
    val factor = Math.pow(10.0, numDecimalPlaces.toDouble())
    return Math.round(this * factor) / factor

}

fun FloatingActionButton.disableButton() {
    this.isEnabled = false
    this.backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.border))
    this.setImageDrawable(ContextCompat.getDrawable(this.context, R.drawable.ic_arrow_forw_disabled))
    this.show()
}

fun FloatingActionButton.enableButton(color: Int = R.color.pink_500) {
    this.isEnabled = true
    this.backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, color))
    this.setImageDrawable(ContextCompat.getDrawable(this.context, R.drawable.ic_arrow_forw_white))
    this.show()
}

fun Context.openWebView(url: String, name: String?, activity: Activity) {
    val intent = Intent(this, SettingsWebViewActivity::class.java)
    val b = Bundle()
//    b.putString(getString(R.string.key_webView), url )
    b.putStringArrayList(getString(R.string.key_webView), arrayListOf(url, name))
    intent.putExtras(b)
    startActivity(intent)
    activity.overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left)
}

fun openHomeActivity(context: Activity, isSigningUp: Boolean = false, isSpottedUser: Boolean = false) {
    val mainIntent = Intent(context, HomeActivity::class.java)
    val extras: Bundle? = context.intent.extras
    extras?.let {
        mainIntent.putExtras(it)
    }
    mainIntent.putExtra(IS_SIGNING_UP, isSigningUp)
    mainIntent.putExtra(IS_SPOTTED_USER, isSpottedUser)
    mainIntent.flags = FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
    context.startActivity(mainIntent)
    context.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
}

fun openProfileBuilderActivity(context: Activity, userModel: UserModel, isSigningUp: Boolean = false, isSpottedUser: Boolean = false) {
     val mainIntent = ProfileBuilderActivity.createIntent(context, userModel, isSigningUp, isSpottedUser)
    mainIntent.flags = FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
    context.startActivity(mainIntent)
    context.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
}

fun openCreateProfile(context: Activity) {
    val intent = Intent(context, SignUpActivity::class.java)
    intent.putExtra(SignUpActivity.DIRECT_TO_CREATE_PROFILE, true)
    intent.flags = FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
    context.startActivity(intent)
    context.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
}

fun getSaltString(): String {
    val saltchars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".toLowerCase()
    val salt = StringBuilder()
    val rnd = Random()
    while (salt.length <= 64) { // length of the random string.
        val index = (rnd.nextFloat() * saltchars.length).toInt()
        salt.append(saltchars[index])
    }
    return salt.toString()
}

fun getS3Url(key: String) = if (URLUtil.isValidUrl(key)) key else "${RemoteConfigUtils.getCdnDomain()}$key"

fun ellipsizeText(tv: String): String {
    val maxLength = 9
    if (tv.length > maxLength) {
        return tv.substring(0, maxLength - 3) + "..."
    }
    return tv
}

fun getFileSize(size: Long): String {
    if (size <= 0) {
        return "0"
    }
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    val digitGroups = (log10(size.toDouble()) / log10(1024.0)).toInt()
    return DecimalFormat("#,##0.#").format(size / 1024.0.pow(digitGroups.toDouble())) + " " + units[digitGroups]
}

fun EditText.onKeyboardNext(callback: () -> Unit) {
    setOnEditorActionListener { _, actionId, _ ->
        if (actionId == EditorInfo.IME_ACTION_NEXT) {
            callback.invoke()
        }
        false
    }
}

fun EditText.onKeyboardDone(callback: () -> Unit) {
    setOnEditorActionListener { _, actionId, _ ->
        if (actionId == EditorInfo.IME_ACTION_DONE) {
            callback.invoke()
        }
        false
    }
}

fun Fragment.hideKeyboard() {
    view?.let { activity?.hideKeyboard(it) }
}

fun Activity.hideKeyboard() {
    hideKeyboard(currentFocus ?: View(this))
}

fun Context.hideKeyboard(view: View) {
    val inputMethodManager = getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
    inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
}

@Suppress("DEPRECATION")
internal fun Configuration.getLocaleCompat(): Locale {
    return if (isAtLeastSdkVersion(Build.VERSION_CODES.N)) locales.get(0) else locale
}

internal fun isAtLeastSdkVersion(versionCode: Int): Boolean {
    return Build.VERSION.SDK_INT >= versionCode
}

fun isAlertDialogShowing(dialog: AlertDialog?): Boolean {
    return dialog?.isShowing!!
}

fun isFragmentDialogShowing(dialog: DialogFragment?): Boolean {
    return dialog?.dialog?.isShowing == true
}

internal fun Activity.resetTitle() {
    try {
        val info = packageManager.getActivityInfo(componentName, GET_META_DATA)
        if (info.labelRes != 0) {
            setTitle(info.labelRes)
        }
    } catch (e: PackageManager.NameNotFoundException) {
        e.printStackTrace()
    }
}

fun Long.formatTime(): String? {
    val dateFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    return dateFormat.format(this)
}

fun Long.formatDownloadDataTime(): String {
    val dateFormat = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
    return dateFormat.format(this)
}

fun Long.formatDate(): String {
    val dateFormat = SimpleDateFormat("dd.MM.yyyy", Locale.getDefault())
    return dateFormat.format(this)
}

fun TextView.makeLinks(textColor: Int? = null, vararg links: Pair<String?, View.OnClickListener>) {
    val text = this.text
    val spannableString = SpannableString(text)
    val mediumTypeface = ResourcesCompat.getFont(context, R.font.tt_norms_pro_medium)

    for (link in links) {
        val textToUnderline = link.first
        var startIndexOfLink = textToUnderline?.let { text.toString().indexOf(it, 0) }
        val clickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                Selection.setSelection((view as TextView).text as Spannable, 0)
                view.invalidate()
                link.second.onClick(view)
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
                textColor?.let { ds.color = resources.getColor(it) }
                if (mediumTypeface != null) {
                    ds.typeface = mediumTypeface
                }
            }
        }
        var ofs = 0
        while (ofs < text.length && startIndexOfLink != -1) {
            startIndexOfLink = textToUnderline?.let { text.indexOf(it, ofs) }
            if (startIndexOfLink == -1) break else {
                startIndexOfLink?.let {
                    if (textToUnderline != null) {
                        spannableString.setSpan(clickableSpan, it, startIndexOfLink + textToUnderline.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }
                }
                this.movementMethod = LinkMovementMethod.getInstance() // without LinkMovementMethod, link can not click
                this.setText(spannableString, TextView.BufferType.SPANNABLE)
            }
            if (startIndexOfLink != null) {
                ofs = startIndexOfLink + 1
            }
        }
    }

}

fun View.setOnSingleClickListener(l: View.OnClickListener) {
    setOnClickListener(OnSingleClickListener(l))
}

fun View.setOnSingleClickListener(delay: Long?, l: (View) -> Unit) {
    setOnClickListener(OnSingleClickListener(delay, l ))
}

fun View.onDoubleClick(l: (View) -> Unit) {
    setOnClickListener(object : DoubleClickListener() {
        override fun onDoubleClick(v: View) {
            l.invoke(v)
        }

    })
}

fun View.setMargin(start: Float? = null, top: Float? = null, end: Float? = null, bottom: Float? = null) {
    layoutParams<ViewGroup.MarginLayoutParams> {
        start?.run { leftMargin = dpToPx(this) }
        top?.run { topMargin = dpToPx(this) }
        end?.run { rightMargin = dpToPx(this) }
        bottom?.run { bottomMargin = dpToPx(this) }
    }
}

inline fun <reified T : ViewGroup.LayoutParams> View.layoutParams(block: T.() -> Unit) {
    if (layoutParams is T) block(layoutParams as T)
}

fun View.dpToPx(dp: Float): Int = context.dpToPx(dp)
fun Context.dpToPx(dp: Float): Int = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, resources.displayMetrics).toInt()

fun RecyclerView.smoothSnapToPosition(position: Int, snapMode: Int = LinearSmoothScroller.SNAP_TO_START) {
    val smoothScroller = object : LinearSmoothScroller(this.context) {
        override fun getVerticalSnapPreference(): Int {
            return snapMode
        }

        override fun getHorizontalSnapPreference(): Int {
            return snapMode
        }
    }
    smoothScroller.targetPosition = position
    layoutManager?.startSmoothScroll(smoothScroller)
}

fun Activity.setTransparentStatusBar() {
    window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
    window.statusBarColor = Color.TRANSPARENT
}

val facebookPermissions = listOf("email", "user_birthday", "user_gender")

fun View.snack(message: String, backgroundColor: Int, textColor: Int, animationMode: Int, duration: Int = Snackbar.LENGTH_SHORT, fontFamily: Int): Snackbar {
    return Snackbar.make(this, message, duration).apply {
        //set animation of snackbar
        this.animationMode = animationMode
        //change color of the snackbar
        val view = this.view
        view.setBackgroundColor(ContextCompat.getColor(<EMAIL>, backgroundColor))
        //center snackbar text
        val snackTextView = view.findViewById<TextView>(com.google.android.material.R.id.snackbar_text)
        snackTextView.setTextColor(ContextCompat.getColor(view.context, textColor))
        snackTextView.setTypeface( ResourcesCompat.getFont(context, fontFamily))


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            snackTextView.textAlignment = View.TEXT_ALIGNMENT_CENTER
        } else {
            snackTextView.gravity = Gravity.CENTER_HORIZONTAL
        }
        //get params to be able to show snack on the top of the view
        val params = CoordinatorLayout.LayoutParams(CoordinatorLayout.LayoutParams.MATCH_PARENT, CoordinatorLayout.LayoutParams.WRAP_CONTENT)
//        params.setMargins(left, top, right, bottom)
        params.gravity = Gravity.TOP
        params.anchorGravity = Gravity.TOP
        view.layoutParams = params
        //show snack
    }
}


fun View.longSnack(message: String, backgroundColor: Int, textColor: Int, animationMode: Int, fontFamily: Int) = snack(message, duration = Snackbar.LENGTH_INDEFINITE, backgroundColor = backgroundColor, textColor = textColor, animationMode = animationMode, fontFamily = fontFamily)


fun createBlacklistDialog(context: Activity, userId: String?) {
    val builder = android.app.AlertDialog.Builder(context)

    builder.setMessage(R.string.if_you_want_us_to_unblock_your_account_please_submit_a_ticket_here)

    builder.setPositiveButton(R.string.submit_ticket) { _, _ ->
        val emailIntent = Intent(Intent.ACTION_SENDTO)
        emailIntent.data = Uri.parse("mailto:")
        emailIntent.putExtra(Intent.EXTRA_EMAIL, arrayOf(context.getString(R.string.dua_support)))
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, "Ticket Submission")
        emailIntent.putExtra(Intent.EXTRA_TEXT, userId)

        try {
            context.startActivity(Intent.createChooser(emailIntent, "Send mail..."))
        } catch (ex: ActivityNotFoundException) {
            ToastUtil.toast("There is no email client installed.")
        }
    }
    builder.setNegativeButton(R.string.cancel) { dialog, _ -> dialog.dismiss() }

    val dialog = builder.create()
    dialog.setOnShowListener {
        dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(context, R.color.dua_red_color))
        dialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.dua_red_color))
    }
    dialog.show()
}

fun Context?.launchCustomTab(url: String?): Unit? {
    return this?.let {
        CustomTabsIntent.Builder()
                .setStartAnimations(it, R.anim.enter_from_right, R.anim.exit_to_left)
                .build()
                .launchUrl(this, Uri.parse(url))
    }
}


fun String?.isChromeTabSupported(context: Context): Boolean {
    // Get default VIEW intent handler that can view a web url.
    val activityIntent = Intent(Intent.ACTION_VIEW, Uri.parse(this))

    // Get all apps that can handle VIEW intents.
    val pm = context.packageManager
    val resolvedActivityList = pm.queryIntentActivities(activityIntent, 0)
    for (info in resolvedActivityList) {
        val serviceIntent = Intent()
        serviceIntent.action = CustomTabsService.ACTION_CUSTOM_TABS_CONNECTION
        serviceIntent.setPackage(info.activityInfo.packageName)
        if (pm.resolveService(serviceIntent, 0) != null) {
            return true
        }
    }
    return false
}


fun getTimeStampAfterXHours(x: Int) = System.currentTimeMillis() + x * 3600 * 1000

fun getTimeStampAfter12Hours() = System.currentTimeMillis() + 43200000

fun getTimeStampAfter24Hours() = System.currentTimeMillis() + 86400000
fun Context.vibrateTap() {
    val vibrator = this.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator

    vibrator ?: return
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        vibrator.vibrate(VibrationEffect.createOneShot(50L, 1))
    } else {
        // Deprecated, but the replacement is only for 26+, so must use deprecated version
        @Suppress("DEPRECATION")
        vibrator.vibrate(50L)
    }
}

//this format is agreed upon iOs, Android, Backend. Use this only for this case
fun String?.toGifFormat(): String? {
    return this?.substringBefore(" ")?.replace(".mp4", ".gif")
}

fun View.simulateButtonPress() {
    val now: Long = SystemClock.uptimeMillis()
    val pressEvent = MotionEvent.obtain(now, now, MotionEvent.ACTION_DOWN, width / 2.toFloat(), height / 2.toFloat(), 0)
    dispatchTouchEvent(pressEvent)
    Handler().postDelayed({
        val now: Long = SystemClock.uptimeMillis()
        val cancelEvent = MotionEvent.obtain(now, now, MotionEvent.ACTION_CANCEL, width / 2.toFloat(), height / 2.toFloat(), 0)
        dispatchTouchEvent(cancelEvent)
    }, 300)
}

fun EditText.setMaxLength(maxLength: Int) {
    filters = arrayOf<InputFilter>(InputFilter.LengthFilter(maxLength))
}

fun <T, K> LiveData<T>.combineWith(
    liveData: LiveData<K>,
    lifecycleOwner: LifecycleOwner,
    block: (T?, K?) -> Unit
) {
    val observer = androidx.lifecycle.Observer<Any?> {
        block(this.value, liveData.value)
    }

    // Observe changes with lifecycle awareness
    this.observe(lifecycleOwner, observer)
    liveData.observe(lifecycleOwner, observer)
}



fun TextView?.setNewColor(color: Int) {
    this?.setTextColor(ContextCompat.getColor(this.context, color))
}


fun getPackageTypeForId(packageId: String): String? {
    return if(packageId.contains(GoogleBillingPackageType.PREMIUM.type))
        PaymentType.PREMIUM.type
    else if(packageId.contains(GoogleBillingPackageType.INSTACHAT.type))
        PaymentType.INSTACHATS.type
    else if(packageId.contains(GoogleBillingPackageType.UNDO.type))
        PaymentType.UNDO.type
    else if(packageId.contains(GoogleBillingPackageType.BOOST.type))
        PaymentType.BOOST.type
    else if(packageId.contains(GoogleBillingPackageType.FLYING.type))
        PaymentType.FLYING.type
    else if(packageId.contains(GoogleBillingPackageType.IMPRESSIONS.type))
        PaymentType.IMPRESSIONS.type
    else null
}

fun getProductTypeForId(packageId: String): String? {
    return if(packageId.contains(GoogleBillingPackageType.INSTACHAT.type))
        AnalyticsInAppProductType.INSTACHAT.type
    else if(packageId.contains(GoogleBillingPackageType.UNDO.type))
        AnalyticsInAppProductType.UNDO.type
    else if(packageId.contains(GoogleBillingPackageType.BOOST.type))
        AnalyticsInAppProductType.BOOST.type
    else if(packageId.contains(GoogleBillingPackageType.FLYING.type))
        AnalyticsInAppProductType.FLIGHT.type
    else if(packageId.contains(GoogleBillingPackageType.IMPRESSIONS.type))
        AnalyticsInAppProductType.IMPRESSION.type
    else null
}

fun RecyclerView.setDivider(@DrawableRes drawableRes: Int) {
    val divider = DividerItemDecoration(
            this.context,
            DividerItemDecoration.VERTICAL
    )
    val drawable = ContextCompat.getDrawable(
            this.context,
            drawableRes
    )
    drawable?.let {
        divider.setDrawable(it)
        addItemDecoration(divider)
    }
}

/**
 * Simulate a button click, including a small delay while it is being pressed to trigger the
 * appropriate animations.
 */
fun ImageButton.simulateClick(delay: Long = 100L) {
    performClick()
    isPressed = true
    invalidate()
    postDelayed({
        invalidate()
        isPressed = false
    }, delay)
}

fun isHomeLocationUser(countryCode: String): Boolean {
    val homeLocations = DuaApplication.instance.getString(R.string.home_countries_country_code).split(",")
    return homeLocations.contains(countryCode)
}

fun ViewPager2.autoScroll(lifecycleScope: LifecycleCoroutineScope, interval: Long) {
    lifecycleScope.launchWhenResumed {
        scrollIndefinitely(interval)
    }
}

private suspend fun ViewPager2.scrollIndefinitely(interval: Long) {
    delay(interval)
    val numberOfItems = adapter?.itemCount ?: 0
    val lastIndex = if (numberOfItems > 0) numberOfItems - 1 else 0
    val nextItem = if (currentItem == lastIndex) 0 else currentItem + 1

    setCurrentItem(nextItem, true)

    scrollIndefinitely(interval)
}

fun getHourFromErrorMessage(message: String): String {
    return try {
        val regex = "\\d{1,2}:\\d{1,2}".toRegex()
        val time = regex.find(message)?.value
        val hour = time?.substringBefore(":")?.toInt()?.plus(1)
        if (hour != null) {
            DuaApplication.instance.getString(R.string.limit_reached_please_try_again_in_x_hours, hour)
        } else {
            DuaApplication.instance.getString(R.string.too_many_times)
        }
    } catch (e: Exception) {
        message
    }
}
fun getStringPlaceHolder(startOfString: String, middleOfString: String, endOfString: String?,color:Int ,context: Context,fontFamily: Int): Spannable {

    val wordToSpan = if (endOfString.toString() == "."){
        SpannableString("$startOfString $middleOfString$endOfString")
    }else{
        SpannableString("$startOfString $middleOfString $endOfString")
    }
    val typeface = ResourcesCompat.getFont(context, fontFamily)

//    middleOfString?.toCharArray()?.lastIndex?.let {
    wordToSpan.setSpan(
            typeface?.let { CustomTypefaceSpan(it) },
            startOfString.length,
            startOfString.length + middleOfString.length + 1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
//}


//    middleOfString?.toCharArray()?.lastIndex?.let {
    wordToSpan.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(context,color)),
            startOfString.length,
            startOfString.length + middleOfString.length + 1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
//    }
    return wordToSpan
}

fun validateName(text: String): Boolean {
    val pattern = "^(?=.{2,20}\$)(?!-)(?!.*-\$)(?:[A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u017F\\u0370-\\u03FF\\u0400-\\u04FF\\u0600-\\u06FF]+(?:-[A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u017F\\u0370-\\u03FF\\u0400-\\u04FF\\u0600-\\u06FF]+)?)\$"
    val regex = pattern.toRegex()

    return text.matches(regex)
}

fun containsSpaces(text: String): Boolean {
    return text.contains(" ")
}

//Use this to avoid multiple navigation attempts

fun NavController.navigateSafer(
    @IdRes resId: Int,
    args: Bundle? = null,
    navOptions: NavOptions? = null,
    navExtras: Navigator.Extras? = null
) {
    val action = currentDestination?.getAction(resId) ?: graph.getAction(resId)
    if (action != null && currentDestination?.id != action.destinationId) {
        navigate(resId, args, navOptions, navExtras)
    }
}

fun isCallServiceRunning(context: Context): Boolean {
    val manager: ActivityManager? = context.getSystemService(ACTIVITY_SERVICE) as ActivityManager?
    manager?.let {
        for (service in manager.getRunningServices(Int.MAX_VALUE)) {
            if ("com.duaag.android.calls.services.CallService" == service.service.className) {
                return true
            }
        }
    }
    return false
}

 fun Context.getStringResourceByName(aString: String): String {
    val packageName: String = packageName
    val resId: Int = resources.getIdentifier(aString, "string", packageName)
    return getString(resId)
}


fun iterateString(string: String) :String{
    val sb = StringBuilder()
    var count = 0
    for (i in string.toCharArray()) {
        sb.append(i)
        if (i == '-') {
            count++

            if (count == 3) {
                break
            }
        }
    }
    return sb.toString()
}

fun firstPartOfConversationId(currentCognitoUserId: String): String {
    val string= iterateString(currentCognitoUserId)
    return currentCognitoUserId.substring(0,string.length-1)
}


fun secondPartOfConversationId(currentCognitoUserId: String): String {
    return currentCognitoUserId.substring(iterateString(currentCognitoUserId).length)
}

fun calculateDistanceBetween(startLat: Double, startLon: Double, endLat: Double, endLon: Double): Float {
    val results = FloatArray(1)
    Location.distanceBetween(startLat, startLon, endLat, endLon, results)
    return if(results.isNotEmpty()) results[0] else 0f
}

fun convertDpToPx(dp: Float): Float {
    return dp * Resources.getSystem().displayMetrics.density
}

fun openSettingsScreen(context: Context) {
    val intent = Intent()
    intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
    val uri: Uri = Uri.fromParts("package", context.packageName, null)
    intent.data = uri
    context.startActivity(intent)
}

fun isDarkModeEnabled(context: Context): Boolean{
    return context.resources.configuration.uiMode and
            Configuration.UI_MODE_NIGHT_MASK == Configuration.UI_MODE_NIGHT_YES
}

fun updateLocale(context: Context) {
    val newLocale = ModifiedLingver.getInstance().getLocale()
    Locale.setDefault(newLocale)

    val res = context.resources
    val config = res.configuration
    config.setLocale(newLocale)
    res.updateConfiguration(config, res.displayMetrics)
}
inline fun RecyclerView.setBlurListener(
    layoutManager: GridLayoutManager,
    function: KFunction1<Int, Pair<Int, Boolean?>>,
    crossinline onItemBlurFound: (position: Int) -> Unit = { _-> }
) : RecyclerView.OnScrollListener {
    val listener = blurListener(layoutManager,function,onItemBlurFound)
    addOnScrollListener(listener)
    return listener
}

inline fun blurListener(
    layoutManager: GridLayoutManager,
    function: KFunction1<Int, Pair<Int, Boolean?>>,
    crossinline onItemBlurFound: (position: Int) -> Unit = { _-> }
) : RecyclerView.OnScrollListener = object :
    BlurScrollListener(layoutManager,function) {
    override fun onBlurItemsFound(position: Int)=onItemBlurFound(position)

}

fun ValueAnimator?.pauseAnimation() {
    if(this?.isRunning == true)
        this?.pause()
}

fun ValueAnimator?.resumeAnimation() {
    if(this?.isPaused == true)
        this?.resume()
}

fun animateDot(userDot: ImageView, delay: Long) {
    userDot.animate().scaleX(1F).scaleY(1F).setDuration(ANIMATION_DOT_DURATION).setStartDelay(delay).withEndAction {
        userDot.animate().scaleX(0F).scaleY(0F).setDuration(ANIMATION_DOT_DURATION)
    }
}

fun createConversationId(senderUserId: String, receiverUserId: String): String {
    val parts1 = senderUserId.split("-")
    val parts2 = receiverUserId.split("-")

    val firstThreeParts1 = parts1.take(3).joinToString("-")

    val lastTwoParts2 = parts2.takeLast(2).joinToString("-")

    val combinedParts = "$firstThreeParts1-$lastTwoParts2"

    return combinedParts
}

fun getLocationServicesStatus(hasLocationEnabled: Boolean, hasBackgroundLocationEnabled: Boolean): String {
    val locationServiceStatus = if (hasLocationEnabled) {
        if (hasBackgroundLocationEnabled) ClevertapLocationServiceStatusValues.ALWAYS
        else ClevertapLocationServiceStatusValues.WHILE_USING_THE_APP
    } else {
        ClevertapLocationServiceStatusValues.NOT_ALLOWED
    }
    return locationServiceStatus.value
}

fun getNotificationsOnClevertapValue(context: Context, duaSharedPrefs: DuaSharedPrefs): Boolean {
    val areNotificationsOn = if(!context.isPostNotificationsPermissionEnabled()){
        false
    } else {
        val isDeviceNotificationOn =
                NotificationHelper.isMessageNotificationsEnables(DuaApplication.instance.applicationContext)
        val isSettingsNotificationOn = duaSharedPrefs.isPushMessageNotificationsEnables()
        (isDeviceNotificationOn && isSettingsNotificationOn)
    }
    return areNotificationsOn
}
/**
 * Executes a coroutine block when the lifecycle reaches a specified state.
 *
 * @param state The Lifecycle.State at which the coroutine should execute.
 * @param context Additional CoroutineContext for the coroutine. Default is EmptyCoroutineContext.
 * @param isDebuggable Boolean indicating whether to log debug information.
 * @param block The suspend function block to execute.
 */
fun Lifecycle.launchWhileAtLeast(
    state: Lifecycle.State,
    context: CoroutineContext = EmptyCoroutineContext,
    isDebuggable: Boolean = BuildConfig.DEBUG,
    block: suspend CoroutineScope.() -> Unit
) {
    coroutineScope.launch(context + CoroutineExceptionHandler { _, exception ->
        Timber.tag("Lifecycle.launchOn").e(exception, "Error in performOn function")
    }) {
        try {
            val job = launch(context + CoroutineExceptionHandler { _, e ->
                Timber.tag("Lifecycle.launchOn").e(e, "Block execution failed")
            }, start = CoroutineStart.LAZY) {
                block()
            }
            if (isDebuggable) {
                Timber.tag("Lifecycle.launchOn").d("Waiting for lifecycle state $state")
            }
            withStateAtLeast(state) {
                if (isDebuggable) {
                    Timber.tag("Lifecycle.launchOn").d("Lifecycle state $state achieved, starting job")
                }
                job.start()
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to reach state $state or start job")
        }
    }
}
fun Context.openAppSettings(action: String = Settings.ACTION_APPLICATION_DETAILS_SETTINGS) {
    val intent = Intent(action).apply {
        data = Uri.fromParts("package", packageName, null)
        addCategory(CATEGORY_DEFAULT)
        addFlags(FLAG_ACTIVITY_NEW_TASK)
        addFlags(FLAG_ACTIVITY_NO_HISTORY)
        addFlags(FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS)
    }
    startActivity(intent)
}

fun calculateDistance(event1: MotionEvent, event2: MotionEvent): Float {
    val dx = event2.x - event1.x
    val dy = event2.y - event1.y
    return sqrt(dx * dx + dy * dy)
}

/**
 * Retrieves a drawable resource for a specific configuration (e.g., night mode or day mode).
 *
 * This extension function allows you to explicitly fetch a drawable resource for either
 * night mode or day mode, regardless of the current theme setting. This can be useful
 * when you want to use a night mode drawable during the day or vice versa.
 *
 * @param drawableId The resource ID of the drawable to be retrieved.
 * @param nightMode A boolean flag indicating whether to retrieve the drawable for night mode (true)
 * or day mode (false).
 * @return The drawable resource for the specified configuration, or null if the drawable could not be found.
 */
fun Context.getDrawableForMode(drawableId: Int, nightMode: Boolean): Drawable? {
    val configuration = Configuration(this.resources.configuration)
    configuration.uiMode = if (nightMode) {
        Configuration.UI_MODE_NIGHT_YES or (configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK.inv())
    } else {
        Configuration.UI_MODE_NIGHT_NO or (configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK.inv())
    }

    val res = this.createConfigurationContext(configuration).resources
    return ResourcesCompat.getDrawable(res, drawableId, this.theme)
}

fun View.setWidth(width: Float) {
    val layoutParams = this.layoutParams
    layoutParams?.width = convertDpToPixel(width, this.context).toInt()
    this.layoutParams = layoutParams
}

fun TextView.setCustomStyledText(
    fullText: String,
    targetText: String,
    fontName: String,
    underline: Boolean = false,
    linkColor: Int? = null,
    onClick: (() -> Unit)? = null
) {
    val typeface = when (fontName) {
        "tt_norms_pro_light" -> ResourcesCompat.getFont(context, R.font.tt_norms_pro_light)
        "tt_norms_pro_normal" -> ResourcesCompat.getFont(context, R.font.tt_norms_pro_normal)
        "tt_norms_pro_regular" -> ResourcesCompat.getFont(context, R.font.tt_norms_pro_regular)
        "tt_norms_pro_medium" -> ResourcesCompat.getFont(context, R.font.tt_norms_pro_medium)
        "tt_norms_pro_demibold" -> ResourcesCompat.getFont(context, R.font.tt_norms_pro_demibold)
        "tt_norms_pro_bold" -> ResourcesCompat.getFont(context, R.font.tt_norms_pro_bold)
        else -> ResourcesCompat.getFont(context, R.font.tt_norms_pro_normal)
    } ?: return

    val spannable = SpannableString(fullText)
    val start = fullText.indexOf(targetText)

    if (start >= 0) {
        spannable.setSpan(
            CustomTypefaceSpan(typeface),
            start,
            start + targetText.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        if (underline) {
            spannable.setSpan(
                UnderlineSpan(),
                start,
                start + targetText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        val clickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                if(onClick != null) onClick()
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = underline
                linkColor?.let { ds.color = it }
            }
        }
        spannable.setSpan(
            clickableSpan,
            start,
            start + targetText.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        this.movementMethod = LinkMovementMethod.getInstance()
        this.highlightColor = Color.TRANSPARENT
    }

    this.text = spannable
}

 inline fun <reified T : Parcelable> Intent.getParcelableExtraCompat(key: String, clazz: Class<T>): T? {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        getParcelableExtra(key, clazz)
    } else {
        @Suppress("DEPRECATION")
        getParcelableExtra(key)
    }
}

fun showErrorDialog(context: Context, errorMessage:String, title: String = "Oopps!") {
    val builder = AlertDialog.Builder(context, R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)

    builder.apply {
        setTitle(title)
        setMessage(errorMessage)
            .setNegativeButton(context.getString(R.string.ok_dialog)) { dialog, which ->
                dialog.cancel()
            }
    }
    return builder.create().run {
        setOnShowListener {
            getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.blue_500))
        }
        show()
    }
}

fun checkIfAppVersionIsSupported(activity: Activity) {
    val oldestSupportedVersionString = RemoteConfigUtils.getOldestVersionSupported()

    Timber.tag("ESAT_LOGI").d("oldestSupportedVersionString $oldestSupportedVersionString")

    if (!oldestSupportedVersionString.isNullOrEmpty()) {
        val oldestSupportedVersion: SemVer = SemVer.parse(RemoteConfigUtils.getOldestVersionSupported())
        val cleanVersionName = BuildConfig.VERSION_NAME.split("-")[0]
        val currentVersion: SemVer = SemVer.parse(cleanVersionName)

        Timber.tag("ESAT_LOGI").d("currentVersion $currentVersion")
        Timber.tag("ESAT_LOGI").d("oldestSupportedVersion $oldestSupportedVersion")
//            if 'oldestSupportedVersion' is greater than 'currentVersion' fun compareTo(Compare two SemVer) returns 1
        if ((oldestSupportedVersion.compareTo(currentVersion)) == 1) {
            showUnsupportedVersionPopup(activity)
        }
    }
}

fun showUnsupportedVersionPopup(activity: Activity) {
    val builder = AlertDialog.Builder(activity, R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)

    builder.apply {
        setCancelable(false)
        setTitle(activity.resources.getString(R.string.unsupported_version))
            .setMessage(activity.resources.getString(R.string.unsupported_version_desc))
            .setNegativeButton(activity.resources.getString(R.string.close_app)) { dialog, which ->
                activity.finish()
            }
            .setPositiveButton(activity.resources.getString(R.string.update)) { dialog, which ->
                val packageName = Uri.parse(activity.packageName)
                try {
                    val link = Uri.parse("market://details?id=$packageName")
                    activity.startActivity(Intent(Intent.ACTION_VIEW, link))
                } catch (e: ActivityNotFoundException) {
                    val link = Uri.parse("https://play.google.com/store/apps/details?id=$packageName")
                    activity.startActivity(Intent(Intent.ACTION_VIEW, link))
                }
            }
    }

    return builder.create().run {
        setOnShowListener {
            getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.red_500))
            getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(context, R.color.gph_purple_badge2))
        }
        show()
    }
}


/**
 * Transitions between two container views with animation.
 * Hides the first container and shows the second one with the specified animation.
 *
 * @param containerToHide The ViewGroup container to hide
 * @param containerToShow The ViewGroup container to show
 * @param animationType The type of animation to use (FADE, SLIDE_LEFT, SLIDE_RIGHT, SLIDE_UP, SLIDE_DOWN)
 * @param duration The duration of the animation in milliseconds
 * @param onAnimationEnd Optional callback to be invoked when the animation completes
 */
fun transitionContainers(
    containerToHide: ViewGroup,
    containerToShow: ViewGroup,
    animationType: TransitionAnimationType = TransitionAnimationType.FADE,
    duration: Long = 300,
    onAnimationEnd: (() -> Unit)? = null
) {
    // Make sure the container to show is initially invisible but in the layout
    containerToShow.visibility = View.INVISIBLE
    containerToShow.alpha = 0f

    // Define the animations based on the animation type
    when (animationType) {
        TransitionAnimationType.FADE -> {
            // Fade out the container to hide
            containerToHide.animate()
                .alpha(0f)
                .setDuration(duration)
                .withEndAction {
                    containerToHide.visibility = View.GONE

                    // Fade in the container to show
                    containerToShow.visibility = View.VISIBLE
                    containerToShow.animate()
                        .alpha(1f)
                        .setDuration(duration)
                        .withEndAction {
                            onAnimationEnd?.invoke()
                        }
                }
        }
        TransitionAnimationType.SLIDE_LEFT -> {
            // Slide out to the left
            containerToHide.animate()
                .translationX(-containerToHide.width.toFloat())
                .alpha(0f)
                .setDuration(duration)
                .withEndAction {
                    containerToHide.visibility = View.GONE
                    containerToHide.translationX = 0f

                    // Prepare container to show for animation
                    containerToShow.translationX = containerToShow.width.toFloat()
                    containerToShow.visibility = View.VISIBLE
                    containerToShow.alpha = 1f

                    // Slide in from the right
                    containerToShow.animate()
                        .translationX(0f)
                        .setDuration(duration)
                        .withEndAction {
                            onAnimationEnd?.invoke()
                        }
                }
        }
        TransitionAnimationType.SLIDE_RIGHT -> {
            // Slide out to the right
            containerToHide.animate()
                .translationX(containerToHide.width.toFloat())
                .alpha(0f)
                .setDuration(duration)
                .withEndAction {
                    containerToHide.visibility = View.GONE
                    containerToHide.translationX = 0f

                    // Prepare container to show for animation
                    containerToShow.translationX = -containerToShow.width.toFloat()
                    containerToShow.visibility = View.VISIBLE
                    containerToShow.alpha = 1f

                    // Slide in from the left
                    containerToShow.animate()
                        .translationX(0f)
                        .setDuration(duration)
                        .withEndAction {
                            onAnimationEnd?.invoke()
                        }
                }
        }
        TransitionAnimationType.SLIDE_UP -> {
            // Slide out to the top
            containerToHide.animate()
                .translationY(-containerToHide.height.toFloat())
                .alpha(0f)
                .setDuration(duration)
                .withEndAction {
                    containerToHide.visibility = View.GONE
                    containerToHide.translationY = 0f

                    // Prepare container to show for animation
                    containerToShow.translationY = containerToShow.height.toFloat()
                    containerToShow.visibility = View.VISIBLE
                    containerToShow.alpha = 1f

                    // Slide in from the bottom
                    containerToShow.animate()
                        .translationY(0f)
                        .setDuration(duration)
                        .withEndAction {
                            onAnimationEnd?.invoke()
                        }
                }
        }
        TransitionAnimationType.SLIDE_DOWN -> {
            // Slide out to the bottom
            containerToHide.animate()
                .translationY(containerToHide.height.toFloat())
                .alpha(0f)
                .setDuration(duration)
                .withEndAction {
                    containerToHide.visibility = View.GONE
                    containerToHide.translationY = 0f

                    // Prepare container to show for animation
                    containerToShow.translationY = -containerToShow.height.toFloat()
                    containerToShow.visibility = View.VISIBLE
                    containerToShow.alpha = 1f

                    // Slide in from the top
                    containerToShow.animate()
                        .translationY(0f)
                        .setDuration(duration)
                        .withEndAction {
                            onAnimationEnd?.invoke()
                        }
                }
        }
    }
}

/**
 * Enum class defining the types of transition animations available.
 */
enum class TransitionAnimationType {
    FADE,
    SLIDE_LEFT,
    SLIDE_RIGHT,
    SLIDE_UP,
    SLIDE_DOWN
}