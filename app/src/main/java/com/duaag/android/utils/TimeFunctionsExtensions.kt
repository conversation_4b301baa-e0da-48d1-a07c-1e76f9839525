package com.duaag.android.utils

import android.content.Context
import android.text.format.DateUtils
import com.duaag.android.R
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

const val DATE_STRING_FORMAT = "yyyy-MM-dd'T'HH:mm:ssZ"
const val DATE_STRING_FORMAT_BACKEND = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
const val DATE_STRING_FORMAT_BIRTHDAY = "dd MMMM yyyy"
const val EVENT_DATE_STRING_FORMAT = "ddMMyyyy"
const val TIME_ZONE_STRING_FORMAT = "ZZZZZ"
const val TIME_STRING_FORMAT = "HHmm"
const val CALENDAR_DAY_FORMAT = "yyyy-MM-dd"
const val SPOTTED_FORMAT = "YYYY-MM-DD"
const val ONLY_TIME_FORMAT = "HH:mm:ss"

fun formatToUTC(timestamp: Long): String {
    val formatter = SimpleDateFormat(DATE_STRING_FORMAT_BIRTHDAY, Locale.getDefault())
    return try {
        val date = Date(timestamp)
        formatter.format(date)
    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun formatOnlyTime(timestamp: Long): String {
    val formatter = SimpleDateFormat(ONLY_TIME_FORMAT, Locale.getDefault())
    return try {
        val date = Date(timestamp)
        formatter.format(date)
    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun isSameDay(ts1: Long, ts2: Long): Boolean {
    val cal1: Calendar = Calendar.getInstance()
    val cal2: Calendar = Calendar.getInstance()
    cal1.timeInMillis = ts1
    cal2.timeInMillis = ts2

    return cal1[Calendar.YEAR] == cal2[Calendar.YEAR] &&
            cal1[Calendar.MONTH] == cal2[Calendar.MONTH] &&
            cal1[Calendar.DAY_OF_MONTH] == cal2[Calendar.DAY_OF_MONTH]

}

fun isSameYear(ts1: Long, ts2: Long): Boolean {
    val cal1: Calendar = Calendar.getInstance()
    val cal2: Calendar = Calendar.getInstance()
    cal1.timeInMillis = ts1
    cal2.timeInMillis = ts2

    return cal1[Calendar.YEAR] == cal2[Calendar.YEAR]
}

fun formatToUTC(calendar: Calendar?): String {
    if (calendar !=null){
    val afterChar = when(calendar.get(Calendar.DAY_OF_MONTH)) {
        1,21,31 -> "st"
        2,22 -> "nd"
        3,23 -> "rd"
        else -> "th"
    }

    val foramt = "d'$afterChar' MMMM yyyy"
    val formatter = SimpleDateFormat(foramt, Locale.getDefault())
    return try {
        val date = Date(calendar.timeInMillis)
        formatter.format(date)
    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }} else return ""
}

fun formatCalendarToAPIString(calendar: Calendar): String {
    val foramt = "dd-MM-yyyy"
    val formatter = SimpleDateFormat(foramt, Locale.getDefault())
    return try {
        val date = Date(calendar.timeInMillis)
        formatter.format(date)
    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun formatCalendarToUIString(calendar: Calendar): String {
    val foramt = "dd/MM/yyyy"
    val formatter = SimpleDateFormat(foramt, Locale.getDefault())
    return try {
        val date = Date(calendar.timeInMillis)
        formatter.format(date)
    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}


fun formatUIDateToBackendDate(date: String?): String {
    val formatFrom = SimpleDateFormat("dd/MM/yyyy")
    return try {
        val mDate = formatFrom.parse(date)
        val formatTo = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
        mDate?.let {
            formatTo.format(it)
        } ?: run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun createCalendarFromTimestamp(date: Long): Calendar {
    val calendar = Calendar.getInstance(Locale.getDefault())
    calendar.time = Date(date)
    return calendar
}

fun getTimeZone(): String {
    val calendar = Calendar.getInstance(
        TimeZone.getTimeZone("GMT"),
        Locale.getDefault()
    )
    val currentLocalTime = calendar.time

    val date = SimpleDateFormat("$TIME_ZONE_STRING_FORMAT", Locale.getDefault())
    val localTime = date.format(currentLocalTime)
    return localTime
}

fun formatTimePassedFromNow(time: String): String {
    val formatFrom = SimpleDateFormat(DATE_STRING_FORMAT, Locale.getDefault())
    return try {
        val date = formatFrom.parse(time)
        date?.let {
            DateUtils.getRelativeTimeSpanString(
                date.time, System.currentTimeMillis(),
                DateUtils.MINUTE_IN_MILLIS,
                DateUtils.FORMAT_NO_NOON
            ).toString()
        } ?: run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun formatDayOfYear(time: String): String {
    val formatFrom = SimpleDateFormat(DATE_STRING_FORMAT, Locale.getDefault())
    val formatTo = SimpleDateFormat("hh:mm a, dd MMM yyyy", Locale.getDefault())
    return try {
        val date = formatFrom.parse(time)
        date?.let {
            formatTo.format(date)
        } ?: run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}


fun formatDayOfYear(date: Date?): String {
    return try {
        val formatTo = SimpleDateFormat("MMM d, yyyy", Locale.getDefault())
        date?.let {
            formatTo.format(it)
        }.run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun convertToTwelveHourFormatUTC(time: String): String {
    val formatFrom =
        SimpleDateFormat("$EVENT_DATE_STRING_FORMAT$TIME_STRING_FORMAT$TIME_ZONE_STRING_FORMAT")
    val formatTo = SimpleDateFormat("hh:mm a", Locale.getDefault())
    return try {
        val date = formatFrom.parse(time)
        date?.let {
            formatTo.format(it)
        } ?: run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun convertToTwelveHourFormat(time: String): String {
    val formatFrom = SimpleDateFormat("HHmm")
    val formatTo = SimpleDateFormat("hh:mm a")
    return try {
        val date = formatFrom.parse(time)
        date?.let {
            formatTo.format(date)
        } ?: run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun getDateFromCalendarDayString(date: String): Date {
    val fmt = SimpleDateFormat("yyyy-M-dd", Locale.getDefault())
    return fmt.parse(date) ?: Date()
}


fun getDateFromCalendarEventString(date: String): Date {
    val fmt = SimpleDateFormat(EVENT_DATE_STRING_FORMAT + TIME_ZONE_STRING_FORMAT)
    return fmt.parse(date) ?: Date()
}

fun getDateFromDateOnlyUTCString(date: String): Date? {
    val formatFrom = SimpleDateFormat(EVENT_DATE_STRING_FORMAT + TIME_ZONE_STRING_FORMAT)
    return try {
        formatFrom.parse(date)
    } catch (e: ParseException) {
        e.printStackTrace()
        null
    }
}

fun formatCalendarDayToUTC(date: String): String {
    val formatFrom = SimpleDateFormat("yyyy-M-dd")
    return try {
        val mDate = formatFrom.parse(date)
        val formatTo = SimpleDateFormat(DATE_STRING_FORMAT, Locale.getDefault())
        mDate?.let {
            formatTo.format(mDate)
        } ?: run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun formatCalendarDayToUTCDateOnly(date: String): String {
    val formatFrom = SimpleDateFormat("yyyy-M-dd")
    return try {
        val mDate = formatFrom.parse(date)
        val formatTo = SimpleDateFormat(EVENT_DATE_STRING_FORMAT, Locale.getDefault())
        mDate?.let {
            formatTo.format(it)
        } ?: run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun convertTimeToUTC(date: String): String {
    val formatFrom = SimpleDateFormat("HHmm")
    val formatTo = SimpleDateFormat("HHmm", Locale.getDefault())
    formatTo.timeZone = TimeZone.getTimeZone("UTC")

    return try {
        @Suppress("UNUSED_VARIABLE")
        val mFormatFrom = formatFrom.parse(date)
        val mFormatTo = SimpleDateFormat(EVENT_DATE_STRING_FORMAT, Locale.getDefault())
        mFormatTo.format(date)
    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun formatDateOfYear(time: String): String {
    val formatFrom = SimpleDateFormat(DATE_STRING_FORMAT, Locale.getDefault())
    val formatTo = SimpleDateFormat("d MMM, yyyy", Locale.getDefault())
    return try {
        val date = formatFrom.parse(time)
        date?.let {
            formatTo.format(it)
        } ?: run {
            ""
        }

    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun isTheSameDay(selectedDay: Date, day: String): Boolean {
    val fmt = SimpleDateFormat(EVENT_DATE_STRING_FORMAT, Locale.getDefault())
    return try {
        getDateFromDateOnlyUTCString(
            day
        )?.let {
            fmt.format(selectedDay) == fmt.format(
                it
            )
        } ?: run {
            false
        }

    } catch (e: Exception) {
        e.printStackTrace()
        false
    }
}

/** @param date should be in this format:: TimeFunctionsExtensions.DATE_STRING_FORMAT_BACKEND */
fun createDateFromUTCString(date: String): Date {
    val formatFrom = SimpleDateFormat(DATE_STRING_FORMAT_BACKEND, Locale.getDefault())
    return try {
        formatFrom.parse(date) ?: run {
            Date()
        }
    } catch (e: ParseException) {
        e.printStackTrace()
        Date()
    }
}

/** @param date should be in this format:: TimeFunctionsExtensions.DATE_STRING_FORMAT_BACKEND */
fun createCalendarFromUTCString(date: String): Calendar {
    val calendar = Calendar.getInstance(Locale.getDefault())
    calendar.time = createDateFromUTCString(date)
    return calendar
}

fun isSameCalendarDay(firstCalendar: Calendar, secondCalendar: Calendar): Boolean {
    return firstCalendar.get(Calendar.YEAR) == secondCalendar.get(Calendar.YEAR) &&
            firstCalendar.get(Calendar.MONTH) == secondCalendar.get(Calendar.MONTH) &&
            firstCalendar.get(Calendar.DAY_OF_MONTH) == secondCalendar.get(Calendar.DAY_OF_MONTH)
}

fun convertEventDateToCalendarPickerDate(time: String): String {
    val formatFrom = SimpleDateFormat(EVENT_DATE_STRING_FORMAT, Locale.getDefault())
    val formatTo = SimpleDateFormat("yyyy-M-dd", Locale.getDefault())
    return try {
        val date = formatFrom.parse(time)
        date?.let { formatTo.format(date) } ?: run {
            ""
        }
    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun getExpirationAfterDays(days: Int): Date {
    val cal = Calendar.getInstance()
    cal.add(Calendar.DAY_OF_YEAR, days)
    return Date(cal.timeInMillis)
}

fun createCalendarFromThirdPartyBirthday(date: String?): Calendar? {
    if(date == null)
        return null

    val formatFrom = SimpleDateFormat("MM/dd/yyyy")
    return try {
        val mDate = formatFrom.parse(date)
        val calendar = Calendar.getInstance()
        mDate?.let {
            calendar.time = it
        } ?: run {
            return null
        }
        return calendar
    } catch (e: ParseException) {
        e.printStackTrace()
        null
    }
}

fun createCalendarFromSpottedBirthday(date: String,isSpottedProfile:Boolean = false): Calendar? {
    val pattern = if (isSpottedProfile) SPOTTED_FORMAT else DATE_STRING_FORMAT
    val formatFrom = SimpleDateFormat(pattern)
    return try {
        val mDate = formatFrom.parse(date)
        val calendar = Calendar.getInstance()
        mDate?.let {
            calendar.time = it
        } ?: run {
            return null
        }
        return calendar
    } catch (e: ParseException) {
        e.printStackTrace()
        null
    }
}


fun createUTCStringFromTimestamp(timestamp: Long): String? {
    val formatTo = SimpleDateFormat(DATE_STRING_FORMAT, Locale.getDefault())
    return try {
        val date = Date(timestamp)
        formatTo.format(date)
    } catch (e: ParseException) {
        e.printStackTrace()
        ""
    }
}

fun getUserAge(it: Calendar): Int {
    val today = Calendar.getInstance()
    val birthdayYear = it.get(Calendar.YEAR)
    val currentYear = today.get(Calendar.YEAR)

    var age = currentYear - birthdayYear

    //check if the year is leap year
    if ((birthdayYear % 400 == 0) || ((birthdayYear % 4 == 0) && (birthdayYear % 100 != 0))) {

        if ((currentYear % 400 == 0) || ((currentYear % 4 == 0) && (currentYear % 100 != 0))) {
            if (today.get(Calendar.DAY_OF_YEAR) < it.get(Calendar.DAY_OF_YEAR)) {
                age--
            }
        } else {
            if (today.get(Calendar.DAY_OF_YEAR) < (it.get(Calendar.DAY_OF_YEAR) - 1)) {
                age--
            }
        }
    } else {

        if ((currentYear % 400 == 0) || ((currentYear % 4 == 0) && (currentYear % 100 != 0))) {
            if ((today.get(Calendar.DAY_OF_YEAR) - 1) < it.get(Calendar.DAY_OF_YEAR)) {
                age--
            }
        } else {
            if (today.get(Calendar.DAY_OF_YEAR) < (it.get(Calendar.DAY_OF_YEAR) - 1)) {
                age--
            }
        }
    }

    return age
}

fun getTimestampAfterPeriodOfTime(now: Long, after: Int, unit: Int): Long{
    val end = Calendar.getInstance()
    end.time = Date(now)
    end.add(unit, after)

    return end.timeInMillis
}

fun isBirthdayFormatCorrect(birthday: String): Boolean{
    val formatter = SimpleDateFormat("dd/mm/yyyy")
    formatter.isLenient = false
    try {
        formatter.parse(birthday)
    } catch (e: ParseException) {
        return false
    }
    return true
}

fun getRemainingMonths(now: Long, expiration: Long):String {
    val start = Calendar.getInstance()
    start.time = Date(now)
    val end = Calendar.getInstance()
    end.time = Date(expiration)
    var monthsBetween = 0
    var dateDiff = end[Calendar.DAY_OF_MONTH] - start[Calendar.DAY_OF_MONTH]
    if (dateDiff < 0) {
        val borrow = end.getActualMaximum(Calendar.DAY_OF_MONTH)
        dateDiff = end[Calendar.DAY_OF_MONTH] + borrow - start[Calendar.DAY_OF_MONTH]
        monthsBetween--
        if (dateDiff > 0) {
            monthsBetween++
        }
    } else {
        monthsBetween++
    }
    monthsBetween += end[Calendar.MONTH] - start[Calendar.MONTH]
    monthsBetween += (end[Calendar.YEAR] - start[Calendar.YEAR]) * 12
    return monthsBetween.toString()
}

fun isLessThan24HoursFromNow(timestamp: Long): Boolean {
    return timestamp < getTimeStampAfter24Hours()
}

fun createDateObjectFromBirthday(birthday: String): Date? {
    val dob = Calendar.getInstance()

    return try {
        val dates = birthday.split("-")
        val day = dates[0].toInt()
        val month = dates[1].toInt() - 1
        val year = dates[2].toInt()

        dob.set(year, month, day)
        dob.time
    } catch (ex: Exception) {
        ex.printStackTrace()
        null
    }

}

fun getDateObjectFromString(dateString: String): Date? {
    val dateFormat = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
    try {
        return dateFormat.parse(dateString)
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return null
}

fun isBirthdayAllowed(birthday: Calendar): Boolean {
    val endDate = getTimestampAfterPeriodOfTime(System.currentTimeMillis(), -18, Calendar.YEAR)
    val maxCalendar = Calendar.getInstance()
    maxCalendar.time = Date(endDate)

    val startDate = getTimestampAfterPeriodOfTime(System.currentTimeMillis(), -100, Calendar.YEAR)
    val minCalendar = Calendar.getInstance()
    minCalendar.time = Date(startDate)

    val isBetween18and100 = birthday.timeInMillis in (startDate) until endDate
    return isBetween18and100
}

fun addDaysToMilliseconds(milliseconds: Long, daysToAdd: Int): Long {
    val calendar = Calendar.getInstance()
    calendar.timeInMillis = milliseconds
    calendar.add(Calendar.DAY_OF_MONTH, daysToAdd)
    return calendar.timeInMillis
}

fun haveMoreThanXMinutesPassedFrom(lastTimeUpdated: Long, x: Long): Boolean {
    return System.currentTimeMillis() - lastTimeUpdated > TimeUnit.MINUTES.toMillis(x)
}

fun calculateTimeLeft(timestampStr: String): String {
    val dateFormat = SimpleDateFormat(DATE_STRING_FORMAT_BACKEND, Locale.getDefault())
    dateFormat.timeZone = TimeZone.getTimeZone("UTC")

    val futureTimestamp = dateFormat.parse(timestampStr)
    val now = Calendar.getInstance(TimeZone.getTimeZone("UTC")).time

    val timeLeftMillis = futureTimestamp.time - now.time

    return if (timeLeftMillis <= 0) {
        "00:00:00" // Time has already passed
    } else {
        val hours = timeLeftMillis / (1000 * 60 * 60) % 24
        val minutes = timeLeftMillis / (1000 * 60) % 60
        val seconds = timeLeftMillis / 1000 % 60
        "%02d:%02d:%02d".format(hours, minutes, seconds)
    }
}

fun hasTimestampPassed(timestamp: String): Boolean {
    val sdf = SimpleDateFormat(DATE_STRING_FORMAT_BACKEND, Locale.getDefault())
    sdf.timeZone = TimeZone.getTimeZone("UTC")
    val timestampDate = sdf.parse(timestamp)
    val currentDate = Date()

    return currentDate.after(timestampDate)
}

fun getTimeLeftForReply(context: Context, expiryDateString: String): String {
    val dateFormat = SimpleDateFormat(DATE_STRING_FORMAT_BACKEND, Locale.getDefault())
    dateFormat.timeZone = TimeZone.getTimeZone("UTC")

    val expiryDate: Date = dateFormat.parse(expiryDateString) ?: return ""
    val currentTime = System.currentTimeMillis()
    val diffInMillis = expiryDate.time - currentTime

    val hours = TimeUnit.MILLISECONDS.toHours(diffInMillis)
    val minutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillis) % 60

    return when {
        hours > 0 -> context.getString(R.string.waiting_for_reply_desc_hour_rmod_an, hours.toInt())
        else -> context.getString(R.string.waiting_for_reply_desc_minute_rmod, minutes.toString())
    }
}

fun convertClevertapDateToString(input: String?): String? {
    if (input == null) return null

    return try {
        val timestampString = input.substringAfter("_").toLongOrNull()

        if (timestampString != null) {
            val date = Date(timestampString * 1000)
            val formatter = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
            val formattedDate = formatter.format(date)
            formattedDate
        } else {
            null
        }
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun convertISODateToString(input: String?): String? {
    if (input == null) return null

    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        inputFormat.timeZone = TimeZone.getTimeZone("UTC")
        val date = inputFormat.parse(input)
        val outputFormat = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
        val formattedDate = outputFormat.format(date)
        formattedDate
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}