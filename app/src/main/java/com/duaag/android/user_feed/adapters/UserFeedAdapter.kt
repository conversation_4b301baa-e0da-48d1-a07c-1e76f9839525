package com.duaag.android.user_feed.adapters

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter

class UserFeedAdapter(fragmentManager: FragmentManager, val lifecycle: Lifecycle) : FragmentStateAdapter(fragmentManager, lifecycle) {

    private lateinit var fragments: List<Fragment>
    override fun getItemCount() = fragments.size
    override fun createFragment(position: Int): Fragment {
        return fragments[position]
    }

    fun setFragments(fragments: List<Fragment>) {
        this.fragments = fragments
    }

}