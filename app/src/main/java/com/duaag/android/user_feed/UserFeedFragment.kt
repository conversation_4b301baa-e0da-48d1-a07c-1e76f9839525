package com.duaag.android.user_feed

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.base.fragment.BaseFragment
import com.duaag.android.chat.fragments.InstaChatFragment
import com.duaag.android.chat.fragments.MatchesTabFragment
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationType
import com.duaag.android.chat.viewmodel.InstaChatViewModel
import com.duaag.android.chat.viewmodel.MatchesTabViewModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapPremiumTypeValues.*
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.UserFeedFragmentBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.PremiumActivity.Companion.PREMIUM_INTENT
import com.duaag.android.user_feed.adapters.UserFeedAdapter
import com.duaag.android.utils.updateLocale
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.google.android.material.tabs.TabLayout.Tab
import com.google.android.material.tabs.TabLayoutMediator
import javax.inject.Inject


class UserFeedFragment : BaseFragment() {

    companion object {
        fun newInstance() = UserFeedFragment()
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val matchesTabViewModel by viewModels<MatchesTabViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val instaChatViewModel by viewModels<InstaChatViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private var _binding: UserFeedFragmentBinding? = null
    private val binding get() = _binding!!

    private var tabLayoutMediator: TabLayoutMediator? = null


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        _binding = UserFeedFragmentBinding.inflate(inflater, container, false)
        val view = binding.root
        return view
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initBaseFunctions()
        userFeedTabClick()
    }

    override fun initialize() {
        val viwPagerAdapter = UserFeedAdapter(childFragmentManager, viewLifecycleOwner.lifecycle)
        viwPagerAdapter.setFragments(listOf(MatchesTabFragment.newInstance(), InstaChatFragment.newInstance()))
        binding.viewpager.isUserInputEnabled = false
        binding.viewpager.offscreenPageLimit = 2
        binding.viewpager.adapter = viwPagerAdapter

        tabLayoutMediator = TabLayoutMediator(binding.tabLayout, binding.viewpager) { tab, position ->
            tab.text = when (position) {
                0 -> getString(R.string.matches)
                else -> getString(R.string.insta_chats_string)
            }
        }
        tabLayoutMediator?.attach()


        viewModel.showOnlyMatchDotNotification.observe(viewLifecycleOwner, Observer {
            binding.tabLayout.getTabAt(0)?.orCreateBadge?.backgroundColor = ContextCompat.getColor(requireContext(),R.color.love)
            binding.tabLayout.getTabAt(0)?.orCreateBadge?.badgeTextColor = ContextCompat.getColor(requireContext(),R.color.gray_50)
            binding.tabLayout.getTabAt(0)?.orCreateBadge?.horizontalOffset = -18
            binding.tabLayout.getTabAt(0)?.orCreateBadge?.verticalOffset = 16
            binding.tabLayout.getTabAt(0)?.orCreateBadge?.isVisible = true
        })
        instaChatCounter()
    }

    fun navigateToChat(conversationModel: ConversationModel) {
        try {

            lifecycleScope.launchWhenResumed {
                if (findNavController().currentDestination?.id == R.id.userFeedFragment) {
                    findNavController().navigate(UserFeedFragmentDirections.actionUserFeedFragmentToConversationFragment3(conversationModel, null))
                    matchesTabViewModel.setIsSearching(false)
                }
            }

        } catch (e: Exception) {
        }
    }

    override fun setToolbar() {
    }

    override fun observeErrors() {
    }

    override fun setCallBacks() {

        instaChatViewModel.instaChatBadge.observe(viewLifecycleOwner) {
            instaChatCounter()
        }

        viewModel.navigateToInstaChat.observe(viewLifecycleOwner) {model->
            //update conversation model if the instachat was switched to default chat
            matchesTabViewModel.conversations.value
                ?.firstOrNull { it.id == model.id }
                ?.let { conversation ->
                    val targetPage = if (conversation.wasInstachat) 1 else 2
                    binding.viewpager.setCurrentItem(targetPage, false)

                    val updatedModel = if (conversation.wasInstachat) {
                        model.copy(wasInstachat = true, type = ConversationType.DEFAULT.value)
                    } else model

                    navigateToChat(updatedModel)
                } ?: run {
                binding.viewpager.setCurrentItem(2, false)
                navigateToChat(model)
            }
            instaChatViewModel.makeConversationSeenAfterNotificationClick(model.id)
        }


        viewModel.navigateToInstaChatList.observe(viewLifecycleOwner) {
            binding.viewpager.setCurrentItem(2, false)
        }
//        viewModel.showMatchTabDotNotification.observe(viewLifecycleOwner, Observer {
//            binding.tabLayout.getTabAt(0)?.orCreateBadge?.isVisible = it
//        })
//
//        viewModel.showLikeYouTabDotNotification.observe(viewLifecycleOwner, Observer {
//            binding.tabLayout.getTabAt(1)?.orCreateBadge?.isVisible = it
//        })

        viewModel.deepLinkMatches.observe(viewLifecycleOwner) {
            binding.viewpager.setCurrentItem(0, false)
        }

        viewModel.deepLinkInstaChat.observe(viewLifecycleOwner) {
            binding.viewpager.setCurrentItem(2, false)
        }

    }


    fun userFeedTabClick() {
        val eventPremiumType = getPremiumTypeEventProperty(viewModel.userProfile.value)

        binding.tabLayout.setOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: Tab) {
                selectTab(tab)
            }

            private fun selectTab(tab: Tab?) {
                if (tab?.position == 1) {
                    sendClevertapEvent(
                        ClevertapEventEnum.INSTACHAT_LIST_SCREENVIEW, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType))
                    if (matchesTabViewModel.getIsSearching())
                        matchesTabViewModel.onSetDefaultData()
                } else if (tab?.position == 0) {

                    binding.tabLayout.getTabAt(0)?.orCreateBadge?.isVisible = false
                    sendClevertapEvent(
                        ClevertapEventEnum.CHAT_LIST_SCREENVIEW, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType))

                    firebaseLogEvent(
                            FirebaseAnalyticsEventsName.CHAT_LIST_SCREENVIEW, mapOf(
                            FirebaseAnalyticsParameterName.CHAT_LIST_SCREENVIEW_COUNT.value to 1L))
                } else {
                    if (matchesTabViewModel.getIsSearching())
                    matchesTabViewModel.onSetDefaultData()
                }
            }

            override fun onTabUnselected(tab: Tab?) {}

            override fun onTabReselected(tab: Tab?) {
                if (tab?.position == 1) {

                } else if (tab?.position == 0) {
                    firebaseLogEvent(
                            FirebaseAnalyticsEventsName.CHAT_LIST_SCREENVIEW, mapOf(
                            FirebaseAnalyticsParameterName.CHAT_LIST_SCREENVIEW_COUNT.value to 1L))
                }
            }
        })
    }

    fun instaChatCounter() {
        val instaChatCountNumber = instaChatViewModel.instaChatCount
        when {
            instaChatCountNumber <= 0 -> binding.tabLayout.getTabAt(2).run {
                this?.orCreateBadge?.isVisible = false
                this?.removeBadge()
            }
            instaChatCountNumber in 1..99 -> binding.tabLayout.getTabAt(2).run {
                this?.orCreateBadge?.isVisible = true
                this?.badge?.horizontalOffset = -18
                this?.badge?.verticalOffset = 16
                this?.badge?.number = instaChatCountNumber
                this?.badge?.setBackgroundColor(ContextCompat.getColor(requireContext(),R.color.love))
                this?.badge?.badgeTextColor = ContextCompat.getColor(requireContext(),R.color.gray_50)
            }
            else -> {
                binding.tabLayout.getTabAt(2).run {
                    this?.orCreateBadge?.isVisible = true
                    this?.badge?.horizontalOffset = -18
                    this?.badge?.verticalOffset = 16
                    this?.badge?.number = instaChatCountNumber
                    this?.badge?.maxCharacterCount = 3
                    this?.badge?.setBackgroundColor(ContextCompat.getColor(requireContext(),R.color.love))
                    this?.badge?.badgeTextColor = ContextCompat.getColor(requireContext(),R.color.gray_50)
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.viewpager.adapter = null
        _binding = null
        tabLayoutMediator?.detach()
        tabLayoutMediator = null

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            PremiumActivity.PREMIUM_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    val sendBroadCastData = Intent(PREMIUM_INTENT)
                    sendBroadCastData.putExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, true)
                    LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(sendBroadCastData)
                    viewModel.fetchUserProfile()
                    viewModel.afterPremiumVerified()
                }
            }
        }
    }
}


