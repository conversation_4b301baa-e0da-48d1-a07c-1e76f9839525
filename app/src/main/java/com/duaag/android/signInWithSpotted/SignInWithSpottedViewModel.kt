package com.duaag.android.signInWithSpotted

import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.Resource
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.auth_interfaces.HasPhoneInput
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.di.ActivityScope
import com.duaag.android.di.ApplicationScope
import com.duaag.android.di.IoDispatcher
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsSetUpSourceValues
import com.duaag.android.signInWithSpotted.models.AddEmailModelSpotted
import com.duaag.android.signInWithSpotted.models.AddPhoneNumberModelSpotted
import com.duaag.android.signInWithSpotted.models.VerifyCodeModelSpotted
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.isEmail
import com.duaag.android.utils.livedata.SingleLiveData
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.catch
import okhttp3.ResponseBody
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@ActivityScope
class SignInWithSpottedViewModel @Inject constructor(private val userRepository: UserRepository) : ViewModel(), HasPhoneInput {

    @IoDispatcher
    @Inject
    lateinit var ioDispatcher: CoroutineDispatcher

    @ApplicationScope
    @Inject
    lateinit var externalScope: CoroutineScope

    private val _emailLiveData = MutableLiveData<String>()
    val emailLiveData: LiveData<String>
        get() = _emailLiveData

    private val _phoneLiveData = MutableLiveData<String>()
    val phoneLiveData: LiveData<String>
        get() = _phoneLiveData

    private val _addEmailAddress = SingleLiveData<Result<String>>()
    val addEmailAddress: LiveData<Result<String>>
        get() = _addEmailAddress

    private val _addPhoneNumber = SingleLiveData<Result<String>>()
    val addPhoneNumber: LiveData<Result<String>>
        get() = _addPhoneNumber

    private val _verifyEmailAddress = SingleLiveData<Result<ResponseBody>>()
    val verifyEmailAddress: LiveData<Result<ResponseBody>>
        get() = _verifyEmailAddress

    private val _verifyPhoneNumber = SingleLiveData<Result<ResponseBody>>()
    val verifyPhoneNumber: LiveData<Result<ResponseBody>>
        get() = _verifyPhoneNumber

    val verifyDigits = MutableLiveData<String>()

    var accountModel = userRepository.getAccount()

    val userProfile = userRepository.user

    val password = MutableLiveData<String>()

    var authMethod: AuthMethod = AuthMethod.PHONE


    fun setPassword(string: String) {
        password.value = string
    }

    fun setEmailAddress(email: String){
        _emailLiveData.value = email
    }

     override fun setPhoneNumber(phone: String) {
        _phoneLiveData.value = phone
    }

     override var selectedCountryCodeAsInt: Int? = null
     var phoneNumberWithoutCountryCode: String? = null

    private val _elapsedTime: MutableLiveData<String> = MutableLiveData("00:00")
    val elapsedTime: LiveData<String>
        get() = _elapsedTime

    private val _showTimer: MutableLiveData<Boolean> = MutableLiveData(false)
    val showTimer: LiveData<Boolean>
        get() = _showTimer

    var codeSentTo :String? = null

     var setupSource: String? = null
         private set


    fun addEmailAddressSpotted() {
        val email = emailLiveData.value?.lowercase()
        if(codeSentTo != email) {
            email?.let {
                _addEmailAddress.postValue(Result.Loading)

                resetTimer()
                setTimer()
                viewModelScope.launch(Dispatchers.IO) {
                    val model = AddEmailModelSpotted(it)
                    userRepository.addEmailAddressSpotted(model)
                        .catch { exception ->
                            codeSentTo = null

                            if (exception is Exception)
                                _addEmailAddress.postValue(Result.Error(exception))
                        }
                        .collect { value ->
                            when (value) {
                                is Resource.Success -> {
                                    Timber.tag("ADDPHONENUMBER").d("Sent Code")
                                    codeSentTo = it
                                    _addEmailAddress.postValue(Result.Success(""))
                                }
                                is Resource.Loading -> {
                                }
                                is Resource.Error -> {
                                }
                            }
                        }
                }
            }
        } else {
            Timber.tag("ADDPHONENUMBER").d("Code already sent!")
            _addEmailAddress.postValue(Result.Success(""))
        }
    }


    fun addPhoneNumber() {
        val phone = phoneLiveData.value
        if(codeSentTo != phone) {
            phone?.let {
                _addPhoneNumber.postValue(Result.Loading)

                resetTimer()
                setTimer()
                viewModelScope.launch(Dispatchers.IO) {
                    val model = AddPhoneNumberModelSpotted(it)
                    userRepository.addPhoneNumberSpotted(model)
                        .catch { exception ->
                            codeSentTo = null

                            if (exception is Exception)
                                _addPhoneNumber.postValue(Result.Error(exception))
                        }
                        .collect { value ->
                            when (value) {
                                is Resource.Success -> {
                                    Timber.tag("ADDPHONENUMBER").d("Sent Code")
                                    codeSentTo = it
                                    _addPhoneNumber.postValue(Result.Success(""))
                                }
                                else -> {}
                            }
                        }
                }
            }
        } else {
            Timber.tag("ADDPHONENUMBER").d("Code already sent!")
            _addPhoneNumber.postValue(Result.Success(""))
        }
    }



    fun verifyEmailAddress(code: String,password:String) {
        _verifyEmailAddress.postValue(Result.Loading)

        viewModelScope.launch(Dispatchers.IO) {
            val model = VerifyCodeModelSpotted(code,password)
            userRepository.verifyEmailCodeSpotted(model)
                .catch { exception ->
                    if (exception is Exception)
                        _verifyEmailAddress.postValue(Result.Error(exception))
                }
                .collect { value ->
                    when (value) {
                        is Resource.Success -> {
                            refreshToken()
                            _verifyEmailAddress.postValue(Result.Success(value.data))
                        }
                        is Resource.Loading -> {
                        }
                        is Resource.Error -> {
                        }
                    }
                }
        }
    }

    fun verifyPhoneCode(code: String,password:String) {
        _verifyPhoneNumber.postValue(Result.Loading)

        viewModelScope.launch(Dispatchers.IO) {
            val model = VerifyCodeModelSpotted(code,password)
            userRepository.verifyPhoneCodeSpotted(model)
                .catch { exception ->
                    if (exception is Exception)
                        _verifyPhoneNumber.postValue(Result.Error(exception))
                }
                .collect { value ->
                    when (value) {
                        is Resource.Success -> {
                            refreshToken()
                            _verifyPhoneNumber.postValue(Result.Success(value.data))
                        }
                        is Resource.Loading -> {
                        }
                        is Resource.Error -> {
                        }
                    }
                }
        }
    }



    private var timer: CountDownTimer? = null
    fun setTimer() {
        createTimer()
        _showTimer.value = true
    }

    private fun createTimer() {
        val triggerTime = System.currentTimeMillis() + 60 * 1000
        timer = object : CountDownTimer(triggerTime - System.currentTimeMillis(), 1000) {
            override fun onTick(p0: Long) {
                var millisUntilFinished = p0
                val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                millisUntilFinished -= TimeUnit.MINUTES.toMillis(minutes)

                val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished)

                val string = String.format("%02d:%02d", minutes, seconds)
                _elapsedTime.value = string

                if (millisUntilFinished <= 0) {
                    resetTimer()
                }
            }

            override fun onFinish() {
                resetTimer()
            }


        }
        timer?.start()
    }

    private fun resetTimer() {
        timer?.cancel()
        _elapsedTime.value = "00:00"
        _showTimer.value = false
    }



    fun updateAccountAfterVerification(isChangeLogin:String? = null) {
        viewModelScope.launch(Dispatchers.IO) {
            if (isChangeLogin != null){
                accountModel.value?.let {
                    val user = it.apply {
                        if (isChangeLogin.isEmail) {
                            copy(email = emailLiveData.value?.lowercase())
                        } else {
                            copy(phone = phoneLiveData.value)
                        }
                    }
                    userRepository.updateAccount(user)
                }
                delay(1000)
            }
            userProfile.value?.let {
                userRepository.updateUser(it.copy(hasBadge1 = true))
            }
        }
    }

    fun onResendCodeClicked() {
        codeSentTo = null
        if (emailLiveData.value != null) {
            addEmailAddressSpotted()
        } else if (phoneLiveData.value != null) {
            addPhoneNumber()
        }
    }

     private fun refreshToken() {
        externalScope.launch(ioDispatcher) {
            DuaApplication.instance.isCredentialUser = true
            AWSInteractor.refresh()
        }
    }

    fun setSetupSource(source: String?) {
          setupSource = source
    }


}