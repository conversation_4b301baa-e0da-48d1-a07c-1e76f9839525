package com.duaag.android.signInWithSpotted.di

import androidx.lifecycle.ViewModel
import com.duaag.android.signInWithSpotted.SignInWithSpottedViewModel
import com.duaag.android.di.ViewModelKey
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap


@Module
abstract class SignInWithSpottedViewModelModule {

    @Binds
    @IntoMap
    @ViewModelKey(SignInWithSpottedViewModel::class)
    abstract fun bindViewModel(signInWithSpottedViewModel: SignInWithSpottedViewModel): ViewModel

}