package com.duaag.android.signInWithSpotted.fragments

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.amazonaws.services.cognitoidentityprovider.model.CodeMismatchException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.auth_interfaces.HasPhoneInput
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentUserDetailsBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsSetUpSourceValues
import com.duaag.android.logevents.firebaseanalytics.signInWithSpottedEvent
import com.duaag.android.signInWithSpotted.SignInWithSpottedActivity
import com.duaag.android.signInWithSpotted.SignInWithSpottedViewModel
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.*
import com.duaag.android.utils.coutry.detectPrefixCountry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class UserDetailsFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInWithSpottedViewModel by viewModels<SignInWithSpottedViewModel>({ activity as SignInWithSpottedActivity }) { viewModelFactory }

    private lateinit var binding: FragmentUserDetailsBinding

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as SignInWithSpottedActivity).signInWithSpottedComponent.inject(this)
        Timber.tag("VIEWMODEL").d(signInWithSpottedViewModel.toString())
    }

    companion object {
        fun newInstance() = UserDetailsFragment()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentUserDetailsBinding.inflate(inflater)

        if(DuaApplication.instance.userEmailOrPhone !=null && DuaApplication.instance.userEmailOrPhone.equals("phone_number")){
            binding.radioGroup.check(R.id.radioButton2)
            binding.phoneInputContainer.visibility = View.VISIBLE
            binding.emailInput.visibility = View.GONE
            binding.phoneLabel.setText(R.string.phone)
            binding.phoneNumberInput.setText(signInWithSpottedViewModel.phoneNumberWithoutCountryCode)
            enableCreateAccountButton(signInWithSpottedViewModel.phoneLiveData.value ?: "", signInWithSpottedViewModel.password.value ?: "")
            signInWithSpottedViewModel.authMethod = AuthMethod.PHONE

        }else{
            binding.phoneLabel.setText(R.string.email_)
            binding.radioGroup.check(R.id.radioButton3)
            binding.phoneInputContainer.visibility = View.GONE
            binding.emailInput.visibility = View.VISIBLE
            binding.emailInput.setText(signInWithSpottedViewModel.emailLiveData.value)
            enableCreateAccountButton(signInWithSpottedViewModel.emailLiveData.value ?: "", signInWithSpottedViewModel.password.value ?: "")
            signInWithSpottedViewModel.authMethod = AuthMethod.EMAIL
        }


        binding.let {
            it.ccPicker.detectPrefixCountry()
            signInWithSpottedViewModel.selectedCountryCodeAsInt?.let {selectedCountryCodeAsInt ->
                it.ccPicker.setCountryForPhoneCode(selectedCountryCodeAsInt)
            }
            it.ccPicker.registerPhoneNumberTextView(it.phoneNumberInput)
            it.phoneNumberInput.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(s: Editable?) {

                    try {
                        (signInWithSpottedViewModel as HasPhoneInput).setPhoneNumber(it.ccPicker.selectedCountryCodeWithPlus + it.ccPicker.phoneNumber.nationalNumber)
                        enableCreateAccountButton(signInWithSpottedViewModel.phoneLiveData.value ?: "", signInWithSpottedViewModel.password.value ?: "")

                    } catch (e: Exception) {
                        (signInWithSpottedViewModel as HasPhoneInput).setPhoneNumber("")
                    }
                }

                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    binding.phoneInputSeparator.background = ContextCompat.getDrawable(requireContext(), R.color.gray_200)
                    binding.phoneInputContainer.background = ContextCompat.getDrawable(requireContext(), R.drawable.gray_200_corner_12dp)
                    clearErrorMessage()
                }
            })
            it.ccPicker.setPhoneNumberInputValidityListener { _, isValid ->
                enableCreateAccountButton(signInWithSpottedViewModel.phoneLiveData.value ?: "", signInWithSpottedViewModel.password.value ?: "")
            }

            it.ccPicker.setOnCountryChangeListener { _ ->
                signInWithSpottedViewModel.selectedCountryCodeAsInt = it.ccPicker.selectedCountryCodeAsInt
                lifecycleScope.launch(Dispatchers.IO) {
                    withContext(Dispatchers.Main) {
                        enableCreateAccountButton(signInWithSpottedViewModel.phoneLiveData.value ?: "", signInWithSpottedViewModel.password.value ?: "")
                    }
                }

            }

        }

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.radioGroup.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.radioButton2 -> {
                    binding.phoneLabel.setText(R.string.phone)
                    binding.phoneInputContainer.visibility = View.VISIBLE
                    binding.emailInput.visibility = View.GONE
                    signInWithSpottedViewModel.authMethod = AuthMethod.PHONE
                    enableCreateAccountButton(signInWithSpottedViewModel.phoneLiveData.value ?: "", signInWithSpottedViewModel.password.value ?: "")

                    clearErrorMessage()
                }
                R.id.radioButton3 -> {
                    binding.phoneLabel.setText(R.string.email_)
                    binding.phoneInputContainer.visibility = View.GONE
                    binding.emailInput.visibility = View.VISIBLE
                    signInWithSpottedViewModel.authMethod = AuthMethod.EMAIL
                    enableCreateAccountButton(signInWithSpottedViewModel.emailLiveData.value ?: "", signInWithSpottedViewModel.password.value ?: "")
                    clearErrorMessage()
                }
            }
        }

        binding.emailInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                clearErrorMessage()
            }

            override fun afterTextChanged(s: Editable?) {
                val email = s.toString()
                signInWithSpottedViewModel.setEmailAddress(email)
                enableCreateAccountButton(signInWithSpottedViewModel.emailLiveData.value ?: "",
                    signInWithSpottedViewModel.password.value ?: "")
            }
        })

        binding.passwordInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                signInWithSpottedViewModel.setPassword(binding.passwordInput.text.toString())
                enableCreateAccountButton(signInWithSpottedViewModel.phoneLiveData.value
                    ?: signInWithSpottedViewModel.emailLiveData.value ?: "",
                    signInWithSpottedViewModel.password.value ?: "")
                clearErrorMessage()

            }
        })


        signInWithSpottedViewModel.addEmailAddress.observe(viewLifecycleOwner, Observer {
            when (it) {
                is Result.Success -> {
                    signInWithSpottedEvent(AuthMethod.EMAIL,FirebaseAnalyticsEventsName.SPOTTED_SETUP_YOUR_ACCOUNT_MEDIUM_ADDED,
                        signInWithSpottedViewModel.setupSource ,null)

                    val signUpOrSignInmMedium = ClevertapSignUpOrSignInMediumValues.EMAIL.value

                    sendClevertapEvent(
                        ClevertapEventEnum.SPOTTED_SETUP_YOUR_ACCOUNT_MEDIUM_ADDED, mapOf(
                            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium,
                            ClevertapEventPropertyEnum.SETUP_SOURCE.propertyName to signInWithSpottedViewModel.setupSource)
                    )

                    binding.progressBar.visibility = View.GONE
                    binding.createAccount.isEnabled = true

                    findNavController().navigate(R.id.action_userDetailsFragment_to_sixDigitWithSpottedFragment)
              }
                is Result.Error -> {
                    binding.progressBar.visibility = View.GONE
                    when (it.exception) {
                        is LimitExceededException -> {
                            val message = getHourFromErrorMessage(it.exception.errorMessage)
                            binding.phoneErrorText.text = message
                        }
                        is CodeMismatchException -> {
                            binding.phoneErrorText.text = getString(R.string.wrong_verification_code)
                        }
                        else -> binding.phoneErrorText.text = it.exception.message
                    }
                }
                is Result.Loading -> {
                    clearErrorMessage()
                    binding.progressBar.visibility = View.VISIBLE
                    binding.createAccount.isEnabled = true
                }
            }
        })


        signInWithSpottedViewModel.addPhoneNumber.observe(viewLifecycleOwner) {
            when (it) {
                is Result.Success -> {
                    signInWithSpottedEvent(AuthMethod.PHONE,FirebaseAnalyticsEventsName.SPOTTED_SETUP_YOUR_ACCOUNT_MEDIUM_ADDED,
                        signInWithSpottedViewModel.setupSource,null)

                    val signUpOrSignInmMedium = ClevertapSignUpOrSignInMediumValues.PHONE.value

                    sendClevertapEvent(
                        ClevertapEventEnum.SPOTTED_SETUP_YOUR_ACCOUNT_MEDIUM_ADDED, mapOf(
                            ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium,
                            ClevertapEventPropertyEnum.SETUP_SOURCE.propertyName to signInWithSpottedViewModel.setupSource))

                    binding.progressBar.visibility = View.GONE
                    binding.createAccount.isEnabled = true


                    findNavController().navigate(R.id.action_userDetailsFragment_to_sixDigitWithSpottedFragment)

                }
                is Result.Error -> {
                    binding.progressBar.visibility = View.GONE
                    binding.createAccount.isEnabled = true
                    when (it.exception) {
                        is LimitExceededException -> {
                            val message = getHourFromErrorMessage(it.exception.errorMessage)
                            binding.phoneErrorText.text = message
                        }
                    }
                }
                is Result.Loading -> {
                    clearErrorMessage()
                    binding.progressBar.visibility = View.VISIBLE
                }
            }
        }


        binding.createAccount.setOnClickListener {
            val input =  when (signInWithSpottedViewModel.authMethod) {
                AuthMethod.PHONE -> {
                    signInWithSpottedViewModel.phoneLiveData.value ?: ""

                }
                AuthMethod.EMAIL -> {
                    signInWithSpottedViewModel.emailLiveData.value ?: ""
                }
                else -> { signInWithSpottedViewModel.phoneLiveData.value ?: "" }
            }
            val password = signInWithSpottedViewModel.password.value ?: ""
            validateInput(input, password)
        }
    }

    private fun validateInput(input: String, password: String) {
        val isPhoneValid = binding.ccPicker.isValid && signInWithSpottedViewModel.authMethod == AuthMethod.PHONE
        val isEmailValid = isEmailValid(input) && signInWithSpottedViewModel.authMethod == AuthMethod.EMAIL
        if ((isPhoneValid || isEmailValid) && isPasswordValid(password)) {
            binding.createAccount.isEnabled = false

            if(signInWithSpottedViewModel.authMethod == AuthMethod.PHONE) {
                signInWithSpottedViewModel.addPhoneNumber()
            }else{
                signInWithSpottedViewModel.addEmailAddressSpotted()
            }

        } else {
            if ((!binding.ccPicker.isValid && signInWithSpottedViewModel.authMethod == AuthMethod.PHONE)) {
                setPhoneError()
            }

            if (!isEmailValid(input) && signInWithSpottedViewModel.authMethod == AuthMethod.EMAIL) {
                setEmailError()
            }

            if (!isPasswordValid(password)) {
                setPasswordError()
            }
        }
    }

    private fun clearErrorMessage() {
        clearPasswordErrorMessage()
        clearPhoneErrorMessage()
    }

    private fun setEmailError() {
        binding.phoneErrorText.visibility = View.VISIBLE
        binding.phoneErrorText.setText(R.string.email_is_not_valid,)
        binding.emailInput.requestFocus()
    }

    private fun setPasswordError() {
        binding.passwordInfoText.isVisible = true
        binding.passwordInfoText.setTextColorRes(R.color.red_500)
        binding.passwordInputLayout.background = ContextCompat.getDrawable(requireContext(),  R.drawable.error_corners_12dp)
        binding.passwordInputLayout.requestFocus()
    }

    private fun clearPasswordErrorMessage() {
        binding.passwordInfoText.isVisible = false
        binding.passwordInfoText.setTextColorRes(R.color.gray_200)
        binding.passwordInputLayout.background = ContextCompat.getDrawable(requireContext(), R.drawable.edit_text_rounded_corners_12_dp)
    }

    private fun setPhoneError() {
        binding.phoneErrorText.visibility = View.VISIBLE
        binding.phoneErrorText.setText(R.string.phone_number_is_not_valid)
        binding.phoneInputContainer.requestFocus()
    }

    private fun clearPhoneErrorMessage() {
        binding.phoneErrorText.isVisible = false
    }


    private fun enableCreateAccountButton(phone: String, password: String) {
        val isEnabled = phone.isNotEmpty() && password.isNotEmpty()
        binding.createAccount.isEnabled = isEnabled
    }



}

