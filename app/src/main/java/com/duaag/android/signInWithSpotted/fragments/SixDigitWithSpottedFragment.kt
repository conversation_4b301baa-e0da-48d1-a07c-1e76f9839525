package com.duaag.android.signInWithSpotted.fragments

import android.content.Context
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.amazonaws.services.cognitoidentityprovider.model.*
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapSignUpOrSignInMediumValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentSixDigitWithSpottedBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsSetUpSourceValues
import com.duaag.android.logevents.firebaseanalytics.signInWithSpottedEvent
import com.duaag.android.signInWithSpotted.SignInWithSpottedActivity
import com.duaag.android.signInWithSpotted.SignInWithSpottedViewModel
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.showKeyboard
import com.duaag.android.views.PinField
import okhttp3.ResponseBody
import timber.log.Timber
import javax.inject.Inject

class SixDigitWithSpottedFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInWithSpottedViewModel by viewModels<SignInWithSpottedViewModel>({ activity as SignInWithSpottedActivity }) { viewModelFactory }

    private var _binding: FragmentSixDigitWithSpottedBinding? = null
    private val binding get() = _binding!!


    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as SignInWithSpottedActivity).signInWithSpottedComponent.inject(this)
        Timber.tag("VIEWMODEL").d(signInWithSpottedViewModel.toString())
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        _binding = FragmentSixDigitWithSpottedBinding.inflate(inflater)

        binding.viewModel = signInWithSpottedViewModel

        signInWithSpottedViewModel.verifyPhoneNumber.observe(viewLifecycleOwner, Observer {
            handleResponsePhone(it)
        })

        signInWithSpottedViewModel.verifyEmailAddress.observe(viewLifecycleOwner, Observer {
            handleResponseEmail(it)
        })

        signInWithSpottedViewModel.elapsedTime.observe(viewLifecycleOwner, {
            val string = getString(R.string.you_can_resend_the_code_an, it)
            binding.timerTextView.text = string
        })
        signInWithSpottedViewModel.showTimer.observe(viewLifecycleOwner, {
            binding.showTimer = it
        })

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val contactInfo =
            if (signInWithSpottedViewModel.authMethod == AuthMethod.PHONE) signInWithSpottedViewModel.phoneLiveData.value
            else signInWithSpottedViewModel.emailLiveData.value?.lowercase()

        val start = getString(R.string.we_ve_sent_a_6_digit_code)
        val email = " $contactInfo"
        val end = getString(R.string.please_enter_your)
        val wordtoSpan: Spannable = SpannableString(start + email + end)
        wordtoSpan.setSpan(ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.pink_500)), start.length, start.length + email.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.emailDescription.text = wordtoSpan
        //Assign custom listener to our custom pin view
        binding.modifiedPinView.onTextCompleteListener = PinFieldListener()
    }

    inner class PinFieldListener : PinField.OnTextCompleteListener {
        override fun onTextComplete(enteredText: String): Boolean {
            if (signInWithSpottedViewModel.phoneLiveData.value != null) {

                signInWithSpottedViewModel.password.value?.let {
                    signInWithSpottedViewModel.verifyPhoneCode(enteredText, it)
                }

            } else if (signInWithSpottedViewModel.emailLiveData.value != null) {

                signInWithSpottedViewModel.password.value?.let {
                    signInWithSpottedViewModel.verifyEmailAddress(enteredText, it)
                }

            }
            return false
        }

        override fun onTextChange(enteredText: String) {
            if (enteredText.isNotEmpty()) binding.errorCode.visibility = View.INVISIBLE
        }

    }


    private fun handleResponsePhone(it: Result<ResponseBody>) {
        when (it) {
            is Result.Success -> {
                signInWithSpottedEvent(AuthMethod.PHONE,FirebaseAnalyticsEventsName.SPOTTED_ACCOUNT_6DIGIT_CODE,signInWithSpottedViewModel.setupSource,null)

                val signUpOrSignInmMedium = ClevertapSignUpOrSignInMediumValues.PHONE.value

                sendClevertapEvent(
                    ClevertapEventEnum.SPOTTED_ACCOUNT_6DIGIT_CODE, mapOf(
                        ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium,
                        ClevertapEventPropertyEnum.SETUP_SOURCE.propertyName to signInWithSpottedViewModel.setupSource))

                signInWithSpottedViewModel.verifyDigits.postValue("")
                binding.progressBar.visibility = View.GONE
                signInWithSpottedViewModel.updateAccountAfterVerification(isChangeLogin = signInWithSpottedViewModel.phoneLiveData.value)
                requireActivity().finish()
            }
            is Result.Loading -> {
                binding.progressBar.visibility = View.VISIBLE
            }

            is Result.Error -> {
                binding.progressBar.visibility = View.GONE
                signInWithSpottedViewModel.verifyDigits.postValue("")
                when (it.exception) {
                    is CodeMismatchException -> {
                        handleVerifyError(R.string.code_mismatch_phone, View.VISIBLE)
                    }
                    is UserNotFoundException -> {
                        handleVerifyError(R.string.user_can_not_be_found, View.VISIBLE)
                    }
                    is UsernameExistsException -> {
                        handleVerifyError(R.string.phone_already_in_use, View.VISIBLE)
                    }
                    is LimitExceededException -> {
                        handleVerifyError(getString(R.string.attempt_limit_reached_try_again_later), View.VISIBLE)
                    }
                    else -> handleVerifyError(it.exception.message ?: "", View.VISIBLE)
                }
            }

        }

    }

    private fun handleResponseEmail(it: Result<ResponseBody>) {
        when (it) {
            is Result.Success -> {
                signInWithSpottedEvent(AuthMethod.EMAIL,FirebaseAnalyticsEventsName.SPOTTED_ACCOUNT_6DIGIT_CODE,signInWithSpottedViewModel.setupSource,null)

                val signUpOrSignInmMedium = ClevertapSignUpOrSignInMediumValues.EMAIL.value

                sendClevertapEvent(
                    ClevertapEventEnum.SPOTTED_ACCOUNT_6DIGIT_CODE, mapOf(
                        ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signUpOrSignInmMedium,
                        ClevertapEventPropertyEnum.SETUP_SOURCE.propertyName to signInWithSpottedViewModel.setupSource))

                signInWithSpottedViewModel.verifyDigits.postValue("")
                binding.progressBar.visibility = View.GONE

                signInWithSpottedViewModel.updateAccountAfterVerification(isChangeLogin = signInWithSpottedViewModel.emailLiveData.value)

                requireActivity().finish()
            }
            is Result.Loading -> {
                binding.progressBar.visibility = View.VISIBLE
            }

            is Result.Error -> {
                binding.progressBar.visibility = View.GONE
                signInWithSpottedViewModel.verifyDigits.postValue("")
                when (it.exception) {
                    is CodeMismatchException -> {
                        handleVerifyError(R.string.code_mismatch_email, View.VISIBLE)
                    }
                    is UserNotFoundException -> {
                        handleVerifyError(R.string.user_can_not_be_found, View.VISIBLE)
                    }
                    is UsernameExistsException -> {
                        handleVerifyError(R.string.email_already_in_use, View.VISIBLE)
                    }
                    is LimitExceededException -> {
                        handleVerifyError(getString(R.string.attempt_limit_reached_try_again_later), View.VISIBLE)
                    }
                    else -> handleVerifyError(it.exception.message ?: "", View.VISIBLE)
                }
            }

        }

    }



    private fun handleVerifyError(message: Int, visibility: Int) {
        binding.errorCode.setText(message)
        binding.errorCode.visibility = visibility
    }

    private fun handleVerifyError(message: String, visibility: Int) {
        binding.errorCode.text = message
        binding.errorCode.visibility = visibility
    }


    override fun onResume() {
        super.onResume()
        binding.modifiedPinView.postDelayed({
            binding.modifiedPinView.requestFocus()
            binding.modifiedPinView.showKeyboard()
        }, 200)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }


}