package com.duaag.android.signInWithSpotted

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.NavigationUI
import com.duaag.android.R
import androidx.navigation.ui.AppBarConfiguration
import com.duaag.android.application.DuaApplication
import com.duaag.android.databinding.ActivitySignInWithdSpottedBinding
import com.duaag.android.signInWithSpotted.di.SignInWithSpottedComponent
import javax.inject.Inject


class SignInWithSpottedActivity : AppCompatActivity() {

    lateinit var signInWithSpottedComponent: SignInWithSpottedComponent

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInWithSpottedViewModel by viewModels<SignInWithSpottedViewModel> { viewModelFactory }


    private var _binding: ActivitySignInWithdSpottedBinding? = null
    private val binding get() = _binding!!

    private lateinit var navController: NavController
    private lateinit var appBarConfiguration: AppBarConfiguration


    override fun onCreate(savedInstanceState: Bundle?) {
        signInWithSpottedComponent = (application as DuaApplication).appComponent.signInWithSpottedComponent().create()
        signInWithSpottedComponent.inject(this)

        super.onCreate(savedInstanceState)
        initData()
        _binding = ActivitySignInWithdSpottedBinding.inflate(layoutInflater)

        setContentView(binding.root)
        setSupportActionBar(binding.toolbar)
        setUpNav()
        setUpToolbar()
        extractSetupSource(intent)

    }

    private fun extractSetupSource(intent: Intent) {
        val setupSource = intent.getStringExtra("SETUP_SOURCE")
        signInWithSpottedViewModel.setSetupSource(setupSource)
    }


    private fun setUpNav() {

        val navHostFragment = supportFragmentManager.findFragmentById(R.id.fragmentContainerView) as NavHostFragment?
        navController = navHostFragment!!.navController

     /*   val bundle = Bundle()
        bundle.putSerializable("SETUP_SOURCE",intent.extras?.getSerializable("SETUP_SOURCE"))
        navController.setGraph(R.navigation.nav_graph_sign_in_with_spotted, bundle)*/

        appBarConfiguration = AppBarConfiguration.Builder()
            .setFallbackOnNavigateUpListener {
                // Trigger the Activity's navigate up functionality
                super.onSupportNavigateUp()
            }.build()
        NavigationUI.setupActionBarWithNavController(this, navController, appBarConfiguration)
        navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                R.id.userDetailsFragment -> {
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_close_black_24dp)
                }

                R.id.sixDigitWithSpottedFragment -> {
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                }
            }
        }
    }

    private fun setUpToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    private fun initData() {
        if(DuaApplication.instance.userEmailOrPhone !=null && DuaApplication.instance.userEmailOrPhone.equals("phone_number")) {
            if (DuaApplication.instance.userEmailOrPhoneValue != null) {
                signInWithSpottedViewModel.selectedCountryCodeAsInt = getPrefixPhoneNumber(DuaApplication.instance.userEmailOrPhoneValue!!).toInt()
                signInWithSpottedViewModel.phoneNumberWithoutCountryCode = DuaApplication.instance.userEmailOrPhoneValue!!.substring(4)
                signInWithSpottedViewModel.setPhoneNumber(DuaApplication.instance.userEmailOrPhoneValue!!)
            }
        } else {
            if (DuaApplication.instance.userEmailOrPhoneValue != null) {
                signInWithSpottedViewModel.setEmailAddress(DuaApplication.instance.userEmailOrPhoneValue!!)
            }
        }
    }

    private fun getPrefixPhoneNumber(phoneNumber: String): String {
        return phoneNumber.substring(0, 4).replace("+", "")
    }



}