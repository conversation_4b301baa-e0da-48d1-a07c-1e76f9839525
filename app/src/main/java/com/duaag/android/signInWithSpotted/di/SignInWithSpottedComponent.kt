package com.duaag.android.signInWithSpotted.di

import com.duaag.android.signInWithSpotted.SignInWithSpottedActivity
import com.duaag.android.signInWithSpotted.fragments.SixDigitWithSpottedFragment
import com.duaag.android.signInWithSpotted.fragments.UserDetailsFragment
import com.duaag.android.di.ActivityScope
import dagger.Subcomponent

@ActivityScope
@Subcomponent(modules = [SignInWithSpottedViewModelModule::class])
interface SignInWithSpottedComponent {

    // Factory to create instances of CallsComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): SignInWithSpottedComponent
    }

    // Classes that can be injected by this Component
    fun inject(activity: SignInWithSpottedActivity)
    fun inject(fragment: SixDigitWithSpottedFragment)
    fun inject(fragment: UserDetailsFragment)
}