package com.duaag.android.signInWithSpotted.fragments

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.SetupYourAccountDialogBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsSetUpSourceValues
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.signInWithSpotted.SignInWithSpottedActivity
import com.duaag.android.utils.imageCircle
import timber.log.Timber
import javax.inject.Inject

class SetUpAccountDialog : DialogFragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private var _binding: SetupYourAccountDialogBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
        Timber.tag("VIEWMODEL").d(homeViewModel.toString())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = SetupYourAccountDialogBinding.inflate(inflater)

        firebaseLogEvent(FirebaseAnalyticsEventsName.SPOTTED_SETUP_YOUR_ACCOUNT_POPUP)

        sendClevertapEvent(ClevertapEventEnum.SPOTTED_SETUP_YOUR_ACCOUNT_POPUP)

        imageCircle(binding.profileImage, homeViewModel.userProfile.value?.profile?.pictureUrl)
        binding.userName.text = "${homeViewModel.userProfile.value?.firstName}, "
        binding.userAge.text = "${homeViewModel.userProfile.value?.age}"

        binding.setUpAccBtn .setOnClickListener {
            val setupSource = FirebaseAnalyticsSetUpSourceValues.POPUP.value

            firebaseLogEvent(FirebaseAnalyticsEventsName.SPOTTED_SETUP_YOUR_ACCOUNT_INITIATED,
                mapOf(FirebaseAnalyticsParameterName.SETUP_SOURCE.value to setupSource))

            sendClevertapEvent(
                ClevertapEventEnum.SPOTTED_SETUP_YOUR_ACCOUNT_INITIATED, mapOf(
                    ClevertapEventPropertyEnum.SETUP_SOURCE.propertyName to setupSource))

            startActivity(Intent(requireContext(), SignInWithSpottedActivity::class.java).putExtra("SETUP_SOURCE",FirebaseAnalyticsSetUpSourceValues.POPUP.value))
            dismissAllowingStateLoss()
        }

        return binding.root

    }

    companion object {
        fun newInstance() = SetUpAccountDialog()
    }
}