package com.duaag.android.db

import androidx.annotation.Keep
import androidx.room.TypeConverter
import com.duaag.android.base.models.TagItem
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

@Keep
class TagItemTypeConverter {

    @TypeConverter
    fun tagsFromJson(json: String?): List<TagItem>? {
        return Gson().fromJson(json, object : TypeToken<List<TagItem>>(){}.type)
    }

    @TypeConverter
    fun tagsToJson(tags: List<TagItem>?): String? {
        return Gson().toJson(tags)
    }
}