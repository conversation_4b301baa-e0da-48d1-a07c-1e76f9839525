package com.duaag.android.sunny_hill.domain.use_case

import com.duaag.android.api.ResourceV2
import com.duaag.android.user.UserRepository
import javax.inject.Inject

class GetSunnyHillAttendeesCountUseCase @Inject constructor(private val userRepository: UserRepository) {
    suspend operator fun invoke(): ResourceV2<SunnyHillAttendeesCountModel> {
        return userRepository.getSunnyHillAttendeesCount()
    }
}