package com.duaag.android.sunny_hill.data.model

import com.duaag.android.home.models.RecommendedUserModel
import com.google.errorprone.annotations.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class SunnyHillAttendeesResponse(
    @SerializedName("attendees")
    val attendees: List<RecommendedUserModel>? = null,

    @SerializedName("nextCursor")
    val nextCursor: Long? = null
)