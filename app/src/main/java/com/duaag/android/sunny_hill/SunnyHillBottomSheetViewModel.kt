package com.duaag.android.sunny_hill

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.ResourceV2
import com.duaag.android.clevertap.setCheckedInSunnyHill
import com.duaag.android.clevertap.setJoinGiveawayFestival
import com.duaag.android.counters.domain.AttendSunnyHillUseCase
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.utils.livedata.SingleLiveData
import kotlinx.coroutines.launch
import javax.inject.Inject

class SunnyHillBottomSheetViewModel @Inject constructor(
    private val incrementCounterAPIUseCase: AttendSunnyHillUseCase,
    private val duaSharedPrefs: DuaSharedPrefs
) : ViewModel() {

    private val _checkedInSuccess: SingleLiveData<Unit> = SingleLiveData()
    val checkedInSuccess: LiveData<Unit>
        get() = _checkedInSuccess


    fun setJoinGiveaway() {
        setJoinGiveawayFestival()
        duaSharedPrefs.setHasUserJoinedGiveawaySunnyHill(true)
    }

    fun incrementAttendeeFestivalCount() {
        viewModelScope.launch {
            val result = incrementCounterAPIUseCase.invoke()
            when (result) {
                is ResourceV2.Success -> {
                    setCheckedInSunnyHill()
                    duaSharedPrefs.setHasUserAttendedSunnyHill(true)
                    _checkedInSuccess.call()
                }

                is ResourceV2.Error -> {
                }
            }
        }
    }

}