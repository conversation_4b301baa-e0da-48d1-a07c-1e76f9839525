package com.duaag.android.sunny_hill

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.bumptech.glide.Glide
import com.duaag.android.R
import com.duaag.android.crosspath.presentation.viewmodel.CrossPathViewModel
import com.duaag.android.databinding.SunnyHillBottomSheetBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import kotlinx.coroutines.launch
import javax.inject.Inject

class SunnyHillBottomSheet : BottomSheetDialogFragment() {


    private var _binding: SunnyHillBottomSheetBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel: HomeViewModel by viewModels({ activity as HomeActivity }) { viewModelFactory }
    private val viewModel by viewModels<SunnyHillBottomSheetViewModel> { viewModelFactory }
    private val mainCrossPathViewModel by viewModels<CrossPathViewModel>({ activity as HomeActivity }) { viewModelFactory }

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    var count: String = ""

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        count = arguments?.getString(SUNNY_HILL_ATTENDEES_COUNT) ?: ""
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SunnyHillBottomSheetBinding.inflate(inflater, container, false)


        Glide.with(binding.backgroundImage)
            .load(R.drawable.sunny_hill_bottomsheet_background)
            .centerInside()
            .into(binding.backgroundImage)

        setAttendeesCount(count)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.closeBtn.setOnSingleClickListener {
            dismissAllowingStateLoss()
        }

        binding.checkInBtn.setOnSingleClickListener {
            viewModel.incrementAttendeeFestivalCount()
            setCheckInButtonDisabled()

            try {
                val countInt = (count.toLong() + 1).toString()
                setAttendeesCount(countInt)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }


        if(duaSharedPrefs.getHasUserAttendedSunnyHill()) {
            setCheckInButtonDisabled()
        }

        viewModel.checkedInSuccess.observe(viewLifecycleOwner) {
            mainCrossPathViewModel.callCheckedIn()
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    mainCrossPathViewModel.attendeesCount.collect { attendeeCount ->
                        setAttendeesCount(attendeeCount)
                    }
                }
            }
        }

    }

    private fun setCheckInButtonDisabled() {
        binding.checkInBtn.isEnabled = false
        binding.checkInBtn.backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(requireContext(), R.color.sunny_hill_button))
        binding.checkInBtn.text = getString(R.string.checked_in_label)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =
            super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.setBackgroundResource(android.R.color.transparent)
            bottomSheet?.let {
                BottomSheetBehavior.from<FrameLayout?>(bottomSheet).state =
                    BottomSheetBehavior.STATE_EXPANDED
            }
            d.dismissWithAnimation = true
        }
        return dialog
    }

    private fun setAttendeesCount(attendeeCount: String) {
        if(attendeeCount.isNotEmpty()) {
            count = attendeeCount
            binding.count.text = getString(R.string.check_ins_text_an, attendeeCount)
        }
    }


    companion object {
        const val SUNNY_HILL_ATTENDEES_COUNT = "sunny_hill_attendees_count"
    }

    override fun onDestroyView() {
        super.onDestroyView()

        _binding = null
    }
}