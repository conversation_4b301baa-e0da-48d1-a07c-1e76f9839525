package com.duaag.android.sunny_hill.domain.use_case

import com.duaag.android.api.ResourceV2
import com.duaag.android.sunny_hill.domain.model.SunnyHillAttendeesResult
import com.duaag.android.user.UserRepository
import javax.inject.Inject

class GetSunnyHillAttendeesUseCase @Inject constructor(private val repository: UserRepository) {
    suspend operator fun invoke(limit: Int = 20, nextCursor: String?): ResourceV2<SunnyHillAttendeesResult> {
        return repository.getSunnyHillAttendees(limit, nextCursor)
    }
}
