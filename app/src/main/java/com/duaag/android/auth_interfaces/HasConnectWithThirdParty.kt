package com.duaag.android.auth_interfaces

import androidx.lifecycle.LiveData
import com.duaag.android.api.Result
import com.duaag.android.aws.models.LoginModel
import com.duaag.android.login.models.AuthProviderResponse
import com.duaag.android.login.models.AuthModel

interface HasConnectWithThirdParty {
    fun onConnectWithFacebookClicked(authModel: AuthModel): LiveData<Result<AuthProviderResponse>>
    fun onConnectWithGoogleClicked(authModel: AuthModel): LiveData<Result<Boolean>>
    fun loginUser(loginModel: LoginModel)
}