package com.duaag.android.events_tracker.models

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize


@Keep
@Parcelize
data class FaceBookDeepLinkBodyModel(
    @SerializedName("fbc")
    val fbc: String?,
    @SerializedName("fbp")
    val fbp: String?,
    @SerializedName("userAgent")
    val userAgent: String?,
    @SerializedName("clientIp")
    val clientIp: String?,
): Parcelable
