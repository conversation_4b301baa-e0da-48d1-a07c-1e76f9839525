package com.duaag.android.events_tracker.models

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class AppsflyerBodyModel(
    @SerializedName("os") val os: String,
    @SerializedName("appsflyerId") val appsflyerId: String,
    @SerializedName("osVersion") val osVersion: String,
    @SerializedName("appVersion") val appVersion: String,
    @SerializedName("advertiserId") val advertiserId: String,
)
