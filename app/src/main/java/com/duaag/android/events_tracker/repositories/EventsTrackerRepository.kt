package com.duaag.android.events_tracker.repositories

import com.duaag.android.api.EventsTrackerService
import com.duaag.android.api.Resource
import com.duaag.android.events_tracker.models.AppsflyerBodyModel
import com.duaag.android.events_tracker.models.FaceBookDeepLinkBodyModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import okhttp3.ResponseBody
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

@Singleton
class EventsTrackerRepository @Inject constructor( @Named("eventsTracker") val eventsTrackerService: EventsTrackerService) {

    fun sendDataToAppsFlyer(dataForAppsflyerBody: AppsflyerBodyModel): Flow<Resource<ResponseBody>> = flow {
            emit(Resource.Loading)
        try {
                val response = eventsTrackerService.sendDataForTheAppsFlyer(dataForAppsflyerBody)

                if (response.isSuccessful) {
                    val result = response.body()!!
                    emit(Resource.Success(result))
                } else {
                    throw Exception(response.errorBody()!!.string())
                }
            } catch (e: Exception) {
                e.printStackTrace()
                throw e
            }

        }

    fun deleteDataForTheAppsFlyer(appsflyerId:String):Flow<Resource<ResponseBody>> = flow {
        emit(Resource.Loading)
        try {
            val response = eventsTrackerService.deleteDataForTheAppsFlyer(appsflyerId)

            if (response.isSuccessful) {
                val result = response.body()!!
                emit(Resource.Success(result))
            } else {
                throw Exception(response.errorBody()!!.string())
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

    fun sendDataForFaceBookFromDeepLink(body: FaceBookDeepLinkBodyModel): Flow<Resource<ResponseBody>> = flow {
        emit(Resource.Loading)
        try {
            val response = eventsTrackerService.sendDataForFaceBookFromDeepLink(body)

            if (response.isSuccessful) {
                val result = response.body()!!
                emit(Resource.Success(result))
            } else {
                throw Exception(response.errorBody()!!.string())
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }

    }
}