package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for retrieving locations within a specified radius around a given point.
 * This is typically used to find nearby points of interest,
 *
 * @param crossPathLocationRepository The repository to interact with location data.
 */
class GetLocationsInRadiusUseCase @Inject constructor(private val crossPathLocationRepository: CrossPathLocationRepository) {

    /**
     * Executes the use case to fetch locations within a specified radius from a central point.
     *
     * @param lat The latitude of the central point.
     * @param lon The longitude of the central point.
     * @param radius The radius in kilometers within which to find locations.
     * @return Flow of a list of Location objects within the specified radius.
     */
    operator fun invoke(lat: Double, lon: Double, radius: Double): Flow<List<CrossPathLocation>> = crossPathLocationRepository.getCrossPathLocationsInRadius(lat,lon,radius)
}