package com.duaag.android.crosspath.domain.model

data class GroupedCrossPathsModel(
    val clusterDistance: Double,
    val clusters: List<ClusterModel>,
    val cognitoUserId: String,
    val count: Int,
    val currentLocation: CurrentLocationModel,
    val locationToken: String?
)

data class ClusterModel(
    val hasPendingInteraction: Boolean,
    val location: LocationModel,
    val users: List<String>
)

data class CurrentLocationModel(
    val latitude: Double,
    val longitude: Double
)