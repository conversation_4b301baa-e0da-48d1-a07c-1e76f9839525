package com.duaag.android.crosspath.presentation.views

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView

class OverlappingCircularImageView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val path = Path()
    private val cutoutPath = Path()

    var overlapPercent: Float = 0.8f
        set(value) {
            field = when {
                value < 0f -> 0f
                value > 1f -> 1f
                else -> value
            }
            invalidate()
        }

    var firstImageBitmap: Bitmap? = null
    var secondImageBitmap: Bitmap? = null

    var strokeWidth: Float = 8f
        set(value) {
            field = value
            invalidate()
        }

    override fun onDraw(canvas: Canvas) {
        val firstBitmap = firstImageBitmap ?: return
        val secondBitmap = secondImageBitmap ?: return

        // Convert stroke width from dp to pixels
        val strokePx = strokeWidth * context.resources.displayMetrics.density

        // Calculate the radius based on the view's dimensions
        val viewSize = width.coerceAtMost(height).toFloat()
        val circleRadius = viewSize / 4f

        // Calculate the overlap offset
        val overlapOffset = circleRadius * 2 * overlapPercent

        // Centers of the left and right circles with overlap
        val circleCenterX1 = width / 2f - overlapOffset / 2f
        val circleCenterY1 = height / 2f
        val circleCenterX2 = width / 2f + overlapOffset / 2f
        val circleCenterY2 = height / 2f

        // Create a matrix to center-crop the first image
        val firstMatrix = createCenterCropMatrix(firstBitmap, (circleRadius * 2).toInt(), (circleRadius * 2).toInt())
        val firstImageBitmapCropped = Bitmap.createBitmap(
            firstBitmap,
            0,
            0,
            firstBitmap.width,
            firstBitmap.height,
            firstMatrix,
            true
        )

        // Create a matrix to center-crop the second image
        val secondMatrix = createCenterCropMatrix(secondBitmap, (circleRadius * 2).toInt(), (circleRadius * 2).toInt())
        val secondImageBitmapCropped = Bitmap.createBitmap(
            secondBitmap,
            0,
            0,
            secondBitmap.width,
            secondBitmap.height,
            secondMatrix,
            true
        )

        // Draw the first image with the cutout of the second image
        path.reset()
        path.addCircle(circleCenterX1, circleCenterY1, circleRadius, Path.Direction.CW)
        cutoutPath.reset()
        cutoutPath.addCircle(circleCenterX2, circleCenterY2, circleRadius + strokePx,
            Path.Direction.CW
        ) // Add stroke width
        path.op(cutoutPath, Path.Op.DIFFERENCE)

        canvas.save()
        canvas.clipPath(path)
        canvas.drawBitmap(firstImageBitmapCropped, circleCenterX1 - circleRadius, circleCenterY1 - circleRadius, paint)
        canvas.restore()

        // Draw the second image
        path.reset()
        path.addCircle(circleCenterX2, circleCenterY2, circleRadius, Path.Direction.CW)
        canvas.save()
        canvas.clipPath(path)
        canvas.drawBitmap(secondImageBitmapCropped, circleCenterX2 - circleRadius, circleCenterY2 - circleRadius, paint)
        canvas.restore()
    }

    private fun createCenterCropMatrix(bitmap: Bitmap, width: Int, height: Int): Matrix {
        val scale: Float
        val dx: Float
        val dy: Float
        if (bitmap.width * height > width * bitmap.height) {
            scale = height.toFloat() / bitmap.height.toFloat()
            dx = (width - bitmap.width * scale) * 0.5f
            dy = 0f
        } else {
            scale = width.toFloat() / bitmap.width.toFloat()
            dx = 0f
            dy = (height - bitmap.height * scale) * 0.5f
        }
        val matrix = Matrix()
        matrix.setScale(scale, scale)
        matrix.postTranslate(dx, dy)
        return matrix
    }

    fun setFirstImage(bitmap: Bitmap) {
        firstImageBitmap = bitmap
        invalidate()
    }

    fun setSecondImage(bitmap: Bitmap) {
        secondImageBitmap = bitmap
        invalidate()
    }
}