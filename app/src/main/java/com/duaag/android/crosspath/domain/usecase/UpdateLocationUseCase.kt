package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import javax.inject.Inject

class UpdateLocationUseCase @Inject constructor(private val crossPathLocationRepository: CrossPathLocationRepository) {
    suspend operator fun invoke(crossPathLocation: CrossPathLocation): Unit = crossPathLocationRepository.updateCrossPathLocation(crossPathLocation)
}