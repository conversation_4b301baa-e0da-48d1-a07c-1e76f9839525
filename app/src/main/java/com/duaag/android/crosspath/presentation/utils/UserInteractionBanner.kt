package com.duaag.android.crosspath.presentation.utils

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.widget.FrameLayout
import com.duaag.android.R
import com.duaag.android.databinding.UserInteractionBannerLayoutBinding
import com.duaag.android.home.models.CrossPath
import com.duaag.android.home.models.InteractionType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext

class UserInteractionBanner @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr), CoroutineScope {

    private val binding: UserInteractionBannerLayoutBinding =
        UserInteractionBannerLayoutBinding.inflate(
            LayoutInflater.from(context), this, true
        )

    private var job: Job? = null

    override val coroutineContext: CoroutineContext
        get() = Dispatchers.Main + SupervisorJob()

    init {
        <EMAIL> = View.INVISIBLE
    }

    fun show(context: Context, crossPathData: CrossPath?, counterPartName: String, autoDismiss: Boolean = true) {
        job?.cancel()
        setInteractions(context, crossPathData , counterPartName)
        this.visibility = View.VISIBLE

        if (autoDismiss) {
            job = launch {
                delay(3000)
                dismiss()
            }
        }
    }

    fun dismiss() {
        val fadeOut = AlphaAnimation(1.0f, 0.0f)
        fadeOut.duration = 500
        fadeOut.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {}

            override fun onAnimationEnd(animation: Animation) {
                <EMAIL> = View.GONE
                job?.cancel()
            }

            override fun onAnimationRepeat(animation: Animation) {}
        })
        this.startAnimation(fadeOut)
    }

    private fun setInteractions(context: Context, crossPathData: CrossPath?, counterPartName: String) {
        binding.notificationTextName.visibility = ViewGroup.GONE
        val currentUserInteraction = crossPathData?.currentUserInteraction
        val counterPartInteraction = crossPathData?.counterPartInteraction
        if (!currentUserInteraction.isNullOrEmpty()) {
            when (currentUserInteraction) {
                InteractionType.LIKE.value -> {
                    setIcon(R.drawable.like_icon)
                    setMainText(context.getString(R.string.sent_like_title))
                    setSubText(context.getString(R.string.sent_like_description))
                }
                InteractionType.INSTA_CHAT.value -> {
                    setIcon(R.drawable.instachat_icon)
                    setMainText(context.getString(R.string.you_sent_instachat_title))
                    setSubText(context.getString(R.string.you_sent_instachat_description))
                }
                InteractionType.DISLIKE.value -> {
                    this.visibility = View.GONE
                }
            }
        }
        if (!counterPartInteraction.isNullOrEmpty()) {
            when (counterPartInteraction) {
                InteractionType.LIKE.value -> {
                    binding.notificationTextName.visibility = GONE
                    setIcon(R.drawable.like_icon)
                    setMainText(context.getString(R.string.likes_you_title, counterPartName))
                    setSubText(context.getString(R.string.like_you_desc))
                }

                InteractionType.INSTA_CHAT.value -> {
                    binding.notificationTextName.visibility = VISIBLE
                    setIcon(R.drawable.instachat_icon)
                    setMainText(context.getString(R.string.sent_instachat_title))
                    setInstachatTextName(counterPartName)
                    setSubText(context.getString(R.string.like_you_desc))
                }
                InteractionType.DISLIKE.value -> {
                    this.visibility = View.GONE
                }
            }
        }
    }

    private fun setIcon(iconRes: Int) {
        binding.icon.setImageResource(iconRes)
    }

    private fun setMainText(mainText: String) {
        binding.notificationText.text = mainText
    }

    private fun setSubText(subText: String) {
        binding.notificationSubtext.text = subText
    }

    private fun setInstachatTextName(textName: String) {
        binding.notificationTextName.text = "$textName "
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        job?.cancel()
    }
}
