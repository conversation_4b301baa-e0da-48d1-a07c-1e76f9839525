package com.duaag.android.crosspath.domain.repository

import com.duaag.android.api.ResourceV2
import com.duaag.android.crosspath.data.remote.CrossPathUsersRaw
import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.model.CrossPathUsersModel
import com.duaag.android.crosspath.domain.model.CrossPathsModel
import com.duaag.android.crosspath.domain.model.GroupedCrossPathsModel
import kotlinx.coroutines.flow.Flow

interface CrossPathLocationRepository {
    suspend fun updateCrossPathLocation(location: CrossPathLocation)
    suspend fun saveCrossPathLocation(crossPathLocation: CrossPathLocation)
    suspend fun deleteLocationById(id: Int)
    suspend fun deleteLocationsByIds(ids: List<Int>)
    suspend fun deleteLocationsOlderThan(timestamp: Long)
    fun getCrossPathLocations(): Flow<List<CrossPathLocation>>
    fun getCrossPathLocationsInRadius(lat: Double, lon: Double, radius: Double): Flow<List<CrossPathLocation>>
    fun getCrossPathLocationsByTime(startTime: Long, endTime: Long): Flow<List<CrossPathLocation>>
    fun getFrequentCrossPathLocations(minVisits: Int): Flow<List<CrossPathLocation>>
    fun getLocationOlderThan(time :Long): Flow<List<CrossPathLocation>>
    suspend fun getLocationOlderThanX(time :Long): List<CrossPathLocation>
    suspend fun addLocationsInBackend(data: List<CrossPathLocation>): ResourceV2<Unit>
    suspend fun getCrossPaths(nextCursor: Long? = null, limit: Int = 10): ResourceV2<CrossPathsModel>
    suspend fun getGroupedCrossPaths(distance: Double, latitude: Double, longitude: Double): ResourceV2<GroupedCrossPathsModel>
    suspend fun getCrossPathUsers(userIds: List<String>, latitude: Double, longitude: Double): ResourceV2<CrossPathUsersModel>
}
