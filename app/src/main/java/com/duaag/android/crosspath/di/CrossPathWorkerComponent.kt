package com.duaag.android.crosspath.di

import com.duaag.android.crosspath.data.workmanager.CrossPathBackendOneTimeSyncWorker
import com.duaag.android.crosspath.data.workmanager.CrossPathBackendPeriodicSyncWorker
import com.duaag.android.crosspath.data.workmanager.CrossPathOneTimeWorker
import com.duaag.android.crosspath.data.workmanager.CrossPathPeriodicWorker
import com.duaag.android.di.ActivityScope
import dagger.Subcomponent

@ActivityScope
@Subcomponent()
interface CrossPathWorkerComponent {

    @Subcomponent.Factory
    interface Factory {
        fun create(): CrossPathWorkerComponent
    }

    fun inject(crossPathPeriodicWorker: CrossPathPeriodicWorker)

    fun inject(crossPathWorker: CrossPathOneTimeWorker)
    fun inject(crossPathBackendPeriodicSyncWorker: CrossPathBackendPeriodicSyncWorker)
    fun inject(crossPathBackendOneTimeSyncWorker: CrossPathBackendOneTimeSyncWorker)
}