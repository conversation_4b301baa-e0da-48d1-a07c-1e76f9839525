package com.duaag.android.crosspath.data.workmanager

import android.content.Context
import androidx.work.ListenableWorker
import androidx.work.WorkerParameters
import com.duaag.android.application.DuaApplication
import com.duaag.android.crosspath.data.geofence.GeofenceManager
import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.usecase.SaveLocationUseCase
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.utils.checkLocationAndBackgroundPermission
import com.duaag.android.utils.location.LocationClient
import com.duaag.android.utils.location.LocationClientImpl
import com.google.android.gms.location.LocationServices
import com.google.android.gms.tasks.CancellationTokenSource
import com.google.common.util.concurrent.ListenableFuture
import com.google.common.util.concurrent.SettableFuture
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

class CrossPathPeriodicWorker(
    val context: Context,
    workerParams: WorkerParameters,
) : ListenableWorker(context, workerParams) {

    private val future: SettableFuture<Result> = SettableFuture.create()
    private val coroutineExceptionHandler = CoroutineExceptionHandler { coroutineContext, throwable ->
        throwable.printStackTrace()
        future.set(Result.retry())
    }
    private val job = SupervisorJob()
    private val scope = CoroutineScope(Dispatchers.IO + job + coroutineExceptionHandler)
    private var locationClient: LocationClient? = null
    private var geofenceManager: GeofenceManager? = null

    @Inject
    lateinit var saveLocationUseCase: SaveLocationUseCase

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    override fun startWork(): ListenableFuture<Result> {
        try {
            (context as DuaApplication)
                .appComponent.crossPathWorkerComponent()
                .create()
                .inject(this)
            duaSharedPrefs.setTESTBefore(duaSharedPrefs.getTESTBefore() + 1)

            if (context.checkLocationAndBackgroundPermission().not()) {
                future.set(Result.retry())
            } else {
                geofenceManager = GeofenceManager(context)
                locationClient = LocationClientImpl(
                    context,
                    LocationServices.getFusedLocationProviderClient(applicationContext)
                )
                getAndSaveCurrentLocation()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            future.set(Result.failure())
        }
        return future
    }

    private fun getAndSaveCurrentLocation() {
        locationClient?.getCurrentLocation(CancellationTokenSource())
            ?.catch { e ->
                e.printStackTrace()
                future.set(Result.retry())
            }
            ?.onEach { location ->
                saveLocationUseCase(
                    CrossPathLocation(
                        location.longitude,
                        location.latitude,
                        System.currentTimeMillis()
                    )
                )
                duaSharedPrefs.setTESTLocation(duaSharedPrefs.getTESTLocation() + 1)

                if (context.checkLocationAndBackgroundPermission()) {
                    geofenceManager?.removeAndAddGeoFences(location) {
                        future.set(Result.success())
                    }
                } else {
                    future.set(Result.success())
                }
            }
            ?.launchIn(scope)
    }

    override fun onStopped() {
        super.onStopped()
        try {
            locationClient?.stopLocationUpdates()
            locationClient = null
            geofenceManager = null
            scope.cancel()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    companion object {
        const val workName = "CrossPathWorker"
        const val repeatInterval = 30L
    }
}