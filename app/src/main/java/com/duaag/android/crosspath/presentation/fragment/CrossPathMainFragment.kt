package com.duaag.android.crosspath.presentation.fragment

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.api.ProfileVisitSourceEnum
import com.duaag.android.api.Resource
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.chat.model.ReportBody
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapMatchTypeValues
import com.duaag.android.clevertap.ClevertapReceivedUserPremiumBadgeValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.PremiumBadgeValues
import com.duaag.android.clevertap.ScreenOutputClevertapValues
import com.duaag.android.clevertap.SwipeSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.crosspath.data.remote.dto.CrossPathErrorResponse
import com.duaag.android.crosspath.presentation.adapter.CrossPathAdapter
import com.duaag.android.crosspath.presentation.fragment.CrossPathFullScreenFragment.Companion.CROSS_PATH_DATA
import com.duaag.android.crosspath.presentation.fragment.CrossPathFullScreenFragment.Companion.USER_LOCATION
import com.duaag.android.crosspath.presentation.model.CrossPathUiState
import com.duaag.android.crosspath.presentation.model.DiscoveredProfilesUiModel
import com.duaag.android.crosspath.presentation.viewmodel.CrossPathViewModel
import com.duaag.android.databinding.CrossPathFragmentBinding
import com.duaag.android.databinding.FeaturedProfileSunnyHillItemBinding
import com.duaag.android.databinding.LayoutLocationServicesBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.fragments.InstaChatDialogFragment
import com.duaag.android.home.fragments.MatchScreenMotionLayoutDialogFragment
import com.duaag.android.home.fragments.UserProfileFragment
import com.duaag.android.home.fragments.VerificationInProgressDialog
import com.duaag.android.home.fragments.VerifyYourProfileDialog
import com.duaag.android.home.models.CrossPath
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.models.LimitReachedModel
import com.duaag.android.home.models.LimitReachedScreenSource
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.report.UserReportable
import com.duaag.android.report.viewmodel.ReportViewModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.checkBackgroundLocationPermission
import com.duaag.android.utils.convertDpToPx
import com.duaag.android.utils.getLocationServicesStatus
import com.duaag.android.utils.getNotificationsOnClevertapValue
import com.duaag.android.utils.isDarkModeEnabled
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.utils.sunnyhill.SunnyHillUtils
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.ItemPaddingHorizontalDecoration
import com.duaag.android.views.LastItemPaddingHorizontalDecoration
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraOptions
import com.mapbox.maps.Projection
import com.mapbox.maps.Style
import com.mapbox.maps.plugin.scalebar.scalebar
import com.mapbox.maps.viewannotation.geometry
import com.mapbox.maps.viewannotation.viewAnnotationOptions
import com.yuyakaido.android.cardstackview.Direction
import kotlinx.coroutines.launch
import sendVerifyYourProfileInitiatedAnalyticsEvent
import sendVerifyYourProfilePopupAnalyticsEvent
import timber.log.Timber
import javax.inject.Inject


class CrossPathMainFragment : Fragment(), UserProfileFragment.UserProfileInteract, UserReportable, VerifyYourProfileDialog.VerifyYourProfileDialogListener {

    private var _binding: CrossPathFragmentBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    @Inject
    lateinit var duaAccount: DuaAccount

    private val crossPathViewModel by viewModels<CrossPathViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val reportViewModel by viewModels<ReportViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private var hasScreenViewBeenSent = false
    private val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { _ -> }
    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        _binding = CrossPathFragmentBinding.inflate(inflater, container, false)

        setupDiscoveredUsers()
        initializeMapView()

        binding.swipeRefresh.setOnRefreshListener {
            crossPathViewModel.loadDiscoveredProfiles()

            getGroupedCrossPaths(forceRefresh = true)
        }

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.mapIcon.setOnClickListener {
            navigateToFullMap()
        }

        binding.expandContainer.setOnSingleClickListener {
            navigateToFullMap()
        }

        binding.spottedToolbar.notificationFeedButton.setOnSingleClickListener {
            (requireActivity() as? HomeActivity)?.navController?.navigateSafer(R.id.action_global_notificationFeed)
        }

        binding.exploreCrossPathContainer.spottedInRealLifeCard.setOnSingleClickListener {
            (requireActivity() as? HomeActivity)?.navController?.navigateSafer(R.id.action_global_allCrossPathProfiles)
        }

        binding.profileInvisible.unhideProfileButton.setOnSingleClickListener {
            val model = homeViewModel.userProfile.value
            model?.let {
                homeViewModel.unHideMyProfile().observe(viewLifecycleOwner) { result ->
                    when (result) {
                        is com.duaag.android.api.Result.Success -> {
                            model.profile.isInvisible = false
                            homeViewModel.updateUserInvisible(model)
                            homeViewModel.reloadRecommendedUsers()

                            crossPathViewModel.loadDiscoveredProfiles()
                            getGroupedCrossPaths(forceRefresh = true)

                            (requireActivity() as? HomeActivity)?.getLocationServicesLayout()?.let {
                                updateLocationServicesUI(requireContext(), it)
                            }
                        }

                        is com.duaag.android.api.Result.Error -> {
                        }

                        is com.duaag.android.api.Result.Loading -> {
                        }
                    }
                }
            }
        }

        homeViewModel.showMatchScreen.observe(viewLifecycleOwner) {
            sendMatchEvent(it)
            (childFragmentManager.findFragmentByTag("UserProfileFragment") as? UserProfileFragment)?.dismissAllowingStateLoss()
            val dialogFragment = MatchScreenMotionLayoutDialogFragment.newInstance(it, hasChat = false)
            dialogFragment.show(requireActivity().supportFragmentManager, "MatchScreenMotionLayoutDialogFragment")
            it.cognitoUserId?.let { cognitoId -> crossPathViewModel.removeCrossPath(cognitoId) }
        }
        homeViewModel.showMatchScreenIfHasChat.observe(viewLifecycleOwner){
            sendMatchEvent(it.recommendedUserModel)
            (childFragmentManager.findFragmentByTag("UserProfileFragment") as? UserProfileFragment)?.dismissAllowingStateLoss()
            val dialogFragment = MatchScreenMotionLayoutDialogFragment.newInstance(it.recommendedUserModel, hasChat = false)
            dialogFragment.show(requireActivity().supportFragmentManager, "MatchScreenMotionLayoutDialogFragment")
            it.recommendedUserModel.cognitoUserId?.let { cognitoId -> crossPathViewModel.removeCrossPath(cognitoId) }
        }
        homeViewModel.showNotificationFeedDotNotification.observe(viewLifecycleOwner) {
            binding.spottedToolbar.badgeView.visibility = if (it) View.VISIBLE else View.GONE
        }
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                crossPathViewModel.crossPathUiState.collect { state ->
                    handleEvent(state)

                    if (!state.isLoadingGroupedPins && !state.isLoadingDiscoveredProfiles)
                        binding.swipeRefresh.isRefreshing = false

                    if(homeViewModel.userProfile.value?.profile?.isInvisible == true) {
                        binding.profileInvisible.root.visibility = View.VISIBLE
                        return@collect
                    } else {
                        binding.profileInvisible.root.visibility = View.GONE
                    }

                    if (state.groupedPinsError == CrossPathErrorResponse.NO_NETWORK_CONNECTION ||
                            state.discoveredItemsError == CrossPathErrorResponse.NO_NETWORK_CONNECTION) {
                        binding.networkError.root.visibility = View.VISIBLE
                        return@collect
                    }

                    binding.networkError.root.visibility = View.GONE

                    handleDiscoveredProfiles(state)

                    handleGroupedPins(state)

                    if(state.lastKnownLocation != null
                        && !state.isLoadingGroupedPins
                        && state.groupedPins == null
                        && state.groupedPinsError == null) {
                        getGroupedCrossPaths()
                        setMapCamera(state.lastKnownLocation.longitude ?: 0.0, state.lastKnownLocation.latitude ?: 0.0)
                    }
                }
            }
        }

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                (requireActivity() as? HomeActivity)?.getLocationServicesLayout()?.let {
                    updateLocationServicesUI(requireContext(), it)
                }

                if(crossPathViewModel.crossPathUiState.value.lastKnownLocation == null
                        && requireContext().isLocationPermissionEnabled()) {
                    crossPathViewModel.requestPermission()
                }
            }
        }
        reportViewModel.reportUser.observe(viewLifecycleOwner) {
            when (it) {
                is Resource.Success -> {
                    it.data.user?.let { user->
                        homeViewModel.removeUserFromFeaturedProfiles(user)
                        user.cognitoUserId?.let { cognitoUserId -> crossPathViewModel.removeCrossPath(cognitoUserId) }
                    }
                    homeViewModel.dissmissCardUserProfile()
                    homeViewModel.userProfileProgressBarShow(false)
                }

                is Resource.Error -> {
                    ToastUtil.toast(getString(R.string.smthg_went_wrong))
                    homeViewModel.userProfileProgressBarShow(false)
                    logError(ErrorStatus.REPORT_USER)
                }

                Resource.Loading -> {
                    homeViewModel.userProfileProgressBarShow(true)
                }
            }
        }
        handleSunnyHillView(binding.sunnyHillGraphic,homeViewModel)
    }
    private fun sendMatchEvent(recommendedUserModel: RecommendedUserModel) {
        val eventActivityType = recommendedUserModel.activityType
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

        val matchTypeValue = ClevertapMatchTypeValues.CROSS_PATH.value
        val recommSource = recommendedUserModel.recommSource ?: "dua"
        val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
        val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

        sendClevertapEvent(
            ClevertapEventEnum.MATCH, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.MATCH_TYPE.propertyName to matchTypeValue,
                ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

            )
        )
    }
    private fun handleSunnyHillView(
        sunnyHillGraphic: FeaturedProfileSunnyHillItemBinding,
        homeViewModel: HomeViewModel
    ) {
        sunnyHillGraphic.buyBtn.setOnSingleClickListener {
            openLinkInBrowser(requireContext(), SunnyHillUtils.URL)
        }
        setSunnyHillGraphicDrawable(homeViewModel, sunnyHillGraphic)
        setVisibility(sunnyHillGraphic.root, SunnyHillUtils.shouldShowSunnyHillViews())
    }

    private fun setSunnyHillGraphicDrawable(
        homeViewModel: HomeViewModel,
        sunnyHillGraphic: FeaturedProfileSunnyHillItemBinding
    ) {
        val drawable =
            if (homeViewModel.userProfile.value?.premiumType != null) ContextCompat.getDrawable(
                sunnyHillGraphic.root.context,
                R.drawable.sunny_hill_premium_item
            ) else ContextCompat.getDrawable(
                sunnyHillGraphic.root.context,
                R.drawable.sunny_hill_freemium_item
            )
        sunnyHillGraphic.sunnyHillGraphic.setImageDrawable(drawable)
    }

    private fun openLinkInBrowser(context: Context,url: String) {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = Uri.parse(url)
        if (intent.resolveActivity(context.packageManager) != null) {
            startActivity(intent)
        }
    }
    private fun handleDiscoveredProfiles(state: CrossPathUiState) {
        val discoveredItems = state.discoveredItems.toMutableList()

        if (state.discoveredItemsError == CrossPathErrorResponse.AN_ERROR_OCCURRED)
            discoveredItems.add(DiscoveredProfilesUiModel.Error)
        else if (discoveredItems.isEmpty()) {
            discoveredItems.addAll(
                    listOf(
                            DiscoveredProfilesUiModel.Shimmer(-1),
                            DiscoveredProfilesUiModel.Shimmer(-2),
                            DiscoveredProfilesUiModel.Shimmer(-3),
                            DiscoveredProfilesUiModel.Shimmer(-4),
                    )
            )
        } else if(state.paginationData.nextCursor != null)
                discoveredItems.add(DiscoveredProfilesUiModel.ViewAll)

        (binding.discoveredProfiles.adapter as CrossPathAdapter).submitData(discoveredItems)
    }

    private fun handleGroupedPins(state: CrossPathUiState) {
        if(state.groupedPinsError == CrossPathErrorResponse.AN_ERROR_OCCURRED) {
            showMapError()
        } else if(state.groupedPins != null
                && !state.isLoadingGroupedPins
                && !state.isLoadingDiscoveredProfiles
                && state.groupedPins.clusters.isNullOrEmpty()
                && state.discoveredItems.isEmpty()) {
            hideMapError()
            showEmptyStateMap()
        } else {
            hideMapError()
            state.groupedPins?.clusters?.forEach { cluster ->
                if (cluster.location.longitude != null && cluster.location.latitude != null)
                    addViewAnnotationToPoint(
                            point = Point.fromLngLat(cluster.location.longitude, cluster.location.latitude),
                            count = cluster.users.size.toString(),
                            hasPendingInteraction = cluster.hasPendingInteraction
                    )
            }
        }
        val currentLocationLatitude = state.groupedPins?.currentLocation?.latitude
        val currentLocationLongitude = state.groupedPins?.currentLocation?.longitude
        if (currentLocationLongitude != null && currentLocationLatitude != null) {
            setMapCamera(currentLocationLongitude, currentLocationLatitude)
        }
    }

    private fun navigateToFullMap() {
        val state = crossPathViewModel.crossPathUiState.value
        if ((RemoteConfigUtils.getShowSunnyHill() || state.discoveredItems.isNotEmpty()) && state.lastKnownLocation != null) {
            val args = bundleOf(CROSS_PATH_DATA to null, USER_LOCATION to state.lastKnownLocation)
            (requireActivity() as? HomeActivity)?.navController?.navigateSafer(
                    resId = R.id.action_global_crossPathFullScreen,
                    args = args
            )
        }
    }
    private fun updateLocationServicesUI(context: Context, layoutBinding: LayoutLocationServicesBinding) {
        if(!context.isLocationPermissionEnabled() && !shouldProvideRationale()) {
            updateLocationServicesUI(
                context,
                layoutBinding,
                R.string.allow_location_service_title,
                R.string.allow_location_service_desc,
                R.string.allow
            )
            layoutBinding.root.visibility = View.VISIBLE
            return
        }
        updateLocationServicesUI(
            context,
            layoutBinding,
            R.string.allow_always_location_title,
            R.string.allow_always_location_desc,
            R.string.settings
        )

        val isInvisible = homeViewModel.userProfile.value?.profile?.isInvisible != true
        val shouldShow = !context.checkBackgroundLocationPermission() && isInvisible

        setVisibility(layoutBinding.root, shouldShow)
    }
    private fun updateLocationServicesUI(context: Context, layoutBinding: LayoutLocationServicesBinding, titleResId: Int, descResId: Int, buttonResId: Int) {
        layoutBinding.title.text = context.getString(titleResId)
        layoutBinding.description.text = context.getString(descResId)
        layoutBinding.settingsButton.text = context.getString(buttonResId)
    }
    private fun shouldProvideRationale(): Boolean {
        val shouldProvideRationale =
            ActivityCompat.shouldShowRequestPermissionRationale(
                requireActivity(),
                Manifest.permission.ACCESS_FINE_LOCATION
            )

        return shouldProvideRationale
    }
    private fun showMapError() {
        binding.groupedPinsError.root.visibility = View.VISIBLE
    }

    private fun hideMapError() {
        binding.groupedPinsError.root.visibility = View.GONE
    }

    private fun handleEvent(state: CrossPathUiState) {
        if(!state.isLoadingGroupedPins && !state.isLoadingDiscoveredProfiles) {
            when {
                state.groupedPinsError == CrossPathErrorResponse.NO_NETWORK_CONNECTION
                        || state.discoveredItemsError == CrossPathErrorResponse.NO_NETWORK_CONNECTION -> {
                    sendCrossPathScreenViewEvent(
                            screenOutputValue = ScreenOutputClevertapValues.NO_INTERNET_CONNECTION,
                            isCrossPathWithResults = false
                    )
                }
                state.groupedPinsError == CrossPathErrorResponse.AN_ERROR_OCCURRED
                        || state.discoveredItemsError == CrossPathErrorResponse.AN_ERROR_OCCURRED -> {
                    sendCrossPathScreenViewEvent(
                            screenOutputValue = ScreenOutputClevertapValues.UNABLE_TO_LOAD_DATA,
                            isCrossPathWithResults = false
                    )
                }
                else -> {
                    sendCrossPathScreenViewEvent(
                            screenOutputValue = ScreenOutputClevertapValues.REGULAR,
                            isCrossPathWithResults = state.discoveredItems.isNotEmpty()
                    )
                }

            }
        }
    }

    private fun sendCrossPathScreenViewEvent(
            screenOutputValue: ScreenOutputClevertapValues,
            isCrossPathWithResults: Boolean
    ) {
        if (hasScreenViewBeenSent)
            return

        hasScreenViewBeenSent = true

        val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
        val isIncognitoKey = ClevertapEventPropertyEnum.IS_INCOGNITO_MODE.propertyName
        val screenOutputKey = ClevertapEventPropertyEnum.SCREEN_OUTPUT.propertyName
        val isCrossPathWithResultsKey = ClevertapEventPropertyEnum.IS_CROSS_PATH_WITH_RESULTS.propertyName
        val locationAccessKey = ClevertapEventPropertyEnum.LOCATION_ACCESS.propertyName

        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userRepository.user.value)
        val locationAccess = getLocationServicesStatus(
                requireContext().isLocationPermissionEnabled(),
                requireContext().checkBackgroundLocationPermission()
        )
        val isIncognitoMode = homeViewModel.userProfile.value?.profile?.isGhost

        sendClevertapEvent(
            ClevertapEventEnum.CROSS_PATH_SCREENVIEW,
            mapOf(
                premiumTypeKey to eventPremiumType,
                screenOutputKey to screenOutputValue.value,
                locationAccessKey to locationAccess,
                isCrossPathWithResultsKey to isCrossPathWithResults,
                isIncognitoKey to isIncognitoMode
            )
        )
    }

    private fun showEmptyStateMap() {
        val expandBlur = binding.expandBlur
        val expandText = binding.expandText

        expandBlur.setImageResource(R.drawable.blur_overlay_center)
        expandText.text = getString(R.string.no_spotted_profiles_heading)

        val constraintSet = ConstraintSet()
        constraintSet.clone(binding.expandContainer)


        // Center the ImageView horizontally and vertically
        constraintSet.connect(expandBlur.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
        constraintSet.connect(expandBlur.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
        constraintSet.connect(expandBlur.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
        constraintSet.connect(expandBlur.id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)

        constraintSet.setHorizontalBias(expandBlur.id, 0.5f)
        constraintSet.setVerticalBias(expandBlur.id, 0.5f)


        // Center the TextView horizontally and vertically
        constraintSet.connect(expandText.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
        constraintSet.connect(expandText.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
        constraintSet.connect(expandText.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
        constraintSet.connect(expandText.id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)

        constraintSet.setMargin(expandText.id, ConstraintSet.BOTTOM, 0)
        constraintSet.setMargin(expandText.id, ConstraintSet.END, 0)

        constraintSet.setHorizontalBias(expandText.id, 0.5f)
        constraintSet.setVerticalBias(expandText.id, 0.5f)

        constraintSet.applyTo(binding.expandContainer)

    }


    private fun initializeMapView() {

        binding.mapView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding.mapView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                getGroupedCrossPaths()
            }
        })


        binding.mapView.mapboxMap.loadStyle(if (isDarkModeEnabled(requireContext())) Style.DARK else Style.LIGHT)
        binding.mapView.scalebar.enabled = false

        val location = crossPathViewModel.crossPathUiState.value.lastKnownLocation
        setMapCamera(location?.longitude ?: 0.0, location?.latitude ?: 0.0)
    }

    fun getGroupedCrossPaths(forceRefresh: Boolean = false) {
        val location = crossPathViewModel.crossPathUiState.value.lastKnownLocation
        val longitude = location?.longitude
        val latitude = location?.latitude
        Timber.tag("MINI_MAP").d(" latitude:${latitude} | longitude:${longitude}")

        if (latitude != null && longitude != null) {
            val distance = getDistance(latitude)
            if(distance != 0.0)
                crossPathViewModel.getGroupedCrossPaths(distance, latitude, longitude, forceRefresh)
        }
    }

    private fun getDistance(latitude: Double): Double {
        val metersPerPoint = Projection.getMetersPerPixelAtLatitude(latitude, MINI_MAP_INITIAL_ZOOM)
        val mapViewWidthPixels = binding.mapView.width// * resources.displayMetrics.density
        return (metersPerPoint * mapViewWidthPixels) / 2.2
    }

    private fun setMapCamera(longitude: Double, latitude: Double) {
        val zoom = if(crossPathViewModel.crossPathUiState.value.lastKnownLocation != null) MINI_MAP_INITIAL_ZOOM else 1.0
        binding.mapView.mapboxMap.setCamera(
                CameraOptions.Builder()
                        .center(Point.fromLngLat(longitude, latitude))
                        .pitch(0.0)
                        .zoom(zoom)
                        .bearing(0.0)
                        .build()
        )
    }

    private fun addViewAnnotationToPoint(point: Point, count: String, hasPendingInteraction: Boolean) {
        val viewAnnotationManager = binding.mapView.viewAnnotationManager

        val viewAnnotation = viewAnnotationManager.addViewAnnotation(
                resId = R.layout.map_marker_layout,
                options = viewAnnotationOptions {
                    geometry(point)
                    allowOverlap(true)
                }
        )
        viewAnnotation.findViewById<TextView>(R.id.count).text = count
        viewAnnotation.findViewById<TextView>(R.id.overlay).isVisible = !hasPendingInteraction
    }


    private fun setupDiscoveredUsers() {
        binding.discoveredProfiles.addItemDecoration(LastItemPaddingHorizontalDecoration(convertDpToPx(24f).toInt()))
        binding.discoveredProfiles.addItemDecoration(ItemPaddingHorizontalDecoration(convertDpToPx(24f).toInt()))
        binding.discoveredProfiles.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        binding.discoveredProfiles.adapter = CrossPathAdapter { model ->
            when (model) {
                is DiscoveredProfilesUiModel.ViewAll -> {
                    (requireActivity() as? HomeActivity)?.navController?.navigateSafer(R.id.action_global_allCrossPathProfiles)
                }

                is DiscoveredProfilesUiModel.Item -> {
                    UserProfileFragment.newInstance(
                        RecommendedUserModel.getEmptyRecommendedUserModel().copy(cognitoUserId = model.cognitoUserId),
                        _isFromHome = true,
                        _profileVisitSource = ProfileVisitSourceEnum.CROSS_PATH.source
                          ).let { userProfileFragment->
                        userProfileFragment.setPictureChangeListener(object :
                            UserProfileFragment.PictureChangeListener {
                            override fun onRightSideClick(index: Int, crossPath: CrossPath?, recommendedUserModel: RecommendedUserModel?) {

                                val hasCurrentUserOnlyOnePicture = homeViewModel.userProfile.value?.hasOnlyOnePicture() == true
                                sendProfileVisitOtherPhotosEvent(hasCurrentUserOnlyOnePicture,crossPath)
                            }

                            override fun onLeftSideClick(index: Int, crossPath: CrossPath?, recommendedUserModel: RecommendedUserModel?) {

                                val hasCurrentUserOnlyOnePicture = homeViewModel.userProfile.value?.hasOnlyOnePicture() == true
                                if(index <= 1) sendProfileVisitOtherPhotosEvent(
                                    hasCurrentUserOnlyOnePicture,
                                    crossPath
                                )
                            }

                        })
                        userProfileFragment.show(childFragmentManager, "UserProfileFragment")
                    }
                    val userProfile = homeViewModel.userProfile.value
                    val premiumType = getPremiumTypeEventProperty(userProfile)
                    val eventSource = ClevertapEventSourceValues.CROSS_PATH.value
                    val areDetailsLocked = userProfile?.tags.isNullOrEmpty()
                    val isPhotoHidden = userProfile?.profile?.hasBlurredPhotos
                    val premiumBadge = if(userProfile?.profile?.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value


                    sendProfileVisitAnalyticsEvents(premiumType, eventSource, areDetailsLocked, model,isPhotoHidden, premiumBadge)
                }

                else -> {}
            }
        }

//        binding.discoveredProfiles.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//
//            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
//                super.onScrolled(recyclerView, dx, dy)
//                val mLayoutManager: LinearLayoutManager = recyclerView.layoutManager as LinearLayoutManager
//                val nextCursor = crossPathViewModel.crossPathUiState.value.paginationData.nextCursor
//                val isLoading = crossPathViewModel.crossPathUiState.value.isLoadingDiscoveredProfiles
//                if (!isLoading && nextCursor != null) {
//                    if (mLayoutManager.findLastCompletelyVisibleItemPosition() == ((binding.discoveredProfiles.adapter?.itemCount ?: 0) - 1)) {
//                        if (NetworkChecker.isNetworkConnected(requireContext()))
//                            crossPathViewModel.loadMoreDiscoveredProfiles(nextCursor)
//                    }
//                }
//            }
//        })
    }

    fun sendProfileVisitOtherPhotosEvent(
        arePhotosLocked: Boolean,
        crossPath: CrossPath?
    ) {
        val receivedUserPremiumBadge = if(homeViewModel.userProfile.value?.profile?.showPremiumBadge == true)
            ClevertapReceivedUserPremiumBadgeValues.SHOWN.value
        else
            ClevertapReceivedUserPremiumBadgeValues.HIDDEN.value

        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userRepository.user.value)
        val interactionReceived = if(crossPath?.counterPartInteraction != "no_interaction") crossPath?.counterPartInteraction else null
        val interactedBefore = if(crossPath?.currentUserInteraction != "no_interaction") crossPath?.currentUserInteraction else null
        val spottedTimes = crossPath?.count
        val spottedLocation = crossPath?.address
        val recommSource = "dua"
        val eventSource = ClevertapEventSourceValues.CROSS_PATH.value
        val isPhotoHidden = homeViewModel.userProfile.value?.profile?.hasBlurredPhotos
        val premiumBadge = if(homeViewModel.userProfile.value?.profile?.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
        firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_VISIT_OTHER_PHOTOS, mapOf(
            FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
            FirebaseAnalyticsParameterName.ARE_PHOTOS_LOCKED.value to arePhotosLocked,
        ))

        sendClevertapEvent(
            ClevertapEventEnum.PROFILE_VISIT_OTHER_PHOTOS, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                ClevertapEventPropertyEnum.ARE_PHOTOS_LOCKED.propertyName to arePhotosLocked,
                ClevertapEventPropertyEnum.INTERACTION_RECEIVED.propertyName to interactionReceived,
                ClevertapEventPropertyEnum.INTERACTED_BEFORE.propertyName to interactedBefore,
                ClevertapEventPropertyEnum.TIMES_SPOTTED.propertyName to spottedTimes,
                ClevertapEventPropertyEnum.SPOTTED_LOCATION.propertyName to spottedLocation,
                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge
            ))


    }

    private fun sendProfileVisitAnalyticsEvents(
        premiumType: String?,
        eventSource: String?,
        areDetailsLocked: Boolean,
        model: DiscoveredProfilesUiModel.Item,
        isPhotoHidden: Boolean?,
        premiumBadge: String?
    ) {
        val interactionReceived = if(model.counterPartInteraction != "no_interaction") model.counterPartInteraction else null
        val interactedBefore = if(model.currentUserInteraction != "no_interaction") model.currentUserInteraction else null
        val spottedTimes = model.count
        val spottedLocation = model.address
        val recommSource = "dua"
        sendClevertapEvent(
            ClevertapEventEnum.PROFILE_VISIT, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                ClevertapEventPropertyEnum.ARE_DETAILS_LOCKED.propertyName to areDetailsLocked,
                ClevertapEventPropertyEnum.INTERACTION_RECEIVED.propertyName to interactionReceived,
                ClevertapEventPropertyEnum.INTERACTED_BEFORE.propertyName to interactedBefore,
                ClevertapEventPropertyEnum.TIMES_SPOTTED.propertyName to spottedTimes,
                ClevertapEventPropertyEnum.SPOTTED_LOCATION.propertyName to spottedLocation,
                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

            )
        )

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.PROFILE_VISIT_CONVERSATION, mapOf(
                FirebaseAnalyticsParameterName.PROFILE_VISIT_CONVERSATION_COUNT.value to 1L
            )
        )
    }
    override fun onPause() {
        super.onPause()
        hideLocationServicesLayout()
    }

    private fun hideLocationServicesLayout() {
        (requireActivity() as? HomeActivity)?.getLocationServicesLayout()?.root?.visibility = View.GONE
    }
    private fun interactUser(
        interactionType: InteractionType,
        recommendedUserModel: RecommendedUserModel
    ) {
        when (interactionType) {
            InteractionType.LIKE -> {
                if (homeViewModel.remainingLikes()) {
                    val source = SwipeSourceValues.CROSS_PATH
                    val eventActivityType = recommendedUserModel.activityType

                    homeViewModel.swipeCard(recommendedUserModel, InteractionType.LIKE)
                    firebaseLogEvent(
                        FirebaseAnalyticsEventsName.SWIPE_RIGHT,
                        mapOf(
                            FirebaseAnalyticsParameterName.SWIPE_RIGHT_COUNT.value to 1L,
                            FirebaseAnalyticsParameterName.SOURCE.value to source.value,
                            FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                        )
                    )

                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                    val user = homeViewModel.userProfile.value
                    val locationAccess = getLocationServicesStatus(
                        requireContext().isLocationPermissionEnabled(),
                        requireContext().checkBackgroundLocationPermission()
                    )
                    val areNotificationsOn = getNotificationsOnClevertapValue(requireContext(), duaSharedPrefs)
                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
                    sendClevertapEvent(
                        ClevertapEventEnum.SWIPE_RIGHT, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.DISTANCE.propertyName to recommendedUserModel.profile.distance?.toInt(),
                            ClevertapEventPropertyEnum.SOURCE.propertyName to source.value,
                            ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                            ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to user?.profilePercentage,
                            ClevertapEventPropertyEnum.IS_BADGE2_VERIFIED.propertyName to (user?.badge2 == Badge2Status.APPROVED.status),
                            ClevertapEventPropertyEnum.LOCATION_ACCESS.propertyName to locationAccess,
                            ClevertapEventPropertyEnum.ARE_NOTIFICATIONS_ON.propertyName to areNotificationsOn,
                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

                        )
                    )


                } else {
                    // rewindCard(direction)
                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.LIKE, LimitReachedScreenSource.CROSS_PATH))
                }

            }

            InteractionType.DISLIKE -> {
                if(!homeViewModel.remainingDislikes()){
                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.DISLIKE, LimitReachedScreenSource.CROSS_PATH))
                    return
                }
                val eventActivityType = recommendedUserModel.activityType
                val source = SwipeSourceValues.CROSS_PATH

                homeViewModel.swipeCard(recommendedUserModel, InteractionType.DISLIKE)
                firebaseLogEvent(
                    FirebaseAnalyticsEventsName.SWIPE_LEFT,
                    mapOf(
                        FirebaseAnalyticsParameterName.SWIPE_LEFT_COUNT.value to 1L,
                        FirebaseAnalyticsParameterName.SOURCE.value to source.value,
                        FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                    )

                )

                val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                val gender =
                    if (homeViewModel.userProfile.value?.gender == GenderType.WOMAN.value) {
                        "female"
                    } else {
                        "male"
                    }
                val recommSource = recommendedUserModel.recommSource ?: "dua"
                val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
                sendClevertapEvent(
                    ClevertapEventEnum.SWIPE_LEFT, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.DISTANCE.propertyName to recommendedUserModel.profile.distance?.toInt(),
                        ClevertapEventPropertyEnum.GENDER.propertyName to gender,
                        ClevertapEventPropertyEnum.SOURCE.propertyName to source.value,
                        ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                        ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                        ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                        ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                        ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

                    )
                )


            }

            InteractionType.INSTA_CHAT -> {

                recommendedUserModel.let {
                    if (recommendedUserModel.cardUserGuideType == null) {
                        if (homeViewModel.badge2 == Badge2Status.APPROVED) {
                            if (!it.isMobAd && !it.isProfileInfo && !it.isProfileActivities) {
                                if (homeViewModel.remainingInstaChatInteractions()) {
                                    val dialog = getInstaChatDialogFragment(
                                        recommendedUserModel,
                                    )
                                    dialog.show(childFragmentManager, "InstaChatDialogFragment")

                                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                                    val eventSourceValue = SwipeSourceValues.CROSS_PATH
                                    val eventActivityType = recommendedUserModel.activityType
                                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                                    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                                    firebaseLogEvent(
                                        FirebaseAnalyticsEventsName.INITIATE_INSTACHAT, mapOf(
                                            FirebaseAnalyticsParameterName.INITIATE_INSTACHAT_COUNT.value to 1L,
                                            FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                            FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                        )
                                    )
                                    sendClevertapEvent(
                                        ClevertapEventEnum.INSTACHAT_INITIATED,
                                        mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                            ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                            ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                                            ClevertapEventPropertyEnum.VERIFICATION_STATUS.propertyName to homeViewModel.badge2.status
                                        )
                                    )

                                } else {
                                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.INSTA_CHAT, LimitReachedScreenSource.CROSS_PATH))
                                }
                            } else {

                                if (homeViewModel.badge2 == Badge2Status.APPROVED) {
                                    if (homeViewModel.remainingInstaChatInteractions()) {
                                        val dialog = getInstaChatDialogFragment(
                                            recommendedUserModel,
                                        )
                                        dialog.show(childFragmentManager, "InstaChatDialogFragment")


                                    } else {
                                        homeViewModel.setLimitReached(
                                            LimitReachedModel(
                                                InteractionType.INSTA_CHAT, LimitReachedScreenSource.CROSS_PATH)
                                        )
                                    }
                                } else {
                                    if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                                        VerificationInProgressDialog.showVerificationInProgressDialog(
                                            childFragmentManager
                                        )
                                    } else {
                                        val eventPremiumType =
                                            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                        sendVerifyYourProfilePopupAnalyticsEvent(
                                            eventPremiumType,
                                            ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value
                                        )

                                        VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                            childFragmentManager,
                                            VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT
                                        )
                                    }
                                }

                            }
                        } else {
                            if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                                VerificationInProgressDialog.showVerificationInProgressDialog(
                                    childFragmentManager
                                )
                            } else {
                                val eventPremiumType =
                                    getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                sendVerifyYourProfilePopupAnalyticsEvent(
                                    eventPremiumType,
                                    ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value
                                )

                                VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                    childFragmentManager,
                                    VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT
                                )
                            }

                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            val eventSourceValue = SwipeSourceValues.CROSS_PATH
                            val eventActivityType = recommendedUserModel.activityType
                            val recommSource = recommendedUserModel.recommSource ?: "dua"
                            val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                            val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.INITIATE_INSTACHAT, mapOf(
                                    FirebaseAnalyticsParameterName.INITIATE_INSTACHAT_COUNT.value to 1L,
                                    FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                    FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                )
                            )
                            sendClevertapEvent(
                                ClevertapEventEnum.INSTACHAT_INITIATED,
                                mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                    ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                    ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                    ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                    ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                    ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                                    ClevertapEventPropertyEnum.VERIFICATION_STATUS.propertyName to homeViewModel.badge2.status
                                )
                            )
                        }
                    } else {
                        val dialog = getInstaChatDialogFragment(
                            recommendedUserModel,
                        )
                        dialog.isCancelable = false
                        dialog.show(childFragmentManager, "InstaChatDialogFragment")
                    }
                }
            }

            else -> {}
        }
    }

    private fun getInstaChatDialogFragment(recommendedUserModel: RecommendedUserModel): InstaChatDialogFragment {
        val dialog = InstaChatDialogFragment.newInstance()
        val arguments = Bundle()
        arguments.putString(InstaChatDialogFragment.USER_NAME, recommendedUserModel.firstName)
        if (recommendedUserModel.cardUserGuideType != null) {
            arguments.putString(
                InstaChatDialogFragment.USER_GUIDE_TEXT,
                getString(R.string.instachat_user_guide_text,"72")
            )
        }
        dialog.arguments = arguments
        dialog.listener = object : InstaChatDialogFragment.InstaChatDialogListener {
            override fun onSendButtonClicked(dialog: InstaChatDialogFragment, message: String?) {
                dialog.dismissAllowingStateLoss()
                if (recommendedUserModel.cardUserGuideType == null) {
                    homeViewModel.swipeCard(
                        recommendedUserModel,
                        InteractionType.INSTA_CHAT,
                        message,
                        swipeSource = SwipeSourceValues.CROSS_PATH
                    )
                }
            }
        }
        return dialog
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }


    companion object {
        const val MINI_MAP_INITIAL_ZOOM = 10.0
    }

    override fun onUserInteracted(
        direction: Direction,
        user: RecommendedUserModel,
        isFeaturedProfile: Boolean
    ) {
        val interactionType = when(direction){
            Direction.Right -> InteractionType.LIKE
            Direction.Left -> InteractionType.DISLIKE
            Direction.Top -> InteractionType.INSTA_CHAT
            else -> null
        }

            interactionType?.let {
                if(it == InteractionType.DISLIKE){
                    user.cognitoUserId?.let { crossPathViewModel.removeCrossPath(it) }
                }
                interactUser(it, user)
            }

    }

    override fun reportUser(reportBody: ReportBody, user: RecommendedUserModel) {
        reportViewModel.reportUser(reportBody, user)
    }
    override fun onVerifyYouProfileClicked(
        openedFrom: VerifyYourProfileDialog.VerifyProfileFromEnum,
        eventName: String?
    ) {
        val eventPremiumType =
            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

        sendVerifyYourProfileInitiatedAnalyticsEvent(
            ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value,
            eventPremiumType
        )

        findNavController().navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,
            bundleOf(EVENT_SOURCE to ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value)
        )

    }
}