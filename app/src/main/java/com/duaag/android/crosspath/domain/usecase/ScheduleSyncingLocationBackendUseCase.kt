package com.duaag.android.crosspath.domain.usecase

import android.content.Context
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.duaag.android.crosspath.data.workmanager.CrossPathBackendPeriodicSyncWorker
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class ScheduleSyncingLocationBackendUseCase @Inject constructor(
    private val userRepository: UserRepository
) {

   suspend operator fun invoke(context: Context) {
       withContext(Dispatchers.IO) {
           val userModel = userRepository.getLoggedInUserModel()
           val isUserDisabled = userModel?.isDisabled ?: false
           if (isUserDisabled.not()) {
               withContext(Dispatchers.Main) {
                   val constraints = Constraints.Builder()
                       .setRequiredNetworkType(NetworkType.CONNECTED)
                       .build()

                   val workManager = WorkManager.getInstance(context)
                   val workRequest = PeriodicWorkRequestBuilder<CrossPathBackendPeriodicSyncWorker>(
                       CrossPathBackendPeriodicSyncWorker.repeatInterval,
                       TimeUnit.MINUTES
                   ).setConstraints(constraints)
                       .build()

                   workManager.enqueueUniquePeriodicWork(
                       CrossPathBackendPeriodicSyncWorker.workName,
                       ExistingPeriodicWorkPolicy.KEEP,
                       workRequest
                   )
               }
           }
       }
    }
}