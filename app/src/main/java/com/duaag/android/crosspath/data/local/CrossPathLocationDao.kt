package com.duaag.android.crosspath.data.local

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow

@Dao
interface CrossPathLocationDao {
    @Query("SELECT * FROM location_cross_path")
    fun getAllLocations(): Flow<List<CrossPathLocationEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLocation(location: CrossPathLocationEntity): Long

    @Update
    suspend fun updateLocation(location: CrossPathLocationEntity)

    @Query("SELECT * FROM location_cross_path WHERE latitude BETWEEN :lat - :radius AND :lat + :radius AND longitude BETWEEN :lon - :radius AND :lon + :radius")
    fun getLocationsInRadius(lat: Double, lon: Double, radius: Double): Flow<List<CrossPathLocationEntity>>

    @Query("SELECT * FROM location_cross_path WHERE timestamp BETWEEN :startTime AND :endTime")
    fun getLocationsByTime(startTime: Long, endTime: Long): Flow<List<CrossPathLocationEntity>>

    @Query("SELECT * FROM location_cross_path WHERE visitCount >= :minVisits")
    fun getFrequentLocations(minVisits: Int): Flow<List<CrossPathLocationEntity>>

    @Query("SELECT * FROM location_cross_path WHERE timestamp < :time")
    fun getLocationOlderThan(time:Long): Flow<List<CrossPathLocationEntity>>

   @Query("SELECT * FROM location_cross_path WHERE timestamp < :time")
    fun getLocationOlderThanX(time:Long): List<CrossPathLocationEntity>

    @Query("DELETE FROM location_cross_path WHERE id = :id")
    suspend fun deleteLocationById(id: Int)

    @Query("DELETE FROM location_cross_path WHERE id IN (:ids)")
    suspend fun deleteLocationsByIds(ids: List<Int>)

    @Query("DELETE FROM location_cross_path WHERE timestamp < :timestamp")
    suspend fun deleteLocationsOlderThan(timestamp: Long)
}
