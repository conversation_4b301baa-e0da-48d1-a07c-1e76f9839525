package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.model.CrossPathLocation
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.pow
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * Use case for tracking visits and updating the duration of stays at specific locations.
 * This is key for monitoring frequent locations, or studying user behavior.
 * For example, it can be used to adjust notifications based on user presence at frequent locations.
 *
 */
class TrackVisitAndUpdateDurationUseCase @Inject constructor(
    private val saveLocationUseCase: SaveLocationUseCase,
    private val getLocationsInRadiusUseCase: GetLocationsInRadiusUseCase,
    private val updateLocationUseCase: UpdateLocationUseCase) {

    /**
     * Executes the use case to update or insert a new location based on its proximity to known locations.
     * If the new location is close enough to an existing location (within a specified radius), it updates
     * the existing location's visit count and duration; otherwise, it adds a new location record.
     *
     * @param newLocation The new Location object to be evaluated and stored.
     * @param radius The radius in kilometers to determine proximity to existing locations.
     * @return None.
     */
    suspend operator fun invoke(newLocation: CrossPathLocation, radius: Double) {
        // Fetch nearby locations within the given radius
    //    val nearbyLocations = crossPathLocationRepository.getCrossPathLocationsInRadius(newLocation.latitude, newLocation.longitude, radius).first()
        val nearbyLocations = getLocationsInRadiusUseCase(newLocation.latitude, newLocation.longitude, radius).first()

        // Check if there is any nearby location that matches the new location
        val existingLocation = nearbyLocations.find {
            haversineDistance(it.latitude, it.longitude, newLocation.latitude, newLocation.longitude) <= radius
        }

        if (existingLocation != null) {
            // Update the existing location with new visit count and duration
            val updatedLocation = existingLocation.copy(
                visitCount = existingLocation.visitCount + 1,
                duration = existingLocation.duration + (System.currentTimeMillis() - newLocation.timestamp)
            )
           // crossPathLocationRepository.updateCrossPathLocation(updatedLocation)
            updateLocationUseCase(updatedLocation)
        } else {
            // Insert new location as a new record
           // crossPathLocationRepository.saveCrossPathLocation(newLocation)
            saveLocationUseCase(newLocation)
        }
    }

    private fun haversineDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val earthRadius = 6371.0 // Earth's radius in kilometers

        val dLat = Math.toRadians(lat2 - lat1)
        val dLon = Math.toRadians(lon2 - lon1)
        val originLat = Math.toRadians(lat1)
        val destinationLat = Math.toRadians(lat2)

        val a = sin(dLat / 2).pow(2) + sin(dLon / 2).pow(2) * cos(originLat) * cos(destinationLat)
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))

        return earthRadius * c
    }
}
