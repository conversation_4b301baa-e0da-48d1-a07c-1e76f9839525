package com.duaag.android.crosspath.presentation.fragment

import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.duaag.android.R
import com.duaag.android.chat.blockspamlinks.utils.toMillis
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.crosspath.domain.model.LocationModel
import com.duaag.android.crosspath.presentation.model.ClusterUiModel
import com.duaag.android.crosspath.presentation.model.CrossPathFullScreenData
import com.duaag.android.crosspath.presentation.model.CrossPathProfilesBottomSheetFragmentArgs
import com.duaag.android.crosspath.presentation.viewmodel.CrossPathFullScreenViewModel
import com.duaag.android.crosspath.presentation.viewmodel.CrossPathViewModel
import com.duaag.android.crosspath.presentation.views.OverlappingCircularImageView
import com.duaag.android.databinding.CrossMapOverlayLayoutBinding
import com.duaag.android.databinding.CrossPathFullScreenFragmentBinding
import com.duaag.android.databinding.MapOverlayFullscreenBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.models.CrossPath
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.calculateDistance
import com.duaag.android.utils.getS3Url
import com.duaag.android.utils.isDarkModeEnabled
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.utils.updateLocale
import com.mapbox.android.gestures.AndroidGesturesManager
import com.mapbox.android.gestures.MoveGestureDetector
import com.mapbox.android.gestures.StandardScaleGestureDetector
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraBoundsOptions
import com.mapbox.maps.CameraOptions
import com.mapbox.maps.MapView
import com.mapbox.maps.MapboxMap
import com.mapbox.maps.Projection
import com.mapbox.maps.ScreenCoordinate
import com.mapbox.maps.Style
import com.mapbox.maps.plugin.gestures.GesturesPlugin
import com.mapbox.maps.plugin.gestures.OnMoveListener
import com.mapbox.maps.plugin.gestures.OnScaleListener
import com.mapbox.maps.plugin.gestures.gestures
import com.mapbox.maps.plugin.scalebar.scalebar
import com.mapbox.maps.viewannotation.geometry
import com.mapbox.maps.viewannotation.viewAnnotationOptions
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Date
import javax.inject.Inject


class CrossPathFullScreenFragment : Fragment() {

    private var _binding: CrossPathFullScreenFragmentBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    @Inject
    lateinit var duaAccount: DuaAccount

    private val crossPathViewModel by viewModels<CrossPathFullScreenViewModel> { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val mainCrossPathViewModel by viewModels<CrossPathViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private lateinit var mapboxMap: MapboxMap
    private lateinit var gesturesPlugin: GesturesPlugin
    private lateinit var gesturesManager: AndroidGesturesManager
    private var previousClusters: List<ClusterUiModel> = mutableListOf()

    private val interactionFlow = MutableSharedFlow<Boolean>()
    private var isUserScaling = false
    private var moveDistance = 0.0

    val SUNNY_HILL_POINT = Point.fromLngLat(SUNNY_HILL_LON, SUNNY_HILL_LAT)


    private val crossPathFullScreenData: CrossPathFullScreenData?
        get() = getCrossPathData(requireArguments())

    private var userLocation: LocationModel? = null
    private var startFromSunnyHillPin: Boolean? = false

    private val moveListener: OnMoveListener = object : OnMoveListener {
        override fun onMoveBegin(detector: MoveGestureDetector) {
        }

        override fun onMove(detector: MoveGestureDetector): Boolean {
            val distance = calculateDistance(detector.previousEvent, detector.currentEvent)
            Timber.tag("MAP_LISTENER").d(" distance $distance")
            moveDistance += distance
            return false
        }

        override fun onMoveEnd(detector: MoveGestureDetector) {
            Timber.tag("MAP_LISTENER").d("moveDistance: $moveDistance")
            Timber.tag("MAP_LISTENER").d("${System.currentTimeMillis()} | onMoveEnd: detector:$detector")

            if(moveDistance >= MOVE_THRESHOLD) {
                viewLifecycleOwner.lifecycleScope.launch {
                    interactionFlow.emit(false)
                }
            }
            moveDistance = 0.0
        }
    }
    private val scaleListener: OnScaleListener = object : OnScaleListener {
        override fun onScaleBegin(detector: StandardScaleGestureDetector) {
            isUserScaling = true
        }

        override fun onScale(detector: StandardScaleGestureDetector) {
        }

        override fun onScaleEnd(detector: StandardScaleGestureDetector) {
            Timber.tag("MAP_LISTENER").d("${System.currentTimeMillis()} | onScaleEnd: detector:$detector")
            viewLifecycleOwner.lifecycleScope.launch {
                interactionFlow.emit(true)
            }
        }
    }
    private fun getCrossPathData(arguments: Bundle?): CrossPathFullScreenData? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(CROSS_PATH_DATA, CrossPathFullScreenData::class.java)
        } else {
            @Suppress("DEPRECATION")
            arguments?.getParcelable(CROSS_PATH_DATA) as? CrossPathFullScreenData
        }
    }
    private fun onInteractionEnded(isScale: Boolean) {
        Timber.tag("FULL_MAP").d("onInteractionEnded: isScale:$isScale")

        getCrossPaths(isScale)

        toggleSunnyHillPin(isAttendingSunnyHill = crossPathViewModel.uiState.value.isAttendingSunnyHill)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    private fun getCrossPaths(isScale: Boolean) {
        val center = mapboxMap.cameraState.center
        val zoom = mapboxMap.cameraState.zoom
        val latitude = center.latitude()
        val longitude = center.longitude()
        val distance = getDistance(zoom, latitude)

        crossPathViewModel.getGroupedCrossPaths(distance, latitude, longitude, isScale)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        userLocation = arguments?.getParcelable(USER_LOCATION)
        startFromSunnyHillPin = arguments?.getBoolean(SUNNY_HILL_START)

        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                findNavController().previousBackStackEntry?.savedStateHandle?.set("resetUserProfile", crossPathFullScreenData != null)
                findNavController().navigateUp()
            }
        }
        requireActivity().onBackPressedDispatcher.addCallback(this, callback)
    }
    override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        _binding = CrossPathFullScreenFragmentBinding.inflate(inflater, container, false)

        sendCrossPathScreenViewEvent(crossPathFullScreenData)

        initializeMapView(crossPathFullScreenData)

        binding.sunnyHillBtn.setOnSingleClickListener {
            focusLocation(SUNNY_HILL_POINT)
            CrossPathProfilesBottomSheetFragment.newInstance(
                CrossPathProfilesBottomSheetFragmentArgs(
                  isSunnyHillMode = true
                )
            ).show(childFragmentManager, CrossPathProfilesBottomSheetFragment.TAG)
            getCrossPaths(false)

            adjustCameraForBottomSheet(binding.mapView, SUNNY_HILL_POINT)
        }

        binding.backBtn.setOnSingleClickListener {
          findNavController().previousBackStackEntry?.savedStateHandle?.set("resetUserProfile", crossPathFullScreenData != null)
            findNavController().navigateUp()
        }

        binding.currentLocationBtn.setOnSingleClickListener {
            val point = Point.fromLngLat(userLocation?.longitude ?: 0.0, userLocation?.latitude ?: 0.0)
            focusLocation(point)

            getCrossPaths(false)
        }

        if(savedInstanceState == null)
            clearViewAnnotations()

        crossPathViewModel.setCrossPathData(crossPathFullScreenData)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if(startFromSunnyHillPin == true) {
            binding.sunnyHillBtn.performClick()
        }
        viewLifecycleOwner.lifecycleScope.launch {
            interactionFlow
                    .debounce(400)
                    .collect { value ->
                        if (value) {
                            isUserScaling = true
                        }
                        Timber.tag("MAP_LISTENER").d("isUserScaling $isUserScaling")
                        onInteractionEnded(isUserScaling)
                        isUserScaling = false
                    }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    crossPathViewModel.uiState.collect { uiState ->

                        setVisibility(binding.currentLocationBtn,(uiState.crossPathStaticData == null && startFromSunnyHillPin == false))
                        if(uiState.crossPathStaticData != null){
                            initializeMapOverlay(binding.mapOverlay, homeViewModel.userProfile.value?.profile?.pictureUrl, uiState.crossPathStaticData)
                            binding.mapOverlay.root.bringToFront()
                            return@collect
                        } else binding.mapOverlay.root.visibility = View.GONE


                        if (uiState.isLoadingGroupedPins)
                            return@collect

                        if (uiState.isScale) {
                            clearViewAnnotations()
                        }

                        updateViewAnnotations(uiState.groupedPins?.clusters?.sortedBy { it.users.size })

                        addSunnyHillViewAnnotationToPoint(SUNNY_HILL_POINT, isAttendingSunnyHill = uiState.isAttendingSunnyHill)
                        toggleSunnyHillPin(isAttendingSunnyHill = uiState.isAttendingSunnyHill)

                        uiState.attendeeCount?.let {
                            mainCrossPathViewModel.setAttendeeCount(it)
                        }
                    }
                }

                launch {
                    mainCrossPathViewModel.crossPathInteract.collect { interaction ->
                        if(interaction.interaction == "match")
                            crossPathViewModel.removeCrossPath(interaction.cognitoId)
                    }
                }

            }
        }

        mainCrossPathViewModel.checkedInSuccess.observe(viewLifecycleOwner) {
            crossPathViewModel.increaseAttendeeCount()
        }
    }
    private fun initializeMapOverlay(
        crossMapSection: MapOverlayFullscreenBinding,
        userPictureUrl: String?,
        crossPathStaticData:  CrossPathFullScreenData?
    ) {

        val crossPathData = crossPathStaticData?.crossPathData


        updateConstraintsAndText(
            context =  crossMapSection.root.context,
            section = crossMapSection.timePlaceSection,
            crossPathData = crossPathData
        )

        loadImageIntoView(crossMapSection.overlappingSpottedUsers, userPictureUrl, true)
        loadImageIntoView(crossMapSection.overlappingSpottedUsers, crossPathStaticData?.counterpartUserPictureUrl, false)
    }

    private fun focusLocation(point: Point) {
        val zoom = mapboxMap.cameraState.zoom
        val cameraPosition = CameraOptions.Builder()
            .zoom(zoom)
            .center(point)
            .build()
        mapboxMap.setCamera(cameraPosition)
    }

    private fun adjustCameraForBottomSheet(mapView: MapView, coordinate: Point) {
        val screenHeight = Resources.getSystem().displayMetrics.heightPixels.toDouble()
        val offsetProportion = screenHeight * 3/4

        // Calculate the latitude offset
        val midX = mapView.width / 2.0
        val midY = mapView.height / 2.0
        val offsetY = midY + offsetProportion / 2

        // Convert screen coordinates to geographical coordinates
        val currentLatLng = mapboxMap.coordinateForPixel(ScreenCoordinate(midX, midY))
        val offsetLatLng = mapboxMap.coordinateForPixel(ScreenCoordinate(midX, offsetY))

        val latitudeOffset = offsetLatLng.latitude() - currentLatLng.latitude()

        // Create new center coordinate by adjusting the latitude
        val newCameraCenter = Point.fromLngLat(coordinate.longitude(), coordinate.latitude() + latitudeOffset)

        val cameraOptions = CameraOptions.Builder()
            .center(newCameraCenter)
            .zoom(mapboxMap.cameraState.zoom)
            .bearing(0.0)
            .pitch(0.0)
            .build()
        mapboxMap.setCamera(cameraOptions)
    }


    private fun toggleSunnyHillPin(isAttendingSunnyHill: Boolean?) {
        if(!RemoteConfigUtils.getShowSunnyHill() || crossPathFullScreenData != null || isAttendingSunnyHill != true)
            return

        val containsPoint = containsPoint(SUNNY_HILL_POINT)
        binding.sunnyHillContainer.isVisible = !containsPoint
    }

    private fun containsPoint(point: Point): Boolean {
        val options = CameraOptions.Builder()
            .zoom(mapboxMap.cameraState.zoom)
            .center(mapboxMap.cameraState.center)
            .build()
        val currentBox = mapboxMap.coordinateBoundsForCamera(options)
        return currentBox.contains(point, true)
    }

    private fun updateConstraintsAndText(
        context: Context,
        section: CrossMapOverlayLayoutBinding,
        crossPathData: CrossPath?
    ) {
        val monthTextView = section.month
        val placeTextView = section.place

        monthTextView.text = getCrossMapMonthText(context, crossPathData?.timestamp)
        placeTextView.text = getCrossMapPlaceText(context, crossPathData?.address)
    }
    private fun getCrossMapMonthText(context: Context, timestamp: String?): String {
        val dayInMillis = 1000 * 60 * 60 * 24
        val currentDate = Date()

        return timestamp?.toMillis()?.let { Date(it) }?.let {
            val timeDifference = currentDate.time - it.time
            val days = (timeDifference / dayInMillis).toInt()
            when {
                days < 1 -> context.getString(R.string.today_section)
                days == 1 -> context.getString(R.string.yesterday_section)
                days in 2..7 -> context.getString(R.string.this_week_section)
                else -> context.getString(R.string.this_month_section)
            }
        } ?: context.getString(R.string.this_month_section)
    }

    private fun getCrossMapPlaceText(context: Context, address: String?): String {
        return buildString {
            append(context.getString(R.string.near_caption))
            append(" ")
            append(address ?: "")
        }
    }

    private fun loadImageIntoView(imageView: OverlappingCircularImageView, imageUrl: String?, firstImage: Boolean) {
        val image = imageUrl?.let { getS3Url(it) }

        Glide.with(imageView.context)
            .asBitmap()
            .load(image)
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    if (firstImage) imageView.setFirstImage(resource) else imageView.setSecondImage(resource)
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle if needed
                }
            })
    }
    private fun configureMapGestures(mapView: MapView) {
        mapView.scalebar.enabled = false
        mapView.gestures.apply {
            pitchEnabled = false
            doubleTapToZoomInEnabled = false
            doubleTouchToZoomOutEnabled = false
            rotateEnabled = false
            pinchToZoomEnabled = false
            pinchScrollEnabled = false
            scrollEnabled = false
            simultaneousRotateAndPinchToZoomEnabled = false
        }
    }
    private fun initializeMapView(crossPathFullScreenData: CrossPathFullScreenData?) {
        mapboxMap = binding.mapView.mapboxMap
        gesturesPlugin = binding.mapView.gestures
        gesturesManager = gesturesPlugin.getGesturesManager()
        val latitude = crossPathFullScreenData?.crossPathData?.location?.lat ?: userLocation?.latitude ?: 0.0
        val longitude = crossPathFullScreenData?.crossPathData?.location?.long ?: userLocation?.longitude ?: 0.0
        if(crossPathFullScreenData != null) {
            configureMapGestures(binding.mapView)
        }
        if(crossPathFullScreenData == null) {

            attachListeners()

            binding.mapView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    binding.mapView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    val longitude = userLocation?.longitude ?: 0.0
                    val latitude = userLocation?.latitude ?: 0.0
                    Timber.tag("FULL_MAP").d(" latitude:${latitude} | longitude:${longitude}")

                    val zoom = mapboxMap.cameraState.zoom
                    val distance = getDistance(zoom, latitude)

                    crossPathViewModel.getGroupedCrossPaths(distance, latitude, longitude, false)

                    Timber.tag("FULL_MAP").d("minimap distance: $distance")
                }
            })

            crossPathViewModel.getAttendeeCount()
        }


        mapboxMap.loadStyle(if (isDarkModeEnabled(requireContext())) Style.DARK else Style.LIGHT)
        binding.mapView.scalebar.enabled = false
        binding.mapView.gestures.rotateEnabled = false
        binding.mapView.gestures.pitchEnabled = false
        val zoom = if(crossPathFullScreenData!= null) MAP_ZOOM_MAX_VALUE else MAP_ZOOM_USER_CROSS_PATH_MIN_VALUE
        setMapCamera(longitude, latitude, zoom)
    }

    private fun getDistance(zoom: Double, latitude: Double): Double {
        val metersPerPoint = Projection.getMetersPerPixelAtLatitude(latitude, zoom)
        val mapViewWidthPixels = binding.mapView.width// * resources.displayMetrics.density
        return (metersPerPoint * mapViewWidthPixels) / 2.5
    }

    private fun setMapCamera(longitude: Double, latitude: Double, zoom: Double) {
        val cameraBoundsOptions = CameraBoundsOptions.Builder()
            .minZoom(MAP_ZOOM_MIN_VALUE)
            .maxZoom(MAP_ZOOM_MAX_VALUE)
            .build()

        mapboxMap.setBounds(cameraBoundsOptions)
        mapboxMap.setCamera(
                CameraOptions.Builder()
                        .center(Point.fromLngLat(longitude, latitude))
                        .pitch(0.0)
                        .zoom(zoom)
                        .bearing(0.0)
                        .build()
        )
    }

    private fun attachListeners() {
        gesturesPlugin.addOnMoveListener(moveListener)
        gesturesPlugin.addOnScaleListener(scaleListener)
    }

    private fun clearViewAnnotations() {
        val viewAnnotationManager = binding.mapView.viewAnnotationManager
        val annotationsToRemove = viewAnnotationManager.annotations.keys.filter { it.tag != SUNNY_HILL_MARKER }
        annotationsToRemove.forEach { viewAnnotation ->
            viewAnnotationManager.removeViewAnnotation(viewAnnotation)
        }
    }

    private fun addAllViewAnnotations(clusters: List<ClusterUiModel>) {
        clusters.forEach { cluster ->
            if (cluster.location.longitude != null
                    && cluster.location.latitude != null)
                addViewAnnotationToPoint(
                        point = Point.fromLngLat(cluster.location.longitude, cluster.location.latitude),
                        count = cluster.users.size.toString(),
                        clusterUiModel = cluster
                )
        }
    }

    private fun sendCrossPathScreenViewEvent(crossPathFullScreenData: CrossPathFullScreenData?) {
        val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
        val isIncognitoKey = ClevertapEventPropertyEnum.IS_INCOGNITO_MODE.propertyName
        val communityKey = ClevertapEventPropertyEnum.COMMUNITY.propertyName

        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userRepository.user.value)
        val community = duaSharedPrefs.getUserCommunityName()
        val isGhost = homeViewModel.userProfile.value?.profile?.isGhost ?: false

        val event = if(crossPathFullScreenData == null) ClevertapEventEnum.CROSS_PATH_MAP_EXPENDED else ClevertapEventEnum.SPOTTED_LOCATION_SCREENVIEW
        sendClevertapEvent(
            event,
            mapOf(
                premiumTypeKey to eventPremiumType,
                communityKey to community,
                isIncognitoKey to isGhost
            )
        )
    }

    private fun updateViewAnnotations(newClusters: List<ClusterUiModel>?) {
        val viewAnnotationManager = binding.mapView.viewAnnotationManager
        val currentTags = newClusters?.map { it.getTag() } ?: emptyList()

        // Remove annotations that are not in the new clusters
        val annotationsToRemove = viewAnnotationManager.annotations.keys.filter { it.tag !in currentTags && it.tag != SUNNY_HILL_MARKER }
        annotationsToRemove.forEach { viewAnnotation ->
            viewAnnotationManager.removeViewAnnotation(viewAnnotation)
        }

        // Update existing annotations or add new ones
        newClusters?.forEach { cluster ->
            Timber.tag("FULL_MAP").d("cluster: $cluster")

            val tag = cluster.getTag()
            val existingAnnotationView = viewAnnotationManager.annotations.keys.find { it.tag == tag }
            if (existingAnnotationView == null) {
                // Add new annotation
                addViewAnnotationToPoint(
                        point = Point.fromLngLat(cluster.location.longitude!!, cluster.location.latitude!!),
                        count = cluster.users.size.toString(),
                        clusterUiModel = cluster
                )
            } else {
                // Update existing annotation
                val countView = existingAnnotationView.findViewById<TextView>(R.id.count)
                val overlayView = existingAnnotationView.findViewById<View>(R.id.overlay)
                if (countView.text.toString() != cluster.users.size.toString()) {
                    countView.text = cluster.users.size.toString()
                }
                overlayView.isVisible = !cluster.hasPendingInteraction
            }
        }

        // Update previous clusters
        previousClusters = newClusters?.toMutableList() ?: mutableListOf()

        Timber.tag("FULL_MAP").d("all views: ${viewAnnotationManager.annotations.size}")
    }

    private fun addViewAnnotationToPoint(point: Point, count: String, clusterUiModel: ClusterUiModel) {
        val viewAnnotationManager = binding.mapView.viewAnnotationManager

        val viewAnnotation = viewAnnotationManager.addViewAnnotation(
                resId = R.layout.map_marker_full_layout,
                options = viewAnnotationOptions {
                    geometry(point)
                    allowOverlap(true)
                }
        )
        viewAnnotation.tag = clusterUiModel.getTag()  // Add a unique tag to the annotation view
        viewAnnotation.setOnClickListener {
            CrossPathProfilesBottomSheetFragment.newInstance(
                CrossPathProfilesBottomSheetFragmentArgs(
                    clusterUiModel.users,
                    clusterUiModel.location.latitude ?: 0.0,
                    clusterUiModel.location.longitude ?: 0.0,
                    clusterUiModel.hasPendingInteraction
                )
            ).show(childFragmentManager, CrossPathProfilesBottomSheetFragment.TAG)
        }
        viewAnnotation.findViewById<TextView>(R.id.count).text = count
        viewAnnotation.findViewById<TextView>(R.id.overlay).isVisible = !clusterUiModel.hasPendingInteraction
    }

    private fun addSunnyHillViewAnnotationToPoint(point: Point, isAttendingSunnyHill: Boolean?) {
        if(!RemoteConfigUtils.getShowSunnyHill() || crossPathFullScreenData !=null || isAttendingSunnyHill != true)
            return

        val viewAnnotationManager = binding.mapView.viewAnnotationManager

        val existingAnnotationView = viewAnnotationManager.annotations.keys.find { it.tag == SUNNY_HILL_MARKER }

        if(existingAnnotationView == null) {
            val viewAnnotation = viewAnnotationManager.addViewAnnotation(
                resId = R.layout.sunny_hill_view_annotation,
                options = viewAnnotationOptions {
                    geometry(point)
                    allowOverlap(true)
                }
            )
            viewAnnotation.tag = SUNNY_HILL_MARKER  // Add a unique tag to the annotation view
            viewAnnotation.setOnClickListener {
                CrossPathProfilesBottomSheetFragment.newInstance(
                    CrossPathProfilesBottomSheetFragmentArgs(
                        isSunnyHillMode = true
                    )
                ).show(childFragmentManager, CrossPathProfilesBottomSheetFragment.TAG)
                adjustCameraForBottomSheet(binding.mapView, SUNNY_HILL_POINT)
            }
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        gesturesPlugin.removeOnMoveListener(moveListener)
        gesturesPlugin.removeOnScaleListener(scaleListener)

        _binding = null
    }

    companion object {
        const val MAP_ZOOM_MIN_VALUE = 7.0
        const val MAP_ZOOM_USER_CROSS_PATH_MIN_VALUE = 10.0
        const val MAP_ZOOM_MAX_VALUE = 15.0

        const val USER_LOCATION = "user_location"
        const val CROSS_PATH_DATA = "cross_path_data"
        const val SUNNY_HILL_START = "sunny_hill_start"
        const val SUNNY_HILL_MARKER = "sunny_hill"
        const val SUNNY_HILL_LAT = 42.704509
        const val SUNNY_HILL_LON = 21.1246594

        private const val MOVE_THRESHOLD = 20f  // Minimum distance in pixels to consider as a move
    }
}