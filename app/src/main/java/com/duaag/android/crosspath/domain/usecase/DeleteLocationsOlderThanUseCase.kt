package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import javax.inject.Inject

class DeleteLocationsOlderThanUseCase @Inject constructor(private val crossPathLocationRepository: CrossPathLocationRepository) {

    suspend operator fun invoke(timestamp: Long) = crossPathLocationRepository.deleteLocationsOlderThan(timestamp)
}