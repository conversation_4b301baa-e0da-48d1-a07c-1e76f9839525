package com.duaag.android.crosspath.data.repository

import com.duaag.android.api.ResourceV2
import com.duaag.android.api.UserService
import com.duaag.android.crosspath.data.local.CrossPathLocationDao
import com.duaag.android.crosspath.data.local.toDomainModel
import com.duaag.android.crosspath.data.remote.dto.CrossPathErrorResponse
import com.duaag.android.crosspath.data.remote.CrossPathUsersRaw
import com.duaag.android.crosspath.data.remote.dto.UpdateLocationDto
import com.duaag.android.crosspath.data.remote.toCrossPathModel
import com.duaag.android.crosspath.data.remote.toCrossPathUsersModel
import com.duaag.android.crosspath.data.remote.toGroupedCrossPathsModel
import com.duaag.android.crosspath.data.remote.toLocationDto
import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.model.CrossPathUsersModel
import com.duaag.android.crosspath.domain.model.CrossPathsModel
import com.duaag.android.crosspath.domain.model.GroupedCrossPathsModel
import com.duaag.android.crosspath.domain.model.toEntity
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import com.duaag.android.exceptions.NoConnectivityException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Named
import kotlin.coroutines.cancellation.CancellationException


class CrossPathCrossPathLocationRepositoryImpl @Inject constructor(
    private val crossPathLocationDao: CrossPathLocationDao,
    @Named("private") private val userService: UserService
    ) : CrossPathLocationRepository {
    override suspend fun updateCrossPathLocation(location: CrossPathLocation) = crossPathLocationDao.updateLocation(location.toEntity())

    override suspend fun saveCrossPathLocation(crossPathLocation: CrossPathLocation) {
        crossPathLocationDao.insertLocation(crossPathLocation.toEntity())
    }

    override suspend fun deleteLocationById(id: Int) = crossPathLocationDao.deleteLocationById(id)

    override suspend fun deleteLocationsByIds(ids: List<Int>) = crossPathLocationDao.deleteLocationsByIds(ids)

    override suspend fun deleteLocationsOlderThan(timestamp: Long) = crossPathLocationDao.deleteLocationsOlderThan(timestamp)

    override fun getCrossPathLocations(): Flow<List<CrossPathLocation>> = crossPathLocationDao.getAllLocations().map { entities ->
        entities.map { it.toDomainModel() }
    }

    override fun getCrossPathLocationsInRadius(lat: Double, lon: Double, radius: Double): Flow<List<CrossPathLocation>> = crossPathLocationDao.getLocationsInRadius(lat, lon, radius).map { entities ->
        entities.map { it.toDomainModel() }
    }

    override fun getCrossPathLocationsByTime(startTime: Long, endTime: Long): Flow<List<CrossPathLocation>> = crossPathLocationDao.getLocationsByTime(startTime, endTime)
        .map { entities -> entities.map { it.toDomainModel() } }

    override fun getFrequentCrossPathLocations(minVisits: Int): Flow<List<CrossPathLocation>> = crossPathLocationDao.getFrequentLocations(minVisits)
        .map { entities -> entities.map { it.toDomainModel() } }

    override fun getLocationOlderThan(time: Long): Flow<List<CrossPathLocation>> = crossPathLocationDao.getLocationOlderThan(time)
        .map { entities -> entities.map { it.toDomainModel() } }

    override suspend fun getLocationOlderThanX(time: Long): List<CrossPathLocation> = crossPathLocationDao.getLocationOlderThanX(time)
        .map { entities -> entities.toDomainModel() }

    override suspend fun addLocationsInBackend(data: List<CrossPathLocation>): ResourceV2<Unit> {
        return try {
            val body = UpdateLocationDto(data.map { it.toLocationDto() })
            val result = userService.addNewUserLocationPin(body)
            if(result.isSuccessful)
                ResourceV2.Success(Unit)
            else
                ResourceV2.Error(result.message())
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "")
        }
    }

    override suspend fun getCrossPaths(nextCursor: Long?, limit: Int): ResourceV2<CrossPathsModel> {
        return try {
            val result = userService.getCrossPaths(nextCursor = nextCursor, limit = limit)

            if(result.isSuccessful)
                ResourceV2.Success(result.body()!!.toCrossPathModel())
            else
                ResourceV2.Error(result.message(), errorType = CrossPathErrorResponse.AN_ERROR_OCCURRED)

        } catch (ex: NoConnectivityException) {
            ResourceV2.Error(message = ex.message, errorType = CrossPathErrorResponse.NO_NETWORK_CONNECTION)
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "", errorType = CrossPathErrorResponse.AN_ERROR_OCCURRED)
        }
    }

    override suspend fun getGroupedCrossPaths(distance: Double, latitude: Double, longitude: Double): ResourceV2<GroupedCrossPathsModel> {
        return try {
            val result = userService.getGroupedCrossPaths(distance, latitude, longitude)

            if(result.isSuccessful)
                ResourceV2.Success(result.body()!!.toGroupedCrossPathsModel())
            else
                ResourceV2.Error(result.message(), errorType = CrossPathErrorResponse.AN_ERROR_OCCURRED)

        } catch (ex: NoConnectivityException) {
            ResourceV2.Error(message = ex.message, errorType = CrossPathErrorResponse.NO_NETWORK_CONNECTION)
        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "", errorType = CrossPathErrorResponse.AN_ERROR_OCCURRED)
        }
    }

    override suspend fun getCrossPathUsers(userIds: List<String>, latitude: Double, longitude: Double): ResourceV2<CrossPathUsersModel> {
        return try {
            val userIdsString = userIds.joinToString(",")
            val result = userService.getCrossPathUsers(userIdsString, latitude, longitude)

            if(result.isSuccessful)
                ResourceV2.Success(result.body().toCrossPathUsersModel())
            else
                ResourceV2.Error(result.message())

        } catch (ex: Exception) {
            if(ex is CancellationException) {
                throw ex
            }

            ResourceV2.Error(message = ex.message ?: "")
        }
    }

}
