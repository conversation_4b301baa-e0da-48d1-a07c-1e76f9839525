package com.duaag.android.crosspath.domain.usecase

import android.content.Context
import androidx.work.Constraints
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.duaag.android.crosspath.data.workmanager.CrossPathBackendOneTimeSyncWorker
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class RunSyncingLocationBackendUseCase @Inject constructor(
    private val userRepository: UserRepository
) {

   suspend operator fun invoke(context: Context) {
       withContext(Dispatchers.IO) {
           val userModel = userRepository.getLoggedInUserModel()
           val isUserDisabled = userModel?.isDisabled ?: false
           if (isUserDisabled.not()) {
               withContext(Dispatchers.Main) {
                   val workManager = WorkManager.getInstance(context)
                   val constraints = Constraints.Builder()
                       .setRequiredNetworkType(NetworkType.CONNECTED)
                       .build()

                   val immediateWorkRequest =
                       OneTimeWorkRequestBuilder<CrossPathBackendOneTimeSyncWorker>()
                           .setConstraints(constraints)
                           .build()

                   workManager.enqueueUniqueWork(
                       CrossPathBackendOneTimeSyncWorker.workName,
                       ExistingWorkPolicy.REPLACE,
                       immediateWorkRequest
                   )
               }
           }
       }
    }
}