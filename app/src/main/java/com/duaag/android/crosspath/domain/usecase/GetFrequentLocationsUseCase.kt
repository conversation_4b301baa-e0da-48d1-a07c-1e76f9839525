package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for retrieving frequently visited locations based on a minimum number of visits,
 * facilitating the retrieval of locations that have been visited at least a specified number of times.
 * This use case is particularly useful to analyze user behavior patterns,
 * optimize user engagement based on frequent activities
 *
 * @property crossPathLocationRepository The repository responsible for accessing location data.
 */
class GetFrequentLocationsUseCase @Inject constructor(private val crossPathLocationRepository: CrossPathLocationRepository) {

   /**
    * Invokes the use case with a specified minimum visit threshold to retrieve frequent locations.
    *
    * @param minVisits The minimum number of visits a location must have to be considered frequent.
    * @return A Flow that emits a list of CrossPathLocation objects, each representing a location
    *         that meets the frequency criterion. The list updates as the underlying data changes.
    */
   operator fun invoke(minVisits: Int): Flow<List<CrossPathLocation>> = crossPathLocationRepository.getFrequentCrossPathLocations(minVisits)
}
