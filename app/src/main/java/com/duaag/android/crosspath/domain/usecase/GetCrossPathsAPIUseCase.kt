package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.api.ResourceV2
import com.duaag.android.crosspath.domain.model.CrossPathsModel
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import com.duaag.android.user.UserRepository
import javax.inject.Inject

class GetCrossPathsAPIUseCase @Inject constructor(
    private val contactsRepository: CrossPathLocationRepository,
    private val userRepository: UserRepository) {
    suspend operator fun invoke(nextCursor: Long? = null, limit: Int = 10): ResourceV2<CrossPathsModel> {
        val user = userRepository.user.value
        if(user?.isDisabled == true) return ResourceV2.Error("User is disabled")
        return contactsRepository.getCrossPaths(nextCursor, limit)
    }
}