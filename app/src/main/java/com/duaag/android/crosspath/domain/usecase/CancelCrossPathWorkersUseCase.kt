package com.duaag.android.crosspath.domain.usecase

import android.content.Context
import androidx.work.WorkManager
import com.duaag.android.crosspath.data.workmanager.CrossPathBackendOneTimeSyncWorker
import com.duaag.android.crosspath.data.workmanager.CrossPathBackendPeriodicSyncWorker
import com.duaag.android.crosspath.data.workmanager.CrossPathOneTimeWorker
import com.duaag.android.crosspath.data.workmanager.CrossPathPeriodicWorker
import javax.inject.Inject

class CancelCrossPathWorkersUseCase @Inject constructor(
    private val workManager: WorkManager,
    private val removeGeoFencesUseCase: RemoveGeoFencesUseCase
) {
   operator fun invoke(context: Context) {
       try {
           // Cancel all unique work
           workManager.cancelUniqueWork(CrossPathOneTimeWorker.workName)
           workManager.cancelUniqueWork(CrossPathPeriodicWorker.workName)
           workManager.cancelUniqueWork(CrossPathBackendOneTimeSyncWorker.workName)
           workManager.cancelUniqueWork(CrossPathBackendPeriodicSyncWorker.workName)

           // Cancel all work by tag to ensure no lingering workers
           workManager.cancelAllWorkByTag(CrossPathOneTimeWorker.workName)
           workManager.cancelAllWorkByTag(CrossPathPeriodicWorker.workName)
           workManager.cancelAllWorkByTag(CrossPathBackendOneTimeSyncWorker.workName)
           workManager.cancelAllWorkByTag(CrossPathBackendPeriodicSyncWorker.workName)

           // Remove geofences
           removeGeoFencesUseCase(context)
       } catch (ex: Exception) {
           ex.printStackTrace()
       }
    }
}