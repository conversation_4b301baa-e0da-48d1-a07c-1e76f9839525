package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for fetching locations based on a specific time range.
 * Useful to analyze or display user movements or location logs over time.
 *
 * @param crossPathLocationRepository The repository to access location data.
 */
class FetchLocationsByTimeUseCase @Inject constructor(private val crossPathLocationRepository: CrossPathLocationRepository) {

    /**
     * Executes the use case to retrieve location data recorded between two timestamps.
     *
     * @param startTime The start timestamp in milliseconds since epoch.
     * @param endTime The end timestamp in milliseconds since epoch.
     * @return Flow of a list of Location objects recorded between the specified timestamps.
     */
    operator fun invoke(startTime: Long, endTime: Long): Flow<List<CrossPathLocation>> = crossPathLocationRepository.getCrossPathLocationsByTime(startTime, endTime)


}
