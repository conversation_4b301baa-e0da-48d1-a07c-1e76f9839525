package com.duaag.android.crosspath.data.remote

import com.duaag.android.crosspath.data.remote.dto.CrossPathsResponseDto
import com.duaag.android.crosspath.data.remote.dto.AddLocationDto
import com.duaag.android.crosspath.data.remote.dto.ClusterDto
import com.duaag.android.crosspath.data.remote.dto.CrossPathDto
import com.duaag.android.crosspath.data.remote.dto.CurrentLocationDto
import com.duaag.android.crosspath.data.remote.dto.GroupedCrossPathsResponseDto
import com.duaag.android.crosspath.data.remote.dto.LocationDto
import com.duaag.android.crosspath.data.remote.dto.ProfileDto
import com.duaag.android.crosspath.domain.model.ClusterModel
import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.model.CrossPathModel
import com.duaag.android.crosspath.domain.model.CrossPathUsersModel
import com.duaag.android.crosspath.domain.model.CrossPathsModel
import com.duaag.android.crosspath.domain.model.CurrentLocationModel
import com.duaag.android.crosspath.domain.model.GroupedCrossPathsModel
import com.duaag.android.crosspath.domain.model.LocationModel
import com.duaag.android.crosspath.domain.model.ProfileModel

fun CrossPathLocation.toLocationDto(): AddLocationDto {
    return AddLocationDto(
        this.timestamp,
        this.longitude,
        this.latitude
    )
}

fun GroupedCrossPathsResponseDto.toGroupedCrossPathsModel(): GroupedCrossPathsModel {
    return GroupedCrossPathsModel(
        clusterDistance = this.clusterDistance,
        clusters = this.clusters.map { it.toClusterModel() },
        cognitoUserId = this.cognitoUserId,
        count = this.count,
        currentLocation = this.currentLocation.toCurrentLocationModel(),
        locationToken = this.locationToken

    )
}

fun ClusterDto.toClusterModel(): ClusterModel {
    return ClusterModel(
        hasPendingInteraction = this.hasPendingInteraction,
        location = this.location.toLocationModel(),
        users = this.users

    )
}

fun CurrentLocationDto.toCurrentLocationModel(): CurrentLocationModel {
    return CurrentLocationModel(
        latitude = this.latitude,
        longitude = this.longitude
    )
}

fun CrossPathsResponseDto.toCrossPathModel(): CrossPathsModel {
    return CrossPathsModel(
        crossPaths = this.crossPaths?.map { it.toCrossPathModel() },
        nextCursor = this.nextCursor
    )
}

fun CrossPathDto.toCrossPathModel(): CrossPathModel {
    return CrossPathModel(
        address = this.address,
        cognitoUserId = this.cognitoUserId,
        count = this.count,
        counterPartInteraction = this.counterPartInteraction,
        currentUserInteraction = this.currentUserInteraction,
        id = this.id,
        location = this.location?.toLocationModel(),
        profile = this.profile?.toProfileModel(),
        timestamp = this.timestamp,

    )
}

fun LocationDto.toLocationModel(): LocationModel {
    return LocationModel(
        latitude = this.latitude,
        longitude = this.longitude
    )
}

fun ProfileDto.toProfileModel(): ProfileModel {
    return ProfileModel(
        badge2 = this.badge2,
        firstName = this.firstName,
        thumbnailUrl = this.thumbnailUrl,
        pictureUrl = this.pictureUrl,
        age = this.age
    )
}

fun CrossPathUsersRaw?.toCrossPathUsersModel(): CrossPathUsersModel {
    return CrossPathUsersModel(
        users = this?.users?: emptyList(),
        clusterAddress = this?.clusterAddress?: "",
    )
}