package com.duaag.android.crosspath.presentation.model

import com.duaag.android.api.ResponseError
import com.duaag.android.crosspath.domain.model.LocationModel

data class CrossPathUiState(
        val discoveredItems: List<DiscoveredProfilesUiModel> = emptyList(),
        val groupedPins: GroupedCrossPathUiModel? = null,
        val isLoadingDiscoveredProfiles: Boolean = false,
        val isLoadingGroupedPins: Boolean = false,
        val discoveredItemsError: ResponseError? = null,
        val groupedPinsError: ResponseError? = null,
        val paginationData: PagingData = PagingData(),
        val lastKnownLocation: LocationModel? = null
)