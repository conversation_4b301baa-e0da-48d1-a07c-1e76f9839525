package com.duaag.android.crosspath.data.remote.dto
import com.google.errorprone.annotations.Keep
import com.google.gson.annotations.SerializedName


data class CrossPathsResponseDto(
    @SerializedName("crossPaths")
    val crossPaths: List<CrossPathDto>?,
    @SerializedName("nextCursor")
    val nextCursor: Long?
)

@Keep
data class CrossPathDto(
    @SerializedName("address")
    val address: String?,
    @SerializedName("cognitoUserId")
    val cognitoUserId: String,
    @SerializedName("count")
    val count: Int?,
    @SerializedName("counterPartInteraction")
    val counterPartInteraction: String?,
    @SerializedName("currentUserInteraction")
    val currentUserInteraction: String?,
    @SerializedName("id")
    val id: Long,
    @SerializedName("location")
    val location: LocationDto?,
    @SerializedName("profile")
    val profile: ProfileDto?,
    @SerializedName("timestamp")
    val timestamp: String?
)

@Keep
data class LocationDto(
    @SerializedName("latitude")
    val latitude: Double?,
    @SerializedName("longitude")
    val longitude: Double?
)

@Keep
data class ProfileDto(
    @SerializedName("badge2")
    val badge2: String?,
    @SerializedName("firstName")
    val firstName: String?,
    @SerializedName("thumbnailUrl")
    val thumbnailUrl: String?,
    @SerializedName("pictureUrl")
    val pictureUrl: String?,
    @SerializedName("age")
    val age: Int?
)