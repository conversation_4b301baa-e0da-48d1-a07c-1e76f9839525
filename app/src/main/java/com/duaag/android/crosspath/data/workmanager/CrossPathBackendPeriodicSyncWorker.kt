package com.duaag.android.crosspath.data.workmanager

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.duaag.android.api.ResourceV2
import com.duaag.android.application.DuaApplication
import com.duaag.android.crosspath.domain.model.removeCloseLocations
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import com.duaag.android.crosspath.domain.usecase.DeleteLocationsOlderThanUseCase
import com.duaag.android.crosspath.domain.usecase.GetLocationOlderThanXUseCase
import com.duaag.android.crosspath.domain.usecase.SyncLocationsBackendUseCase
import com.duaag.android.sharedprefs.DuaSharedPrefs
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class CrossPathBackendPeriodicSyncWorker(
    val context: Context,
    workerParams: WorkerParameters,
) : CoroutineWorker(context, workerParams) {


    @Inject
    lateinit var crossPathLocationRepository: CrossPathLocationRepository

    @Inject
    lateinit var syncLocationsBackendUseCase: SyncLocationsBackendUseCase

    @Inject
    lateinit var getLocationOlderThanUseCase: GetLocationOlderThanXUseCase

    @Inject
    lateinit var deleteLocationsOlderThanUseCase: DeleteLocationsOlderThanUseCase

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    override suspend fun doWork(): Result {
        try {
            (context as DuaApplication)
                .appComponent.crossPathWorkerComponent()
                .create()
                .inject(this)

            val workerCalled = duaSharedPrefs.getSyncWorkerCalled()
            duaSharedPrefs.setSyncWorkerCalled(workerCalled + 1)

            val time = System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(30L)
            val locations = getLocationOlderThanUseCase.invoke(time)
            val unSyncedLocations = removeCloseLocations(locations)

            Timber.tag("SYNC_PERIODIC_LOCATIONS").d("locations: $unSyncedLocations")
            if(unSyncedLocations.isEmpty()) {
                val success = duaSharedPrefs.getSyncLocationSuccess()
                duaSharedPrefs.setSyncLocationSuccess(success + 1)

                return Result.success()
            }

            val result = syncLocationsBackendUseCase.invoke(unSyncedLocations)
            Timber.tag("SYNC_PERIODIC_LOCATIONS").d("result: $result")

            return when(result) {
                is ResourceV2.Success -> {
                    Timber.tag("SYNC_PERIODIC_LOCATIONS").d("deleting: $unSyncedLocations")
                    deleteLocationsOlderThanUseCase.invoke(time)

                    val success = duaSharedPrefs.getSyncLocationSuccess()
                    duaSharedPrefs.setSyncLocationSuccess(success + 1)

                    Result.success()
                }
                is ResourceV2.Error -> {
                    val failed = duaSharedPrefs.getSyncLocationFailed()
                    duaSharedPrefs.setSyncLocationFailed(failed + 1)

                    Result.failure()
                }
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
            val failed = duaSharedPrefs.getSyncLocationFailed()
            duaSharedPrefs.setSyncLocationFailed(failed + 1)

            return  Result.failure()
        }
    }

    companion object {
        const val workName = "CrossPathBackendSyncWorker"
        const val repeatInterval = 45L
    }
}