package com.duaag.android.crosspath.presentation

import android.Manifest
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapSourceValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentProfileRecommendationsBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.checkBackgroundLocationPermission
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.openAppSettings
import com.duaag.android.utils.requestLocationPermissionsWithLauncher
import com.duaag.android.utils.setOnSingleClickListener
import javax.inject.Inject

class ProfileRecommendationsDialogFragment : DialogFragment() {

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs
    @Inject
    lateinit var duaAccount: DuaAccount

    private var  _binding: FragmentProfileRecommendationsBinding? = null
    private val binding get() = _binding
    private val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->


        }

    private var premiumType: String? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreenStyle)
        premiumType = arguments?.getString(USER_PREMIUM_TYPE)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentProfileRecommendationsBinding.inflate(inflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding?.btnContinue?.setOnSingleClickListener{
            manageLocationPermissions(requireContext())
            dismissAllowingStateLoss()
        }

        binding?.notNowButton?.setOnSingleClickListener{
            sendNotNowEvent(premiumType)
            dismissAllowingStateLoss()
        }
        sendScreenViewEvent(premiumType)
    }
    private fun sendScreenViewEvent(premiumType: String?) {
        val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
        val communityKey =   ClevertapEventPropertyEnum.COMMUNITY.propertyName

        val eventPremiumType = premiumType
        val community = duaSharedPrefs.getUserCommunityName()
        sendClevertapEvent(
            ClevertapEventEnum.CROSS_PATH_LOCATION_TO_USE_SCREENVIEW,
            mapOf(
                premiumTypeKey to eventPremiumType,
                communityKey to community
            )
        )
    }

    private fun sendNotNowEvent(premiumType: String?){
        val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
        val communityKey =   ClevertapEventPropertyEnum.COMMUNITY.propertyName
        val sourceKey = ClevertapEventPropertyEnum.SOURCE.propertyName

        val eventPremiumType = premiumType
        val community = duaSharedPrefs.getUserCommunityName()
        val source = ClevertapSourceValues.SCREEN.value

        sendClevertapEvent(
            ClevertapEventEnum.CROSS_PATH_LOCATION_TO_USE_DISMISSED,
            mapOf(
                premiumTypeKey to eventPremiumType,
                communityKey to community,
                sourceKey to source
            )
        )
    }

    private fun manageLocationPermissions(context: Context) {
        if (!context.isLocationPermissionEnabled() && !shouldProvideRationale() ) {
            requestLocationPermissionsWithLauncher(requestPermissionLauncher)
            return
        }
        if (!context.checkBackgroundLocationPermission()) {
            context.openAppSettings()
        }
    }
     private fun shouldProvideRationale(): Boolean {
        val shouldProvideRationale =
            ActivityCompat.shouldShowRequestPermissionRationale(
                requireActivity(),
                Manifest.permission.ACCESS_FINE_LOCATION
            )

        return shouldProvideRationale
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val USER_PREMIUM_TYPE = "USER_PREMIUM_TYPE"

        fun newInstance(premiumType:String?): ProfileRecommendationsDialogFragment {
            val fragment = ProfileRecommendationsDialogFragment()
            val args = bundleOf(USER_PREMIUM_TYPE to premiumType)
            fragment.arguments = args
            return  fragment
        }
    }
}