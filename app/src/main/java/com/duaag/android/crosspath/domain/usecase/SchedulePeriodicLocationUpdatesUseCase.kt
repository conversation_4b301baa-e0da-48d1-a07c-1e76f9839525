package com.duaag.android.crosspath.domain.usecase

import android.content.Context
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.duaag.android.crosspath.data.workmanager.CrossPathPeriodicWorker
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class SchedulePeriodicLocationUpdatesUseCase @Inject constructor(
    private val userRepository: UserRepository
) {

    suspend operator fun invoke(context: Context) {
        withContext(Dispatchers.IO) {
            val userModel = userRepository.getLoggedInUserModel()
            val isUserDisabled = userModel?.isDisabled ?: false
            if (isUserDisabled.not()) {
                withContext(Dispatchers.Main) {
                    val workManager = WorkManager.getInstance(context)
                    val workRequest = PeriodicWorkRequestBuilder<CrossPathPeriodicWorker>(
                        CrossPathPeriodicWorker.repeatInterval,
                        TimeUnit.MINUTES
                    ).build()

                    workManager.enqueueUniquePeriodicWork(
                        CrossPathPeriodicWorker.workName,
                        ExistingPeriodicWorkPolicy.KEEP,
                        workRequest
                    )
                }
            }
        }
    }
}