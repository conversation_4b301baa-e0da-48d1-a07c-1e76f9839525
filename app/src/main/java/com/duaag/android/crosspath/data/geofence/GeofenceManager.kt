package com.duaag.android.crosspath.data.geofence

import android.annotation.SuppressLint
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.location.Location
import android.os.Build
import com.duaag.android.utils.checkLocationAndBackgroundPermission
import com.google.android.gms.location.Geofence
import com.google.android.gms.location.GeofencingClient
import com.google.android.gms.location.GeofencingRequest
import com.google.android.gms.location.LocationServices
import timber.log.Timber

class GeofenceManager(val context: Context) {

    private var geofencingClient: GeofencingClient = LocationServices.getGeofencingClient(context)
    private var geofenceList: MutableList<Geofence> = ArrayList()

    private val intentFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
    } else {
        PendingIntent.FLAG_UPDATE_CURRENT
    }

    private val geofencePendingIntent: PendingIntent by lazy {
        val intent = Intent(context, CrossPathGeofenceBroadcastReceiver::class.java)
        // We use FLAG_UPDATE_CURRENT so that we get the same pending intent back when calling
        // addGeofences() and removeGeofences().
        PendingIntent.getBroadcast(context, 0, intent, intentFlag)
    }

    fun removeAndAddGeoFences(location: Location, callback: (Boolean) -> Unit) {
        removeGeoFences { isRemoved ->
            if (isRemoved) {
                addFences(location, callback)
            } else {
                callback.invoke(false)
            }
        }
    }

    fun removeGeoFences(callback: (Boolean) -> Unit) {
        geofencingClient.removeGeofences(geofencePendingIntent).run {
            addOnSuccessListener {
                callback.invoke(true)
            }
            addOnFailureListener {
                Timber.e(it)
                callback.invoke(true)
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun addFences(location: Location, callback: (Boolean) -> Unit) {
        if (context.checkLocationAndBackgroundPermission().not()) {
            callback.invoke(false)
        }

        geofenceList = ArrayList()
        geofenceList.add(
            Geofence.Builder()
                .setRequestId(REQUEST_ID)
                .setCircularRegion(
                    location.latitude,
                    location.longitude,
                    GEOFENCE_RADIUS_IN_METERS
                )
                .setExpirationDuration(Geofence.NEVER_EXPIRE)
                .setTransitionTypes(Geofence.GEOFENCE_TRANSITION_EXIT)
                .build()
        )

        geofencingClient.addGeofences(getGeofencingRequest(), geofencePendingIntent).run {
            addOnSuccessListener {
                callback.invoke(true)
            }
            addOnFailureListener {
                Timber.e(it)
                callback.invoke(false)
            }
        }
    }

    private fun getGeofencingRequest(): GeofencingRequest = GeofencingRequest.Builder().apply {
        setInitialTrigger(GeofencingRequest.INITIAL_TRIGGER_EXIT)
        addGeofences(geofenceList)
    }.build()

    companion object {

        private const val GEOFENCE_RADIUS_IN_METERS = 200f
        private const val REQUEST_ID = "cross-path-geofence"
    }

}