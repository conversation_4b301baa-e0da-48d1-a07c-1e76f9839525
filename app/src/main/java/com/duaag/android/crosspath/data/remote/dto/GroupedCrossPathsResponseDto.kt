package com.duaag.android.crosspath.data.remote.dto
import com.google.errorprone.annotations.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class GroupedCrossPathsResponseDto(
    @SerializedName("clusterDistance")
    val clusterDistance: Double,
    @SerializedName("clusters")
    val clusters: List<ClusterDto>,
    @SerializedName("cognitoUserId")
    val cognitoUserId: String,
    @SerializedName("count")
    val count: Int,
    @SerializedName("currentLocation")
    val currentLocation: CurrentLocationDto,
    @SerializedName("locationToken")
    val locationToken: String
)

@Keep
data class ClusterDto(
    @SerializedName("hasPendingInteraction")
    val hasPendingInteraction: Boolean,
    @SerializedName("location")
    val location: LocationDto,
    @SerializedName("users")
    val users: List<String>
)

@Keep
data class CurrentLocationDto(
    @SerializedName("latitude")
    val latitude: Double,
    @SerializedName("longitude")
    val longitude: Double
)