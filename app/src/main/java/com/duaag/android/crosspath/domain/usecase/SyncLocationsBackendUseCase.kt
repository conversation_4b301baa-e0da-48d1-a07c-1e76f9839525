package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.api.ResourceV2
import com.duaag.android.base.models.UserModel
import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class SyncLocationsBackendUseCase @Inject constructor(
    private val crossPathLocationRepository: CrossPathLocationRepository,
    private val userRepository: UserRepository) {
    suspend operator fun invoke(location: List<CrossPathLocation>): ResourceV2<Unit> {
        var userModel: UserModel?

        withContext(Dispatchers.IO) {
            userModel = userRepository.getLoggedInUserModel()
        }

        val isUserDisabled = userModel?.isDisabled ?: false
        if (isUserDisabled) return ResourceV2.Error("User is disabled")
        return crossPathLocationRepository.addLocationsInBackend(location)
    }
}