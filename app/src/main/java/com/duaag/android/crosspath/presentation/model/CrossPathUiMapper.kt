package com.duaag.android.crosspath.presentation.model

import com.duaag.android.crosspath.domain.model.ClusterModel
import com.duaag.android.crosspath.domain.model.CrossPathModel
import com.duaag.android.crosspath.domain.model.CurrentLocationModel
import com.duaag.android.crosspath.domain.model.GroupedCrossPathsModel
import com.duaag.android.crosspath.domain.model.LocationModel
import com.duaag.android.crosspath.domain.model.ProfileModel
import com.duaag.android.home.models.CrossPath
import com.duaag.android.home.models.Location
import com.duaag.android.home.models.RecommendedUserModel

fun CrossPathModel.toDiscoveredProfilesUiModel(): DiscoveredProfilesUiModel.Item {
    return DiscoveredProfilesUiModel.Item(
        id = this.id,
        cognitoUserId = this.cognitoUserId,
        count = this.count,
        address = this.address ?: "",
        location = location?.toCrossPathLocation(),
        timestamp = this.timestamp,
        currentUserInteraction = this.currentUserInteraction,
        counterPartInteraction = this.counterPartInteraction,
        profile = this.profile?.toCrossPathProfileUiModel()
    )
}

fun LocationModel.toCrossPathLocation(): CrossPathLocation {
    return CrossPathLocation(
        latitude = this.latitude,
        longitude = this.longitude
    )
}

fun CurrentLocationModel.toCrossPathLocation(): CrossPathLocation {
    return CrossPathLocation(
            latitude = this.latitude,
            longitude = this.longitude
    )
}

fun ProfileModel.toCrossPathProfileUiModel(): CrossPathProfileUiModel {
    return CrossPathProfileUiModel(
        thumbnailUrl = this.thumbnailUrl ?: "",
        firstName = this.firstName ?: "",
        badge2 = this.badge2
    )
}

fun GroupedCrossPathsModel.toGroupedCrossPathUiModel(): GroupedCrossPathUiModel {
    return GroupedCrossPathUiModel(
            clusterDistance = this.clusterDistance,
            clusters = this.clusters.map { it.toClusterUiModel() },
            cognitoUserId = this.cognitoUserId,
            count = this.count,
            currentLocation = this.currentLocation.toCrossPathLocation()
    )
}

fun ClusterModel.toClusterUiModel(): ClusterUiModel {
    return ClusterUiModel(
            hasPendingInteraction = this.hasPendingInteraction,
            location = this.location.toCrossPathLocation(),
            users = this.users
    )
}

fun List<CrossPathModel>?.toListOfRecommendedUserModel(): List<RecommendedUserModel> {
    return this?.map {crossPathModel ->
        RecommendedUserModel.getEmptyRecommendedUserModel().copy(
        cognitoUserId = crossPathModel.cognitoUserId,
        firstName = crossPathModel.profile?.firstName?:"",
        badge2 = crossPathModel.profile?.badge2,
        age = crossPathModel.profile?.age ?: 0,
        crossPath = CrossPath(
            id = crossPathModel.id,
            cognitoUserId = crossPathModel.cognitoUserId,
            count = crossPathModel.count ?: 0,
            counterPartInteraction = crossPathModel.counterPartInteraction,
            currentUserInteraction = crossPathModel.currentUserInteraction,
            address = crossPathModel.address ?: "",
            location = crossPathModel.location?.let { location -> Location(location.latitude ?:0.0, location.longitude ?:0.0) },
            timestamp = crossPathModel.timestamp ?: ""
        ), profile = RecommendedUserModel.getEmptyRecommendedUserModel().profile.copy(
            pictureUrl = crossPathModel.profile?.pictureUrl ?: ""
        )


    ) } ?: emptyList()
}