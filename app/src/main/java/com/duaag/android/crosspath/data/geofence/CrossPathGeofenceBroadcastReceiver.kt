package com.duaag.android.crosspath.data.geofence

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.duaag.android.application.DuaApplication
import com.duaag.android.crosspath.domain.usecase.RemoveGeoFencesUseCase
import com.duaag.android.crosspath.domain.usecase.ScheduleOnTimeLocationUpdatesUseCase
import com.duaag.android.di.ApplicationScope
import com.duaag.android.di.IoDispatcher
import com.google.android.gms.location.Geofence
import com.google.android.gms.location.GeofenceStatusCodes
import com.google.android.gms.location.GeofencingEvent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

class CrossPathGeofenceBroadcastReceiver : BroadcastReceiver(){
    @Inject
    lateinit var scheduleOnTimeLocationUpdatesUseCase: ScheduleOnTimeLocationUpdatesUseCase

    @ApplicationScope
    @Inject
    lateinit var externalScope: CoroutineScope

    override fun onReceive(context: Context, intent: Intent?) {
        (context.applicationContext as DuaApplication).appComponent
            .inject(this)
        val geofencingEvent = intent?.let { GeofencingEvent.fromIntent(it) } ?: return
        if (geofencingEvent.hasError()) {
            val errorMessage = GeofenceStatusCodes
                .getStatusCodeString(geofencingEvent.errorCode)
            Timber.tag(TAG).e(errorMessage)
            return
        }
        val geofenceTransition = geofencingEvent.geofenceTransition

        if (geofenceTransition == Geofence.GEOFENCE_TRANSITION_EXIT) {
          externalScope.launch {
                scheduleOnTimeLocationUpdatesUseCase(context)
            }
            RemoveGeoFencesUseCase().invoke(context)
        } else {
            Timber.tag(TAG).i("Geofence transition invalid type: $geofenceTransition")
        }
    }

    companion object {
        private const val TAG = "CrossPathGeofenceBroadcastReceiver"
    }
}