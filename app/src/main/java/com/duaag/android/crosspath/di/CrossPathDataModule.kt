package com.duaag.android.crosspath.di

import com.duaag.android.crosspath.data.repository.CrossPathCrossPathLocationRepositoryImpl
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import dagger.Binds
import dagger.Module
import javax.inject.Singleton

@Module
abstract class CrossPathDataModule {

    @Binds
    @Singleton
    abstract fun provideLocationRepository(locationRepository: CrossPathCrossPathLocationRepositoryImpl): CrossPathLocationRepository
}
