package com.duaag.android.crosspath.presentation.model

sealed class DiscoveredProfilesUiModel {
    data class Item(
        val id: Long,
        val cognitoUserId: String,
        val count: Int?,
        val address: String,
        val location: CrossPathLocation?,
        val timestamp: String?,
        val currentUserInteraction: String?,
        val counterPartInteraction: String?,
        val profile: CrossPathProfileUiModel?
    ) : DiscoveredProfilesUiModel()

    object Loading : DiscoveredProfilesUiModel()
    object Error : DiscoveredProfilesUiModel()
    object ViewAll : DiscoveredProfilesUiModel()
    data class Shimmer(val id: Long) : DiscoveredProfilesUiModel()
}

data class CrossPathLocation(
    val latitude: Double?,
    val longitude: Double?,
)

data class CrossPathProfileUiModel(
    val thumbnailUrl: String,
    val firstName: String,
    val badge2: String?
)

data class PagingData(
    val nextCursor: Long? = null,
    val limit: Int = 20,
)