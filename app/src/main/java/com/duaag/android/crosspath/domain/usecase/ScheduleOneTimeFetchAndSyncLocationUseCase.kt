package com.duaag.android.crosspath.domain.usecase

import android.content.Context
import androidx.lifecycle.map
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.duaag.android.crosspath.data.workmanager.CrossPathBackendOneTimeSyncWorker
import com.duaag.android.crosspath.data.workmanager.CrossPathOneTimeWorker
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class ScheduleOneTimeFetchAndSyncLocationUseCase @Inject constructor(
    private val userRepository: UserRepository
) {

    suspend operator fun invoke(context: Context){
        withContext(Dispatchers.IO) {
            val userModel = userRepository.getLoggedInUserModel()
            val isUserDisabled = userModel?.isDisabled ?: false
            if (isUserDisabled.not()) {
                withContext(Dispatchers.Main) {
                    val workManager = WorkManager.getInstance(context)

                    val firstWorkRequest = OneTimeWorkRequestBuilder<CrossPathOneTimeWorker>()
                        .addTag(CrossPathOneTimeWorker.workName)
                        .build()

                    val secondWorkRequest =
                        OneTimeWorkRequestBuilder<CrossPathBackendOneTimeSyncWorker>()
                            .addTag(CrossPathBackendOneTimeSyncWorker.workName)
                            .build()

                    workManager
                        .beginWith(firstWorkRequest)
                        .then(secondWorkRequest)
                        .enqueue()
                }
            }
        }
    }
}