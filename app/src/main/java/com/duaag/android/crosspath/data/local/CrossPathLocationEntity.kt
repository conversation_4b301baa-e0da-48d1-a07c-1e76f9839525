package com.duaag.android.crosspath.data.local

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.duaag.android.crosspath.domain.model.CrossPathLocation

@Entity(tableName = "location_cross_path",
    indices = [
        Index(value = ["latitude", "longitude"]),  // Helps speed up radius-based queries
        Index(value = ["timestamp"])               // Optimizes time range queries
    ])
data class CrossPathLocationEntity(
    @PrimaryKey(autoGenerate = true) val id: Int = 0,
    @ColumnInfo(name = "longitude") val longitude: Double,
    @ColumnInfo(name = "latitude") val latitude: Double,
    @ColumnInfo(name = "timestamp") val timestamp: Long, // Storing as Unix timestamp.
    @ColumnInfo(name = "duration") val duration: Long = 0,  // Duration of stay at the location in milliseconds
    @ColumnInfo(name = "visitCount") val visitCount: Int = 0  // Count of visits to this coordinate
)

fun CrossPathLocationEntity.toDomainModel(): CrossPathLocation {
    return CrossPathLocation(
        id = id,
        longitude = longitude,
        latitude = latitude,
        timestamp = timestamp
    )
}

