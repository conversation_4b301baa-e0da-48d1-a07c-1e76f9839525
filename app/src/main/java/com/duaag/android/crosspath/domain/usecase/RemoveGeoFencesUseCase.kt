package com.duaag.android.crosspath.domain.usecase

import android.content.Context
import com.duaag.android.crosspath.data.geofence.GeofenceManager
import timber.log.Timber
import javax.inject.Inject

class RemoveGeoFencesUseCase @Inject constructor() {

    operator fun invoke(context: Context) {
        val geofenceManager = GeofenceManager(context)
        geofenceManager.removeGeoFences {
            Timber.tag(TAG).i("Geofence removed: $it")
        }
    }

    companion object
    {
        private const val TAG = "RemoveGeoFencesUseCase"
    }
}