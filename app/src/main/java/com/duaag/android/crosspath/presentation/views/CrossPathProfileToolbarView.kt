package com.duaag.android.crosspath.presentation.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.duaag.android.R
import com.duaag.android.databinding.CrossPathProfilesToolbarViewBinding
class CrossPathProfileToolbarView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val binding = CrossPathProfilesToolbarViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun bind(viewEntity: ViewEntity) {
        val nearText = "${binding.root.context.getString(R.string.near_caption)} ${viewEntity.nearText}"
        binding.toolbarExpanded.isVisible = viewEntity.state == State.Expanded
        binding.toolbarCollapsed.isVisible = viewEntity.state == State.Collapsed
        when (viewEntity.state) {
            State.Expanded -> {
                binding.expandedNear.text = nearText
                binding.closeButton.setOnClickListener {
                    viewEntity.onButtonClick()
                }
            }
            State.Collapsed -> {
                binding.collapsedNear.text = nearText
            }
        }
    }
 class ViewEntity(
        val state: State,
        val nearText: String,
        val onButtonClick: () -> Unit,
    )

    sealed class State {
        object Expanded : State()
        object Collapsed : State()
    }

}
