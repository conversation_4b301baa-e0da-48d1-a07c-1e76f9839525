package com.duaag.android.crosspath.presentation.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.crosspath.presentation.model.CrossPathProfilesListModel
import com.duaag.android.databinding.CrossPathProfileItemBinding
import com.duaag.android.databinding.CrossPathProfileShimmerItemBinding
import com.duaag.android.databinding.EmptyCrossPathProfilesItemBinding
import com.duaag.android.home.models.InteractionType
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.utils.bindImage
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import timber.log.Timber

class CrossPathProfilesAdapter(
    private val userClickListener: (CrossPathProfilesListModel.Item) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val differ: AsyncListDiffer<CrossPathProfilesListModel> =
        AsyncListDiffer(this, object : DiffUtil.ItemCallback<CrossPathProfilesListModel>() {
            override fun areItemsTheSame(
                oldItem: CrossPathProfilesListModel,
                newItem: CrossPathProfilesListModel,
            ): Boolean {
                return if (oldItem is CrossPathProfilesListModel.Item && newItem is CrossPathProfilesListModel.Item) {
                    oldItem.recommendedUserModel.id == newItem.recommendedUserModel.id
                } else if (oldItem is CrossPathProfilesListModel.Shimmer && newItem is CrossPathProfilesListModel.Shimmer) {
                    oldItem.id == newItem.id
                } else if (oldItem is CrossPathProfilesListModel.Empty && newItem is CrossPathProfilesListModel.Empty) {
                    oldItem.id == newItem.id
                }else {
                    false
                }
            }

            override fun areContentsTheSame(
                oldItem: CrossPathProfilesListModel,
                newItem: CrossPathProfilesListModel,
            ): Boolean {
                return oldItem == newItem
            }
        })

    fun submitData(list: List<CrossPathProfilesListModel>) {
        differ.submitList(list)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_VIEW -> CrossPathViewHolder(
                CrossPathProfileItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )
            SHIMMER_ITEM -> ShimmerViewHolder(
                CrossPathProfileShimmerItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            EMPTY_VIEW -> EmptyViewHolder(
                EmptyCrossPathProfilesItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
       when (getItemViewType(position)) {
            ITEM_VIEW -> (holder as CrossPathViewHolder).bind((differ.currentList[position] as CrossPathProfilesListModel.Item))
            SHIMMER_ITEM -> (holder as ShimmerViewHolder)
            EMPTY_VIEW -> (holder as EmptyViewHolder)
            else -> {}
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (differ.currentList[position]) {
            is CrossPathProfilesListModel.Item -> ITEM_VIEW
            is CrossPathProfilesListModel.Shimmer -> SHIMMER_ITEM
            is CrossPathProfilesListModel.Empty -> EMPTY_VIEW
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun getItemCount(): Int = differ.currentList.size


    inner class CrossPathViewHolder(private val binding: CrossPathProfileItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: CrossPathProfilesListModel.Item) {
            binding.featuredUserAge.visibility = View.VISIBLE
            binding.root.setOnSingleClickListener{
                userClickListener(item)
            }
            setVisibility(
                binding.featuredUserBadge,
                item.recommendedUserModel.badge2.equals(Badge2Status.APPROVED.status)
            )
            binding.featuredUserName.text = item.recommendedUserModel.firstName
            binding.featuredUserAge.text = ", ${item.recommendedUserModel.age}"

            val isInteracted = item.recommendedUserModel.crossPath?.counterPartInteraction.isNullOrEmpty().not() &&
                    item.recommendedUserModel.crossPath?.counterPartInteraction != NO_INTERACTION_VALUE
            binding.interactionView.isVisible = isInteracted

            if (isInteracted) {
                val interactedType = item.recommendedUserModel.crossPath?.counterPartInteraction
                binding.interactionIcon.setImageResource(
                    when (interactedType) {
                        InteractionType.LIKE.value -> R.drawable.cross_path_like_icon
                        else -> R.drawable.cross_path_instachat_icon
                    }
                )
                binding.interactionText.text = when (interactedType) {
                    InteractionType.LIKE.value -> binding.root.context.getString(R.string.likes_you_status)
                    else -> binding.root.context.getString(R.string.messaged_you_status)
                }
            }
            val count = item.recommendedUserModel.crossPath?.count ?: 0
            val isCounted = count > 1
            binding.counterSpottedCard.isVisible = isCounted
            if (isCounted) {
                binding.countSpottedTxt.text = binding.root.context.getString(R.string.times_spotted_tag, count.toString())
            }
            val imageUrl = item.recommendedUserModel.profile.pictureUrl

            bindImage(binding.featuredUserImage, imageUrl)
        }
    }

    inner class ShimmerViewHolder( binding: CrossPathProfileShimmerItemBinding) :
        RecyclerView.ViewHolder(binding.root)

    inner class EmptyViewHolder(binding: EmptyCrossPathProfilesItemBinding) :
        RecyclerView.ViewHolder(binding.root)



    companion object {
        private const val ITEM_VIEW = 1
        private const val SHIMMER_ITEM = 2
        const val EMPTY_VIEW = 3



        private const val NO_INTERACTION_VALUE = "no_interaction"
    }

}