package com.duaag.android.crosspath.presentation.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.ResourceV2
import com.duaag.android.application.DuaApplication
import com.duaag.android.crosspath.domain.model.LocationModel
import com.duaag.android.crosspath.domain.usecase.GetCrossPathsAPIUseCase
import com.duaag.android.crosspath.domain.usecase.GetGroupedCrossPathsAPIUseCase
import com.duaag.android.crosspath.presentation.model.CrossPathInteractedModel
import com.duaag.android.crosspath.presentation.model.CrossPathUiState
import com.duaag.android.crosspath.presentation.model.DiscoveredProfilesUiModel
import com.duaag.android.crosspath.presentation.model.PagingData
import com.duaag.android.crosspath.presentation.model.toDiscoveredProfilesUiModel
import com.duaag.android.crosspath.presentation.model.toGroupedCrossPathUiModel
import com.duaag.android.di.ActivityScope
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.livedata.SingleLiveData
import com.duaag.android.utils.location.LocationClientImpl
import com.google.android.gms.location.LocationServices
import com.google.android.gms.tasks.CancellationTokenSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@ActivityScope
class CrossPathViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val getCrossPathsAPIUseCase: GetCrossPathsAPIUseCase,
    private val getGroupedCrossPathsAPIUseCase: GetGroupedCrossPathsAPIUseCase
) : ViewModel() {

    val user = userRepository.user

    private val _crossPathUiState: MutableStateFlow<CrossPathUiState> = MutableStateFlow(CrossPathUiState())
    val crossPathUiState: StateFlow<CrossPathUiState>
        get() = _crossPathUiState.asStateFlow()


    private val _crossPathInteract: MutableSharedFlow<CrossPathInteractedModel> = MutableSharedFlow()
    val crossPathInteract: SharedFlow<CrossPathInteractedModel>
        get() = _crossPathInteract.asSharedFlow()

    private val _attendeesCount: MutableStateFlow<String> = MutableStateFlow("")
    val attendeesCount: StateFlow<String>
        get() = _attendeesCount.asStateFlow()


    var _lastTimeDiscoveredProfilesUpdated: Long = 0
        private set

    var _lastTimeGroupedPinsUpdated: Long = 0
        private set

    var lastDistance: Double? = null
        private set
    var lastLatitude: Double? = null
        private set
    var lastLongitude: Double? = null
        private set

    private val _checkedInSuccess: SingleLiveData<Unit> = SingleLiveData()
    val checkedInSuccess: LiveData<Unit>
        get() = _checkedInSuccess

    private val locationClient by lazy {
        LocationClientImpl(
                DuaApplication.instance,
                LocationServices.getFusedLocationProviderClient(DuaApplication.instance)
        )
    }

    init {
        if (userRepository.user.value?.profile?.isInvisible != true) {
            loadDiscoveredProfiles()
            requestPermission()
        }
    }

    fun requestPermission() {
        Timber.tag("CROSS_PATHS").d("requestPermission")

        locationClient.getCurrentLocation(CancellationTokenSource())
                .catch { e ->
                    e.printStackTrace()
                    Timber.tag("CROSS_PATHS").d("catch ${e.message}")
                }
                .onEach { location ->
                    Timber.tag("CROSS_PATHS").d("onEach location$location")

                    _crossPathUiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                                lastKnownLocation = LocationModel(location.latitude, location.longitude)
                        )
                    }
                }.flowOn(Dispatchers.IO)
                .launchIn(viewModelScope)
    }

    fun loadDiscoveredProfiles() {
        viewModelScope.launch {
            val result = getCrossPathsAPIUseCase.invoke(limit = 10)
            when (result) {
                is ResourceV2.Success -> {
                    _lastTimeDiscoveredProfilesUpdated = System.currentTimeMillis()

                    _crossPathUiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                                discoveredItems = result.data.crossPaths?.map {
                                    it.toDiscoveredProfilesUiModel()
                                } ?: emptyList(),
                                paginationData = PagingData(nextCursor = result.data.nextCursor),
                                isLoadingDiscoveredProfiles = false,
                                discoveredItemsError = null
                        )
                    }
                    Timber.tag("CROSS_PATHS").d("success result ${result.data}")
                }

                is ResourceV2.Error -> {
                    _crossPathUiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                                discoveredItems = emptyList(),
                                isLoadingDiscoveredProfiles = false,
                                discoveredItemsError = result.errorType
                        )
                    }
                }
            }
        }
    }

    fun getGroupedCrossPaths(distance: Double, latitude: Double, longitude: Double, forceRefresh: Boolean = false) {
        lastDistance = distance
        lastLatitude = latitude
        lastLongitude = longitude

        if((_crossPathUiState.value.groupedPins != null || _crossPathUiState.value.isLoadingGroupedPins) && !forceRefresh)
            return

        viewModelScope.launch {
            _crossPathUiState.update { crossPathUiState ->
                crossPathUiState.copy(isLoadingGroupedPins = true)
            }

            val result = getGroupedCrossPathsAPIUseCase.invoke(distance, latitude, longitude)
            when (result) {
                is ResourceV2.Success -> {
                    _lastTimeGroupedPinsUpdated = System.currentTimeMillis()

                    _crossPathUiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                                groupedPins = result.data.toGroupedCrossPathUiModel(),
                                isLoadingGroupedPins = false,
                                groupedPinsError = null
                        )
                    }
                    Timber.tag("CROSS_PATHS").d("grouped success result ${result.data}")
                }

                is ResourceV2.Error -> {
                    _crossPathUiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                                groupedPins = null,
                                isLoadingGroupedPins = false,
                                groupedPinsError = result.errorType
                        )
                    }
                    Timber.tag("CROSS_PATHS").d("grouped fail result ${result.message}")
                }
            }
        }
    }

    fun loadMoreDiscoveredProfiles(nextCursor: Long) {
        if (_crossPathUiState.value.isLoadingDiscoveredProfiles)
            return

        viewModelScope.launch {
            _crossPathUiState.update { crossPathUiState ->
                crossPathUiState.copy(isLoadingDiscoveredProfiles = true)
            }
            val result = getCrossPathsAPIUseCase.invoke(nextCursor)
            when (result) {
                is ResourceV2.Success -> {
                    _crossPathUiState.update { crossPathUiState ->
                        val currentProfiles = crossPathUiState.discoveredItems
                        val newItems = result.data.crossPaths?.map {
                            it.toDiscoveredProfilesUiModel()
                        } ?: emptyList()

                        val uiItems = currentProfiles + newItems
                        crossPathUiState.copy(
                            discoveredItems = uiItems,
                            paginationData = PagingData(result.data.nextCursor),
                            isLoadingDiscoveredProfiles = false
                        )
                    }
                    Timber.tag("CROSS_PATHS").d("success result ${result.data}")
                }

                is ResourceV2.Error -> {
                    _crossPathUiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                            isLoadingDiscoveredProfiles = false
                        )
                    }
                    Timber.tag("CROSS_PATHS").d("fail result ${result.message}")
                }
            }
        }
    }

    fun matchReceived(cognitoUserId: String) {
        removeCrossPath(cognitoUserId)

        viewModelScope.launch {
            _crossPathInteract.emit(CrossPathInteractedModel(cognitoUserId, "match"))
        }
    }

    fun removeCrossPath(cognitoUserId: String) {
        _crossPathUiState.update { currentState ->
            val updatedClusters = currentState.groupedPins?.clusters?.map { cluster ->
                val filteredUsers = cluster.users.filterNot { it == cognitoUserId }
                cluster.copy(users = filteredUsers)
            }?.filter { it.users.isNotEmpty() }

            val updatedGroupedPins = currentState.groupedPins?.copy(clusters = updatedClusters)

            val filteredList = currentState.discoveredItems.filterNot {
                it is DiscoveredProfilesUiModel.Item && it.cognitoUserId == cognitoUserId
            }

            currentState.copy(
                    discoveredItems = filteredList,
                    groupedPins = updatedGroupedPins
            )
        }
    }

    fun newLikeReceived(cognitoUserId: String) {
        _crossPathUiState.update { currentState ->
            val updatedList = currentState.discoveredItems.map { item ->
                if (item is DiscoveredProfilesUiModel.Item && item.cognitoUserId == cognitoUserId) {
                    item.copy(counterPartInteraction = "like")
                } else {
                    item
                }
            }

            currentState.copy(discoveredItems = updatedList)
        }
    }

    fun newInstachatReceived(cognitoUserId: String) {
        _crossPathUiState.update { currentState ->
            val updatedList = currentState.discoveredItems.map { item ->
                if (item is DiscoveredProfilesUiModel.Item && item.cognitoUserId == cognitoUserId) {
                    item.copy(counterPartInteraction = "instachat")
                } else {
                    item
                }
            }

            currentState.copy(discoveredItems = updatedList)
        }
    }

    fun callCheckedIn() {
        _checkedInSuccess.call()
    }

    fun setAttendeeCount(count: String) {
        _attendeesCount.update { count }
    }
}