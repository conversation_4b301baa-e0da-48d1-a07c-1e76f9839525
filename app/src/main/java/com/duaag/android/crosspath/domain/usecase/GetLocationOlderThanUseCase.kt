package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import javax.inject.Inject

class GetLocationOlderThanUseCase @Inject constructor(private val crossPathLocationRepository: CrossPathLocationRepository) {
    /**
     * Executes the use case to retrieve location entries older than the specified timestamp.
     * 
     * @param timeThreshold The timestamp in milliseconds. Locations older than this time will be returned.
     */
    operator fun invoke(timeThreshold: Long) = crossPathLocationRepository.getLocationOlderThan(timeThreshold)

}