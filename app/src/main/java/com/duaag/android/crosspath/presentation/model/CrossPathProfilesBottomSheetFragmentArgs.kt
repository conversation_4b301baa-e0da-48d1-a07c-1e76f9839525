package com.duaag.android.crosspath.presentation.model

import java.io.Serializable

data class CrossPathProfilesBottomSheetFragmentArgs(
    val users: List<String>? =null,
    val latitude: Double?=null,
    val longitude: Double?=null,
    val hasInteractedBefore: Boolean?=null,
    val isSunnyHillMode: Boolean = false
): Serializable {
    companion object {
        private const val serialVersionUID = 1L
        const val KEY = "CrossPathProfilesBottomSheetFragmentArgs"
    }
}