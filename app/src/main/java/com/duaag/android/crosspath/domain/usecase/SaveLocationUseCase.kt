package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SaveLocationUseCase @Inject constructor(
    private val crossPathLocationRepository: CrossPathLocationRepository,
    private val userRepository: UserRepository
) {
    suspend operator fun invoke(location: CrossPathLocation) {
        withContext(Dispatchers.IO) {
        val userModel = userRepository.getLoggedInUserModel()
        val isUserDisabled = userModel?.isDisabled ?: false
            if (isUserDisabled.not()) crossPathLocationRepository.saveCrossPathLocation(location)
    }
    }
}