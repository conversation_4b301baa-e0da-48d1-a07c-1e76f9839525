package com.duaag.android.crosspath.presentation.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.api.ProfileVisitSourceEnum
import com.duaag.android.api.Resource
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.chat.model.ReportBody
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapMatchTypeValues
import com.duaag.android.clevertap.ClevertapReceivedUserPremiumBadgeValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.PremiumBadgeValues
import com.duaag.android.clevertap.SwipeSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.crosspath.presentation.adapter.CrossPathProfilesAdapter
import com.duaag.android.crosspath.presentation.model.AllCrossPathProfilesUiState
import com.duaag.android.crosspath.presentation.model.CrossPathProfilesListModel
import com.duaag.android.crosspath.presentation.viewmodel.AllCrossPathProfilesViewModel
import com.duaag.android.databinding.FragmentAllCrossPathProfilesBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.fragments.InstaChatDialogFragment
import com.duaag.android.home.fragments.MatchScreenMotionLayoutDialogFragment
import com.duaag.android.home.fragments.UserProfileFragment
import com.duaag.android.home.fragments.VerificationInProgressDialog
import com.duaag.android.home.fragments.VerifyYourProfileDialog
import com.duaag.android.home.models.CrossPath
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.models.LimitReachedModel
import com.duaag.android.home.models.LimitReachedScreenSource
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.report.UserReportable
import com.duaag.android.report.viewmodel.ReportViewModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.checkBackgroundLocationPermission
import com.duaag.android.utils.getLocationServicesStatus
import com.duaag.android.utils.getNotificationsOnClevertapValue
import com.duaag.android.utils.isLocationPermissionEnabled
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.updateLocale
import com.yuyakaido.android.cardstackview.Direction
import kotlinx.coroutines.launch
import sendVerifyYourProfileInitiatedAnalyticsEvent
import sendVerifyYourProfilePopupAnalyticsEvent
import javax.inject.Inject

class AllCrossPathProfilesFragment : Fragment(), UserProfileFragment.UserProfileInteract, UserReportable, VerifyYourProfileDialog.VerifyYourProfileDialogListener {

    private var _binding: FragmentAllCrossPathProfilesBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    private val viewModel by viewModels<AllCrossPathProfilesViewModel> { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel>({ requireActivity() as HomeActivity }) { viewModelFactory }
    private val reportViewModel by viewModels<ReportViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private var adapter: CrossPathProfilesAdapter? = null
    private var layoutManager: GridLayoutManager? = null
    private var hasScreenViewBeenSent = false


    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateLocale(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        _binding = FragmentAllCrossPathProfilesBinding.inflate(inflater, container, false)
        initView()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        homeViewModel.showMatchScreen.observe(viewLifecycleOwner) { recommendedUserModel ->
            sendMatchEvent(recommendedUserModel)
            (childFragmentManager.findFragmentByTag("UserProfileFragment") as? UserProfileFragment)?.dismissAllowingStateLoss()
            val dialogFragment = MatchScreenMotionLayoutDialogFragment.newInstance(recommendedUserModel, hasChat = false)
            dialogFragment.show(requireActivity().supportFragmentManager, "MatchScreenMotionLayoutDialogFragment")
            recommendedUserModel.cognitoUserId?.let { cognitoId -> viewModel.deleteItemByUserId(cognitoId) }
        }
        homeViewModel.showMatchScreenIfHasChat.observe(viewLifecycleOwner){
            sendMatchEvent(it.recommendedUserModel)
            (childFragmentManager.findFragmentByTag("UserProfileFragment") as? UserProfileFragment)?.dismissAllowingStateLoss()
            val dialogFragment = MatchScreenMotionLayoutDialogFragment.newInstance(it.recommendedUserModel, hasChat = false)
            dialogFragment.show(requireActivity().supportFragmentManager, "MatchScreenMotionLayoutDialogFragment")
            it.recommendedUserModel.cognitoUserId?.let { cognitoId -> viewModel.deleteItemByUserId(cognitoId) }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { uiState ->
                    handleUiState(uiState)
                }
            }
        }
        reportViewModel.reportUser.observe(viewLifecycleOwner) {
            when (it) {
                is Resource.Success -> {

                    it.data.user?.let { user->
                         homeViewModel.removeUserFromFeaturedProfiles(user)
                        user.cognitoUserId?.let { cognitoUserId -> viewModel.deleteItemByUserId(cognitoUserId) }
                    }
                    homeViewModel.dissmissCardUserProfile()
                    homeViewModel.userProfileProgressBarShow(false)
                }

                is Resource.Error -> {
                    ToastUtil.toast(getString(R.string.smthg_went_wrong))
                    homeViewModel.userProfileProgressBarShow(false)
                    logError(ErrorStatus.REPORT_USER)
                }

                Resource.Loading -> {
                    homeViewModel.userProfileProgressBarShow(true)
                }
            }
        }
    }

    private fun sendMatchEvent(recommendedUserModel: RecommendedUserModel) {
        val eventActivityType = recommendedUserModel.activityType
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

        val matchTypeValue = ClevertapMatchTypeValues.CROSS_PATH.value
        val recommSource = recommendedUserModel.recommSource ?: "dua"
        val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
        val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

        sendClevertapEvent(
            ClevertapEventEnum.MATCH, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.MATCH_TYPE.propertyName to matchTypeValue,
                ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

            )
        )
    }

    private fun handleUiState(uiState: AllCrossPathProfilesUiState) {

        if (uiState.showShimmerItems) {
            adapter?.submitData(shimmerItems)
        } else {
            if (uiState.crossPathItems.isEmpty()) {
                adapter?.submitData(listOf(CrossPathProfilesListModel.Empty(-10)))
                sendCrossPathScreenViewEvent(false)
            } else {
                adapter?.submitData(uiState.crossPathItems)
                sendCrossPathScreenViewEvent(true)
            }
        }
    }


    private fun initView() {
        binding.closeButton.setOnClickListener {
            findNavController().popBackStack()
        }

        // Initialize the adapter
        adapter = CrossPathProfilesAdapter { item ->
            UserProfileFragment.newInstance(
                RecommendedUserModel.getEmptyRecommendedUserModel().copy(
                    cognitoUserId = item.recommendedUserModel.cognitoUserId,
                    profile =RecommendedUserModel.getEmptyRecommendedUserModel().profile.copy(pictureUrl = item.recommendedUserModel.profile.pictureUrl)
                ),
                _isFromHome = true,
                _profileVisitSource = ProfileVisitSourceEnum.CROSS_PATH.source
            ).let { userProfileFragment->
                userProfileFragment.setPictureChangeListener(object :
                    UserProfileFragment.PictureChangeListener {
                    override fun onRightSideClick(index: Int, crossPath: CrossPath?, recommendedUserModel: RecommendedUserModel?) {

                        val hasCurrentUserOnlyOnePicture = homeViewModel.userProfile.value?.hasOnlyOnePicture() == true
                    sendProfileVisitOtherPhotosEvent(hasCurrentUserOnlyOnePicture,crossPath, recommendedUserModel)
                    }

                    override fun onLeftSideClick(index: Int, crossPath: CrossPath?, recommendedUserModel: RecommendedUserModel?) {

                        val hasCurrentUserOnlyOnePicture = homeViewModel.userProfile.value?.hasOnlyOnePicture() == true
                        if(index <= 1) sendProfileVisitOtherPhotosEvent(
                            hasCurrentUserOnlyOnePicture,
                            crossPath,
                            recommendedUserModel
                        )
                    }

                })
                userProfileFragment.show(childFragmentManager, "UserProfileFragment")
            }
       }
        layoutManager = GridLayoutManager(requireContext(),2).apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when(adapter?.getItemViewType(position)){
                        CrossPathProfilesAdapter.EMPTY_VIEW -> 2
                        else -> 1
                    }
                }

            }
        }

        binding.recyclerView.layoutManager = layoutManager
        binding.recyclerView.adapter = adapter

        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                val layoutManager = recyclerView.layoutManager as GridLayoutManager
                val lastVisibleItemPosition = layoutManager.findLastCompletelyVisibleItemPosition()
                val totalItemCount = adapter?.itemCount ?: 0

                if (lastVisibleItemPosition >= totalItemCount - 1 && !viewModel.checkIfLoading() && viewModel.checkIfHasMoreUsersToLoad()) {
                    viewModel.loadCrossPathProfiles()
                }
            }
        })

    }

    fun sendProfileVisitOtherPhotosEvent(
        arePhotosLocked: Boolean,
        crossPath: CrossPath?,
        recommendedUserModel: RecommendedUserModel?
    ) {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userRepository.user.value)
        val interactionReceived = if(crossPath?.counterPartInteraction != "no_interaction") crossPath?.counterPartInteraction else null
        val interactedBefore = if(crossPath?.currentUserInteraction != "no_interaction") crossPath?.currentUserInteraction else null
        val spottedTimes = crossPath?.count
        val spottedLocation = crossPath?.address
        val recommSource = recommendedUserModel?.recommSource ?: "dua"
        val isPhotoHidden = recommendedUserModel?.profile?.hasBlurredPhotos
        val premiumBadge = if(recommendedUserModel?.profile?.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
        val eventSource = ClevertapEventSourceValues.CROSS_PATH.value

        firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_VISIT_OTHER_PHOTOS, mapOf(
            FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
            FirebaseAnalyticsParameterName.ARE_PHOTOS_LOCKED.value to arePhotosLocked,
        ))

        sendClevertapEvent(
            ClevertapEventEnum.PROFILE_VISIT_OTHER_PHOTOS, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                ClevertapEventPropertyEnum.ARE_PHOTOS_LOCKED.propertyName to arePhotosLocked,
                ClevertapEventPropertyEnum.INTERACTION_RECEIVED.propertyName to interactionReceived,
                ClevertapEventPropertyEnum.INTERACTED_BEFORE.propertyName to interactedBefore,
                ClevertapEventPropertyEnum.TIMES_SPOTTED.propertyName to spottedTimes,
                ClevertapEventPropertyEnum.SPOTTED_LOCATION.propertyName to spottedLocation,
                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge
            ))
    }

    private fun sendProfileVisitAnalyticsEvents(
        premiumType: String?,
        eventSource: String?,
        areDetailsLocked: Boolean,
        model: CrossPath?,
        isPhotoHidden: Boolean?,
        premiumBadge: String?
    ) {
        val receivedUserPremiumBadge = if(homeViewModel.userProfile.value?.profile?.showPremiumBadge == true)
            ClevertapReceivedUserPremiumBadgeValues.SHOWN.value
        else
            ClevertapReceivedUserPremiumBadgeValues.HIDDEN.value

        val interactionReceived = if(model?.counterPartInteraction != "no_interaction") model?.counterPartInteraction else null
        val interactedBefore = if(model?.currentUserInteraction != "no_interaction") model?.currentUserInteraction else null
        val spottedTimes = model?.count
        val spottedLocation = model?.address
        val recommSource = "dua"
        sendClevertapEvent(
            ClevertapEventEnum.PROFILE_VISIT, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType,
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                ClevertapEventPropertyEnum.ARE_DETAILS_LOCKED.propertyName to areDetailsLocked,
                ClevertapEventPropertyEnum.INTERACTION_RECEIVED.propertyName to interactionReceived,
                ClevertapEventPropertyEnum.INTERACTED_BEFORE.propertyName to interactedBefore,
                ClevertapEventPropertyEnum.TIMES_SPOTTED.propertyName to spottedTimes,
                ClevertapEventPropertyEnum.SPOTTED_LOCATION.propertyName to spottedLocation,
                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

            )
        )

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.PROFILE_VISIT_CONVERSATION, mapOf(
                FirebaseAnalyticsParameterName.PROFILE_VISIT_CONVERSATION_COUNT.value to 1L
            )
        )
    }
    private fun sendCrossPathScreenViewEvent(
        isCrossPathWithResults: Boolean
    ) {
        if (hasScreenViewBeenSent)
            return

        hasScreenViewBeenSent = true

        val premiumTypeKey = ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName
        val isCrossPathWithResultsKey = ClevertapEventPropertyEnum.IS_CROSS_PATH_WITH_RESULTS.propertyName
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userRepository.user.value)

        sendClevertapEvent(
            ClevertapEventEnum.SPOTTED_IN_REAL_LIFE_SCREENVIEW,
            mapOf(
                premiumTypeKey to eventPremiumType,
                isCrossPathWithResultsKey to isCrossPathWithResults
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        adapter = null
    }
    companion object {
        private val shimmerItems by lazy {
            listOf(
                CrossPathProfilesListModel.Shimmer(-1),
                CrossPathProfilesListModel.Shimmer(-2),
                CrossPathProfilesListModel.Shimmer(-3),
                CrossPathProfilesListModel.Shimmer(-4))
        }
    }
    private fun interactUser(
        interactionType: InteractionType,
        recommendedUserModel: RecommendedUserModel
    ) {
        when (interactionType) {
            InteractionType.LIKE -> {
                if (homeViewModel.remainingLikes()) {
                    val source = SwipeSourceValues.CROSS_PATH
                    val eventActivityType = recommendedUserModel.activityType

                    homeViewModel.swipeCard(recommendedUserModel, InteractionType.LIKE)
                              firebaseLogEvent(
                                  FirebaseAnalyticsEventsName.SWIPE_RIGHT,
                                  mapOf(
                                      FirebaseAnalyticsParameterName.SWIPE_RIGHT_COUNT.value to 1L,
                                      FirebaseAnalyticsParameterName.SOURCE.value to source.value,
                                      FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                  )
                              )

                              val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                              val user = homeViewModel.userProfile.value
                              val locationAccess = getLocationServicesStatus(
                                  requireContext().isLocationPermissionEnabled(),
                                  requireContext().checkBackgroundLocationPermission()
                              )
                              val areNotificationsOn = getNotificationsOnClevertapValue(requireContext(), duaSharedPrefs)
                              val recommSource = recommendedUserModel.recommSource ?: "dua"
                              val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                              val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                    sendClevertapEvent(
                                  ClevertapEventEnum.SWIPE_RIGHT, mapOf(
                                      ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                      ClevertapEventPropertyEnum.DISTANCE.propertyName to recommendedUserModel.profile.distance?.toInt(),
                                      ClevertapEventPropertyEnum.SOURCE.propertyName to source.value,
                                      ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                      ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE.propertyName to user?.profilePercentage,
                                      ClevertapEventPropertyEnum.IS_BADGE2_VERIFIED.propertyName to (user?.badge2 == Badge2Status.APPROVED.status),
                                      ClevertapEventPropertyEnum.LOCATION_ACCESS.propertyName to locationAccess,
                                      ClevertapEventPropertyEnum.ARE_NOTIFICATIONS_ON.propertyName to areNotificationsOn,
                                      ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                      ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                      ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                      ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

                                  )
                              )


                } else {
                    // rewindCard(direction)
                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.LIKE, LimitReachedScreenSource.CROSS_PATH))
                }

            }

            InteractionType.DISLIKE -> {
                if(!homeViewModel.remainingDislikes()){
                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.DISLIKE, LimitReachedScreenSource.CROSS_PATH))
                    return
                }
                val eventActivityType = recommendedUserModel.activityType
                val source = SwipeSourceValues.CROSS_PATH

                homeViewModel.swipeCard(recommendedUserModel, InteractionType.DISLIKE)
                firebaseLogEvent(
                    FirebaseAnalyticsEventsName.SWIPE_LEFT,
                    mapOf(
                        FirebaseAnalyticsParameterName.SWIPE_LEFT_COUNT.value to 1L,
                        FirebaseAnalyticsParameterName.SOURCE.value to source.value,
                        FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                    )

                )

                val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                val gender =
                    if (homeViewModel.userProfile.value?.gender == GenderType.WOMAN.value) {
                        "female"
                    } else {
                        "male"
                    }
                val recommSource = recommendedUserModel.recommSource ?: "dua"
                val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
              sendClevertapEvent(
                    ClevertapEventEnum.SWIPE_LEFT, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.DISTANCE.propertyName to recommendedUserModel.profile.distance?.toInt(),
                        ClevertapEventPropertyEnum.GENDER.propertyName to gender,
                        ClevertapEventPropertyEnum.SOURCE.propertyName to source.value,
                        ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                        ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                        ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                        ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                        ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge
                    )
                )


            }

            InteractionType.INSTA_CHAT -> {

                recommendedUserModel.let {
                    if (recommendedUserModel.cardUserGuideType == null) {
                        if (homeViewModel.badge2 == Badge2Status.APPROVED) {
                            if (!it.isMobAd && !it.isProfileInfo && !it.isProfileActivities) {
                                if (homeViewModel.remainingInstaChatInteractions()) {
                                    val dialog = getInstaChatDialogFragment(
                                        recommendedUserModel,
                                    )
                                    dialog.show(childFragmentManager, "InstaChatDialogFragment")

                                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                                            val eventSourceValue = SwipeSourceValues.CROSS_PATH
                                            val eventActivityType = recommendedUserModel.activityType
                                            val recommSource = recommendedUserModel.recommSource ?: "dua"
                                            val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                                            val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.INITIATE_INSTACHAT, mapOf(
                                                    FirebaseAnalyticsParameterName.INITIATE_INSTACHAT_COUNT.value to 1L,
                                                    FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                                    FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                                )
                                            )
                                            sendClevertapEvent(
                                                ClevertapEventEnum.INSTACHAT_INITIATED,
                                                mapOf(
                                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                                    ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                                    ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                                    ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                                    ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                                    ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                                    ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                                                    ClevertapEventPropertyEnum.VERIFICATION_STATUS.propertyName to homeViewModel.badge2.status
                                                )
                                            )

                                } else {
                                    homeViewModel.setLimitReached(LimitReachedModel(InteractionType.INSTA_CHAT, LimitReachedScreenSource.CROSS_PATH))
                                }
                            } else {

                                if (homeViewModel.badge2 == Badge2Status.APPROVED) {
                                    if (homeViewModel.remainingInstaChatInteractions()) {
                                        val dialog = getInstaChatDialogFragment(
                                            recommendedUserModel,
                                        )
                                        dialog.show(childFragmentManager, "InstaChatDialogFragment")


                                    } else {
                                        homeViewModel.setLimitReached(
                                            LimitReachedModel(
                                                InteractionType.INSTA_CHAT, LimitReachedScreenSource.CROSS_PATH)
                                        )
                                    }
                                } else {
                                    if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                                        VerificationInProgressDialog.showVerificationInProgressDialog(
                                            childFragmentManager
                                        )
                                    } else {
                                        val eventPremiumType =
                                            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                        sendVerifyYourProfilePopupAnalyticsEvent(
                                            eventPremiumType,
                                            ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value
                                        )

                                        VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                            childFragmentManager,
                                            VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT
                                        )
                                    }
                                }

                            }
                        } else {
                            if (homeViewModel.badge2 == Badge2Status.PROCESSING) {
                                VerificationInProgressDialog.showVerificationInProgressDialog(
                                    childFragmentManager
                                )
                            } else {
                                val eventPremiumType =
                                    getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                sendVerifyYourProfilePopupAnalyticsEvent(
                                    eventPremiumType,
                                    ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value
                                )

                                VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                    childFragmentManager,
                                    VerifyYourProfileDialog.VerifyProfileFromEnum.INSTACHAT
                                )
                            }

                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            val eventSourceValue = SwipeSourceValues.CROSS_PATH
                            val eventActivityType = recommendedUserModel.activityType
                            val recommSource = recommendedUserModel.recommSource ?: "dua"
                            val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                            val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value
                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.INITIATE_INSTACHAT, mapOf(
                                    FirebaseAnalyticsParameterName.INITIATE_INSTACHAT_COUNT.value to 1L,
                                    FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                    FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                )
                            )
                            sendClevertapEvent(
                                ClevertapEventEnum.INSTACHAT_INITIATED,
                                mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                    ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                    ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                    ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                    ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                    ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge,
                                    ClevertapEventPropertyEnum.VERIFICATION_STATUS.propertyName to homeViewModel.badge2.status
                                )
                            )
                        }
                    } else {
                        val dialog = getInstaChatDialogFragment(
                            recommendedUserModel,
                        )
                        dialog.isCancelable = false
                        dialog.show(childFragmentManager, "InstaChatDialogFragment")
                    }
                }
            }

            else -> {}
        }
    }
    private fun getInstaChatDialogFragment(recommendedUserModel: RecommendedUserModel): InstaChatDialogFragment {
        val dialog = InstaChatDialogFragment.newInstance()
        val arguments = Bundle()
        arguments.putString(InstaChatDialogFragment.USER_NAME, recommendedUserModel.firstName)
        if (recommendedUserModel.cardUserGuideType != null) {
            arguments.putString(
                InstaChatDialogFragment.USER_GUIDE_TEXT,
                getString(R.string.instachat_user_guide_text,"72")
            )
        }
        dialog.arguments = arguments
        dialog.listener = object : InstaChatDialogFragment.InstaChatDialogListener {
            override fun onSendButtonClicked(dialog: InstaChatDialogFragment, message: String?) {
                dialog.dismissAllowingStateLoss()
                if (recommendedUserModel.cardUserGuideType == null) {
                    homeViewModel.swipeCard(
                        recommendedUserModel,
                        InteractionType.INSTA_CHAT,
                        message,
                        swipeSource = SwipeSourceValues.CROSS_PATH
                    )
                }
            }
        }
        return dialog
    }

    override fun onUserInteracted(
        direction: Direction,
        user: RecommendedUserModel,
        isFeaturedProfile: Boolean
    ) {
        val interactionType = when(direction){
            Direction.Right -> InteractionType.LIKE
            Direction.Left -> InteractionType.DISLIKE
            Direction.Top -> InteractionType.INSTA_CHAT
            else -> null
        }

        interactionType?.let {
            if(it == InteractionType.DISLIKE){
                user.cognitoUserId?.let { viewModel.deleteItemByUserId(it) }
            }
            interactUser(it, user)
        }
    }

    override fun reportUser(reportBody: ReportBody, user: RecommendedUserModel) {
        reportViewModel.reportUser(reportBody,user)
    }

    override fun onVerifyYouProfileClicked(
        openedFrom: VerifyYourProfileDialog.VerifyProfileFromEnum,
        eventName: String?
    ) {
        val eventPremiumType =
            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

        sendVerifyYourProfileInitiatedAnalyticsEvent(
            ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value,
            eventPremiumType
        )

        findNavController().navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,
            bundleOf(EVENT_SOURCE to ClevertapVerificationSourceValues.TO_SEND_INSTACHAT.value)
        )
    }
}
