package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.crosspath.domain.model.CrossPathLocation
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class GetLocationsUseCase @Inject constructor(private val crossPathLocationRepository: CrossPathLocationRepository) {
    operator fun invoke(): Flow<List<CrossPathLocation>> = crossPathLocationRepository.getCrossPathLocations()
}