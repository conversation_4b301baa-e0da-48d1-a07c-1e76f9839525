package com.duaag.android.crosspath.presentation.model

data class GroupedCrossPathUiModel(
        val clusterDistance: Double,
        val clusters: List<ClusterUiModel>?,
        val cognitoUserId: String,
        val count: Int?,
        val currentLocation: CrossPathLocation,
)

data class ClusterUiModel(
        val hasPendingInteraction: Boolean,
        val location: CrossPathLocation,
        val users: List<String>
) {
    fun getTag(): String {
        return "${location.latitude}-${location.longitude}-${users.size}"
    }
}