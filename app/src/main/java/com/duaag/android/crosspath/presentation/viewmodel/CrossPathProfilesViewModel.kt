package com.duaag.android.crosspath.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.ResourceV2
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import com.duaag.android.crosspath.presentation.model.CrossPathProfileUiState
import com.duaag.android.crosspath.presentation.model.CrossPathProfilesBottomSheetFragmentArgs
import com.duaag.android.crosspath.presentation.model.CrossPathProfilesListModel
import com.duaag.android.sunny_hill.domain.use_case.GetSunnyHillAttendeesUseCase
import com.duaag.android.utils.sunnyhill.SunnyHillUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

class CrossPathProfilesViewModel @Inject constructor(
    private val crossPathLocationRepository: CrossPathLocationRepository,
    private val getSunnyHillAttendeesUseCase: GetSunnyHillAttendeesUseCase
): ViewModel() {

    private val _uiState : MutableStateFlow<CrossPathProfileUiState> = MutableStateFlow(CrossPathProfileUiState.ShimmerView)
    val uiState: StateFlow<CrossPathProfileUiState>
        get() = _uiState.asStateFlow()

    private val usersIDs = mutableListOf<String>()
    private var args: CrossPathProfilesBottomSheetFragmentArgs? = null
    private var _isLoading = false
    private var nearby: String = ""
    private val _items: MutableList<CrossPathProfilesListModel> = mutableListOf()

    //Sunny Hill
    private var nextCursor: String? = null
    private var isLastPage = false

    fun init(args: CrossPathProfilesBottomSheetFragmentArgs?) {
        this.args = args
        usersIDs.addAll(args?.users ?: emptyList())
        if(args?.isSunnyHillMode == true) getSunnyHillAttendees() else getCrossPathProfiles()
    }
    fun getCrossPathProfiles() {
        _isLoading = true
        val users = usersIDs.take(10)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val result = crossPathLocationRepository.getCrossPathUsers(users, args?.latitude ?: 0.0 , args?.longitude ?: 0.0 )

                when(result) {
                    is ResourceV2.Success -> {
                        usersIDs.removeAll(users)
                        nearby = result.data.clusterAddress
                        _items.addAll(result.data.users.map { CrossPathProfilesListModel.Item(it)})
                        _uiState.emit(CrossPathProfileUiState.Success(_items.toList()))
                        _isLoading = false
                    }
                     is ResourceV2.Error -> {
                        _isLoading = false
                    }
                }
            } catch (e: Exception) {
               e.stackTrace
                _isLoading = false
            }
        }
    }

    fun getSunnyHillAttendees(limit: Int = 20) {
        viewModelScope.launch {
            _isLoading = true

            val result = getSunnyHillAttendeesUseCase(limit, nextCursor)
            when (result) {
                is ResourceV2.Success -> {
                    nearby = SunnyHillUtils.NAME
                    _items.addAll(result.data.attendees.map { CrossPathProfilesListModel.Item(it)})
                    <EMAIL> = result.data.nextCursor?.toString()
                    isLastPage = <EMAIL> == null
                    _uiState.emit(CrossPathProfileUiState.Success(_items.toList()))
                }
                is ResourceV2.Error -> {
                }
            }
            _isLoading = false
        }
    }

    fun checkIfLoading() = _isLoading
    fun checkIfHasMoreUsersToLoad(sunnyHillMode: Boolean): Boolean {
      return if(!sunnyHillMode) usersIDs.isNotEmpty() else !isLastPage
    }

    fun getNearby(): String = nearby

    fun deleteItemByUserId(userId: String) {
        viewModelScope.launch {
            _items.removeAll { it is CrossPathProfilesListModel.Item && it.recommendedUserModel.cognitoUserId == userId }
            if (_items.isEmpty()) {
                _uiState.emit(CrossPathProfileUiState.ListIsEmpty)
            } else {
                _uiState.emit(CrossPathProfileUiState.Success(_items.toList()))
            }
        }
    }

}