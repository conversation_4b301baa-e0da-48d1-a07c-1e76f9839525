package com.duaag.android.crosspath.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.ResourceV2
import com.duaag.android.crosspath.domain.usecase.GetGroupedCrossPathsAPIUseCase
import com.duaag.android.crosspath.presentation.model.CrossPathFullScreenData
import com.duaag.android.crosspath.presentation.model.CrossPathFullScreenUiState
import com.duaag.android.crosspath.presentation.model.toGroupedCrossPathUiModel
import com.duaag.android.sunny_hill.domain.use_case.GetSunnyHillAttendeesCountUseCase
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class CrossPathFullScreenViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val getGroupedCrossPathsAPIUseCase: GetGroupedCrossPathsAPIUseCase,
    private val getSunnyHillAttendeesCountUseCase: GetSunnyHillAttendeesCountUseCase
) : ViewModel() {

    val user = userRepository.userFlow()

    private val _uiState: MutableStateFlow<CrossPathFullScreenUiState> = MutableStateFlow(CrossPathFullScreenUiState())
    val uiState: StateFlow<CrossPathFullScreenUiState>
        get() = _uiState.asStateFlow()

    init {
        viewModelScope.launch {
           val isAttendingSunnyHill = user.map { it.userAuxiliaryData?.isAttendingSunnyHill }.firstOrNull()
            _uiState.update {
                it.copy(isAttendingSunnyHill = isAttendingSunnyHill)
            }
        }
    }

    fun getGroupedCrossPaths(distance: Double, latitude: Double, longitude: Double, isScale: Boolean) {
        viewModelScope.launch(
                CoroutineExceptionHandler { coroutineContext, throwable ->
                    throwable.printStackTrace()
                }
        ) {
            _uiState.update { crossPathUiState ->
                crossPathUiState.copy(
                        isLoadingGroupedPins = true
                )
            }

            val result = getGroupedCrossPathsAPIUseCase.invoke(distance, latitude, longitude)
            when (result) {
                is ResourceV2.Success -> {
                    _uiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                                groupedPins = result.data.toGroupedCrossPathUiModel(),
                                isLoadingGroupedPins = false,
                                isScale = isScale
                        )
                    }
                    Timber.tag("CROSS_PATHS").d("grouped success result ${result.data}")
                }

                is ResourceV2.Error -> {
                    _uiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                                isLoadingGroupedPins = false,
                                isScale = isScale
                        )
                    }
                    Timber.tag("CROSS_PATHS").d("grouped fail result ${result.message}")
                }
            }
        }
    }

    fun getAttendeeCount() {
        viewModelScope.launch {
            val result = getSunnyHillAttendeesCountUseCase.invoke()
            when (result) {
                is ResourceV2.Success -> {
                    _uiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                            attendeeCount = result.data.counterValue.toString()
                        )
                    }
                    Timber.tag("CROSS_PATHS").d("getCounterAPIUseCase ${result.data}")
                }

                is ResourceV2.Error -> {
                    Timber.tag("CROSS_PATHS").d("getCounterAPIUseCase ${result.message}")
                }
            }
        }
    }

    fun removeCrossPath(cognitoId: String) {
        _uiState.update { currentState ->
            val updatedClusters = currentState.groupedPins?.clusters?.map { cluster ->
                val filteredUsers = cluster.users.filterNot { it == cognitoId }
                cluster.copy(users = filteredUsers)
            }?.filter { it.users.isNotEmpty() }

            val updatedGroupedPins = currentState.groupedPins?.copy(clusters = updatedClusters)

            currentState.copy(groupedPins = updatedGroupedPins)
        }
    }

    fun setCrossPathData(data: CrossPathFullScreenData?) {
        _uiState.update {
            it.copy(crossPathStaticData = data)
        }
    }

    fun increaseAttendeeCount() {
        try {
            val count = uiState.value.attendeeCount
            if(!count.isNullOrEmpty()) {
                val increasedCount = count.toInt().plus(1).toString()
                _uiState.update {
                    it.copy(attendeeCount = increasedCount)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}