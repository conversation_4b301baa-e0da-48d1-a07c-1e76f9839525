package com.duaag.android.crosspath.presentation.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.crosspath.presentation.model.DiscoveredProfilesUiModel
import com.duaag.android.databinding.CrossPathViewAllItemBinding
import com.duaag.android.databinding.DiscoveredCrossPathItemBinding
import com.duaag.android.databinding.DiscoveredCrossPathsErrorItemBinding
import com.duaag.android.databinding.DiscoveredCrossPathsLoadingItemBinding
import com.duaag.android.databinding.DiscoveredProfileShimmerItemBinding
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.utils.imageUrlCircleCrop
import com.duaag.android.utils.setOnSingleClickListener


class CrossPathAdapter(
    private val userClickListener: (DiscoveredProfilesUiModel) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val differ: AsyncListDiffer<DiscoveredProfilesUiModel> = AsyncListDiffer(this, object : DiffUtil.ItemCallback<DiscoveredProfilesUiModel>() {
        override fun areItemsTheSame(oldItem: DiscoveredProfilesUiModel, newItem: DiscoveredProfilesUiModel): Boolean {
            return if (oldItem is DiscoveredProfilesUiModel.Item && newItem is DiscoveredProfilesUiModel.Item) {
                oldItem.id == newItem.id
            } else if (oldItem is DiscoveredProfilesUiModel.Shimmer && newItem is DiscoveredProfilesUiModel.Shimmer) {
                oldItem.id == newItem.id
            } else if (oldItem is DiscoveredProfilesUiModel.Error && newItem is DiscoveredProfilesUiModel.Error) {
                true
            } else if (oldItem is DiscoveredProfilesUiModel.ViewAll && newItem is DiscoveredProfilesUiModel.ViewAll) {
                true
            } else if (oldItem is DiscoveredProfilesUiModel.Loading && newItem is DiscoveredProfilesUiModel.Loading) {
                true
            } else {
                false
            }
        }

        override fun areContentsTheSame(oldItem: DiscoveredProfilesUiModel, newItem: DiscoveredProfilesUiModel): Boolean {
            return oldItem == newItem
        }
    })

    fun submitData(list: List<DiscoveredProfilesUiModel>) {
        differ.submitList(list)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_VIEW -> CrossPathViewHolder(DiscoveredCrossPathItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            LOADING_VIEW -> LoadingViewHolder(DiscoveredCrossPathsLoadingItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            SHIMMER_ITEM -> ShimmerViewHolder(DiscoveredProfileShimmerItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            ERROR_ITEM -> ErrorViewHolder(DiscoveredCrossPathsErrorItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            VIEW_ALL -> ViewAllHolder(CrossPathViewAllItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            ITEM_VIEW -> (holder as CrossPathViewHolder).bind(differ.currentList[position] as DiscoveredProfilesUiModel.Item)
            LOADING_VIEW -> (holder as LoadingViewHolder)
            SHIMMER_ITEM -> (holder as ShimmerViewHolder)
            ERROR_ITEM -> (holder as ErrorViewHolder)
            VIEW_ALL -> (holder as ViewAllHolder)
            else -> {}
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (differ.currentList[position]) {
            is DiscoveredProfilesUiModel.Item -> ITEM_VIEW
            is DiscoveredProfilesUiModel.Loading -> LOADING_VIEW
            is DiscoveredProfilesUiModel.Error -> ERROR_ITEM
            is DiscoveredProfilesUiModel.Shimmer -> SHIMMER_ITEM
            is DiscoveredProfilesUiModel.ViewAll -> VIEW_ALL
        }
    }

    override fun getItemCount(): Int = differ.currentList.size

    inner class CrossPathViewHolder(private val binding: DiscoveredCrossPathItemBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnSingleClickListener{
                (differ.currentList[bindingAdapterPosition] as? DiscoveredProfilesUiModel.Item)?.let{
                    userClickListener(it)
                }
            }
        }

        fun bind(model: DiscoveredProfilesUiModel.Item) {
            binding.userName.text = model.profile?.firstName
            imageUrlCircleCrop(binding.userImage, model.profile?.thumbnailUrl)
            when(model.counterPartInteraction) {
                "like" -> binding.interactionIcon.setImageResource(R.drawable.cross_path_like_icon)
                "instachat" -> binding.interactionIcon.setImageResource(R.drawable.cross_path_instachat_icon)
                else -> binding.interactionIcon.setImageBitmap(null)
            }
            when(model.profile?.badge2) {
                Badge2Status.APPROVED.status -> binding.badgeIcon.visibility = View.VISIBLE
                else -> binding.badgeIcon.visibility = View.GONE
            }
        }
    }

    private class LoadingViewHolder(binding: DiscoveredCrossPathsLoadingItemBinding) : RecyclerView.ViewHolder(binding.root)
    private class ShimmerViewHolder(binding: DiscoveredProfileShimmerItemBinding) : RecyclerView.ViewHolder(binding.root)
    private class ErrorViewHolder(binding: DiscoveredCrossPathsErrorItemBinding) : RecyclerView.ViewHolder(binding.root)
    private inner class ViewAllHolder(binding: CrossPathViewAllItemBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnSingleClickListener {
                (differ.currentList[bindingAdapterPosition] as? DiscoveredProfilesUiModel.ViewAll)?.let{
                    userClickListener(it)
                }
            }

        }
    }

    companion object {
        private const val ITEM_VIEW = 1
        private const val LOADING_VIEW = 2
        private const val SHIMMER_ITEM = 3
        private const val ERROR_ITEM = 4
        private const val VIEW_ALL = 5
    }
}