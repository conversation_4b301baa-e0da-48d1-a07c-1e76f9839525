package com.duaag.android.crosspath.presentation.model

data class CrossPathFullScreenUiState(
        val groupedPins: GroupedCrossPathUiModel? = null,
        val isLoadingGroupedPins: Boolean = false,
        val crossPathStaticData: CrossPathFullScreenData? = null,
        val attendeeCount: String? = null,
        val isAttendingSunnyHill: Boolean? = null,
        val isScale: Boolean = false,
        val error: Exception? = null,

)