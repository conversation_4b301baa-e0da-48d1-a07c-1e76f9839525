package com.duaag.android.crosspath.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.ResourceV2
import com.duaag.android.crosspath.domain.usecase.GetCrossPathsAPIUseCase
import com.duaag.android.crosspath.presentation.model.AllCrossPathProfilesUiState
import com.duaag.android.crosspath.presentation.model.CrossPathProfilesListModel
import com.duaag.android.crosspath.presentation.model.toListOfRecommendedUserModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class AllCrossPathProfilesViewModel @Inject constructor(
    private val getCrossPathsAPIUseCase: GetCrossPathsAPIUseCase
) : ViewModel() {

    private val _uiState: MutableStateFlow<AllCrossPathProfilesUiState> = MutableStateFlow(AllCrossPathProfilesUiState(showShimmerItems = true))
    val uiState: StateFlow<AllCrossPathProfilesUiState>
        get() = _uiState.asStateFlow()

     init {
        loadCrossPathProfiles()
    }
    fun loadCrossPathProfiles() {
        viewModelScope.launch(
            CoroutineExceptionHandler { coroutineContext, throwable ->
                throwable.printStackTrace()
            }
        ) {
            _uiState.update { crossPathUiState ->
                crossPathUiState.copy(
                    isLoading = true
                )
            }
            val result = getCrossPathsAPIUseCase.invoke(_uiState.value.pagingData.nextCursor,_uiState.value.pagingData.limit)
            when (result) {
                is ResourceV2.Success -> {
                    _uiState.update { crossPathUiState ->
                        val currentProfiles = crossPathUiState.crossPathItems
                        val newItems = result.data.crossPaths?.toListOfRecommendedUserModel()?.map {
                            CrossPathProfilesListModel.Item(it)} ?: emptyList()

                        val uiItems = currentProfiles + newItems
                        crossPathUiState.copy(
                            crossPathItems = uiItems,
                            pagingData = AllCrossPathProfilesUiState.PagingData(nextCursor = result.data.nextCursor),
                            isLoading = false,
                            showShimmerItems = false
                        )
                    }
                }

                is ResourceV2.Error -> {
                    _uiState.update { crossPathUiState ->
                        crossPathUiState.copy(
                            isLoading = false,
                            showShimmerItems = false
                        )
                    }
                }
            }
        }
    }

    fun checkIfLoading() = _uiState.value.isLoading
    fun checkIfHasMoreUsersToLoad() = _uiState.value.pagingData.nextCursor != null

    fun deleteItemByUserId(userId: String) {
        viewModelScope.launch {
            val _items = _uiState.value.crossPathItems.toMutableList()
            _items.removeAll { it is CrossPathProfilesListModel.Item && it.recommendedUserModel.cognitoUserId == userId }
            _uiState.update { crossPathUiState ->
                crossPathUiState.copy(
                    crossPathItems = _items
                )
            }
        }
    }
}