package com.duaag.android.crosspath.domain.model

import com.duaag.android.crosspath.data.local.CrossPathLocationEntity

data class CrossPathLocation(
    val longitude: Double,
    val latitude: Double,
    val timestamp: Long,
    val duration: Long = 0,
    val visitCount: Int = 0,
    val id: Int? = null
)

fun CrossPathLocation.toEntity(): CrossPathLocationEntity {
    return CrossPathLocationEntity(
        id = id ?: 0,
        longitude = longitude,
        latitude = latitude,
        timestamp = timestamp,
        duration = duration,
        visitCount = visitCount
    )
}
