package com.duaag.android.crosspath.domain.usecase

import com.duaag.android.api.ResourceV2
import com.duaag.android.crosspath.domain.model.CrossPathsModel
import com.duaag.android.crosspath.domain.model.GroupedCrossPathsModel
import com.duaag.android.crosspath.domain.repository.CrossPathLocationRepository
import com.duaag.android.user.UserRepository
import javax.inject.Inject

class GetGroupedCrossPathsAPIUseCase @Inject constructor(
    private val contactsRepository: CrossPathLocationRepository,
    private val userRepository: UserRepository) {
    suspend operator fun invoke(distance: Double, latitude: Double, longitude: Double): ResourceV2<GroupedCrossPathsModel> {
        val user = userRepository.user.value
        if(user?.isDisabled == true) return ResourceV2.Error("User is disabled")
        return contactsRepository.getGroupedCrossPaths(distance, latitude, longitude)
    }
}