package com.duaag.android.crosspath.domain.model

import android.location.Location
import kotlin.math.abs

fun removeCloseLocations(locations: List<CrossPathLocation>): List<CrossPathLocation> {
    if (locations.isEmpty()) return locations

    val filteredLocations = mutableListOf<CrossPathLocation>()
    val currentTime = System.currentTimeMillis()
    val twoHoursInMillis = 2 * 60 * 60 * 1000L

    val sortedLocations = locations.sortedBy { it.timestamp }

    for (location in sortedLocations) {
        if (currentTime - location.timestamp > twoHoursInMillis) continue

        if (filteredLocations.isEmpty() || !isCloseInTimeAndSpace(filteredLocations.last(), location)) {
            filteredLocations.add(location)
        }
    }

    return filteredLocations
}

fun isCloseInTimeAndSpace(loc1: CrossPathLocation, loc2: CrossPathLocation): Boolean {
    val timeDifference = abs(loc2.timestamp - loc1.timestamp)
    val distance = calculateDistance(loc1.latitude, loc1.longitude, loc2.latitude, loc2.longitude)

    return timeDifference <= 30_000 && distance <= 1.0
}

fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Float {
    val results = FloatArray(1)
    Location.distanceBetween(lat1, lon1, lat2, lon2, results)
    return results[0]
}