package com.duaag.android.crosspath.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class CrossPathsModel(
    val crossPaths: List<CrossPathModel>?,
    val nextCursor: Long?
)

data class CrossPathModel(
    val address: String?,
    val cognitoUserId: String,
    val count: Int?,
    val counterPartInteraction: String?,
    val currentUserInteraction: String?,
    val id: Long,
    val location: LocationModel?,
    val profile: ProfileModel?,
    val timestamp: String?
)

@Parcelize
data class LocationModel(
    val latitude: Double?,
    val longitude: Double?
): Parcelable

data class ProfileModel(
    val badge2: String?,
    val firstName: String?,
    val thumbnailUrl: String?,
    val pictureUrl: String?,
    val age: Int?
)