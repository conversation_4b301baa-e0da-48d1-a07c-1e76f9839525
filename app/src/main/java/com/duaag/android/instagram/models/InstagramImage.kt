package com.duaag.android.instagram.models

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize


@Keep
@Parcelize
data class InstagramMediaResponse(
		@SerializedName("media")
		val media: Media?
) : Parcelable

@Keep
@Parcelize
data class Media(
		@SerializedName("data")
		val `data`: List<InstagramMediaModel>?,
		@SerializedName("paging")
		val paging: Paging?
) : Parcelable

@Keep
@Parcelize
data class InstagramMediaModel(
		@SerializedName("id")
		val id: String?,
		@SerializedName("media_type")
		val mediaType: String?,
		@SerializedName("media_url")
		val mediaUrl: String?,
		@SerializedName("timestamp")
		val timestamp: String?,
		@SerializedName("username")
		val username: String?
) : Parcelable

@Keep
@Parcelize
data class Paging(
		@SerializedName("cursors")
		val cursors: Cursors?,
		@SerializedName("next")
		val next: String?,
		@SerializedName("previous")
		val previous: String?
) : Parcelable

@Keep
@Parcelize
data class Cursors(
		@SerializedName("after")
		val after: String?,
		@SerializedName("before")
		val before: String?
) : Parcelable