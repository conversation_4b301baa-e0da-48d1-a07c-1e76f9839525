package com.duaag.android.instagram

import android.app.Dialog
import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.view.WindowManager
import android.webkit.CookieManager
import android.webkit.WebView
import android.webkit.WebViewClient
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.google.android.material.appbar.MaterialToolbar
import timber.log.Timber
import java.net.URLDecoder


class InstagramAuthenticationDialog(context: Context, private val listener: AuthenticationListener) : Dialog(context, R.style.full_screen_dialog) {
    companion object {
        private const val redirectURL = BuildConfig.INSTAGRAM_REDIRECT_URI
        private const val baseUrl = "https://api.instagram.com/"
        private const val appId = BuildConfig.INSTAGRAM_APP_ID
        private const val scopes = "user_profile,user_media"
        private const val responseType = "code"
    }

    private val requestUrl: String = "${baseUrl}oauth/authorize/?app_id=$appId&redirect_uri=$redirectURL&scope=$scopes&response_type=$responseType"


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.instagram_auth_dialog)
        findViewById<MaterialToolbar>(R.id.toolbar).setNavigationOnClickListener { dismiss() }
        window!!.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT)
        initializeWebView()
    }


    private fun initializeWebView() {
        val webView = findViewById<WebView>(R.id.webView)

        CookieManager.getInstance().removeAllCookies(null)
        webView.clearCache(true)
        webView.clearHistory()

        webView.settings.javaScriptEnabled = true
        webView.loadUrl(requestUrl)
        webView.webViewClient = webViewClient
    }

    var webViewClient: WebViewClient = object : WebViewClient() {
        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            if (url.startsWith(redirectURL)) {
                dismiss()
                return true
            }
            return false
        }

        override fun onPageFinished(view: WebView, url: String) {
            super.onPageFinished(view, url)
            val decodedUrl = URLDecoder.decode(url, "UTF-8")
            if (decodedUrl.contains("${redirectURL}?code=")) {
                val uri = Uri.parse(url)
                val uParameter = uri.getQueryParameter("u")
                val code = uParameter?.substringAfter("${redirectURL}?code=")?.substringBefore("#_")
                if (code != null) {
                    Timber.tag("access_token").e(code)
                    listener.onTokenReceived(code)
                    dismiss()
                }
            } else if (url.contains("?error")) {
                Timber.tag("access_token").e("getting error fetching access token")
                dismiss()
            }
        }
    }
}