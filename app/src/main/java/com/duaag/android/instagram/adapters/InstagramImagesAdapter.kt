package com.duaag.android.instagram.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.InstagramImageCardItemBinding
import com.duaag.android.databinding.InstagramImageEditItemBinding
import com.duaag.android.databinding.InstagramImageItemBinding
import com.duaag.android.instagram.models.InstagramMediaModel

class InstagramImagesAdapter(val viewType: Int, val listener: InstagramImageClickListener) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        const val PROFILE_VIEW = 0
        const val CARD_VIEW = 1
        const val EDIT_PROFILE_VIEW = 2
    }

    private var items: ArrayList<InstagramMediaModel> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            PROFILE_VIEW -> {
                val binding: InstagramImageItemBinding = InstagramImageItemBinding.inflate(layoutInflater, parent, false)
                ProfileViewHolder(binding)
            }
            EDIT_PROFILE_VIEW -> {
                val binding: InstagramImageEditItemBinding = InstagramImageEditItemBinding.inflate(layoutInflater, parent, false)
                EditProfileViewHolder(binding)
            }
            else -> {
                val binding: InstagramImageCardItemBinding = InstagramImageCardItemBinding.inflate(layoutInflater, parent, false)
                CardViewHolder(binding)
            }
        }
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (viewType) {
            PROFILE_VIEW -> (holder as ProfileViewHolder).bind(items[position])
            CARD_VIEW -> (holder as CardViewHolder).bind(items[position])
            else -> (holder as EditProfileViewHolder).bind(items[position])
        }
    }

    override fun getItemViewType(position: Int): Int {
        return viewType
    }

    fun getData(): List<InstagramMediaModel> {
        return items
    }

    fun setData(newItems: List<InstagramMediaModel>) {
        val diffCallback = InstagramImageDiffCallback(items, newItems)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        items.clear()
        items.addAll(newItems)
        diffResult.dispatchUpdatesTo(this)
    }

    inner class ProfileViewHolder(val binding: InstagramImageItemBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickListener {
                listener.onImageClick(items.map { it.mediaUrl!! }, bindingAdapterPosition)
            }
        }

        fun bind(model: InstagramMediaModel) {
            binding.model = model
            binding.executePendingBindings()
        }
    }

    inner class EditProfileViewHolder(val binding: InstagramImageEditItemBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickListener {
                listener.onImageClick(items.map { it.mediaUrl!! }, bindingAdapterPosition)
            }
        }

        fun bind(model: InstagramMediaModel) {
            binding.model = model
            binding.executePendingBindings()
        }
    }

    inner class CardViewHolder(val binding: InstagramImageCardItemBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            itemView.setOnClickListener {
                listener.onImageClick(items.map { it.mediaUrl!! }, bindingAdapterPosition)
            }
        }
        fun bind(model: InstagramMediaModel) {
            binding.model = model
            binding.executePendingBindings()
        }
    }


    class InstagramImageDiffCallback(private val oldList: List<InstagramMediaModel>, private val newList: List<InstagramMediaModel>) : DiffUtil.Callback() {

        override fun getOldListSize() = oldList.size

        override fun getNewListSize() = newList.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }
    }

    interface InstagramImageClickListener {
        fun onImageClick(images: List<String>, index: Int)
    }
}