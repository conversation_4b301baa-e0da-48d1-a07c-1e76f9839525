package com.duaag.android.instagram.models

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class InstagramErrorBody(
    @SerializedName("type")
    val type: String?
)

enum class InstagramErrorType(val value: String) {
    CONNECTING_INSTAGRAM("connecting_instagram"),
    INSTAGRAM_NOT_FOUND("instagram_of_user_not_found"),
    GETTING_MEDIA("getting_media"),
}