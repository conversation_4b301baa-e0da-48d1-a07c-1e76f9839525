package com.duaag.android.recommender.domain.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.duaag.android.home.models.RecommendedUsersActivityStatus
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class FeaturedProfileModel(
    val age: Int,
    val badge2: String?,
    val cognitoUserId: String,
    val firstName: String,
    val gender: String,
    val hasBadge1: Boolean = false,
    val id: Int,
    val instagramStatus: String?,
    val isBlurred: Boolean = false,
    val isPremium: Boolean = false,
    val isSpotlight: Boolean = false,
    val mode: String?,
    val profile: ProfileModel,
    val tags: List<TagModel>? = emptyList(),
    val zodiac: String?,
    val activityType: RecommendedUsersActivityStatus?,
): Parcelable

@Keep
@Parcelize
data class ProfileModel(
    val actualAddress: String?,
    val address: String?,
    val blurredProfileUrl: String?,
    val description: String?,
    val distance: Double?,
    val educations: String?,
    val hobbies: String?,
    val jobs: String?,
    val languages: String?,
    val pictureUrl: String?,
    val pictures: List<PictureModel>? = emptyList(),
    val showPremiumBadge: Boolean? = false,
): Parcelable

@Keep
@Parcelize
data class TagModel(
    val id: Int,
    val tagItemId: Int,
    val tagTypeId: Int
): Parcelable

@Keep
@Parcelize
data class PictureModel(
    val position: Int,
    val url: String
): Parcelable