package com.duaag.android.recommender.data.dto

import androidx.annotation.Keep
import com.duaag.android.home.models.RecommendedUserModel
import com.google.gson.annotations.SerializedName

@Keep
data class UserRecommendationsDto(
    @SerializedName("cards")
    val cards: List<RecommendedUserModel>?,
    @SerializedName("featuredProfiles")
    val featuredProfiles: List<FeaturedProfileDto>?,
    @SerializedName("RMOD")
    val rmod: RMODDto?
)