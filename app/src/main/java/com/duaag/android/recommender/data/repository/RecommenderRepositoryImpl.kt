package com.duaag.android.recommender.data.repository

import com.duaag.android.api.InteractionErrorBody
import com.duaag.android.api.InteractionErrorType
import com.duaag.android.api.RecommendationUserService
import com.duaag.android.api.Resource
import com.duaag.android.exceptions.LimitReachedException
import com.duaag.android.recommender.data.mapper.toUserRecommendationsModel
import com.duaag.android.recommender.domain.model.UserRecommendationsModel
import com.duaag.android.recommender.domain.repository.RecommenderRepository
import com.google.gson.Gson
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Named

class RecommenderRepositoryImpl @Inject constructor(
  @Named("recommendationUser") private val recommendationUserService: RecommendationUserService
  ): RecommenderRepository {

  override fun getCardsAndFeaturedProfiles(fetchCards: Boolean, fetchFeaturedProfiles: Boolean): Flow<Resource<UserRecommendationsModel>> = flow {
    emit(Resource.Loading)
    try {
      val response = recommendationUserService.getCardsAndFeaturedProfiles()
      if (response.isSuccessful) {
        val recommendedUsersId = response.headers()["x-recomm-id"]
        val recommendedUsersSource = response.headers()["x-recomm-source"]
        val body = response.body()!!
        val updatedBody = body.copy(cards = body.cards?.map { it.copy(recommId = recommendedUsersId, recommSource = recommendedUsersSource) })
        emit(Resource.Success(updatedBody.toUserRecommendationsModel()))
      }
      else {
        when(response.code()){
          403 -> {
            Timber.tag("USERS_FOUND").d("403")
            val error = response.errorBody()!!.string()
            val errorObject = Gson().fromJson<InteractionErrorBody>(
              error,
              InteractionErrorBody::class.java
            )
            when (errorObject.type) {
              InteractionErrorType.INSTA_CHAT.value -> {
                throw LimitReachedException(InteractionErrorType.INSTA_CHAT)
              }
              InteractionErrorType.LIKE_DISLIKE.value -> {
                throw LimitReachedException(InteractionErrorType.LIKE_DISLIKE)
              }
              else -> throw Exception("Error")
            }
          }
        }
      }
    } catch (e: Exception) {
      e.printStackTrace()
      throw e
    }
  }

  override fun setRmodSeen(): Flow<Resource<Unit>> = flow {
    emit(Resource.Loading)
    try {
      val response = recommendationUserService.setRmodSeen()
      if (response.isSuccessful) {
        emit(Resource.Success(Unit))
      } else {
        val error = response.errorBody()!!.string()
        throw Exception(error)
      }
    } catch (e: Exception) {
      e.printStackTrace()
      throw e
    }
  }
}