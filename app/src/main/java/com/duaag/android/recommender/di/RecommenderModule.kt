package com.duaag.android.recommender.di

import com.duaag.android.recommender.data.repository.RecommenderRepositoryImpl
import com.duaag.android.recommender.domain.repository.RecommenderRepository
import dagger.Module
import dagger.Provides
import javax.inject.Singleton

@Module
class RecommenderModule {

    @Provides
    @Singleton
    fun provideFeaturedProfileRepository(featuredProfilesRepositoryImpl: RecommenderRepositoryImpl): RecommenderRepository {
        return featuredProfilesRepositoryImpl
    }
}