package com.duaag.android.recommender.presentation

import androidx.annotation.Keep
import com.duaag.android.exceptions.NoConnectivityException
import com.duaag.android.recommender.domain.model.FeaturedProfileModel

@Keep
data class FeaturedProfilesState(
    val list: List<FeaturedProfileModel> = emptyList(),
    val isLoading: Boolean = false,
    val userProfile: com.duaag.android.base.models.Profile? = null,
    val error: Exception? = null
) {
    val isNetworkDisconnected: Boolean = error?.let { it.cause is NoConnectivityException } ?: false
}
