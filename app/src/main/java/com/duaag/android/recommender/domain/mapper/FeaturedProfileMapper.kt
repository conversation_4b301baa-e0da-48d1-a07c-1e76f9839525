package com.duaag.android.recommender.domain.mapper

import com.duaag.android.base.models.INSTAGRAM_STATUS_DISCONNECTED
import com.duaag.android.base.models.TagItem
import com.duaag.android.recommender.domain.model.FeaturedProfileModel
import com.duaag.android.recommender.domain.model.PictureModel
import com.duaag.android.recommender.domain.model.ProfileModel
import com.duaag.android.recommender.domain.model.TagModel
import com.duaag.android.home.models.Picture
import com.duaag.android.home.models.Profile
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.utils.GenderType


fun TagModel.toRecommendedUserTagsModel(): TagItem {
    return TagItem(
        id = this.id,
        tagItemId = this.tagItemId,
        tagTypeId = this.tagTypeId
    )
}

fun PictureModel.toRecommendedUserPictureModel(): Picture {
    return Picture(
        position = this.position,
        url = this.url
    )
}

fun ProfileModel.toRecommendedUserProfile(): Profile {
    return Profile(
        description = this.description,
        hobbies = this.hobbies,
        educations = this.educations,
        jobs = this.jobs,
        languages = this.languages,
        distance = this.distance,
        actualAddress = this.actualAddress,
        pictureUrl = this.pictureUrl,
        thumbnailUrl = null,
        pictures = this.pictures?.map { it.toRecommendedUserPictureModel() } ?: emptyList(),
        address = this.address
    )
}

fun FeaturedProfileModel.toRecommendedUserModel(): RecommendedUserModel {
    return RecommendedUserModel(
        cognitoUserId = this.cognitoUserId,
        existingInteractionId = null,
        age = this.age,
        firstName = this.firstName,
        gender = this.gender,
        profile = this.profile.toRecommendedUserProfile(),
        id = this.id,
        isMatch = false,
        hasBadge1 = this.hasBadge1,
        badge2 = this.badge2,
        instagramStatus = this.instagramStatus ?: INSTAGRAM_STATUS_DISCONNECTED,
        tags = this.tags?.map { it.toRecommendedUserTagsModel()} ?: emptyList(),
        instagramToken = null,
        instagramMedia = null
    )
}