package com.duaag.android.recommender.data.dto

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName


@Keep
data class FeaturedProfileDto(
    @SerializedName("age")
    val age: Int,
    @SerializedName("badge2")
    val badge2: String?,
    @SerializedName("cognitoUserId")
    val cognitoUserId: String,
    @SerializedName("firstName")
    val firstName: String?,
    @SerializedName("gender")
    val gender: String,
    @SerializedName("hasBadge1")
    val hasBadge1: Boolean?,
    @SerializedName("id")
    val id: Int,
    @SerializedName("instagramStatus")
    val instagramStatus: String?,
    @SerializedName("isBlurred")
    val isBlurred: Boolean?,
    @SerializedName("isPremium")
    val isPremium: Boolean?,
    @SerializedName("isSpotlight")
    val isSpotlight: Boolean?,
    @SerializedName("mode")
    val mode: String?,
    @SerializedName("profile")
    val profile: ProfileDto,
    @SerializedName("tags")
    val tags: List<TagDto>?,
    @SerializedName("zodiac")
    val zodiac: String?,
    @SerializedName("activityType")
    var activityType: String? = null,
)

@Keep
data class ProfileDto(
    @SerializedName("actualAddress")
    val actualAddress: String?,
    @SerializedName("address")
    val address: String?,
    @SerializedName("blurredProfileUrl")
    val blurredProfileUrl: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("distance")
    val distance: Double?,
    @SerializedName("educations")
    val educations: String?,
    @SerializedName("hobbies")
    val hobbies: String?,
    @SerializedName("jobs")
    val jobs: String?,
    @SerializedName("languages")
    val languages: String?,
    @SerializedName("pictureUrl")
    val pictureUrl: String?,
    @SerializedName("pictures")
    val pictures: List<PictureDto>?
)

@Keep
data class TagDto(
    @SerializedName("id")
    val id: Int,
    @SerializedName("tagItemId")
    val tagItemId: Int,
    @SerializedName("tagTypeId")
    val tagTypeId: Int
)

@Keep
data class PictureDto(
    @SerializedName("position")
    val position: Int,
    @SerializedName("url")
    val url: String
)