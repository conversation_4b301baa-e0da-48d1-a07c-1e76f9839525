package com.duaag.android.recommender.data.dto

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class RMODDto(
    @SerializedName("fate")
    val fate: Int,
    @SerializedName("isHidden")
    val isHidden: Boolean,
    @SerializedName("language")
    val language: LanguagesDto,
    @SerializedName("partner")
    val partner: RmodPartnerDto,
    @SerializedName("rmodActiveUntil")
    val rmodActiveUntil: String,
    @SerializedName("totalPercentage")
    val totalPercentage: Int,
    @SerializedName("activities")
    val activities: RmodActivitiesDto,
    @SerializedName("age")
    val age: RmodAgeDto,
    @SerializedName("children")
    val children: RmodChildrenDto,
    @SerializedName("location")
    val location: RmodLocationDto,
    @SerializedName("lookingFor")
    val lookingFor: RmodLookingForDto,
    @SerializedName("religion")
    val religion: RmodReligionDto,
    @SerializedName("smoking")
    val smoking: RmodSmookingDto,
    @SerializedName("zodiac")
    val zodiac: RmodZodiacDto,
    @SerializedName("isDeleted")
    val isDeleted: Boolean,
    @SerializedName("isSeen")
    val isSeen: Boolean
) : Parcelable

@Keep
@Parcelize
data class LanguagesDto(
    @SerializedName("matchingTags")
    val matchingLanguages: List<TagItemDto>,
    @SerializedName("percentage")
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class TagItemDto(
    @SerializedName("tagItemId")
    val tagItemId: Int,
    @SerializedName("tagTypeId")
    val tagTypeId: Int
): Parcelable

@Keep
@Parcelize
data class RmodPartnerDto(
    @SerializedName("age")
    val age: Int,
    @SerializedName("badge2")
    val badge2: String?,
    @SerializedName("cognitoUserId")
    val cognitoUserId: String,
    @SerializedName("firstName")
    val firstName: String,
    @SerializedName("gender")
    val gender: String,
    @SerializedName("hasBadge1")
    val hasBadge1: Boolean,
    @SerializedName("id")
    val id: Int,
    @SerializedName("instagramStatus")
    val instagramStatus: String,
    @SerializedName("isPremium")
    val isPremium: Boolean,
    @SerializedName("mode")
    val mode: String,
    @SerializedName("profile")
    val profile: RmodProfileDto,
    @SerializedName("tags")
    val tags: List<RmodTagDto>,
    @SerializedName("zodiacSign")
    val zodiacSign: String?,
    @SerializedName("activityType")
    val activityType: String?
) : Parcelable

@Keep
@Parcelize
data class RmodProfileDto(
    @SerializedName("actualAddress")
    val actualAddress: String?,
    @SerializedName("address")
    val address: String?,
    @SerializedName("blurredProfileUrl")
    val blurredProfileUrl: String?,
    @SerializedName("description")
    val description: String,
    @SerializedName("distance")
    val distance: Double?,
    @SerializedName("educations")
    val educations: String?,
    @SerializedName("hobbies")
    val hobbies: String?,
    @SerializedName("jobs")
    val jobs: String?,
    @SerializedName("pictureUrl")
    val pictureUrl: String,
    @SerializedName("pictures")
    val pictures: List<RmodPictureDto>?,
    @SerializedName("showPremiumBadge")
    var showPremiumBadge: Boolean? = false,
) : Parcelable

@Keep
@Parcelize
data class RmodTagDto(
    @SerializedName("tagItemId")
    val tagItemId: Int,
    @SerializedName("tagTypeId")
    val tagTypeId: Int
) : Parcelable

@Keep
@Parcelize
data class RmodPictureDto(
    @SerializedName("position")
    val position: Int,
    @SerializedName("url")
    val url: String
) : Parcelable

@Keep
@Parcelize
data class RmodActivitiesDto(
    @SerializedName("matchingTags")
    val matchingActivities: List<TagItemDto>?,
    @SerializedName("percentage")
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodAgeDto(
    @SerializedName("partnerAge")
    val partnerAge: Int,
    @SerializedName("percentage")
    val percentage: Int,
    @SerializedName("userAge")
    val userAge: Int
) : Parcelable

@Keep
@Parcelize
data class RmodChildrenDto(
    @SerializedName("matchingTags")
    val matchingChildren: List<TagItemDto>,
    @SerializedName("percentage")
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodLocationDto(
    @SerializedName("distance")
    val distance: Double,
    @SerializedName("percentage")
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodLookingForDto(
    @SerializedName("matchingTags")
    val matchingLookingFor: List<TagItemDto>,
    @SerializedName("percentage")
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodReligionDto(
    @SerializedName("matchingTags")
    val matchingReligions: List<TagItemDto>,
    @SerializedName("percentage")
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodSmookingDto(
    @SerializedName("matchingTags")
    val matchingSmoking: List<TagItemDto>,
    @SerializedName("percentage")
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodZodiacDto(
    @SerializedName("partnerZodiacSign")
    val partnerZodiacSign: String,
    @SerializedName("percentage")
    val percentage: Int,
    @SerializedName("userZodiacSign")
    val userZodiacSign: String
) : Parcelable
