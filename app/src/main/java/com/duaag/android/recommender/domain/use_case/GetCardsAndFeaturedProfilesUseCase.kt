package com.duaag.android.recommender.domain.use_case

import com.duaag.android.recommender.domain.repository.RecommenderRepository
import javax.inject.Inject

class GetCardsAndFeaturedProfilesUseCase @Inject constructor(
    private val recommenderRepository: RecommenderRepository
) {
    operator fun invoke(fetchCards: Boolean = true, fetchFeaturedProfiles: Boolean = true) = recommenderRepository.getCardsAndFeaturedProfiles(fetchCards, fetchFeaturedProfiles)
}