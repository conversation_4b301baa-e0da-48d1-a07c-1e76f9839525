package com.duaag.android.recommender.domain.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.duaag.android.home.models.RecommendedUserModel
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class UserRecommendationsModel(
    val cards: List<RecommendedUserModel> = emptyList(),
    val featuredProfiles: List<FeaturedProfileModel> = emptyList(),
    var rmodItem: RmodModel? = null,
    var recommId: String? = null,
    var recommSource: String? = null
): Parcelable