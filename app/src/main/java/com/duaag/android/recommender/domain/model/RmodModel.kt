package com.duaag.android.recommender.domain.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationType
import kotlinx.parcelize.Parcelize


@Keep
@Parcelize
data class RmodModel(
    val fate: Int,
    var isHidden: Boolean,
    val language: LanguageModel,
    val partner: RmodPartnerModel,
    val rmodActiveUntil: String,
    val totalPercentage: Int,
    val activities: RmodActivitiesModel,
    val age: RmodAgeModel,
    val children: RmodChildrenModel,
    val location: RmodLocationModel,
    val lookingFor: RmodLookingForModel,
    val religion: RmodReligionModel,
    val smoking: RmodSmookingModel,
    val zodiac: RmodZodiacModel,
    var isDeleted: Boolean,
    var isSeen: Boolean
) : Parcelable

@Keep
@Parcelize
data class InterestsModel(
    val matchingInterests: List<TagItemModel>,
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class LanguageModel(
    val matchingLanguages: List<TagItemModel>,
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class TagItemModel(
    val tagItemId: Int,
    val tagTypeId: Int
): Parcelable

@Keep
@Parcelize
data class RmodPartnerModel(
    val age: Int,
    val badge2: String?,
    val cognitoUserId: String,
    val firstName: String,
    val gender: String,
    val hasBadge1: Boolean,
    val id: Int,
    val instagramStatus: String,
    val isPremium: Boolean,
    val mode: String,
    val profile: RmodProfileModel,
    val tags: List<TagItemModel>,
    val zodiacSign: String?,
    val activityType: String?
) : Parcelable

@Keep
@Parcelize
data class RmodProfileModel(
    val actualAddress: String?,
    val address: String?,
    val blurredProfileUrl: String?,
    val description: String?,
    val distance: Double?,
    val educations: String?,
    val hobbies: String?,
    val jobs: String?,
    val pictureUrl: String,
    val showPremiumBadge: Boolean?,
    val pictures: List<RmodPictureModel>?
) : Parcelable

@Keep
@Parcelize
data class RmodPictureModel(
    val position: Int,
    val url: String
) : Parcelable

@Keep
@Parcelize
data class RmodActivitiesModel(
    val matchingActivities: List<TagItemModel>?,
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodAgeModel(
    val partnerAge: Int,
    val percentage: Int,
    val userAge: Int
) : Parcelable

@Keep
@Parcelize
data class RmodChildrenModel(
    val matchingChildren: List<TagItemModel>,
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodLocationModel(
    val distance: Double,
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodLookingForModel(
    val matchingLookingFor: List<TagItemModel>,
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodReligionModel(
    val matchingReligions: List<TagItemModel>,
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodSmookingModel(
    val matchingSmoking: List<TagItemModel>,
    val percentage: Int
) : Parcelable

@Keep
@Parcelize
data class RmodZodiacModel(
    val partnerZodiacSign: String,
    val percentage: Int,
    val userZodiacSign: String
) : Parcelable

fun RmodModel.toConversationModel(conversationId: String? = null): ConversationModel {
    return ConversationModel(
        id = conversationId,
        userId = partner.cognitoUserId,
        lastMessageText = "",
        lastMessageSender = "",
        lastMessageType = "txt",
        lastMessageTime = System.currentTimeMillis(),
        name = partner.firstName,
        gender = partner.gender,
        pictureUrl = partner.profile.pictureUrl,
        thumbnailUrl = null,
        bluredThumbnailUrl = null,
        seen = if(this.isSeen) 1 else 0,
        isStarred = 0,
        type = ConversationType.INSTANT_CHAT.value,
        hasBadge1 = partner.hasBadge1,
        badge2 = partner.badge2,
        wasInstachat = false,
        areIncomingCallsAllowed = false,
        areOutgoingCallsAllowed = false,
        createdAt = 0,
        isRMOD = true,
        isDeleted = if(this.isDeleted) 1 else 0
    )
}