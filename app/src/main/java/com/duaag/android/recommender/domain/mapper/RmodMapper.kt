import com.duaag.android.recommender.data.dto.LanguagesDto
import com.duaag.android.recommender.data.dto.RMODDto
import com.duaag.android.recommender.data.dto.RmodActivitiesDto
import com.duaag.android.recommender.data.dto.RmodAgeDto
import com.duaag.android.recommender.data.dto.RmodChildrenDto
import com.duaag.android.recommender.data.dto.RmodLocationDto
import com.duaag.android.recommender.data.dto.RmodLookingForDto
import com.duaag.android.recommender.data.dto.RmodPartnerDto
import com.duaag.android.recommender.data.dto.RmodPictureDto
import com.duaag.android.recommender.data.dto.RmodProfileDto
import com.duaag.android.recommender.data.dto.RmodReligionDto
import com.duaag.android.recommender.data.dto.RmodSmookingDto
import com.duaag.android.recommender.data.dto.RmodTagDto
import com.duaag.android.recommender.data.dto.RmodZodiacDto
import com.duaag.android.recommender.data.dto.TagItemDto
import com.duaag.android.recommender.domain.model.LanguageModel
import com.duaag.android.recommender.domain.model.RmodModel
import com.duaag.android.recommender.domain.model.RmodActivitiesModel
import com.duaag.android.recommender.domain.model.RmodAgeModel
import com.duaag.android.recommender.domain.model.RmodChildrenModel
import com.duaag.android.recommender.domain.model.RmodLocationModel
import com.duaag.android.recommender.domain.model.RmodLookingForModel
import com.duaag.android.recommender.domain.model.RmodPartnerModel
import com.duaag.android.recommender.domain.model.RmodPictureModel
import com.duaag.android.recommender.domain.model.RmodProfileModel
import com.duaag.android.recommender.domain.model.RmodReligionModel
import com.duaag.android.recommender.domain.model.RmodSmookingModel
import com.duaag.android.recommender.domain.model.RmodZodiacModel
import com.duaag.android.recommender.domain.model.TagItemModel

fun RMODDto.toRmodModel(): RmodModel {
    return RmodModel(
        fate = this.fate,
        isHidden = this.isHidden,
        language = this.language.toLanguageModel(),
        partner = this.partner.toRmodPartnerModel(),
        rmodActiveUntil = this.rmodActiveUntil,
        totalPercentage = this.totalPercentage,
        activities = this.activities.toRmodActivitiesModel(),
        age = this.age.toRmodAgeModel(),
        children = this.children.toRmodChildrenModel(),
        location = this.location.toRmodLocationModel(),
        lookingFor = this.lookingFor.toRmodLookingForModel(),
        religion = this.religion.toRmodReligionModel(),
        smoking = this.smoking.toRmodSmookingModel(),
        zodiac = this.zodiac.toRmodZodiacModel(),
        isDeleted = this.isDeleted,
        isSeen = this.isSeen
    )
}

fun RmodAgeDto.toRmodAgeModel(): RmodAgeModel {
    return RmodAgeModel(
        partnerAge = this.partnerAge,
        percentage = this.percentage,
        userAge = this.userAge
    )
}

fun LanguagesDto.toLanguageModel(): LanguageModel {
    return LanguageModel(
        matchingLanguages = this.matchingLanguages.map { it.toTagItemModel() },
        percentage = this.percentage
    )
}

fun TagItemDto.toTagItemModel(): TagItemModel {
    return TagItemModel(
        tagItemId = this.tagItemId,
        tagTypeId = this.tagTypeId
    )
}

fun RmodLocationDto.toRmodLocationModel(): RmodLocationModel {
    return RmodLocationModel(
        distance = this.distance,
        percentage = this.percentage
    )
}

fun RmodPartnerDto.toRmodPartnerModel(): RmodPartnerModel {
    return RmodPartnerModel(
        age = this.age,
        badge2 = this.badge2,
        cognitoUserId = this.cognitoUserId,
        firstName = this.firstName,
        gender = this.gender,
        hasBadge1 = this.hasBadge1,
        id = this.id,
        instagramStatus = this.instagramStatus,
        isPremium = this.isPremium,
        mode = this.mode,
        profile = this.profile.toRmodProfileModel(),
        tags = this.tags.map { it.toTagItemModel() },
        zodiacSign = this.zodiacSign,
        activityType = this.activityType
    )
}

fun RmodProfileDto.toRmodProfileModel(): RmodProfileModel {
    return RmodProfileModel(
        actualAddress = this.actualAddress,
        address = this.address,
        blurredProfileUrl = this.blurredProfileUrl,
        description = this.description,
        distance = this.distance,
        educations = this.educations,
        hobbies = this.hobbies,
        jobs = this.jobs,
        pictureUrl = this.pictureUrl,
        pictures = this.pictures?.map { it.toRmodPictureModel() },
        showPremiumBadge = this.showPremiumBadge
    )
}

fun RmodPictureDto.toRmodPictureModel(): RmodPictureModel {
    return RmodPictureModel(
        position = this.position,
        url = this.url
    )
}

fun RmodActivitiesDto.toRmodActivitiesModel(): RmodActivitiesModel {
    return RmodActivitiesModel(
        matchingActivities = this.matchingActivities?.map { it.toTagItemModel() },
        percentage = this.percentage
    )
}

fun RmodChildrenDto.toRmodChildrenModel(): RmodChildrenModel {
    return RmodChildrenModel(
        matchingChildren = this.matchingChildren.map { it.toTagItemModel() },
        percentage = this.percentage
    )
}

fun RmodLookingForDto.toRmodLookingForModel(): RmodLookingForModel {
    return RmodLookingForModel(
        matchingLookingFor = this.matchingLookingFor.map { it.toTagItemModel() },
        percentage = this.percentage
    )
}

fun RmodReligionDto.toRmodReligionModel(): RmodReligionModel {
    return RmodReligionModel(
        matchingReligions = this.matchingReligions.map { it.toTagItemModel() },
        percentage = this.percentage
    )
}

fun RmodSmookingDto.toRmodSmookingModel(): RmodSmookingModel {
    return RmodSmookingModel(
        matchingSmoking = this.matchingSmoking.map { it.toTagItemModel() },
        percentage = this.percentage
    )
}

fun RmodZodiacDto.toRmodZodiacModel(): RmodZodiacModel {
    return RmodZodiacModel(
        partnerZodiacSign = this.partnerZodiacSign,
        percentage = this.percentage,
        userZodiacSign = this.userZodiacSign
    )
}

fun RmodTagDto.toTagItemModel(): TagItemModel {
    return TagItemModel(
        tagItemId = this.tagItemId,
        tagTypeId = this.tagTypeId
    )
}
