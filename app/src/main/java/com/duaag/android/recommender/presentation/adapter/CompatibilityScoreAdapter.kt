package com.duaag.android.recommender.presentation.adapter

import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.CompatibilityScoreHeaderBinding
import com.duaag.android.databinding.CompatibilityScoreItemBinding
import com.duaag.android.recommender.presentation.model.CompatibilityScoreItem
import com.duaag.android.utils.convertDpToPixel

class CompatibilityScoreAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    companion object {
        const val HEADER_VIEW_TYPE = 0
        const val REGULAR_VIEW_TYPE = 1
    }

    private var items: ArrayList<CompatibilityScoreItem> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            HEADER_VIEW_TYPE -> {
                val binding = CompatibilityScoreHeaderBinding.inflate(layoutInflater, parent, false)
                CompatibilityScoreHeaderViewHolder(binding)
            }
            else -> {
                val binding = CompatibilityScoreItemBinding.inflate(layoutInflater, parent, false)
                CompatibilityItemViewHolder(binding)
            }
        }
    }

    fun setData(data: List<CompatibilityScoreItem>) {
        val diffCallback = CompatibilityItemDiffUtil(items, data)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        items.clear()
        items.addAll(data)
        diffResult.dispatchUpdatesTo(this)
    }

    override fun getItemViewType(position: Int): Int {
        return if(position == 0) HEADER_VIEW_TYPE else REGULAR_VIEW_TYPE
    }
    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            HEADER_VIEW_TYPE -> (holder as CompatibilityScoreHeaderViewHolder).bind(items[position].percentage)
            else -> (holder as CompatibilityItemViewHolder).bind(items[position])
        }
    }

    inner class CompatibilityItemViewHolder(val binding: CompatibilityScoreItemBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(model: CompatibilityScoreItem) {
            val percentage = "${model.percentage}%"

            binding.icon.setImageResource(model.icon)
            binding.percentage.text = percentage

            val originalText = model.spannableString ?: SpannableString(model.description ?: "")

            binding.description.text = originalText
            binding.description.maxLines = Integer.MAX_VALUE

            val viewAllString = "\n${binding.root.context.getString(R.string.view_all)}"
            binding.description.post {
                if (binding.description.lineCount > 3) {
                    val end = binding.description.layout.getLineEnd(2)
                    val truncatedText = SpannableStringBuilder(originalText, 0, end).append(viewAllString)
                    binding.description.text = truncatedText
                    binding.description.maxLines = 4
                }
                else {
                    binding.description.maxLines = 3
                }
            }

            binding.description.setOnClickListener {
                if (!model.isExpanded) {
                    binding.description.text = originalText
                    binding.description.maxLines = Integer.MAX_VALUE
                    model.isExpanded = true
                }
            }
        }
    }

    inner class CompatibilityScoreHeaderViewHolder(val binding: CompatibilityScoreHeaderBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(totalPercentage: Int) {
            val context = binding.root.context
            val percentage = "$totalPercentage%"
            binding.percentageTxt.text = percentage

            binding.compatibilityCircle.setProgress(totalPercentage.toFloat())
            binding.compatibilityCircle.setRounded(true)
            binding.compatibilityCircle.setProgressColor(ContextCompat.getColor(context, R.color.pink_500))
            binding.compatibilityCircle.setProgressBackgroundColor(ContextCompat.getColor(context, R.color.border))
            binding.compatibilityCircle.setProgressWidth(convertDpToPixel(6f, context))
        }
    }


    class CompatibilityItemDiffUtil(private val oldList: List<CompatibilityScoreItem>, private val newList: List<CompatibilityScoreItem>) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldList.size

        override fun getNewListSize(): Int = newList.size


        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }

        override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
            return super.getChangePayload(oldItemPosition, newItemPosition)
        }
    }
}