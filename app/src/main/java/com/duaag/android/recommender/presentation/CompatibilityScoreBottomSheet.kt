package com.duaag.android.recommender.presentation

import android.app.Dialog
import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.base.models.TagTypeModel
import com.duaag.android.clevertap.RmodRemovedValues
import com.duaag.android.clevertap.RmodViewValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendRmodEvent
import com.duaag.android.databinding.FragmentCompatibilityScoreBottomSheetBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.recommender.domain.model.RmodModel
import com.duaag.android.recommender.presentation.adapter.CompatibilityScoreAdapter
import com.duaag.android.recommender.presentation.model.CompatibilityScoreItem
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.updateLocale
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import javax.inject.Inject

class CompatibilityScoreBottomSheet : BottomSheetDialogFragment() {

    private var _binding: FragmentCompatibilityScoreBottomSheetBinding? = null
    private val binding get() = _binding!!

    var rmodModel: RmodModel? = null

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as? HomeActivity)?.homeComponent?.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        rmodModel = arguments?.getParcelable(RMOD_MODEL_DATA)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCompatibilityScoreBottomSheetBinding.inflate(inflater, container, false)
        val adapter = CompatibilityScoreAdapter()

        binding.compatibilityScoreList.adapter = adapter
        binding.compatibilityScoreList.layoutManager = LinearLayoutManager(requireContext())

        val compatibilityScoreItems = getCompatibiltyScreenItemsFromRmod(requireContext(), rmodModel!!, DuaAccount.tags.orEmpty())
        adapter.setData(compatibilityScoreItems)

        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

        sendRmodEvent(RmodViewValues.COMPATIBILITY_SCORE, RmodRemovedValues.NULL, eventPremiumType, rmodModel?.totalPercentage)

        return binding.root
    }


    companion object {
        const val RMOD_MODEL_DATA = "rmod_model_data"

        val CHILDREN_YES = 870
        val CHILDREN_NO =  871
        val CHILDREN_WANT_TO_HAVE = 872
        val CHILDREN_DONT_WANT_TO_HAVE = 873
        val SERIOUS_RELATIONSHIP = 850
        val LOOKING_FOR_A_FRIEND = 851
        val LOOKING_TO_HAVE_A_DATE = 852
        val LOOKING_TO_FIND_PARTNER = 853
        val ISLAM = 601
        val CHRISTIANITY = 602
        val NON_RELIGIOUS = 603
        val BUDDISM = 604
        val ANOTHER_RELIGION = 605
        val SMOKING_YES = 860
        val SMOKING_NO = 861
        val SMOKING_SOMETIMES = 862

        fun newInstance(rmodModel: RmodModel): CompatibilityScoreBottomSheet =
            CompatibilityScoreBottomSheet().apply {
                val bundle = Bundle()
                bundle.putParcelable(RMOD_MODEL_DATA, rmodModel)
                arguments = bundle
            }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =
            super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.let {
                BottomSheetBehavior.from<FrameLayout?>(bottomSheet).state =
                    BottomSheetBehavior.STATE_EXPANDED
            }
            d.dismissWithAnimation = true
        }
        return dialog
    }

    private fun getCompatibiltyScreenItemsFromRmod(context: Context, rmodModel: RmodModel, tags: List<TagTypeModel>): List<CompatibilityScoreItem> {
        val compatibilityScoreItems = mutableListOf<CompatibilityScoreItem>()

//        HEADER
        compatibilityScoreItems.add(CompatibilityScoreItem(0, 0, "", null, rmodModel.totalPercentage))

//        LANGUAGE
        if(rmodModel.language.percentage != 0)
            createLanguageItem(context, rmodModel, tags)?.let {
                compatibilityScoreItems.add(it)
            }

//        HOROSCOPE
        if(rmodModel.zodiac.percentage != 0)
            compatibilityScoreItems.add(createHoroscopeItem(requireContext(), rmodModel))

//        AGE
        if(rmodModel.age.percentage != 0)
            compatibilityScoreItems.add(createAgeItem(requireContext(), rmodModel))


//        CHILDREN
        if(rmodModel.children.percentage != 0)
            createChildrenItem(requireContext(), rmodModel, tags)?.let {
                compatibilityScoreItems.add(it)
            }

//        LOOKING_FOR
        if(rmodModel.lookingFor.percentage != 0)
            createLookingForItem(requireContext(), rmodModel, tags)?.let {
                compatibilityScoreItems.add(it)
            }

//        INTERESTS
        if(rmodModel.activities.percentage != 0)
            createInterestsItem(requireContext(), rmodModel, tags)?.let {
                compatibilityScoreItems.add(it)
            }

//        RELIGION
        if(rmodModel.religion.percentage != 0)
            createReligionItem(requireContext(), rmodModel, tags)?.let {
                compatibilityScoreItems.add(it)
            }


//        SMOKING
        if(rmodModel.smoking.percentage != 0)
            createSmokingItem(requireContext(), rmodModel, tags)?.let {
                compatibilityScoreItems.add(it)
            }

//        LOCATION
        if(rmodModel.location.percentage != 0)
            compatibilityScoreItems.add(createLocationItem(requireContext(), rmodModel))

//        DESTINY
        if(rmodModel.fate != 0)
            compatibilityScoreItems.add(createDestinyItem(requireContext(), rmodModel))

        return compatibilityScoreItems
    }

    private fun createHoroscopeItem(context: Context, rmodModel: RmodModel): CompatibilityScoreItem {
        val horoscopeString = if(rmodModel.zodiac.partnerZodiacSign == rmodModel.zodiac.userZodiacSign) {
            val partnerZodiacSign = try {
                getString(resources.getIdentifier("${rmodModel.zodiac.partnerZodiacSign}_sign", "string", context.packageName))
            } catch (ex: Exception) {
                ex.printStackTrace()
                ""
            }

            context.getString(R.string.mutual_zodiac_sign_rmod_an, partnerZodiacSign)
        } else {
            val partnerZodiacSign = try {
                getString(resources.getIdentifier("${rmodModel.zodiac.partnerZodiacSign}_sign", "string", context.packageName))
            } catch (ex: Exception) {
                ex.printStackTrace()
                ""
            }

            val userZodiacSign = try {
                getString(resources.getIdentifier("${rmodModel.zodiac.userZodiacSign}_sign", "string", context.packageName))
            } catch (ex: Exception) {
                ex.printStackTrace()
                ""
            }

            if(homeViewModel.userProfile.value?.gender == GenderType.MAN.value)
                context.getString(R.string.different_zodiac_sign_male_pov_rmod_an, userZodiacSign, partnerZodiacSign)
            else
                context.getString(R.string.different_zodiac_sign_female_pov_rmod_an, userZodiacSign, partnerZodiacSign)
        }

        return CompatibilityScoreItem(2, R.drawable.ic_zodiac, horoscopeString, null, rmodModel.zodiac.percentage)
    }

    private fun createLanguageItem(context: Context, rmodModel: RmodModel, tags: List<TagTypeModel>): CompatibilityScoreItem? {
        val languageTagTypeModels: List<TagTypeModel> = tags

        val languageMatchingTagItemIds = rmodModel.language.matchingLanguages.map { it.tagItemId }
        val languageMatchingTitles = languageTagTypeModels
            .flatMap { it.items }
            .filter { tagModel -> languageMatchingTagItemIds.contains(tagModel.id) }
            .map { it.title }

        if(languageMatchingTagItemIds.isNotEmpty()) {
            val userLanguagesString = languageMatchingTitles.joinToString(separator = ", ")

            val formattedLanguages = if (languageMatchingTitles.size > 1) {
                languageMatchingTitles.subList(0, languageMatchingTitles.size - 1).joinToString(separator = ", ") +
                        " ${getString(R.string.and_string)} " + languageMatchingTitles.last()
            } else {
                userLanguagesString
            }
            val formattedString = context.getString(R.string.single_mutual_language_rmod_an, formattedLanguages)
            val spannableString = SpannableString(formattedString)
            languageMatchingTitles.forEach { language ->
                val start = spannableString.indexOf(language)
                if (start >= 0) {
                    spannableString.setSpan(StyleSpan(Typeface.BOLD), start, start + language.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            }

            return CompatibilityScoreItem(
                    1,
                    R.drawable.ic_globe_v2,
                    null,
                    spannableString,
                    rmodModel.language.percentage
                )

        } else return null
    }

    private fun createAgeItem(context: Context, rmodModel: RmodModel): CompatibilityScoreItem {
        val ageString = context.getString(R.string.age_filter_rmod, rmodModel.partner.firstName, rmodModel.partner.age.toString())
        return CompatibilityScoreItem(3, R.drawable.ic_filter_v2, ageString, null, rmodModel.age.percentage)
    }

    private fun createChildrenItem(context: Context, rmodModel: RmodModel, tags: List<TagTypeModel>): CompatibilityScoreItem? {
        val childrenTagTypeModels: List<TagTypeModel> = tags

        val childrenMatchingTagItemIds = rmodModel.children.matchingChildren.map { it.tagItemId }
        val childrenMatchingId = childrenTagTypeModels
            .flatMap { it.items }
            .filter { tagModel -> childrenMatchingTagItemIds.contains(tagModel.id) }
            .map { it.id }
            .firstOrNull()

        val childrenString: String?  = when(childrenMatchingId) {
            CHILDREN_YES -> { context.getString(R.string.yes_have_children_rmod) }
            CHILDREN_NO -> { context.getString(R.string.dont_have_children_rmod) }
            CHILDREN_WANT_TO_HAVE -> { context.getString(R.string.want_to_have_children_rmod) }
            CHILDREN_DONT_WANT_TO_HAVE -> { context.getString(R.string.dont_want_to_have_children_rmod) }
            else -> null
        }


        return if(childrenString != null) {
            CompatibilityScoreItem(4, R.drawable.ic_childrens_v2, childrenString, null, rmodModel.children.percentage)
        } else null
    }

    private fun createLookingForItem(context: Context, rmodModel: RmodModel, tags: List<TagTypeModel>): CompatibilityScoreItem? {
        val lookingForTagTypeModels: List<TagTypeModel> = tags

        val lookingForMatchingTagItemIds = rmodModel.lookingFor.matchingLookingFor.map { it.tagItemId }
        val lookingForMatchingId = lookingForTagTypeModels
            .flatMap { it.items }
            .filter { tagModel -> lookingForMatchingTagItemIds.contains(tagModel.id) }
            .map { it.id }
            .firstOrNull()

        val lookingForString: String?  = when(lookingForMatchingId) {
            SERIOUS_RELATIONSHIP -> { context.getString(R.string.looking_for_serious_relationship_rmod) }
            LOOKING_FOR_A_FRIEND -> { context.getString(R.string.looking_for_a_friend_rmod) }
            LOOKING_TO_HAVE_A_DATE -> { context.getString(R.string.looking_to_have_a_date_rmod) }
            LOOKING_TO_FIND_PARTNER -> { context.getString(R.string.looking_to_find_a_partner_rmod) }
            else -> null
        }

        return if(lookingForString != null) {
            CompatibilityScoreItem(5, R.drawable.ic_search, lookingForString, null, rmodModel.lookingFor.percentage)
        } else null
    }

    private fun createInterestsItem(context: Context, rmodModel: RmodModel, tags: List<TagTypeModel>): CompatibilityScoreItem? {

        val interestsTagTypeModels: List<TagTypeModel> = tags

        val interestsMatchingTagItemIds = rmodModel.activities.matchingActivities?.map { it.tagItemId } ?: emptyList()
        val interestsMatchingTitles = interestsTagTypeModels
            .flatMap { it.items }
            .filter { tagModel -> interestsMatchingTagItemIds.contains(tagModel.id) }
            .map { it.title }

        val formattedInterests = if (interestsMatchingTitles.size > 1) {
            interestsMatchingTitles.subList(0, interestsMatchingTitles.size - 1).joinToString(separator = ", ") +
                    " ${getString(R.string.and_string)} " + interestsMatchingTitles.last()
        } else {
            interestsMatchingTitles.joinToString(separator = ", ")
        }
        val interestsString = context.getString(R.string.single_mutual_interest_rmod, formattedInterests)
        val spannableString = SpannableString(interestsString)
        interestsMatchingTitles.forEach { interest ->
            val start = spannableString.indexOf(interest)
            if (start >= 0) {
                spannableString.setSpan(StyleSpan(Typeface.BOLD), start, start + interest.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }

        return if(interestsMatchingTitles.isNotEmpty()) {
            CompatibilityScoreItem(6, R.drawable.puzzle_icon, interestsString, spannableString, rmodModel.activities.percentage)
        } else null
    }

    private fun createReligionItem(context: Context, rmodModel: RmodModel, tags: List<TagTypeModel>): CompatibilityScoreItem? {

        val religionTagTypeModels: List<TagTypeModel> = tags

        val religionMatchingTagItemIds = rmodModel.religion.matchingReligions.map { it.tagItemId }
        val religionMatchingId = religionTagTypeModels
            .flatMap { it.items }
            .filter { tagModel -> religionMatchingTagItemIds.contains(tagModel.id) }
            .map { it.id }
            .firstOrNull()

        val religionString: String?  = when(religionMatchingId) {
            ISLAM -> { context.getString(R.string.both_follow_islam_rmod) }
            CHRISTIANITY -> { context.getString(R.string.both_follow_christianity_rmod) }
            NON_RELIGIOUS -> { context.getString(R.string.you_both_are_nonreligious_rmod) }
            BUDDISM -> { context.getString(R.string.you_both_follow_buddism_rmod) }
            ANOTHER_RELIGION -> { context.getString(R.string.you_both_follow_diff_religions_rmod) }
            else -> null
        }

        return if(religionString != null) {
            CompatibilityScoreItem(7, R.drawable.ic_religion_v2, religionString, null, rmodModel.religion.percentage)
        } else null
    }

    private fun createSmokingItem(context: Context, rmodModel: RmodModel, tags: List<TagTypeModel>): CompatibilityScoreItem? {

        val smokingTagTypeModels: List<TagTypeModel> = tags

        val smokingMatchingTagItemIds = rmodModel.smoking.matchingSmoking.map { it.tagItemId }
        val smokingMatchingId = smokingTagTypeModels
            .flatMap { it.items }
            .filter { tagModel -> smokingMatchingTagItemIds.contains(tagModel.id) }
            .map { it.id }
            .firstOrNull()

        val smokingString: String?  = when(smokingMatchingId) {
            SMOKING_YES -> { context.getString(R.string.yes_smoking_rmod) }
            SMOKING_NO -> { context.getString(R.string.no_smoking_rmod) }
            SMOKING_SOMETIMES -> { context.getString(R.string.sometimes_smoking_rmod) }
            else -> null
        }

        return if(smokingString != null) {
            CompatibilityScoreItem(8, R.drawable.ic_cigarette_v2, smokingString, null, rmodModel.smoking.percentage)
        } else null
    }

    private fun createLocationItem(context: Context, rmodModel: RmodModel): CompatibilityScoreItem {
        val locationString =
            if(homeViewModel.userProfile.value?.gender == GenderType.MAN.value)
                context.getString(R.string.she_is_around_the_corner_rmod)
            else
                context.getString(R.string.he_is_around_the_corner_rmod)
        return CompatibilityScoreItem(9, R.drawable.pin_rmod, locationString, null, rmodModel.location.percentage)

    }
    private fun createDestinyItem(context: Context, rmodModel: RmodModel): CompatibilityScoreItem {
        val destinyString =  context.getString(R.string.destiny_together_rmod)
        return CompatibilityScoreItem(10, R.drawable.destiny_icon, destinyString, null, rmodModel.fate)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}