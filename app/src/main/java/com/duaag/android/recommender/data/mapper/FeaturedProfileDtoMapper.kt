package com.duaag.android.recommender.data.mapper

import com.duaag.android.home.models.RecommendedUsersActivityStatus
import com.duaag.android.recommender.data.dto.FeaturedProfileDto
import com.duaag.android.recommender.data.dto.PictureDto
import com.duaag.android.recommender.data.dto.ProfileDto
import com.duaag.android.recommender.data.dto.TagDto
import com.duaag.android.recommender.data.dto.UserRecommendationsDto
import com.duaag.android.recommender.domain.model.FeaturedProfileModel
import com.duaag.android.recommender.domain.model.PictureModel
import com.duaag.android.recommender.domain.model.ProfileModel
import com.duaag.android.recommender.domain.model.TagModel
import com.duaag.android.recommender.domain.model.UserRecommendationsModel
import toRmodModel

fun UserRecommendationsDto.toUserRecommendationsModel(): UserRecommendationsModel {
    return UserRecommendationsModel(
        cards = this.cards ?: emptyList(),
        featuredProfiles = this.featuredProfiles?.map { it.toFeaturedProfile() } ?: emptyList(),
        rmodItem = this.rmod?.toRmodModel(),
    )
}


fun TagDto.toTagModel(): TagModel {
    return TagModel(
        id = this.id,
        tagItemId  = this.tagItemId,
        tagTypeId = this.tagTypeId
    )
}

fun PictureDto.toPictureModel(): PictureModel {
    return PictureModel(
        position = position,
        url = this.url
    )
}

fun ProfileDto.toProfileModel(): ProfileModel{
    return ProfileModel(
        actualAddress = this.actualAddress ?: "",
        address = this.address ?: "",
        blurredProfileUrl = this.blurredProfileUrl,
        description = this.description,
        distance = this.distance,
        educations = this.educations,
        hobbies = this.hobbies,
        jobs = this.jobs,
        languages = this.languages,
        pictureUrl = this.pictureUrl,
        pictures = this.pictures?.map { it.toPictureModel() }
    )
}

fun FeaturedProfileDto.toFeaturedProfile(): FeaturedProfileModel {

    return FeaturedProfileModel(
        age = this.age,
        badge2 = this.badge2,
        cognitoUserId = this.cognitoUserId,
        firstName = this.firstName ?: "",
        gender = this.gender,
        hasBadge1 = this.hasBadge1 ?: false,
        id = this.id,
        instagramStatus = this.instagramStatus,
        isBlurred = this.isBlurred ?: false,
        isPremium = this.isPremium ?: false,
        isSpotlight = this.isSpotlight ?: false,
        mode = this.mode,
        profile = this.profile.toProfileModel(),
        tags = this.tags?.map { it.toTagModel() },
        zodiac = this.zodiac,
        activityType = RecommendedUsersActivityStatus.fromType(activityType)
    )
}