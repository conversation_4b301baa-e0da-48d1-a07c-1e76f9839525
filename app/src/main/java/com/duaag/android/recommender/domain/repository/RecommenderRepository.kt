package com.duaag.android.recommender.domain.repository

import com.duaag.android.api.Resource
import com.duaag.android.recommender.domain.model.UserRecommendationsModel
import kotlinx.coroutines.flow.Flow


interface RecommenderRepository {

  fun getCardsAndFeaturedProfiles(fetchCards: <PERSON>olean, fetchFeaturedProfiles: Boolean): Flow<Resource<UserRecommendationsModel>>
  fun setRmodSeen(): Flow<Resource<Unit>>

}