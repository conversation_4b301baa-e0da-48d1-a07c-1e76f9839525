package com.duaag.android.recommender.presentation.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.core.view.doOnPreDraw
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.FeaturedProfileEmptyItemBinding
import com.duaag.android.databinding.FeaturedProfileHeaderBinding
import com.duaag.android.databinding.FeaturedProfileHiddenProfileItemBinding
import com.duaag.android.databinding.FeaturedProfileItemBinding
import com.duaag.android.databinding.FeaturedProfileNetworkItemBinding
import com.duaag.android.databinding.FeaturedProfileShimmerItemBinding
import com.duaag.android.databinding.FeaturedProfileSpotlightItemBinding
import com.duaag.android.databinding.FeaturedProfileSunnyHillItemBinding
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.models.RecommendedUsersActivityStatus
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.recommender.domain.model.FeaturedProfileModel
import com.duaag.android.recommender.presentation.FeaturedProfilesState
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.bindImage
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.utils.sunnyhill.SunnyHillUtils
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class FeaturedProfileAdapter(
    private val homeViewModel: HomeViewModel,
    private val featuredProfileInteraction: FeaturedProfileInteraction,
) : ListAdapter<FeaturedProfileDataItem,
        RecyclerView.ViewHolder>(FeaturedProfileDiffCallback()) {

    private val adapterScope = CoroutineScope(Dispatchers.Default)
    companion object {
         const val ITEM_VIEW_TYPE_SPOTLIGHT = 0
         const val ITEM_VIEW_TYPE_ITEM = 1
         const val ITEM_VIEW_TYPE_EMPTY = 2
         const val ITEM_VIEW_TYPE_HEADER = 3
         const val ITEM_VIEW_TYPE_SHIMMER = 4
         const val ITEM_VIEW_TYPE_NETWORK = 5
         const val ITEM_VIEW_TYPE_HIDDEN = 6
         const val ITEM_VIEW_TYPE_SUNNY_HILL = 7
    }

    fun addData(data: FeaturedProfilesState) {
        var isSunnyHillGraphicShown = false
        adapterScope.launch {
            val items = when {
                data.isLoading -> listOf(FeaturedProfileDataItem.Shimmer)
                data.isNetworkDisconnected -> listOf(FeaturedProfileDataItem.Network)
                data.userProfile?.isInvisible == true -> listOf(FeaturedProfileDataItem.HiddenProfile)
                else -> if(data.list.isEmpty()) listOf(FeaturedProfileDataItem.EmptyState)
                else data.list.map { featuredProfile ->
                    if(featuredProfile.isSpotlight)
                        FeaturedProfileDataItem.FeaturedSpotlightItem(featuredProfile)
                    else if (SunnyHillUtils.shouldShowSunnyHillViews() && !isSunnyHillGraphicShown) {
                        isSunnyHillGraphicShown = true
                        FeaturedProfileDataItem.SunnyHill
                    } else FeaturedProfileDataItem.FeaturedProfileItem(featuredProfile)

                }
            }
              withContext(Dispatchers.Main) {
                  submitList(items)
              }
            }
        }


    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position) as FeaturedProfileDataItem

        when (holder) {
            is SpotlightViewHolder -> bindSpotLightViewHolder(item as FeaturedProfileDataItem.FeaturedSpotlightItem, holder)
            is ItemViewHolder -> bindItemViewHolder(item as FeaturedProfileDataItem.FeaturedProfileItem, holder)
            is HiddenProfileViewHolder -> holder.bind(featuredProfileInteraction)
            is SunnyHillViewHolder -> holder.bind(featuredProfileInteraction,homeViewModel)

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_VIEW_TYPE_ITEM -> ItemViewHolder.from(parent)
            ITEM_VIEW_TYPE_SPOTLIGHT -> SpotlightViewHolder.from(parent)
            ITEM_VIEW_TYPE_SUNNY_HILL -> SunnyHillViewHolder.from(parent)
            ITEM_VIEW_TYPE_EMPTY -> EmptyStateViewHolder.from(parent)
            ITEM_VIEW_TYPE_HEADER -> HeaderViewHolder.from(parent)
            ITEM_VIEW_TYPE_SHIMMER -> ShimmerViewHolder.from(parent)
            ITEM_VIEW_TYPE_NETWORK -> NetworkViewHolder.from(parent)
            ITEM_VIEW_TYPE_HIDDEN -> HiddenProfileViewHolder.from(parent)
            else -> throw ClassCastException("Unknown viewType $viewType")
        }
    }


    override fun getItemViewType(position: Int): Int {
        return when(getItem(position)) {
            is FeaturedProfileDataItem.EmptyState -> ITEM_VIEW_TYPE_EMPTY
            is FeaturedProfileDataItem.Header -> ITEM_VIEW_TYPE_HEADER
            is FeaturedProfileDataItem.FeaturedSpotlightItem -> ITEM_VIEW_TYPE_SPOTLIGHT
            is FeaturedProfileDataItem.Shimmer -> ITEM_VIEW_TYPE_SHIMMER
            is FeaturedProfileDataItem.Network -> ITEM_VIEW_TYPE_NETWORK
            is FeaturedProfileDataItem.HiddenProfile -> ITEM_VIEW_TYPE_HIDDEN
            is FeaturedProfileDataItem.SunnyHill -> ITEM_VIEW_TYPE_SUNNY_HILL
            else -> ITEM_VIEW_TYPE_ITEM

        }
    }
    private fun bindItemViewHolder(
        item: FeaturedProfileDataItem.FeaturedProfileItem,
        holder: RecyclerView.ViewHolder
    ) {
        when {
             item.featuredProfile.isBlurred -> (holder as ItemViewHolder).bindBlur(item.featuredProfile,featuredProfileInteraction)
             item.featuredProfile.gender == GenderType.WOMAN.value -> (holder as ItemViewHolder).bindFemale(
                item.featuredProfile,
                homeViewModel,
                featuredProfileInteraction,
            )

            else -> (holder as ItemViewHolder).bind(
                item.featuredProfile,
                homeViewModel,
                featuredProfileInteraction
            )
        }
    }

    private fun bindSpotLightViewHolder(
        item: FeaturedProfileDataItem.FeaturedSpotlightItem,
        holder: SpotlightViewHolder
    ) {
        when (item.featuredProfile.gender) {
            GenderType.WOMAN.value -> holder.bindFemale(
                item.featuredProfile,
                homeViewModel,
                featuredProfileInteraction
            )

            else -> holder.bind(item.featuredProfile, homeViewModel,featuredProfileInteraction)
        }
    }
    fun isItemBlurred(position: Int): Pair<Int, Boolean?> {
        val item = getItem(position) as? FeaturedProfileDataItem.FeaturedProfileItem
        val filterNotBlurred = currentList.filterNot { (it as? FeaturedProfileDataItem.FeaturedProfileItem)?.featuredProfile?.isBlurred == true }
        return Pair(filterNotBlurred.size,item?.featuredProfile?.isBlurred)
    }

    fun showHeader() {
        adapterScope.launch {
            val items = listOf(FeaturedProfileDataItem.Header) + currentList
            withContext(Dispatchers.Main) {
                submitList(items)
            }
        }
    }

    class SpotlightViewHolder private constructor(val binding: FeaturedProfileSpotlightItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(
            item: FeaturedProfileModel,
            homeViewModel: HomeViewModel,
            featuredProfileInteraction: FeaturedProfileInteraction
        ) {
            binding.featuredUserName.text = item.firstName
            binding.featuredUserAge.text  = ", ${item.age}"
            binding.featuredSpotUserButtonText.apply {
                text = context.getString(R.string.like_action)
            }
            updateUsersActivityStatusUi(item,binding.userActivity)

            binding.featuredSpotUserButton.apply {
                background = ContextCompat.getDrawable(
                    binding.root.context,
                    R.drawable.dua_button_background
                )
                setOnSingleClickListener {
                    featuredProfileInteraction.onCardInteraction(item,InteractionType.LIKE)
                }
            }

            binding.root.setOnSingleClickListener{
                featuredProfileInteraction.onItemInteraction(item)
            }
            binding.featuredSpotUserButtonIcon.setImageResource(R.drawable.heart_icon)
            val imageUrl = item.profile.pictureUrl

            bindImage(binding.featuredUserImage, imageUrl)

        }

        fun bindFemale(
            item: FeaturedProfileModel,
            homeViewModel: HomeViewModel,
            featuredProfileInteraction: FeaturedProfileInteraction
        ) {
            binding.featuredUserName.text = item.firstName
            binding.featuredUserAge.text = ", ${item.age}"
            binding.featuredSpotUserButtonText.apply {
                text = context.getString(R.string.insta_chats_string)
            }
            updateUsersActivityStatusUi(item,binding.userActivity)

            binding.featuredSpotUserButton.apply {
                background = ContextCompat.getDrawable(
                    binding.root.context,
                    R.drawable.featured_user_female_background
                )
                setOnSingleClickListener {
                    featuredProfileInteraction.onCardInteraction(item,InteractionType.INSTA_CHAT)
                }
            }
            binding.featuredSpotUserButtonIcon.apply {
                setImageResource(R.drawable.ic_instachat_small)
                imageTintList = ContextCompat.getColorStateList(context, R.color.gray_50)
            }
            binding.root.setOnSingleClickListener{
                featuredProfileInteraction.onItemInteraction(item)
            }
            val imageUrl = item.profile.pictureUrl

            bindImage(binding.featuredUserImage, imageUrl)
        }

        private fun updateUsersActivityStatusUi(item: FeaturedProfileModel, userActivityView: AppCompatImageView) {
            setVisibility(userActivityView,item.activityType != null)
            updateUsersActivityStatusDrawable(binding.userActivity,item)
        }

        private fun updateUsersActivityStatusDrawable(userActivityView: AppCompatImageView, item: FeaturedProfileModel) {
            val statusDrawable = when (item.activityType) {
                RecommendedUsersActivityStatus.ACTIVE_NOW -> ContextCompat.getDrawable(
                    userActivityView.context,
                    R.drawable.activity_status_now_illustration
                )

                RecommendedUsersActivityStatus.ACTIVE_TODAY -> ContextCompat.getDrawable(
                    userActivityView.context,
                    R.drawable.activity_status_today_illustration
                )

                RecommendedUsersActivityStatus.RECENTLY_ACTIVE -> ContextCompat.getDrawable(
                    userActivityView.context,
                    R.drawable.activity_status_recently_illustration
                )

                null ->  null
            }
            userActivityView.setImageDrawable(statusDrawable)
        }

        companion object {
            fun from(parent: ViewGroup): RecyclerView.ViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding =
                    FeaturedProfileSpotlightItemBinding.inflate(layoutInflater, parent, false)

                return SpotlightViewHolder(binding)
            }
        }
    }
    class EmptyStateViewHolder private constructor(val binding: FeaturedProfileEmptyItemBinding) :
            RecyclerView.ViewHolder(binding.root){
                companion object {
                    fun from(parent: ViewGroup) : EmptyStateViewHolder {
                        val layoutInflater = LayoutInflater.from(parent.context)
                        val binding =
                            FeaturedProfileEmptyItemBinding.inflate(layoutInflater,parent,false)
                        return EmptyStateViewHolder(binding)
                    }
                }
            }
    class SunnyHillViewHolder private constructor(val binding: FeaturedProfileSunnyHillItemBinding) :
        RecyclerView.ViewHolder(binding.root){
        fun bind(
            featuredProfileInteraction: FeaturedProfileInteraction,
            viewModel: HomeViewModel
        ) {
            binding.buyBtn.setOnSingleClickListener {
                featuredProfileInteraction.onSunnyHillItemInteraction()
            }
            val drawable = if(viewModel.userProfile.value?.premiumType != null) ContextCompat.getDrawable(binding.root.context,
                R.drawable.sunny_hill_premium_item
            ) else ContextCompat.getDrawable(binding.root.context,
                R.drawable.sunny_hill_freemium_item
            )
            binding.sunnyHillGraphic.setImageDrawable(drawable)
        }

        companion object {
            fun from(parent: ViewGroup) : SunnyHillViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding =
                    FeaturedProfileSunnyHillItemBinding.inflate(layoutInflater,parent,false)
                return SunnyHillViewHolder(binding)
            }
        }
    }
    class HeaderViewHolder private constructor(val binding: FeaturedProfileHeaderBinding) :
        RecyclerView.ViewHolder(binding.root){
        companion object {
            fun from(parent: ViewGroup) : HeaderViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding =
                    FeaturedProfileHeaderBinding.inflate(layoutInflater,parent,false)
                return HeaderViewHolder(binding)
            }
        }
    }
    class ShimmerViewHolder private constructor(val binding: FeaturedProfileShimmerItemBinding) :
        RecyclerView.ViewHolder(binding.root){
        companion object {
            fun from(parent: ViewGroup) : ShimmerViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding =
                    FeaturedProfileShimmerItemBinding.inflate(layoutInflater,parent,false)
                return ShimmerViewHolder(binding)
            }
        }
    }

    class NetworkViewHolder private constructor(val binding: FeaturedProfileNetworkItemBinding) :
        RecyclerView.ViewHolder(binding.root){
        companion object {
            fun from(parent: ViewGroup) : NetworkViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding =
                    FeaturedProfileNetworkItemBinding.inflate(layoutInflater,parent,false)
                return NetworkViewHolder(binding)
            }
        }
    }
    class HiddenProfileViewHolder private constructor(val binding: FeaturedProfileHiddenProfileItemBinding) :
        RecyclerView.ViewHolder(binding.root){
        fun bind(
            featuredProfileInteraction: FeaturedProfileInteraction
        ) {
            binding.unhideProfileButton.setOnSingleClickListener {
                featuredProfileInteraction.onUnHideProfileInteraction()
            }
        }

        companion object {
            fun from(parent: ViewGroup) : HiddenProfileViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding =
                    FeaturedProfileHiddenProfileItemBinding.inflate(layoutInflater,parent,false)
                return HiddenProfileViewHolder(binding)
            }
        }
    }
    class ItemViewHolder private constructor(val binding: FeaturedProfileItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(
            item: FeaturedProfileModel,
            homeViewModel: HomeViewModel,
            featuredProfileInteraction: FeaturedProfileInteraction
        ) {
            binding.featuredUserAge.visibility = View.VISIBLE
            binding.root.setOnSingleClickListener{
                featuredProfileInteraction.onItemInteraction(item)
            }
            updateUsersActivityStatusUi(item,binding.userActivity)

            binding.featuredUserButton.featuredLikeButton.visibility = View.VISIBLE
            setVisibility(
                binding.featuredUserBadge,
                item.badge2.equals(Badge2Status.APPROVED.status)
            )

            binding.featuredUserName.text = item.firstName
            binding.featuredUserAge.text = ", ${item.age}"
            binding.featuredUserButton.materialButton.apply {
                text =  context.getString(R.string.like_action)
                icon = ContextCompat.getDrawable(context, R.drawable.heart_icon)
                backgroundTintList = null
                background =
                    ContextCompat.getDrawable(
                        binding.root.context,
                        R.drawable.dua_button_background
                    )
                setOnSingleClickListener {
                    featuredProfileInteraction.onCardInteraction(item,InteractionType.LIKE)
                }
                doOnPreDraw {
                    val isTextEllipsized = (it as MaterialButton).lineCount != 1
                    if(!isTextEllipsized) {
                        text = context.getString(R.string.like_action)
                    } else {
                        text = ""
                        setLines(1)

                    }
                }
            }

            val imageUrl = item.profile.pictureUrl

            bindImage(binding.featuredUserImage, imageUrl)

        }

        private fun updateUsersActivityStatusUi(
            item: FeaturedProfileModel,
            userActivityView: AppCompatImageView
        ) {
            setVisibility(userActivityView,item.activityType != null)
            updateUsersActivityStatusDrawable(binding.userActivity,item)
        }

        private fun updateUsersActivityStatusDrawable(
            userActivityView: AppCompatImageView,
            item: FeaturedProfileModel
        ) {
            val statusDrawable = when (item.activityType) {
                RecommendedUsersActivityStatus.ACTIVE_NOW -> ContextCompat.getDrawable(
                    userActivityView.context,
                    R.drawable.activity_status_now_illustration
                )

                RecommendedUsersActivityStatus.ACTIVE_TODAY -> ContextCompat.getDrawable(
                    userActivityView.context,
                    R.drawable.activity_status_today_illustration
                )

                RecommendedUsersActivityStatus.RECENTLY_ACTIVE -> ContextCompat.getDrawable(
                    userActivityView.context,
                    R.drawable.activity_status_recently_illustration
                )

                null ->  null
            }
            userActivityView.setImageDrawable(statusDrawable)
        }

        fun bindBlur(
            item: FeaturedProfileModel,
            featuredProfileInteraction: FeaturedProfileInteraction
        ) {
            binding.root.setOnSingleClickListener{
                featuredProfileInteraction.onBlurItemInteraction(item)
            }
            binding.featuredUserAge.visibility = View.GONE
            // TODO: Check if the firstName comes truncated from backend or we should handle it
            binding.featuredUserName.text = item.firstName.substring(0, 2) + "***"      //only for testing

            binding.featuredUserButton.featuredLikeButton.visibility = View.GONE
            binding.featuredUserBadge.visibility = View.GONE

            //get bluredThumbnailUrl
            bindImage(binding.featuredUserImage, item.profile.blurredProfileUrl)
        }

        fun bindFemale(
            item: FeaturedProfileModel,
            homeViewModel: HomeViewModel,
            featuredProfileInteraction: FeaturedProfileInteraction
        ) {
            binding.featuredUserAge.visibility = View.VISIBLE
            binding.root.setOnSingleClickListener{
                featuredProfileInteraction.onItemInteraction(item)
            }
            binding.featuredUserButton.featuredLikeButton.visibility = View.VISIBLE
            binding.featuredUserName.text = item.firstName
            binding.featuredUserAge.text = ", ${item.age}"
            binding.featuredUserButton.materialButton.apply {
                text = context.getString(R.string.insta_chats_string)
                backgroundTintList = null
                icon = ContextCompat.getDrawable(context, R.drawable.ic_instachat_small)
                background =
                    ContextCompat.getDrawable(
                        binding.root.context,
                        R.drawable.featured_user_female_background
                    )

                setOnSingleClickListener {
                    featuredProfileInteraction.onCardInteraction(item,InteractionType.INSTA_CHAT)
                }

               doOnPreDraw {
                   val isTextEllipsized = (it as MaterialButton).lineCount != 1
                   if(!isTextEllipsized) {
                       text = context.getString(R.string.insta_chats_string)
                   } else {
                       text = ""
                       setLines(1)

                   }
               }

                }

            setVisibility(
                binding.featuredUserBadge,
                item.badge2.equals(Badge2Status.APPROVED.status)
            )

            val imageUrl = item.profile.pictureUrl

            bindImage(binding.featuredUserImage, imageUrl)

        }

        companion object {
            fun from(parent: ViewGroup): RecyclerView.ViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding = FeaturedProfileItemBinding.inflate(layoutInflater, parent, false)

                return ItemViewHolder(binding)
            }
        }
    }

    interface FeaturedProfileInteraction {
        fun onCardInteraction(featuredProfile: FeaturedProfileModel, interactionType: InteractionType)
        fun onBlurItemInteraction(featuredProfile: FeaturedProfileModel)
        fun onItemInteraction(featuredProfile: FeaturedProfileModel)
        fun onSunnyHillItemInteraction()
        fun onUnHideProfileInteraction()
    }
}

sealed class FeaturedProfileDataItem {

    data class FeaturedSpotlightItem(val featuredProfile: FeaturedProfileModel,
                                     override val id: Int? = featuredProfile.id
    ) : FeaturedProfileDataItem()

    data class FeaturedProfileItem(val featuredProfile: FeaturedProfileModel,
                                   override val id: Int? = featuredProfile.id
    ) : FeaturedProfileDataItem()
    object EmptyState : FeaturedProfileDataItem() {
        override val id: Int?
            get() = Int.MIN_VALUE
    }
    object Header : FeaturedProfileDataItem() {
        override val id: Int?
            get() = Int.MIN_VALUE
    }

    object Shimmer : FeaturedProfileDataItem() {
        override val id: Int?
            get() = Int.MIN_VALUE
    }

    object Network : FeaturedProfileDataItem() {
        override val id: Int?
            get() = Int.MIN_VALUE
    }

    object HiddenProfile : FeaturedProfileDataItem() {
        override val id: Int?
            get() = Int.MIN_VALUE

    }

    object SunnyHill : FeaturedProfileDataItem() {
        override val id: Int?
            get() = Int.MIN_VALUE

    }
    abstract val id : Int?
}

class FeaturedProfileDiffCallback : DiffUtil.ItemCallback<FeaturedProfileDataItem>() {
    override fun areItemsTheSame(
        oldItem: FeaturedProfileDataItem,
        newItem: FeaturedProfileDataItem
    ): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(
        oldItem: FeaturedProfileDataItem,
        newItem: FeaturedProfileDataItem
    ): Boolean {
        return oldItem == newItem
    }
}

