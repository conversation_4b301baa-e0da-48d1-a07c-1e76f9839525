package com.duaag.android.manage_pictures.di

import androidx.lifecycle.ViewModel
import com.duaag.android.di.ViewModelKey
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class ManagePicturesViewModelModule {

    @Binds
    @IntoMap
    @ViewModelKey(ChoosePictureViewModel::class)
    abstract fun bindChoosePicturesViewModel(myViewModel: ChoosePictureViewModel): ViewModel

}