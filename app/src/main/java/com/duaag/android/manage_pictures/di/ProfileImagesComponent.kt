package com.duaag.android.manage_pictures.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.signup.fragment.ChoosePicturesBottomSheet
import dagger.Subcomponent

@ActivityScope
@Subcomponent(modules = [ProfileImagesViewModelModule::class])
interface ProfileImagesComponent {

    // Factory to create instances of RegistrationComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): ProfileImagesComponent
    }

    // Classes that can be injected by this Component
    fun inject(fragment: ChoosePicturesBottomSheet)

}