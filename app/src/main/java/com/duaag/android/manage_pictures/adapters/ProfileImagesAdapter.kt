package com.duaag.android.manage_pictures.adapters

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.application.DuaApplication
import com.duaag.android.databinding.AddImageItemBinding
import com.duaag.android.databinding.EmptyContainerItemBinding
import com.duaag.android.databinding.FilledContainerItemBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.manage_pictures.models.ProfileImageModel
import com.duaag.android.manage_pictures.viewmodels.ProfileImagesViewModel.Companion.FREEMIUM_MAX_ITEMS
import com.duaag.android.manage_pictures.viewmodels.ProfileImagesViewModel.Companion.MAX_ITEMS


class ProfileImagesAdapter(val clickListener: UserPictureClickListener,
                           val homeViewModel: HomeViewModel,
                           val shouldShowPremiumScreen: Boolean = true,
                           val showMaxItems: Boolean = false) : ListAdapter<ProfileImageModel, RecyclerView.ViewHolder>(DiffUtilCallBack()) {

    companion object {
        const val EMPTY_VIEW_TYPE = 0
        const val FILLED_VIEW_TYPE = 1
        const val ADD_VIEW_TYPE = 2
    }

    private val maxItems : Int by lazy{
        if (DuaApplication.instance.getBillingAvailable() &&
            DuaApplication.instance.shouldShowPremium ||
            homeViewModel.userProfile.value?.premiumType != null ||
            showMaxItems)
            MAX_ITEMS
        else
            FREEMIUM_MAX_ITEMS
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ADD_VIEW_TYPE -> {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding = AddImageItemBinding.inflate(layoutInflater, parent, false)
                AddImageViewHolder(binding, clickListener)
            }
            EMPTY_VIEW_TYPE -> {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding = EmptyContainerItemBinding.inflate(layoutInflater, parent, false)
                EmptyContainerViewHolder(binding)
            }
            FILLED_VIEW_TYPE -> {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding = FilledContainerItemBinding.inflate(layoutInflater, parent, false)
                FilledContainerViewHolder(binding, homeViewModel, clickListener)
            }
            else -> throw ClassCastException("Unknown viewType $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is FilledContainerViewHolder -> clickListener.let { holder.bind(getItem(position)) }
            is AddImageViewHolder -> clickListener.let { holder.bind(getItem(position)) }
            is EmptyContainerViewHolder -> clickListener.let { holder.bind() }
        }
    }

    override fun getItemViewType(position: Int): Int {
        val isEmpty = currentList[position].url.isNullOrEmpty()

        return if (isEmpty) {
            if (position < MAX_ITEMS
                && (position > 0 && !currentList[position - 1].url.isNullOrEmpty())
                && (position < FREEMIUM_MAX_ITEMS || position >= FREEMIUM_MAX_ITEMS && shouldShowPremiumScreen))
                ADD_VIEW_TYPE
            else {
                EMPTY_VIEW_TYPE
            }
        } else {
            FILLED_VIEW_TYPE
        }
    }

    fun betaSubmitList(list: List<ProfileImageModel>) {
        val newList = list.toMutableList()
        for (i in 1..maxItems - list.size)
            newList.add(ProfileImageModel(null))

        submitList(newList)
    }

    inner class FilledContainerViewHolder constructor(var binding: FilledContainerItemBinding,
                                                        val homeViewModel: HomeViewModel,
                                                        val clickListener: UserPictureClickListener) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.imageButton.setOnClickListener {
                clickListener.onDeleteButtonClicked(bindingAdapterPosition)
            }

            binding.deleteThisPicture.setOnClickListener {
                clickListener.onDeleteButtonClicked(bindingAdapterPosition)
            }
        }

        fun bind(item: ProfileImageModel) {
            val isShadowBanned = homeViewModel.userProfile.value?.profile?.isShadowBanned ?: false
            binding.item = item
            binding.blurred = homeViewModel.userProfile.value?.profile?.hasBlurredPhotos ?: false

            binding.showDeleteButton = isShadowBanned && bindingAdapterPosition == 0 && currentList.count { it.url != null } == 1

            binding.executePendingBindings()
        }

    }

    class EmptyContainerViewHolder constructor(var binding: EmptyContainerItemBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind() {
            binding.executePendingBindings()
        }

    }

    class AddImageViewHolder constructor(var binding: AddImageItemBinding,
                                                 val clickListener: UserPictureClickListener) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.cardView2.setOnClickListener {
                clickListener.onAddButtonClicked(bindingAdapterPosition)
            }
        }

        fun bind(item: ProfileImageModel) {
            binding.item = item
            binding.executePendingBindings()
        }

    }

    //Implementing DiffUtil in our Adapter
    class DiffUtilCallBack : DiffUtil.ItemCallback<ProfileImageModel>() {
        override fun areItemsTheSame(oldItem: ProfileImageModel, newItem: ProfileImageModel): Boolean {
            return oldItem.url == newItem.url
        }

        @SuppressLint("DiffUtilEquals")
        override fun areContentsTheSame(oldItem: ProfileImageModel, newItem: ProfileImageModel): Boolean {
            //We compare something unique
            return oldItem == newItem
        }

    }

    interface UserPictureClickListener {
        fun onDeleteButtonClicked(position: Int)
        fun onAddButtonClicked(position: Int)
    }

}


