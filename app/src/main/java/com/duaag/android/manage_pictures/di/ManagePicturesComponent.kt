package com.duaag.android.manage_pictures.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.image_verification.fragments.ImageDeniedFragment
import com.duaag.android.manage_pictures.ConfirmHideMyPhotosFragment
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.signup.fragment.ChoosePicturesBottomSheet
import dagger.Subcomponent

@ActivityScope
@Subcomponent(modules = [
    ManagePicturesViewModelModule::class,
    ProfileImagesViewModelModule::class
])
interface ManagePicturesComponent {

    // Factory to create instances of RegistrationComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): ManagePicturesComponent
    }

    // Classes that can be injected by this Component
    fun inject(fragment: ManagePicturesActivity)
    fun inject(fragment: ChoosePicturesBottomSheet)
    fun inject(imageDeniedFragment: ImageDeniedFragment)
    fun inject(confirmHideMyPhotosFragment: ConfirmHideMyPhotosFragment)

}