package com.duaag.android.manage_pictures

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.views.DragHandler
import com.duaag.android.manage_pictures.adapters.ProfileImagesAdapter

class DragCallback(mDragListener: DragHandler) : ItemTouchHelper.SimpleCallback(ItemTouchHelper.UP or ItemTouchHelper.DOWN or ItemTouchHelper.START or ItemTouchHelper.END, 0) {

    private var dragListener = mDragListener
    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)
        when (viewHolder) {
            is ProfileImagesAdapter.FilledContainerViewHolder -> {
                viewHolder.itemView.alpha = 1.0f
            }
        }

    }


    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        val dragFlags = ItemTouchHelper.START or ItemTouchHelper.END or ItemTouchHelper.UP or ItemTouchHelper.DOWN
        //Disable drag on one viewHolder
        return if (viewHolder.itemViewType == ProfileImagesAdapter.EMPTY_VIEW_TYPE
                || viewHolder.itemViewType == ProfileImagesAdapter.ADD_VIEW_TYPE) 0 else ItemTouchHelper.Callback.makeMovementFlags(dragFlags, 0)
    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        super.onSelectedChanged(viewHolder, actionState)

        //change alpha on LongClick on our desired ViewHolder
        when (viewHolder) {
            is ProfileImagesAdapter.FilledContainerViewHolder -> {
                if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
                    viewHolder.itemView.alpha = 0.8f
                }
            }
        }
    }


    override fun onMove(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        when (viewHolder) {
            is ProfileImagesAdapter.FilledContainerViewHolder -> {
                when (target) {
                    is ProfileImagesAdapter.FilledContainerViewHolder -> target.adapterPosition
                    else -> return false
                }
                return true
            }
        }
        return false
    }

    override fun onMoved(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, fromPos: Int, target: RecyclerView.ViewHolder, toPos: Int, x: Int, y: Int) {
        super.onMoved(recyclerView, viewHolder, fromPos, target, toPos, x, y)
        when (viewHolder) {
            is ProfileImagesAdapter.FilledContainerViewHolder -> {
                val adapter = recyclerView.adapter as ProfileImagesAdapter
                val from = viewHolder.adapterPosition
                val to = when (target) {
                    is ProfileImagesAdapter.FilledContainerViewHolder -> target.adapterPosition
                    else -> return
                }
                dragListener.onDataDraged(from, to)
                adapter.notifyItemMoved(from, to)
            }
        }
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

}

