package com.duaag.android.manage_pictures

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentConfirmHideMyPhotosBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.manage_pictures.viewmodels.ProfileImagesViewModel
import com.duaag.android.utils.loadImageRoundedCorners
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import javax.inject.Inject


class ConfirmHideMyPhotosFragment : Fragment() {

    private var _binding: FragmentConfirmHideMyPhotosBinding? = null
    private val binding get() = _binding!!

    companion object {
        private const val TAG = "ConfirmHideMyPhotosFragment"
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val profileImagesViewModel by viewModels<ProfileImagesViewModel>({activity as ManagePicturesActivity}) { viewModelFactory }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as ManagePicturesActivity).managePicturesComponent.inject(this)
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        _binding = FragmentConfirmHideMyPhotosBinding.inflate(inflater, container, false)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        validateCheckboxes()

        binding.limitationsCheckbox.setOnCheckedChangeListener { buttonView, isChecked ->
            validateCheckboxes()
        }
        binding.likesCheckbox.setOnCheckedChangeListener { buttonView, isChecked ->
            validateCheckboxes()
        }

        binding.confirmBtn.setOnSingleClickListener {
            profileImagesViewModel.hideMyPhotos(true)
        }

        profileImagesViewModel.hasBlurredPhotos.observe(viewLifecycleOwner) { hasBlurredPhotos ->
            if(hasBlurredPhotos) {
                onBlurredPhotosSuccessFullyEvent(profileImagesViewModel.user.value)
                findNavController().popBackStack()
            }

        }

        loadImageRoundedCorners(binding.image, profileImagesViewModel.user.value?.profile?.pictureUrl ?: "", 16, false)
        loadImageRoundedCorners(binding.blurredImage, profileImagesViewModel.user.value?.profile?.pictureUrl ?: "", 16, true)
        sendScreenViewEvent(profileImagesViewModel.user.value)
    }

    private fun onBlurredPhotosSuccessFullyEvent(user: UserModel?) {
        val premiumType = getPremiumTypeEventProperty(user)

        sendClevertapEvent(
            ClevertapEventEnum.HIDE_PHOTOS,
            mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType)
        )
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.HIDE_PHOTOS,
            mapOf(FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType)
        )
    }

    private fun sendScreenViewEvent(user: UserModel?) {
        val premiumType = getPremiumTypeEventProperty(user)
        sendClevertapEvent(ClevertapEventEnum.HIDE_PHOTOS_INITIATED,
            mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType)
        )
    }

    private fun validateCheckboxes() {
        val limitationsChecked = binding.limitationsCheckbox.isChecked
        val likesChecked = binding.likesCheckbox.isChecked

        binding.confirmBtn.isEnabled =  limitationsChecked && likesChecked
    }


    @SuppressLint("RestrictedApi")
    override fun onDestroyView() {
        super.onDestroyView()

        _binding = null
    }

}
