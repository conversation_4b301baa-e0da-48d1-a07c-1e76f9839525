package com.duaag.android.manage_pictures

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.Navigation
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.NavigationUI
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.fragment.ImagePickerFragment
import com.duaag.android.clevertap.ClevertapEditProfileSourceValues
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.UploadImageSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.ManagePicturesActivityBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.manage_pictures.di.ManagePicturesComponent
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.PremiumActivity.Companion.UPGRADED_PREMIUM
import com.duaag.android.profile_new.editprofile.EditProfileActivity.Companion.EDIT_PROFILE_SOURCE
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.hideKeyboard
import com.yalantis.ucrop.UCrop
import timber.log.Timber
import javax.inject.Inject


class ManagePicturesActivity : AppCompatActivity() {

    companion object {
        const val TAG = "ManagePicturesActivity"
        const val IMAGES_EXTRA = "images_extra"
        const val FEATURE_REQUIRES_PREMIUM_PAYWALL = "feature_requires_premium"
        const val WILL_BADGE2_BE_REMOVED = "WILL_BADGE2_BE_REMOVED"
        const val UPLOAD_IMAGE_SOURCE = "upload_image_source"

        const val NEW_IMAGES_REQUEST = 37
        const val REQUEST_CODE_PERMISSIONS = 101
        const val OPENED_FROM = "opened_from"
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val choosePicturesViewModel by viewModels<ChoosePictureViewModel> { viewModelFactory }
    lateinit var managePicturesComponent: ManagePicturesComponent

    private var _binding: ManagePicturesActivityBinding? = null
    private val binding get() = _binding!!

    private lateinit var navController: NavController
    private lateinit var appBarConfiguration: AppBarConfiguration

    //this is used because in Disabled feature if the user is not premium
    //we should not show premium activity
    var featureRequiresPremium = true

    var upgradedToPremium = false


    override fun onCreate(savedInstanceState: Bundle?) {
        managePicturesComponent = (application as DuaApplication).appComponent.managePicturesComponent().create()
        managePicturesComponent.inject(this)

        super.onCreate(savedInstanceState)
        _binding = ManagePicturesActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setSupportActionBar(binding.toolbar)
        setUpNav()
        val uploadImageSource = intent.getStringExtra(UPLOAD_IMAGE_SOURCE) ?: ClevertapEditProfileSourceValues.PROFILE.value
         sendScreenViewedEvent(choosePicturesViewModel,uploadImageSource)
        binding.let {
            it.toolbar.setNavigationOnClickListener {
                hideKeyboard()
                onBackPressed()
            }
        }

        if (intent.hasExtra(FEATURE_REQUIRES_PREMIUM_PAYWALL)) {
            featureRequiresPremium = intent.getBooleanExtra(FEATURE_REQUIRES_PREMIUM_PAYWALL, true)
        }
    }

    fun setToolbarTitle(title: String) {
        binding.toolbarTitle.text = title
    }
    private fun sendScreenViewedEvent(viewModel: ChoosePictureViewModel,uploadImageSource:String?) {
        val eventPremiumType = getPremiumTypeEventProperty(choosePicturesViewModel.user.value)
        val isPhotoHidden = viewModel.user.value?.profile?.hasBlurredPhotos

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.MY_PHOTOS, mapOf(
            FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
        ))

        sendClevertapEvent(
            ClevertapEventEnum.MY_PHOTOS, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                ClevertapEventPropertyEnum.UPLOAD_IMAGE_SOURCE.propertyName to uploadImageSource
            ))
    }

    fun handleChosenPhotos(images: List<String>,willBadge2BeRemoved: Boolean = false){
        val data = Intent()
        data.putExtra(UPGRADED_PREMIUM, upgradedToPremium)
        data.putStringArrayListExtra(IMAGES_EXTRA, ArrayList(images))
        data.putExtra(WILL_BADGE2_BE_REMOVED, willBadge2BeRemoved)

        setResult(Activity.RESULT_OK, data)
        finish()
    }

    override fun onSupportNavigateUp(): Boolean {
        return NavigationUI.navigateUp(navController, appBarConfiguration) || super.onSupportNavigateUp()
    }

    private fun setUpNav() {
        navController = Navigation.findNavController(this, R.id.nav_host_fragment)
        appBarConfiguration = AppBarConfiguration.Builder()
                .setFallbackOnNavigateUpListener {
                    // Trigger the Activity's navigate up functionality
                    super.onSupportNavigateUp()
                }.build()
        NavigationUI.setupActionBarWithNavController(this, navController, appBarConfiguration)
        navController.addOnDestinationChangedListener { _, destination, _ ->
            binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_angle_left)
        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        checkIfIsFromUICrop(requestCode, resultCode, data)
        when (requestCode) {
            PremiumActivity.PREMIUM_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    upgradedToPremium = true
                }
            }
        }
    }

    private fun checkIfIsFromUICrop(requestCode: Int, resultCode: Int, data: Intent?){
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                ImagePickerFragment.REQUEST_IMAGE_CAPTURE -> {
                    if (!NetworkChecker.isNetworkConnected(this)) {
                        ToastUtil.toast(getString(R.string.no_internet_string))
                        return
                    }
                    choosePicturesViewModel.imageChosen(data)
                }
                else -> {
                    data?.let {
                        UCrop.getOutput(it)?.let { outputUri ->
                            Timber.tag(TAG).d("outputUri: $outputUri")
                            if (!NetworkChecker.isNetworkConnected(this)) {
                                ToastUtil.toast(getString(R.string.no_internet_string))
                                return
                            }
                            choosePicturesViewModel.imageCropped(outputUri)
                        } ?: Timber.tag(TAG).d("outputUri: null")
                    }
                }
            }
        } else if (resultCode == UCrop.RESULT_ERROR) {
            Timber.tag("TAG").e("handleCrop: cannot crop the image")
        }
    }
}
