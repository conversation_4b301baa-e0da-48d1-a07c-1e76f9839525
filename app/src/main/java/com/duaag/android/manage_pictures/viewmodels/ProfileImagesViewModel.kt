package com.duaag.android.manage_pictures.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.api.ResourceV2
import com.duaag.android.api.Result
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.counters.domain.SyncUserCountersUseCase
import com.duaag.android.disabled.models.MarkReasonsAsResolvedRequestModel
import com.duaag.android.exceptions.NoConnectivityException
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.manage_pictures.models.ProfileImageModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.livedata.SingleLiveData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.collections.ArrayList

class ProfileImagesViewModel @Inject constructor(
    val userRepository: UserRepository,
    private val syncUserCountersUseCase: SyncUserCountersUseCase
) : ViewModel() {

    companion object {
        const val MAX_ITEMS = 6
        const val FREEMIUM_MAX_ITEMS = 6
    }


    private var initialStateImages = arrayListOf(
            ProfileImageModel.createEmptyImage(),
            ProfileImageModel.createEmptyImage(),
            ProfileImageModel.createEmptyImage())

    private var initialHasBlurredPhotos = false

    private var images = mutableListOf<ProfileImageModel>()

    //The Internal MutableLiveData List that stores data from API
    private val _photos = MutableLiveData<MutableList<ProfileImageModel>>()
    val photos: LiveData<MutableList<ProfileImageModel>>
        get() = _photos

    private val _stateChanged = MutableLiveData<Boolean>()
    val stateChanged: LiveData<Boolean>
        get() = _stateChanged

    private val _hasBlurredPhotos = MutableLiveData(false)
    val hasBlurredPhotos: LiveData<Boolean>
        get() = _hasBlurredPhotos

    var user = userRepository.user
    var deletedLastPhoto = false

    private val _userUpdated = SingleLiveData<Void>()
    val userUpdated: LiveData<Void>
        get() = _userUpdated

    fun getUserImages(): List<ProfileImageModel> {
        return images.filter { it.url != null }
    }

    fun checkStateChange() {
        if(images.size != initialStateImages.size ||
            initialHasBlurredPhotos != hasBlurredPhotos.value) {
            _stateChanged.value = true
        } else {
            var changed = false
            for (i in images.indices) {
                if (initialStateImages[i] != images[i])
                    changed = true
            }
            _stateChanged.value = changed
        }
    }

    fun setImages(images: List<ProfileImageModel>) {
        this.images.clear()
        this.images.addAll(images)

        this.images.removeAll { it.url == null }

        _photos.value = this.images
        initialStateImages = ArrayList(this.images)
        checkStateChange()
    }

    fun setHideMyPhotosInitialState(hidden: Boolean) {
        initialHasBlurredPhotos = hidden
    }

    fun setNewImages(urls: List<String>, willBadge2BeRemoved: Boolean = false) {
        if (deletedLastPhoto) {
            images.clear()
        } else {
            images.removeAll { it.url == null }
        }

        //add new chosen images
        urls.forEach {
            val image = ProfileImageModel(it)
            images.add(image)
        }

        images = images.distinctBy { it.url }.toMutableList()

        updateImages(
            images = getUserImages().map { it.url!! },
            willBadge2BeRemoved = willBadge2BeRemoved
        )

        _photos.value = images

        //reset deleted last photo so that it can handle the next time as it should
        deletedLastPhoto = false
    }

    fun updateImages(images: List<String>, willBadge2BeRemoved: Boolean = false) {
        viewModelScope.launch {
            if (images.isNotEmpty()) {
                val result = userRepository.updateAllUserPictures(images)
                when (result) {
                    is ResourceV2.Success -> {
                        syncUserCountersUseCase.invoke()
                            .catch { e -> Timber.e(e) }
                            .collect{ Timber.d("Counters updated") }
                        if (willBadge2BeRemoved) {
                            setUserBadge2NotApproved()
                        }
                        checkStateChange()
                        _userUpdated.call()

                        userRepository.updateProfilePercentageInClevertap()
                    }

                    is ResourceV2.Error -> {
                        Timber.tag("updateImages").d("updateImages ${result.message}")
                    }
                }
            }
        }
    }

    fun removePhoto(position: Int) {
        images.removeAt(position)
        _photos.value = images
    }

    fun addImageItem(addPosition: Int, item: ProfileImageModel) {
        images.add(addPosition, item)
        _photos.value = images
    }


    fun swapItemPositions(from: Int, to: Int) {
        val tempItem = images[from]
        images.removeAt(from)
        images.add(to, tempItem)
    }

    fun getValidImagesNumber(): Int {
        return images.filter { it.url != null }.size
    }

    fun getImageFromPosition(position: Int): ProfileImageModel {
        return images[position]
    }

    private fun updatePictures(images: List<String>): LiveData<Result<Boolean>> {
        return userRepository.updateUserPictures(images)
    }

    fun setProfilePicture(pictureUrl: String): LiveData<Result<Boolean>> {
        val params = mapOf("profile" to mapOf("pictureUrl" to pictureUrl))
        return userRepository.updateProfilePicture(pictureUrl, params)
    }

    fun deleteImage(position: Int, model: ProfileImageModel) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.deletePicture(position)
                .catch { ex ->
                    withContext(Dispatchers.Main){
                        ToastUtil.toast(ex.message!!)
                    }
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {
                            withContext(Dispatchers.Main){
                                Timber.tag("DELETE_PICTURE").d("Success")
                                checkStateChange()
                                _userUpdated.call()
                            }
                        }
                        is Resource.Error -> {
                        }
                        is Resource.Loading -> {
                        }
                    }
                }
        }
    }

    fun setUserBadge2NotApproved() {
        viewModelScope.launch(Dispatchers.IO) {
            val profile = user.value
            profile?.let {
                it.badge2 = Badge2Status.NOT_APPROVED.status
                userRepository.updateUser(it)
            }
        }
    }

    fun hideMyPhotos(hasBlurredPhotos: Boolean) {
        val newUser = user.value!!.copy()
        newUser.profile.hasBlurredPhotos = hasBlurredPhotos
        val param = mapOf("profile" to mapOf("hasBlurredPhotos" to hasBlurredPhotos))

        viewModelScope.launch {
            userRepository.adjustProfileInfoFlow(param, newUser)
                .catch { ex ->
                    ex.printStackTrace()
                    if(ex is NoConnectivityException)
                        ToastUtil.toast(R.string.no_internet_connection)

                    delay(500)
                    setHasBlurredPhotos(!hasBlurredPhotos)
                    checkStateChange()
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Result.Success -> {
                                setHasBlurredPhotos(hasBlurredPhotos)
                                if(!hasBlurredPhotos) {
                                    sendUnhidePhotosEvent(user.value)
                                }
                                checkStateChange()
                            }
                            else -> {}
                        }
                    }
                }
        }
    }

    private fun sendUnhidePhotosEvent(user: UserModel?) {
        val premiumType = getPremiumTypeEventProperty(user)
        sendClevertapEvent(
            ClevertapEventEnum.UNHIDE_PHOTOS,
            mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType)
        )
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.UNHIDE_PHOTOS,
            mapOf(FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to premiumType)
        )
    }

    fun setHasBlurredPhotos(hasBlurredPhotos: Boolean) {
        _hasBlurredPhotos.value = hasBlurredPhotos
    }

    fun getUploadMaximumImagesCount(): Int {
        val user = user.value
        return if (user?.premiumType != null) {
            ChoosePictureViewModel.MAX_IMAGES_PREMIUM
        } else {
            ChoosePictureViewModel.MAX_IMAGES_FREEMIUM
        }
    }
}