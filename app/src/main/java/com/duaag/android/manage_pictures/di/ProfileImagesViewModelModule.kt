package com.duaag.android.manage_pictures.di

import androidx.lifecycle.ViewModel
import com.duaag.android.di.ViewModelKey
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.manage_pictures.viewmodels.ProfileImagesViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class ProfileImagesViewModelModule {

    @Binds
    @IntoMap
    @ViewModelKey(ProfileImagesViewModel::class)
    abstract fun bindViewModel(myViewModel: ProfileImagesViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(HomeViewModel::class)
    abstract fun bindHomeViewModel(myViewModel: HomeViewModel): ViewModel
}