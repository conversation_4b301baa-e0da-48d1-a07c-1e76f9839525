package com.duaag.android.calls.fragments

import android.Manifest
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.Rect
import android.media.AudioManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.format.DateUtils
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.MotionEvent.*
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.calls.CallActivity
import com.duaag.android.calls.adapters.VideoCallOptionsAdapter
import com.duaag.android.calls.adapters.VideoCallOptionsAdapter.Companion.AUDIO_ACTION
import com.duaag.android.calls.adapters.VideoCallOptionsAdapter.Companion.BLUETOOTH
import com.duaag.android.calls.adapters.VideoCallOptionsAdapter.Companion.END_CALL
import com.duaag.android.calls.adapters.VideoCallOptionsAdapter.Companion.LOCAL_VIDEO
import com.duaag.android.calls.adapters.VideoCallOptionsAdapter.Companion.MUTE_ACTION
import com.duaag.android.calls.adapters.VideoCallOptionsAdapter.Companion.SWITCH_CAMERA
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.CALL_ACCEPT_ACTION
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.CALL_INCOMING_ACTION
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.VIDEO_CALL_DATA_KEY
import com.duaag.android.calls.models.*
import com.duaag.android.calls.services.CallService
import com.duaag.android.calls.utils.CameraCapturerCompat
import com.duaag.android.calls.viewmodels.CallViewModel
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.databinding.OngoingCallFragmentBinding
import com.duaag.android.firebase.model.CallModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.utils.*
import com.google.android.material.snackbar.Snackbar
import com.twilio.video.*
import jp.wasabeef.glide.transformations.BlurTransformation
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.properties.Delegates

class OngoingCallFragment : Fragment() {

    companion object {
        const val TAG = "OngoingCallFragment"
        private val CAMERA_PERMISSION_REQUEST_CODE = 1

        private const val CONTROLS_ANIMATION_DURATION = 300L
        private const val VIDEO_DRAG_DURATION = 300L

        const val TOP_SMALL_FEED_CONTROLS_ENABLED_MARGIN = 68f
        const val TOP_SMALL_FEED_CONTROLS_DISABLED_MARGIN = 44f
        const val DOWN_SMALL_FEED_CONTROLS_DISABLED_MARGIN = 40f
        const val DOWN_SMALL_FEED_CONTROLS_ENABLED_MARGIN = 16f
        const val SMALL_FEED_WIDTH = 106f
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel: HomeViewModel by viewModels({ activity as CallActivity }) { viewModelFactory }
    private val callViewModel: CallViewModel by viewModels({ activity as CallActivity }) { viewModelFactory }

    private var _binding: OngoingCallFragmentBinding? = null
    private val binding get() = _binding!!

    private var fadeAnimator: ObjectAnimator? = null
    private var localAudioTrack: LocalAudioTrack? = null
    private var localVideoTrack: LocalVideoTrack? = null
    private var remoteVideoTrack: VideoTrack? = null
    var shouldCheckForPermission = false

    /*
* Room events listener
*/
    private val roomListener = object : Room.Listener {
        override fun onConnected(room: Room) {
            binding.videoStatusTextView.text = "Connected to ${room.name}"

            // Only one participant is supported
            room.remoteParticipants.firstOrNull()?.let {
                addRemoteParticipant(it)
                if (localVideoTrack != null && localVideoTrack!!.isEnabled)
                    moveLocalVideoToThumbnailView()
            }
            setConnectedStateUI()
        }

        override fun onReconnected(room: Room) {
            binding.videoStatusTextView.text = "Connected to ${room.name}"
//            binding.reconnectingProgressBar.visibility = View.GONE
        }

        override fun onReconnecting(room: Room, twilioException: TwilioException) {
            binding.videoStatusTextView.text = "Reconnecting to ${room.name}"
//            binding.reconnectingProgressBar.visibility = View.VISIBLE
        }

        override fun onConnectFailure(room: Room, e: TwilioException) {
            binding.videoStatusTextView.text = "Failed to connect"
        }

        override fun onDisconnected(room: Room, e: TwilioException?) {
            binding.videoStatusTextView.text = "Disconnected from ${room.name}"
//            binding.reconnectingProgressBar.visibility = View.GONE
            // Only reinitialize the UI if disconnect was not called from onDestroy()
            if (!disconnectedFromOnDestroy) {
//                initializeUI()
                moveLocalVideoToPrimaryView()
            }
        }

        override fun onParticipantConnected(room: Room, participant: RemoteParticipant) {
            Timber.tag(TAG).d("onParticipantConnected")

            addRemoteParticipant(participant)
        }

        override fun onParticipantDisconnected(room: Room, participant: RemoteParticipant) {
            removeRemoteParticipant(participant)
        }

        override fun onRecordingStarted(room: Room) {
            /*
             * Indicates when media shared to a Room is being recorded. Note that
             * recording is only available in our Group Rooms developer preview.
             */
            Timber.tag(TAG).d("onRecordingStarted")
        }

        override fun onRecordingStopped(room: Room) {
            /*
             * Indicates when media shared to a Room is no longer being recorded. Note that
             * recording is only available in our Group Rooms developer preview.
             */
            Timber.tag(TAG).d("onRecordingStopped")
        }
    }

    /*
* RemoteParticipant events listener
*/
    private val participantListener = object : RemoteParticipant.Listener {
        override fun onAudioTrackPublished(
            remoteParticipant: RemoteParticipant,
            remoteAudioTrackPublication: RemoteAudioTrackPublication
        ) {
            Timber.tag(TAG).i("onAudioTrackPublished: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteAudioTrackPublication: sid=${remoteAudioTrackPublication.trackSid}, " +
                    "enabled=${remoteAudioTrackPublication.isTrackEnabled}, " +
                    "subscribed=${remoteAudioTrackPublication.isTrackSubscribed}, " +
                    "name=${remoteAudioTrackPublication.trackName}]"
            )

            binding.videoStatusTextView.text = "onAudioTrackAdded"
        }

        override fun onAudioTrackUnpublished(
            remoteParticipant: RemoteParticipant,
            remoteAudioTrackPublication: RemoteAudioTrackPublication
        ) {
            Timber.tag(TAG).i("onAudioTrackUnpublished: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteAudioTrackPublication: sid=${remoteAudioTrackPublication.trackSid}, " +
                    "enabled=${remoteAudioTrackPublication.isTrackEnabled}, " +
                    "subscribed=${remoteAudioTrackPublication.isTrackSubscribed}, " +
                    "name=${remoteAudioTrackPublication.trackName}]"
            )
            binding.videoStatusTextView.text = "onAudioTrackRemoved"
        }

        override fun onDataTrackPublished(
            remoteParticipant: RemoteParticipant,
            remoteDataTrackPublication: RemoteDataTrackPublication
        ) {
            Timber.tag(TAG).i("onDataTrackPublished: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteDataTrackPublication: sid=${remoteDataTrackPublication.trackSid}, " +
                    "enabled=${remoteDataTrackPublication.isTrackEnabled}, " +
                    "subscribed=${remoteDataTrackPublication.isTrackSubscribed}, " +
                    "name=${remoteDataTrackPublication.trackName}]"
            )
            binding.videoStatusTextView.text = "onDataTrackPublished"
        }

        override fun onDataTrackUnpublished(
            remoteParticipant: RemoteParticipant,
            remoteDataTrackPublication: RemoteDataTrackPublication
        ) {
            Timber.tag(TAG).i("onDataTrackUnpublished: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteDataTrackPublication: sid=${remoteDataTrackPublication.trackSid}, " +
                    "enabled=${remoteDataTrackPublication.isTrackEnabled}, " +
                    "subscribed=${remoteDataTrackPublication.isTrackSubscribed}, " +
                    "name=${remoteDataTrackPublication.trackName}]"
            )
            binding.videoStatusTextView.text = "onDataTrackUnpublished"
        }

        override fun onVideoTrackPublished(
            remoteParticipant: RemoteParticipant,
            remoteVideoTrackPublication: RemoteVideoTrackPublication
        ) {
            Timber.tag(TAG).i("onVideoTrackPublished: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteVideoTrackPublication: sid=${remoteVideoTrackPublication.trackSid}, " +
                    "enabled=${remoteVideoTrackPublication.isTrackEnabled}, " +
                    "subscribed=${remoteVideoTrackPublication.isTrackSubscribed}, " +
                    "name=${remoteVideoTrackPublication.trackName}]"
            )
            binding.videoStatusTextView.text = "onVideoTrackPublished"
            if (remoteVideoTrackPublication.videoTrack?.isEnabled == true)
                remoteVideoTrackPublication.videoTrack?.let {
                    addRemoteParticipantVideo(it)
                    if (localVideoTrack != null && localVideoTrack!!.isEnabled)
                        moveLocalVideoToThumbnailView()
                }
        }

        override fun onVideoTrackUnpublished(
            remoteParticipant: RemoteParticipant,
            remoteVideoTrackPublication: RemoteVideoTrackPublication
        ) {
            Timber.tag(TAG).i("onVideoTrackUnpublished: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteVideoTrackPublication: sid=${remoteVideoTrackPublication.trackSid}, " +
                    "enabled=${remoteVideoTrackPublication.isTrackEnabled}, " +
                    "subscribed=${remoteVideoTrackPublication.isTrackSubscribed}, " +
                    "name=${remoteVideoTrackPublication.trackName}]"
            )
            binding.videoStatusTextView.text = "onVideoTrackUnpublished"
            remoteVideoTrackPublication.videoTrack?.let { removeParticipantVideo(it) }
        }

        override fun onAudioTrackSubscribed(
            remoteParticipant: RemoteParticipant,
            remoteAudioTrackPublication: RemoteAudioTrackPublication,
            remoteAudioTrack: RemoteAudioTrack
        ) {
            Timber.tag(TAG).i("onAudioTrackSubscribed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteAudioTrack: enabled=${remoteAudioTrack.isEnabled}, " +
                    "playbackEnabled=${remoteAudioTrack.isPlaybackEnabled}, " +
                    "name=${remoteAudioTrack.name}]"
            )
            binding.videoStatusTextView.text = "onAudioTrackSubscribed"
        }

        override fun onAudioTrackUnsubscribed(
            remoteParticipant: RemoteParticipant,
            remoteAudioTrackPublication: RemoteAudioTrackPublication,
            remoteAudioTrack: RemoteAudioTrack
        ) {
            Timber.tag(TAG).i("onAudioTrackUnsubscribed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteAudioTrack: enabled=${remoteAudioTrack.isEnabled}, " +
                    "playbackEnabled=${remoteAudioTrack.isPlaybackEnabled}, " +
                    "name=${remoteAudioTrack.name}]"
            )
            lifecycleScope.launchWhenResumed {
                binding.videoStatusTextView.text = "onAudioTrackUnsubscribed"
            }
        }

        override fun onAudioTrackSubscriptionFailed(
            remoteParticipant: RemoteParticipant,
            remoteAudioTrackPublication: RemoteAudioTrackPublication,
            twilioException: TwilioException
        ) {
            Timber.tag(TAG).i("onAudioTrackSubscriptionFailed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteAudioTrackPublication: sid=${remoteAudioTrackPublication.trackSid}, " +
                    "name=${remoteAudioTrackPublication.trackName}]" +
                    "[TwilioException: code=${twilioException.code}, " +
                    "message=${twilioException.message}]"
            )
            binding.videoStatusTextView.text = "onAudioTrackSubscriptionFailed"
        }

        override fun onDataTrackSubscribed(
            remoteParticipant: RemoteParticipant,
            remoteDataTrackPublication: RemoteDataTrackPublication,
            remoteDataTrack: RemoteDataTrack
        ) {
            Timber.tag(TAG).i("onDataTrackSubscribed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteDataTrack: enabled=${remoteDataTrack.isEnabled}, " +
                    "name=${remoteDataTrack.name}]"
            )
            binding.videoStatusTextView.text = "onDataTrackSubscribed"
        }

        override fun onDataTrackUnsubscribed(
            remoteParticipant: RemoteParticipant,
            remoteDataTrackPublication: RemoteDataTrackPublication,
            remoteDataTrack: RemoteDataTrack
        ) {
            Timber.tag(TAG).i("onDataTrackUnsubscribed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteDataTrack: enabled=${remoteDataTrack.isEnabled}, " +
                    "name=${remoteDataTrack.name}]"
            )
            binding.videoStatusTextView.text = "onDataTrackUnsubscribed"
        }

        override fun onDataTrackSubscriptionFailed(
            remoteParticipant: RemoteParticipant,
            remoteDataTrackPublication: RemoteDataTrackPublication,
            twilioException: TwilioException
        ) {
            Timber.tag(TAG).i("onDataTrackSubscriptionFailed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteDataTrackPublication: sid=${remoteDataTrackPublication.trackSid}, " +
                    "name=${remoteDataTrackPublication.trackName}]" +
                    "[TwilioException: code=${twilioException.code}, " +
                    "message=${twilioException.message}]"
            )
            binding.videoStatusTextView.text = "onDataTrackSubscriptionFailed"
        }

        override fun onVideoTrackSubscribed(
            remoteParticipant: RemoteParticipant,
            remoteVideoTrackPublication: RemoteVideoTrackPublication,
            remoteVideoTrack: RemoteVideoTrack
        ) {
            Timber.tag(TAG).i("onVideoTrackSubscribed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteVideoTrack: enabled=${remoteVideoTrack.isEnabled}, " +
                    "name=${remoteVideoTrack.name}]"
            )
            binding.videoStatusTextView.text = "onVideoTrackSubscribed"
            if (remoteVideoTrackPublication.videoTrack?.isEnabled == true)
                addRemoteParticipantVideo(remoteVideoTrack)

            if (localVideoTrack != null && localVideoTrack!!.isEnabled)
                moveLocalVideoToThumbnailView()
        }

        override fun onVideoTrackUnsubscribed(
            remoteParticipant: RemoteParticipant,
            remoteVideoTrackPublication: RemoteVideoTrackPublication,
            remoteVideoTrack: RemoteVideoTrack
        ) {
            Timber.tag(TAG).i("onVideoTrackUnsubscribed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteVideoTrack: enabled=${remoteVideoTrack.isEnabled}, " +
                    "name=${remoteVideoTrack.name}]"
            )
            binding.videoStatusTextView.text = "onVideoTrackUnsubscribed"
            removeParticipantVideo(remoteVideoTrack)
        }

        override fun onVideoTrackSubscriptionFailed(
            remoteParticipant: RemoteParticipant,
            remoteVideoTrackPublication: RemoteVideoTrackPublication,
            twilioException: TwilioException
        ) {
            Timber.tag(TAG).i("onVideoTrackSubscriptionFailed: " +
                    "[RemoteParticipant: identity=${remoteParticipant.identity}], " +
                    "[RemoteVideoTrackPublication: sid=${remoteVideoTrackPublication.trackSid}, " +
                    "name=${remoteVideoTrackPublication.trackName}]" +
                    "[TwilioException: code=${twilioException.code}, " +
                    "message=${twilioException.message}]"
            )
            binding.videoStatusTextView.text = "onVideoTrackSubscriptionFailed"
            Snackbar.make(
                    binding.controlContainer,
                    "Failed to subscribe to ${remoteParticipant.identity}",
                    Snackbar.LENGTH_LONG
            )
                    .show()
        }

        override fun onAudioTrackEnabled(
            remoteParticipant: RemoteParticipant,
            remoteAudioTrackPublication: RemoteAudioTrackPublication
        ) {
        }

        override fun onVideoTrackEnabled(
            remoteParticipant: RemoteParticipant,
            remoteVideoTrackPublication: RemoteVideoTrackPublication
        ) {
        }

        override fun onVideoTrackDisabled(
            remoteParticipant: RemoteParticipant,
            remoteVideoTrackPublication: RemoteVideoTrackPublication
        ) {
        }

        override fun onAudioTrackDisabled(
            remoteParticipant: RemoteParticipant,
            remoteAudioTrackPublication: RemoteAudioTrackPublication
        ) {
        }
    }

    private var savedVolumeControlStream by Delegates.notNull<Int>()

    private var disconnectedFromOnDestroy = false
    var lastAction: Int = ACTION_UP

    lateinit var callType: CallType
    lateinit var callModel: CallModel

    var screenWidth = 0f
    var screenHeight = 0f
    var controlsHeight = 0f


    //region callOptions
    private val localVideoOption by lazy {
        VideoCallOptionModel(
            LOCAL_VIDEO,
            R.drawable.ic_video_on,
            R.drawable.ic_video_off,
            R.drawable.background_call_option_black,
            R.drawable.background_call_option_white,
            localVideoTrack != null && localVideoTrack?.isEnabled == true
        )
    }

    private val audioOption by lazy {
        VideoCallOptionModel(
            AUDIO_ACTION,
            R.drawable.ic_volume_2,
            R.drawable.ic_volume_1,
            R.drawable.background_call_option_white,
            R.drawable.background_call_option_black,
            localVideoTrack != null && localVideoTrack?.isEnabled == true
        )
    }

    private val muteOption by lazy {
        VideoCallOptionModel(
            MUTE_ACTION,
            R.drawable.ic_mic_on,
            R.drawable.ic_mic_off,
            R.drawable.background_call_option_black,
            R.drawable.background_call_option_white,
            localAudioTrack?.isEnabled == true
        )
    }

    private val endCallOption by lazy {
        VideoCallOptionModel(
            END_CALL,
            R.drawable.ic_end_call,
            R.drawable.ic_end_call,
            R.drawable.end_call_backround,
            R.drawable.end_call_backround,
            true
        )
    }

    private val switchCameraOption by lazy {
        VideoCallOptionModel(
            SWITCH_CAMERA,
            R.drawable.ic_switch_camera_white,
            R.drawable.ic_switch_camera_white,
            R.drawable.background_call_option_black,
            R.drawable.background_call_option_black,
            false
        )
    }

    private val bluetoothOption by lazy {
        VideoCallOptionModel(
            VideoCallOptionsAdapter.BLUETOOTH,
            R.drawable.ic_bluetooth_on,
            R.drawable.ic_bluetooth_off,
            R.drawable.background_call_option_white,
            R.drawable.background_call_option_black,
            localVideoTrack != null && localVideoTrack?.isEnabled == true && callViewModel.hasBluetoothDevice()
        )
    }
    //endregion

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = OngoingCallFragmentBinding.inflate(inflater)

        /*
         * Enable changing the volume using the up/down keys during a conversation
         */
        savedVolumeControlStream = requireActivity().volumeControlStream
        requireActivity().volumeControlStream = AudioManager.STREAM_VOICE_CALL

        callViewModel.callStarted.observe(viewLifecycleOwner, {
            when (it) {
                is Result.Success -> {
                }
                is Result.Error -> {
                }
                is Result.Loading -> {
                    setCallingStateUI()
                }
            }
        })

        callViewModel.callAccepted.observe(viewLifecycleOwner, {
            when (it) {
                is Result.Success -> {
                }
                is Result.Error -> {
                }
                is Result.Loading -> {
                    setConnectingStateUI()
                }
            }
        })

        callViewModel.callEnded.observe(viewLifecycleOwner) {
            when (it) {
                is Result.Success -> {
                    if (binding.callStatus.text != getString(R.string.no_answer) ||
                        binding.callStatus.text != getString(R.string.user_busy)
                    ) {
                        setCallStatusUI(getString(R.string.call_ended))
                    }

                    lifecycleScope.launch {
                        delay(1000)
                        callViewModel.stopCallService()
                        if (it.data == VideoCallEndedReason.DURATION_LIMIT_REACHED.type) {
                            //startPremium activity
                            if (DuaApplication.instance.getPremiumAvailable()) {
                                val intent = Intent(requireContext(), PremiumActivity::class.java)
                                intent.putExtra(PremiumActivity.VIDEO_CALL_COUNTER_RESET_TIME, getTimeStampAfter12Hours())
                                intent.putExtra(PremiumActivity.VIDEO_CALL_NAME, callModel.caller?.name)

                                requireActivity().openPremiumPaywall(
                                    viewPagerStartPosition = BenefitsPremiumAdapter.PremiumPaywallList.VIDEO_CALLS_ITEM,
                                    eventSourceClevertap = ClevertapEventSourceValues.FINISHED_ONE_FREE_MIN_IN_CALL,
                                    placementId = PurchaselyPlacement.FINISHED_ONE_FREE_MIN_IN_CALL.id,
                                    userModel = homeViewModel.userProfile.value,
                                    purchaselyIntent = intent
                                )

                            } else {
                                ToastUtil.toast(
                                    R.string.no_billing_available_calls_come_back_after_twelve_hours,
                                    Toast.LENGTH_LONG
                                )
                            }
                        }
                        (requireActivity() as CallActivity).closeCallActivity(hasCallEnded = true)
                    }
                }
                is Result.Error -> {
                }
                is Result.Loading -> {
                }
            }
        }

        callViewModel.callFailed.observe(viewLifecycleOwner) {
            when (it) {
                is Result.Success -> {
                }
                is Result.Error -> {
                    when (it.exception) {
                        is UserVideoCallsLimitReachedException -> {
                            setCallStatusUI("")

                            callViewModel.stopCallService()

                            //startPremium activity
                            if (DuaApplication.instance.getPremiumAvailable()) {
                                val premiumReason = if (callModel.isVoiceCall) FirebaseAnalyticsEventsName.GO_PREMIUM_VOICE_CALL else FirebaseAnalyticsEventsName.GO_PREMIUM_VIDEO_CALL
                                val placementId = if (callModel.isVoiceCall) PurchaselyPlacement.VOICE_CALL else PurchaselyPlacement.VIDEO_CALL
                                val intent = Intent(requireContext(), PremiumActivity::class.java)
                                intent.putExtra(PremiumActivity.VIDEO_CALL_COUNTER_RESET_TIME, it.exception.time)
                                intent.putExtra(PremiumActivity.VIDEO_CALL_NAME, callModel.caller?.name)

                                requireActivity().openPremiumPaywall(
                                    viewPagerStartPosition = BenefitsPremiumAdapter.PremiumPaywallList.VIDEO_CALLS_ITEM,
                                    eventSourceClevertap = if (callModel.isVoiceCall) ClevertapEventSourceValues.VOICE_CALL else ClevertapEventSourceValues.VIDEO_CALL,
                                    placementId = placementId.id,
                                    userModel = homeViewModel.userProfile.value,
                                    purchaselyIntent = intent
                                )

                            } else {
                                ToastUtil.toast(
                                    R.string.no_billing_available_calls_come_back_after_twelve_hours,
                                    Toast.LENGTH_LONG
                                )
                            }

                            (requireActivity() as CallActivity).closeCallActivity(hasCallEnded = true)
                        }
                        is UserVideoCallsNotAllowedException -> {
                        }
                        is UserBusyException -> {
                            setCallStatusUI(getString(R.string.user_busy))

                            lifecycleScope.launch {
                                delay(1000)
                                callViewModel.stopCallService()
                                (requireActivity() as CallActivity).closeCallActivity(hasCallEnded = true)
                            }
                        }
                        else -> {
                            ToastUtil.toast(it.exception.message.toString())

                            lifecycleScope.launch {
                                delay(1000)
                                callViewModel.stopCallService()
                                (requireActivity() as CallActivity).closeCallActivity(hasCallEnded = true)
                            }
                        }
                    }
                }
                is Result.Loading -> {
                }
            }
        }

        callViewModel.binder.observe(viewLifecycleOwner) { binder ->
            if (binder == null) {
                Timber.tag(TAG).e("onChanged: unbound from service")
            } else {
                Timber.tag(TAG).e("onChanged: bound to service.")


                //check if this is a new call or a call in progress
                //idle is the initial state of a call so we check for that
                if (binder.service.getCallState() == CallState.IDLE) {
                    val extras = requireActivity().intent?.extras
                    callModel = extras?.getParcelable(VIDEO_CALL_DATA_KEY)!!
                    callType =
                        if (callModel.isVoiceCall) CallType.AUDIO_CALL else CallType.VIDEO_CALL

                    if (requireActivity().intent.action == CALL_ACCEPT_ACTION || requireActivity().intent.action == CALL_INCOMING_ACTION) {
                        callViewModel.acceptCall(callModel.caller?.userId!!)
                    } else {
                        val startCallBody =
                            requireActivity().intent?.extras?.getParcelable<StartCallBody>(
                                CallActivity.START_CALL_BODY
                            )!!

                        callViewModel.startCall(startCallBody)
                    }
                } else {

                    callModel = binder.service.callModel
                    callType = binder.service.callType

                }

                val service = binder.service
                localAudioTrack = service.getLocalAudioTrack()
                localVideoTrack = service.getLocalVideoTrack()
                remoteVideoTrack = service.getRemoteVideoTrack()

                service.setViewRoomListener(roomListener)
                service.setViewParticipantListener(participantListener)


                /*
   * Set the initial state of the UI
   */
                initializeUI()

                restoreVideoFeeds()

                setupCallDurationObserer()
            }
        }


        callViewModel.bluetoothDeviceConnected.observe(viewLifecycleOwner, {
            val adapter = binding.controlContainer.adapter as VideoCallOptionsAdapter
            val hasLocalVideoTrack = localVideoTrack != null && localVideoTrack?.isEnabled == true
            if (callViewModel.isBluetoothAudioSelected()) {
                if (hasLocalVideoTrack) {

                    //if you have video turned on replace audio button with bluetooth
                    if (!adapter.items.map { it.type }.contains(BLUETOOTH))
                        adapter.replaceAudioWithBluetoothButton(bluetoothOption)
                } else {
                    adapter.updateAudioDevice(callViewModel.getSelectedAudioDevice()!!.name)
                }
            } else {
                if (hasLocalVideoTrack) {
                    if (callViewModel.hasBluetoothDevice())
                        adapter.addBluetoothOption(bluetoothOption.apply { enabled = false })
                    else
                        adapter.removeBluetoothOption()
                } else {
                    adapter.updateAudioDevice(callViewModel.getSelectedAudioDevice()!!.name)
                }
            }


            //recalculate controlsHeight after changing number of items
            binding.draggableControls.post {
                controlsHeight = binding.draggableControls.height.toFloat()
            }
        })

        return binding.root
    }

    fun setupCallDurationObserer() {
        callViewModel.callDuration.observe(viewLifecycleOwner) {
            if (callViewModel.getCallState() == CallState.CONNECTED)
                if (callViewModel.isReconnecting())
                    binding.callStatus.text = getString(R.string.reconnecting)
                else
                    binding.callStatus.text = DateUtils.formatElapsedTime(it)
        }
    }

    override fun onResume() {
        super.onResume()

        if (shouldCheckForPermission) {
            shouldCheckForPermission = false
            requestPermissionForCamera()
        }


        val durationFilter = IntentFilter()
        durationFilter.addAction(CallService.CALL_STATE_FILTER)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            requireActivity().registerReceiver(callStatusReceiver, durationFilter, RECEIVER_EXPORTED)
        } else {
            requireActivity().registerReceiver(callStatusReceiver, durationFilter)
        }
    }

    override fun onPause() {
        super.onPause()

        try {
            requireActivity().unregisterReceiver(callStatusReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun requestPermissionForCamera() {
        if (shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) {
            val description =
                getString(R.string.open_settings_and_turn_on_camera_access)
            showSettingsPermissionsDialog(description)
            disableCameraIcon()
        } else {
            requestPermissions(arrayOf(Manifest.permission.CAMERA), CAMERA_PERMISSION_REQUEST_CODE)
        }
    }

    private fun hasCameraPermission(): Boolean {
        val resultCamera =
            ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA)

        return resultCamera == PackageManager.PERMISSION_GRANTED
    }

    /*
* The initial state when there is no active room.
*/
    private fun initializeUI() {

        setupRecyclerView()

        binding.root.viewTreeObserver?.addOnGlobalLayoutListener(
            object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    binding.root.viewTreeObserver!!.removeOnGlobalLayoutListener(this)

                    //set controlsHeight width and height
                    controlsHeight = binding.draggableControls.height.toFloat()

                    //set offscreen coordinates
                    val displayMetrics = DisplayMetrics()
                    requireActivity().windowManager.defaultDisplay.getMetrics(displayMetrics)
                    screenHeight = displayMetrics.heightPixels.toFloat()
                    screenWidth = displayMetrics.widthPixels.toFloat()

                    //set smallVideoView position top right
                    binding.smallVideoViewContainer.x = screenWidth - convertDpToPixel(SMALL_FEED_WIDTH, requireContext()) - convertDpToPixel(16f, requireContext())
                    binding.smallVideoViewContainer.y =
                        convertDpToPixel(
                            TOP_SMALL_FEED_CONTROLS_ENABLED_MARGIN,
                            requireContext()
                        )
                }
            })

        binding.closeBtn.setOnClickListener {
            (requireActivity() as CallActivity).closeCallActivity(hasCallEnded = false)
        }

        binding.primaryVideoView.setOnClickListener {
            binding.draggableControls.isEnabled = !binding.draggableControls.isEnabled
            binding.closeBtn.visibility =
                if (binding.draggableControls.isEnabled) View.VISIBLE else View.GONE
            animateControlsViewHeight()
        }
        binding.callerBackground.setOnClickListener {
            binding.draggableControls.isEnabled = !binding.draggableControls.isEnabled
            binding.closeBtn.visibility =
                if (binding.draggableControls.isEnabled) View.VISIBLE else View.GONE
            animateControlsViewHeight()
        }


        setVideoViewDrag()

        if (callType == CallType.AUDIO_CALL) {
            binding.userInfoContainer.visibility = View.VISIBLE
            binding.callerBackground.visibility = View.VISIBLE
            toggleDimView(true)

            binding.primaryVideoView.visibility = View.GONE
        } else {
            binding.userInfoContainer.visibility = View.GONE
            binding.callerBackground.visibility = View.GONE
            toggleDimView(false)

            binding.primaryVideoView.visibility = View.VISIBLE
        }

        binding.nameAgeText.text =  callModel.caller?.name

        imageUrlCircle(binding.callerImage, callModel.caller?.pictureUrl)
        Glide.with(requireContext())
            .load(getS3Url(callModel.caller?.pictureUrl!!))
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .apply(
                RequestOptions()
                    .apply(RequestOptions.bitmapTransform(BlurTransformation(25, 3)))
                    .error(R.drawable.ic_broken_image)
            )
            .into(binding.callerBackground)

        setDragFeedTouchListener()

        binding.draggableControls.isClickable = true
        bindProfileBadge(callModel.caller?.badge2)

        callViewModel.getSelectedAudioDevice()?.let {
            val adapter = binding.controlContainer.adapter as VideoCallOptionsAdapter
            adapter.updateAudioDevice(it.name)
        }

    }

    fun setupRecyclerView() {
        val optionsList = mutableListOf(
            localVideoOption,
            muteOption,
            endCallOption
        )


        //add switch camera button if there is a local video track otherwise add toggle audio button
        if (localVideoTrack != null && localVideoTrack?.isEnabled == true) {
            optionsList.add(
                1, switchCameraOption
            )
        } else {
            optionsList.add(
                1, audioOption
            )
        }

        //add bluetooth button if there is a bluetooth device connected
        if (localVideoTrack != null && localVideoTrack?.isEnabled == true && callViewModel.hasBluetoothDevice()) {
            optionsList.add(
                3, bluetoothOption
            )
        }


        binding.controlContainer.addItemDecoration(OverlapDecoration())
        binding.controlContainer.layoutManager = GridLayoutManager(requireContext(), 4)
        binding.controlContainer.adapter =
            VideoCallOptionsAdapter(
                optionsList,
                object : VideoCallOptionsAdapter.OptionClickListener {
                    override fun onItemClick(model: VideoCallOptionModel) {
                        when (model.type) {
                            LOCAL_VIDEO -> {
                                if (hasCameraPermission()) {
                                    onLocalVideoClicked()
                                } else {
                                    requestPermissionForCamera()
                                }
                            }
                            SWITCH_CAMERA -> {
                                onSwitchCameraClicked()
                            }
                            AUDIO_ACTION -> {
                                toggleAudio()
                            }
                            MUTE_ACTION -> {
                                onMuteClicked(model.enabled)
                            }
                            END_CALL -> {
                                if (!callViewModel.isEndingCall) {
                                    firebaseLogEvent(FirebaseAnalyticsEventsName.END_CALL_BUTTONCLICK)

                                    callViewModel.endCall()
                                }
                            }
                            BLUETOOTH -> {
                                toggleBluetooth()
                            }
                        }
                    }
                }
            )

        (binding.controlContainer.layoutManager as GridLayoutManager).spanSizeLookup =
            object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return if (position == 4) 4 else 1
                }
            }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setDragFeedTouchListener() {
        var dY = 0f

        binding.draggableControls.setOnTouchListener { v, event ->
            val newY: Float

            when (event.actionMasked) {
                ACTION_DOWN -> {
                    dY = v.y - event.rawY
                    lastAction = ACTION_DOWN
                }
                ACTION_MOVE -> {
                    newY = event.rawY + dY
                    Timber.tag("translationY").d("newY: $newY | event.rawY ${event.rawY} | dY: $dY")

                    // check if the view out of screen
                    if (event.rawY <= screenHeight - controlsHeight) {
                        v.y = screenHeight - controlsHeight
                        lastAction = ACTION_MOVE
                        return@setOnTouchListener true
                    }

                    v.y = newY
                    lastAction = ACTION_MOVE
                }
                ACTION_UP -> if (lastAction == ACTION_DOWN) {
                    Timber.tag(TAG).d("setDragFeedTouchListener: Clicked!")
                } else {
                    snapView(event.rawY, v)
                }
                else -> return@setOnTouchListener false
            }
            true
        }
    }

    fun restoreVideoFeeds() {
        if (localVideoTrack != null && localVideoTrack?.isEnabled == true) {
            if (remoteVideoTrack != null && remoteVideoTrack!!.isEnabled) {
                binding.callerBackground.visibility = View.GONE
                binding.userInfoContainer.visibility = View.GONE
                toggleDimView(false)
                binding.primaryVideoView.visibility = View.VISIBLE
                binding.smallVideoViewContainer.visibility = View.VISIBLE
                binding.smallVideoView.visibility = View.VISIBLE

                localVideoTrack?.addSink(binding.smallVideoView)
                remoteVideoTrack?.addSink(binding.primaryVideoView)

            } else if (remoteVideoTrack == null || remoteVideoTrack?.isEnabled == false) {
                if (callViewModel.hasParticipantJoined()) {
                    binding.callerBackground.visibility = View.VISIBLE
                    binding.userInfoContainer.visibility = View.VISIBLE
                    binding.smallVideoViewContainer.visibility = View.VISIBLE
                    binding.smallVideoView.visibility = View.VISIBLE
                    binding.primaryVideoView.visibility = View.GONE
                    toggleDimView(true)

                    localVideoTrack?.addSink(binding.smallVideoView)
                } else {
                    binding.callerBackground.visibility = View.GONE
                    binding.userInfoContainer.visibility = View.VISIBLE
                    binding.smallVideoViewContainer.visibility = View.GONE
                    binding.smallVideoView.visibility = View.GONE
                    binding.primaryVideoView.visibility = View.VISIBLE
                    toggleDimView(true)

                    localVideoTrack?.addSink(binding.primaryVideoView)
                }


            }
        } else if (localVideoTrack == null || localVideoTrack?.isEnabled == false) {
            if (remoteVideoTrack != null && remoteVideoTrack!!.isEnabled) {
                binding.callerBackground.visibility = View.GONE
                binding.userInfoContainer.visibility = View.GONE
                toggleDimView(false)
                binding.primaryVideoView.visibility = View.VISIBLE

                remoteVideoTrack?.addSink(binding.primaryVideoView)
            } else if (remoteVideoTrack == null || remoteVideoTrack?.isEnabled == false) {
                binding.callerBackground.visibility = View.VISIBLE
                binding.userInfoContainer.visibility = View.VISIBLE
                toggleDimView(true)
                binding.primaryVideoView.visibility = View.GONE

                localVideoTrack?.addSink(binding.primaryVideoView)
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setVideoViewDrag() {
        val displaymetrics = DisplayMetrics()
        requireActivity().windowManager.defaultDisplay.getMetrics(displaymetrics)
        val screenWidth = displaymetrics.widthPixels
        var dX = 0f
        var dY = 0f

        binding.smallVideoViewContainer.setOnTouchListener { v, event ->
            val newX: Float
            val newY: Float

            when (event.actionMasked) {
                ACTION_DOWN -> {
                    dX = v.x - event.rawX
                    dY = v.y - event.rawY
                    lastAction = ACTION_DOWN
                }
                ACTION_MOVE -> {
                    newX = event.rawX + dX
                    newY = event.rawY + dY

                    // check if the view out of screen
                    if (newX <= 0 || newX >= screenWidth - v.width || newY <= 0 || newY >= binding.draggableControls.y - v.height) {
                        lastAction = ACTION_MOVE
                        return@setOnTouchListener true
                    }
                    v.x = newX
                    v.y = newY
                    lastAction = ACTION_MOVE
                }
                ACTION_UP -> if (lastAction == ACTION_DOWN) {
                    Timber.tag(TAG).d("setDragFeedTouchListener: Clicked!")
                } else {
                    moveFeedToCorner(v.x + v.width / 2, v.y + v.height / 2, v)
                }
                else -> return@setOnTouchListener false
            }
            true
        }
    }

    /*
    * Called when participant joins the room
    */
    private fun addRemoteParticipant(remoteParticipant: RemoteParticipant) {
        /*
         * This app only displays video for one additional participant per Room
         */
        if (binding.smallVideoView.visibility == View.VISIBLE) {
            Timber.tag(TAG).d("addRemoteParticipant: Multiple participants are not currently support in this UI")
            return
        }
        binding.videoStatusTextView.text = "Participant ${remoteParticipant.identity} joined"

        /*
         * Add participant renderer
         */
        remoteParticipant.remoteVideoTracks.firstOrNull()?.let { remoteVideoTrackPublication ->
            if (remoteVideoTrackPublication.isTrackSubscribed) {
                remoteVideoTrackPublication.remoteVideoTrack?.let { addRemoteParticipantVideo(it) }
            }
        }
    }

    /*
    * Set primary view as renderer for participant video track
    */
    private fun addRemoteParticipantVideo(videoTrack: VideoTrack) {

        toggleDimView(false)
        binding.primaryVideoView.visibility = View.VISIBLE
        binding.callerBackground.visibility = View.GONE
        binding.userInfoContainer.visibility = View.GONE

        videoTrack.addSink(binding.primaryVideoView)
        binding.primaryVideoView.mirror = false
    }

    private fun moveLocalVideoToThumbnailView() {
        if (binding.smallVideoView.visibility == View.GONE) {
            binding.smallVideoViewContainer.visibility = View.VISIBLE
            binding.smallVideoView.visibility = View.VISIBLE
            with(localVideoTrack) {
                this?.removeSink(binding.primaryVideoView)
                this?.addSink(binding.smallVideoView)
            }
            binding.smallVideoView.mirror =
                callViewModel.getCameraSource() == CameraCapturerCompat.Source.FRONT_CAMERA
        }
    }

    private fun removeLocalVideoFromThumbnail() {
        binding.smallVideoViewContainer.visibility = View.GONE
        binding.smallVideoView.visibility = View.GONE
        with(localVideoTrack) {
            this?.removeSink(binding.smallVideoView)
        }
        binding.smallVideoView.mirror =
            callViewModel.getCameraSource() == CameraCapturerCompat.Source.FRONT_CAMERA
    }

    /*
     * Called when participant leaves the room
     */
    private fun removeRemoteParticipant(remoteParticipant: RemoteParticipant) {
        binding.videoStatusTextView.text = "Participant $remoteParticipant.identity left."
        if (remoteParticipant.identity != callViewModel.getParticipantIdentity()) {
            return
        }

        /*
         * Remove participant renderer
         */
        remoteParticipant.remoteVideoTracks.firstOrNull()?.let { remoteVideoTrackPublication ->
            if (remoteVideoTrackPublication.isTrackSubscribed) {
                remoteVideoTrackPublication.remoteVideoTrack?.let { removeParticipantVideo(it) }
            }
        }
        moveLocalVideoToPrimaryView()
    }

    private fun removeParticipantVideo(videoTrack: VideoTrack) {
        remoteVideoTrack?.removeSink(binding.primaryVideoView)

        binding.primaryVideoView.visibility = View.GONE
        binding.callerBackground.visibility = View.VISIBLE
        binding.userInfoContainer.visibility = View.VISIBLE

        toggleDimView(true)
    }

    private fun moveLocalVideoToPrimaryView() {
        if (binding.smallVideoView.visibility == View.VISIBLE) {
            binding.smallVideoViewContainer.visibility = View.GONE
            binding.smallVideoView.visibility = View.GONE
            with(localVideoTrack) {
                this?.removeSink(binding.smallVideoView)
                this?.addSink(binding.primaryVideoView)
            }
            binding.primaryVideoView.mirror =
                callViewModel.binder.value?.service?.cameraCapturerCompat?.cameraSource == CameraCapturerCompat.Source.FRONT_CAMERA
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as CallActivity).callsComponent.inject(this)
    }

    private fun onSwitchCameraClicked() {
        callViewModel.switchCamera()
        if (binding.smallVideoView.visibility == View.VISIBLE) {
            binding.smallVideoView.mirror =
                callViewModel.getCameraSource() == CameraCapturerCompat.Source.BACK_CAMERA
        } else {
            binding.primaryVideoView.mirror =
                callViewModel.getCameraSource() == CameraCapturerCompat.Source.BACK_CAMERA
        }
    }

    private fun onLocalVideoClicked() {
        /*
         * Enable/disable the local video track
         */
        if (localVideoTrack == null) {
            localVideoTrack = callViewModel.createLocalVideoTrack(false)
        }

        localVideoTrack?.let {
            val enable = !it.isEnabled
            it.enable(enable)
            if (enable) {
                (binding.controlContainer.adapter as VideoCallOptionsAdapter).replaceAudioWithCameraSwitch(
                    switchCameraOption
                )

                val adapter = (binding.controlContainer.adapter as VideoCallOptionsAdapter)

                //put audio on speaker
                if (!callViewModel.hasBluetoothDevice()) {
                    setAudioDevice(AudioOutputDevice.SPEAKERPHONE)
                } else {
                    bluetoothOption.apply { enabled = callViewModel.isBluetoothAudioSelected() }
                    adapter.replaceAudioWithBluetoothButton(bluetoothOption)
                    binding.draggableControls.post {
                        controlsHeight = binding.draggableControls.height.toFloat()
                    }
                }

                callViewModel.publishLocalVideoTrack()
                moveLocalVideoToThumbnailView()

                if (!callViewModel.hasSwitchedCallMode() && callModel.isVoiceCall) {
                    callViewModel.setHasSwitchedCallMode()
                    firebaseLogEvent(FirebaseAnalyticsEventsName.SWITCH_VOICE_TO_VIDEO)
                }

            } else {

                val adapter = (binding.controlContainer.adapter as VideoCallOptionsAdapter)

                adapter.replaceCameraSwitchWithAudio(audioOption)

                //put audio on speaker
                if (!callViewModel.hasBluetoothDevice()) {
                    setAudioDevice(AudioOutputDevice.EARPIECE)
                } else {
                    adapter.removeBluetoothOption()
                    adapter.updateAudioDevice(callViewModel.getSelectedAudioDevice()!!.name)
                    binding.draggableControls.post {
                        controlsHeight = binding.draggableControls.height.toFloat()
                    }
                }


                //unpublish and nullify localVideoTrack
                callViewModel.unPublishLocalVideoTrack()
                localVideoTrack = null

                removeLocalVideoFromThumbnail()

                binding.smallVideoViewContainer.visibility = View.GONE
                binding.smallVideoView.visibility = View.GONE

                if (!callViewModel.hasSwitchedCallMode() && !callModel.isVoiceCall) {
                    callViewModel.setHasSwitchedCallMode()
                    firebaseLogEvent(FirebaseAnalyticsEventsName.SWITCH_VIDEO_TO_VOICE)
                }
            }
        }
    }

    private fun setNoRemoteVideoUI() {
        binding.primaryVideoView.visibility = View.GONE
        binding.callerBackground.visibility = View.VISIBLE
        binding.userInfoContainer.visibility = View.VISIBLE
        toggleDimView(true)
    }

    private fun setRemoteVideoUI() {
        binding.primaryVideoView.visibility = View.VISIBLE
        binding.callerBackground.visibility = View.GONE
        binding.userInfoContainer.visibility = View.GONE
        toggleDimView(false)
    }

    private fun onMuteClicked(enabled: Boolean) {
        /*
         * Enable/disable the local audio track. The results of this operation are
         * signaled to other Participants in the same Room. When an audio track is
         * disabled, the audio is muted.
         */
        localAudioTrack?.enable(enabled)
    }

    private fun toggleAudio() {
        val selectedDevice = callViewModel.toggleAudioDevice()
        selectedDevice?.let {
            (binding.controlContainer.adapter as VideoCallOptionsAdapter).updateAudioDevice(it.name)
        }
    }

    private fun toggleBluetooth() {
        val selectedDevice = callViewModel.toggleAudioDevice()
        selectedDevice?.let {
            (binding.controlContainer.adapter as VideoCallOptionsAdapter).updateBluetooth(it.name)
        }
    }

    private fun setAudioDevice(audioDevice: AudioOutputDevice) {
        val hasSet = callViewModel.setAudioDevice(audioDevice)
        if (hasSet) {
            (binding.controlContainer.adapter as VideoCallOptionsAdapter).updateAudioDevice(
                audioDevice.type
            )
        }
    }

/*fun updateAudioDeviceIcon(name: String) {
     when (name) {
         AudioOutputDevice.EARPIECE.type -> {
             binding.audioAction.setImageResource(R.drawable.ic_volume_1)
             binding.audioAction.setBackgroundResource(R.drawable.background_call_option_black)
         }
         AudioOutputDevice.SPEAKERPHONE.type -> {
             binding.audioAction.setImageResource(R.drawable.ic_volume_2)
             binding.audioAction.setBackgroundResource(R.drawable.background_call_option_white)
         }
         else -> {
             binding.audioAction.setImageResource(R.drawable.ic_bluetooth)
             binding.audioAction.imageTintList =
                 ColorStateList.valueOf(Color.parseColor("#414140"))
             binding.audioAction.setBackgroundResource(R.drawable.background_call_option_white)
         }
     }
}*/

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        if (requestCode == CAMERA_PERMISSION_REQUEST_CODE) {
            var cameraAndMicPermissionGranted = true

            for (grantResult in grantResults) {
                cameraAndMicPermissionGranted = cameraAndMicPermissionGranted and
                        (grantResult == PackageManager.PERMISSION_GRANTED)
            }

            if (cameraAndMicPermissionGranted) {
                onLocalVideoClicked()
            } else {
                disableCameraIcon()

                ToastUtil.toast(R.string.permission_needed, Toast.LENGTH_LONG)
            }
        }
    }

    private fun disableCameraIcon() {
        val adapter = binding.controlContainer.adapter as VideoCallOptionsAdapter
        adapter.disableCameraIcon()
    }

    private fun moveFeedToCorner(
        x: Float,
        y: Float,
        feed: View,
        draggableControlsHeight: Float? = null
    ) {
        val width = binding.draggableControls.width.toFloat()

        val height = draggableControlsHeight ?: binding.draggableControls.y

        //left side
        if (x < width / 2) {
            //top left side
            if (y < height / 2) {
                if (binding.closeBtn.translationY >= 0)
                    binding.closeBtn.animate().translationYBy(-convertDpToPixel(12f, feed.context))
                        .setDuration(VIDEO_DRAG_DURATION).start()
                animateFeed(
                    feed,
                    convertDpToPixel(16f, feed.context),
                    convertDpToPixel(
                        if (binding.draggableControls.isEnabled) TOP_SMALL_FEED_CONTROLS_ENABLED_MARGIN else TOP_SMALL_FEED_CONTROLS_DISABLED_MARGIN,
                        feed.context
                    )
                )
            } else {
                //bottom left side
                if (binding.closeBtn.translationY < 0)
                    binding.closeBtn.animate().translationYBy(convertDpToPixel(12f, feed.context))
                        .setDuration(VIDEO_DRAG_DURATION).start()

                animateFeed(
                    feed,
                    convertDpToPixel(16f, feed.context),
                    height - feed.height - convertDpToPixel(
                        if (binding.draggableControls.isEnabled) DOWN_SMALL_FEED_CONTROLS_ENABLED_MARGIN else DOWN_SMALL_FEED_CONTROLS_DISABLED_MARGIN,
                        feed.context
                    )
                )
            }
        } else {
            //top right side
            if (y < height / 2) {
                if (binding.closeBtn.translationY < 0)
                    binding.closeBtn.animate().translationYBy(convertDpToPixel(12f, feed.context))
                        .setDuration(VIDEO_DRAG_DURATION).start()

                animateFeed(
                    feed,
                    width - convertDpToPixel(SMALL_FEED_WIDTH, requireContext()) - convertDpToPixel(16f, feed.context),
                    convertDpToPixel(
                        if (binding.draggableControls.isEnabled) TOP_SMALL_FEED_CONTROLS_ENABLED_MARGIN else TOP_SMALL_FEED_CONTROLS_DISABLED_MARGIN,
                        feed.context
                    )
                )
            } else {
                //bottom right side
                if (binding.closeBtn.translationY < 0)
                    binding.closeBtn.animate().translationYBy(convertDpToPixel(12f, feed.context))
                        .setDuration(VIDEO_DRAG_DURATION).start()

                animateFeed(
                    feed,
                    width - convertDpToPixel(SMALL_FEED_WIDTH, requireContext()) - convertDpToPixel(16f, feed.context),
                    height - feed.height - convertDpToPixel(
                        if (binding.draggableControls.isEnabled) DOWN_SMALL_FEED_CONTROLS_ENABLED_MARGIN else DOWN_SMALL_FEED_CONTROLS_DISABLED_MARGIN,
                        feed.context
                    )
                )
            }
        }
    }

    private fun snapView(y: Float, container: View) {

        if (y < (screenHeight - container.height / 2)) {
            binding.draggableControls.isEnabled = true
            binding.closeBtn.visibility = View.VISIBLE

            binding.draggableControls.animate()
                .translationYBy(-(container.y - (screenHeight - controlsHeight)))
                .setDuration(CONTROLS_ANIMATION_DURATION)
                .withEndAction {
                    binding.draggableControls.y = screenHeight - controlsHeight
                    moveFeedToCorner(
                        binding.smallVideoViewContainer.x,
                        binding.smallVideoViewContainer.y,
                        binding.smallVideoViewContainer,
                        screenHeight - controlsHeight
                    )
                }
                .start()

        } else {
            binding.draggableControls.isEnabled = false
            binding.closeBtn.visibility = View.GONE

            binding.draggableControls.animate()
                .translationYBy(controlsHeight - (screenHeight - y))
                .setDuration(CONTROLS_ANIMATION_DURATION)
                .withEndAction {
                    binding.draggableControls.y = screenHeight
                    moveFeedToCorner(
                        binding.smallVideoViewContainer.x,
                        binding.smallVideoViewContainer.y,
                        binding.smallVideoViewContainer,
                        screenHeight
                    )
                }
                .start()

        }
    }

    private fun animateFeed(feed: View, toX: Float, toY: Float) {
        feed.animate().translationX(toX).translationY(toY).setDuration(VIDEO_DRAG_DURATION).start()
    }

    private fun animateControlsViewHeight() {
        if (!binding.draggableControls.isEnabled) {
            binding.draggableControls.animate()
                .translationYBy(controlsHeight)
                .setDuration(CONTROLS_ANIMATION_DURATION)
                .withStartAction {
                    binding.draggableControls.y = screenHeight - controlsHeight
                    moveFeedToCorner(
                        binding.smallVideoViewContainer.x,
                        binding.smallVideoViewContainer.y,
                        binding.smallVideoViewContainer,
                        screenHeight
                    )
                }
                .withEndAction {
                    binding.draggableControls.y = screenHeight
                }
                .start()
        } else {
            binding.draggableControls.animate()
                .translationYBy(-controlsHeight)
                .setDuration(CONTROLS_ANIMATION_DURATION)
                .withStartAction {
                    binding.draggableControls.y = screenHeight
                    moveFeedToCorner(
                        binding.smallVideoViewContainer.x,
                        binding.smallVideoViewContainer.y,
                        binding.smallVideoViewContainer,
                        screenHeight - controlsHeight
                    )
                }
                .withEndAction {
                    binding.draggableControls.y = screenHeight - controlsHeight
                }
                .start()
        }
    }

    private fun setCallStatusUI(endCallReason: String) {
        binding.userInfoContainer.visibility = View.VISIBLE
        binding.callStatus.text = endCallReason
        binding.callerBackground.visibility = View.VISIBLE
        toggleDimView(true)

        binding.draggableControls.isEnabled = false
        animateControlsViewHeight()
    }

    private fun setCallingStateUI() {
        binding.userInfoContainer.visibility = View.VISIBLE
        binding.callStatus.text =
            if (callModel.isVoiceCall)
                getString(R.string.calling_ongoing)
            else
                getString(R.string.video_calling)
    }

    fun setConnectingStateUI() {

        binding.userInfoContainer.visibility = View.VISIBLE
        binding.callStatus.text = getString(R.string.connecting_ongoing)
    }

    fun setConnectedStateUI() {
        binding.userInfoContainer.visibility = View.VISIBLE
    }

    private fun bindProfileBadge(badge2Status: String?) {
        val badgeApproved = binding.badgeApproved
        val badgeProcessing = binding.badgeProcessing
        when (badge2Status) {
            Badge2Status.APPROVED.status -> {
                badgeApproved.setImageDrawable(
                    ContextCompat.getDrawable(
                        badgeApproved.context,
                        R.drawable.ic_image_verification
                    )
                )
                badgeApproved.visibility = View.VISIBLE
                badgeProcessing.visibility = View.INVISIBLE
                badgeApproved.alpha = 1f
                fadeAnimator?.cancel()
            }
            Badge2Status.PROCESSING.status -> {
                badgeApproved.setImageDrawable(
                    ContextCompat.getDrawable(
                        badgeApproved.context,
                        R.drawable.ic_image_verification
                    )
                )
                badgeProcessing.setImageDrawable(
                    ContextCompat.getDrawable(
                        badgeProcessing.context,
                        R.drawable.ic_image_verification_processinng
                    )
                )
                badgeApproved.visibility = View.VISIBLE
                badgeProcessing.visibility = View.VISIBLE
                badgeApproved.alpha = 1f
                fadeAnimator = ObjectAnimator.ofFloat(badgeApproved, "alpha", 1f, 0f).apply {
                    duration = 800
                    repeatCount = ObjectAnimator.INFINITE
                    repeatMode = ObjectAnimator.REVERSE
                }

                fadeAnimator?.start()
            }
            else -> {
                badgeProcessing.visibility = View.INVISIBLE
                fadeAnimator?.cancel()
                    badgeApproved.alpha = 0f
                    badgeApproved.visibility = View.GONE
                    badgeProcessing.visibility = View.INVISIBLE

            }
        }
    }


    private val callStatusReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val duration = intent.getLongExtra(CallService.CALL_DURATION_KEY, 0L)
            val callState = intent.getStringExtra(CallService.CALL_STATUS_KEY)

            Timber.tag(TAG).d("onReceive: CALL_DURATION_KEY $duration")
            Timber.tag(TAG).d("onReceive: CALL_STATUS_KEY $callState")

            if (callState != null && callState == CallState.NO_ANSWER.type) {
                binding.callStatus.text = getString(R.string.no_answer)
                return
            }
        }
    }

    class OverlapDecoration : RecyclerView.ItemDecoration() {
        companion object {
            private val overlap = convertDpToPixel(24f, DuaApplication.instance)
        }

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            val itemPosition = parent.getChildAdapterPosition(view)
            if (itemPosition == 4) {
                outRect.set(0, overlap.toInt(), 0, 0)
            }
        }
    }

    private fun showSettingsPermissionsDialog(description: String) {
        val builder = AlertDialog.Builder(requireContext())
            .setMessage(description)
            .setPositiveButton("Settings") { dialogInterface, i ->
                try {
                    shouldCheckForPermission = true
                    openSettingsScreen()
                } catch (e: java.lang.Exception) {
                    Timber.tag("error").d(e.toString())
                }
            }
            .setNegativeButton("Cancel") { dialogInterface, i ->
                ToastUtil.toast(R.string.permission_needed, Toast.LENGTH_LONG)
            }

        val dialog = builder.create()
        dialog.setOnShowListener {
            dialog.getButton(AlertDialog.BUTTON_POSITIVE)
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_500))
            dialog.getButton(AlertDialog.BUTTON_NEGATIVE)
                .setTextColor(ContextCompat.getColor(requireContext(), R.color.red_500))
        }
        dialog.show()
    }

    private fun openSettingsScreen() {
        val intent = Intent()
        intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        intent.apply {
            `package` = requireContext().packageName
        }
        val uri: Uri = Uri.fromParts("package", requireActivity().packageName, null)
        intent.data = uri
        this.startActivity(intent)
    }

    fun toggleDimView(enable: Boolean) {
        if (enable)
            binding.dimView.setBackgroundColor(Color.parseColor("#A6000000"))
        else
            binding.dimView.setBackgroundColor(Color.TRANSPARENT)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.closeBtn.animate().cancel()
        binding.draggableControls.animate().cancel()
        callViewModel.binder.value?.service?.setViewRoomListener(null)
        callViewModel.binder.value?.service?.setViewParticipantListener(null)
        requireActivity().volumeControlStream = savedVolumeControlStream
        
        _binding = null
        fadeAnimator = null
        localAudioTrack = null
        localVideoTrack = null
        remoteVideoTrack = null
    }
}