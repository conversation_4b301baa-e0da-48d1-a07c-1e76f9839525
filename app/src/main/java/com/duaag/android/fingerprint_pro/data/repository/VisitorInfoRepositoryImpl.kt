package com.duaag.android.fingerprint_pro.data.repository


import com.duaag.android.application.DuaApplication
import com.duaag.android.fingerprint_pro.Credentials.apiKey
import com.duaag.android.fingerprint_pro.Credentials.region
import com.duaag.android.fingerprint_pro.data.datasource.local.VisitorInfoLocalDataSource
import com.duaag.android.fingerprint_pro.domain.repository.VisitorInfoRepository
import com.fingerprintjs.android.fpjs_pro.Configuration
import com.fingerprintjs.android.fpjs_pro.FingerprintJSFactory
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import javax.inject.Inject
import kotlin.coroutines.resume

class VisitorInfoRepositoryImpl @Inject constructor(
    private val localDataSource: VisitorInfoLocalDataSource,
    private val fingerprintJSFactory: FingerprintJSFactory,
) : VisitorInfoRepository {

    companion object {
        const val NETWORK_TIMEOUT_MILLIS = 60_000
    }

    private val mutex = Mutex()

    private val fingerprintJS by lazy {
        fingerprintJSFactory.createInstance(
            Configuration(
                apiKey = apiKey,
                region = region,
            )
        )
    }

    override suspend fun getVisitorId(): String {
        mutex.withLock {
            val cachedVisitorId = localDataSource.getCachedVisitorId()
            if (!cachedVisitorId.isNullOrEmpty()) {
                return cachedVisitorId
            }

            return try {
                val visitorId = fetchVisitorIdFromSDK()
                localDataSource.saveVisitorId(visitorId)
                visitorId
            } catch (exception: Exception) {
                val fallbackVisitorId =
                    localDataSource.getLegacyDeviceId(DuaApplication.instance.applicationContext)
                fallbackVisitorId
            }
        }
    }


    override suspend fun saveVisitorId(visitorId: String) {
        localDataSource.saveVisitorId(visitorId)
    }

    override suspend fun getCachedVisitorId(): String? {
        return localDataSource.getCachedVisitorId()
    }

    override suspend fun clearCache() {
        localDataSource.clearCache()
    }

    private suspend fun fetchVisitorIdFromSDK(): String {
        return suspendCancellableCoroutine { continuation ->
            fingerprintJS.getVisitorId(
                timeoutMillis = NETWORK_TIMEOUT_MILLIS,
                listener = { result ->
                    continuation.resume(result.visitorId)
                },
                errorListener = { sdkError ->
                    val throwable =
                        Exception("FingerprintJS error: requestId:${sdkError.requestId}, description:${sdkError.description}")
                    continuation.resumeWith(Result.failure(throwable))
                }
            )
        }
    }
}
