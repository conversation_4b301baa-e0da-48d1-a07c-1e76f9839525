package com.duaag.android.fingerprint_pro.data.datasource.local

import android.content.Context
import com.duaag.android.sharedprefs.DuaSharedPrefs
import javax.inject.Inject

class VisitorInfoLocalDataSourceImpl @Inject constructor( val duaSharedPrefs: DuaSharedPrefs
) : VisitorInfoLocalDataSource {

    override suspend fun getCachedVisitorId(): String? {
        return duaSharedPrefs.getVisitorId()
    }

    override suspend fun saveVisitorId(visitorId: String?) {
        duaSharedPrefs.saveVisitorId(visitorId)
    }

    override suspend fun getLegacyDeviceId(context: Context): String {
        return  duaSharedPrefs.getDeviceId(context)
    }

    override suspend fun clearCache() {
        duaSharedPrefs.saveVisitorId(null)
    }
}