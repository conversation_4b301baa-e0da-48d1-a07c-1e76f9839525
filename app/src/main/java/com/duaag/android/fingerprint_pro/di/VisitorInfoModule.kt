package com.duaag.android.fingerprint_pro.di

import android.content.Context
import com.duaag.android.fingerprint_pro.data.datasource.local.VisitorInfoLocalDataSource
import com.duaag.android.fingerprint_pro.data.datasource.local.VisitorInfoLocalDataSourceImpl
import com.duaag.android.fingerprint_pro.data.repository.VisitorInfoRepositoryImpl
import com.duaag.android.fingerprint_pro.domain.repository.VisitorInfoRepository
import com.duaag.android.fingerprint_pro.domain.usecase.VisitorIdUseCase
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.fingerprintjs.android.fpjs_pro.FingerprintJSFactory
import dagger.Module
import dagger.Provides
import tvi.webrtc.Priority
import javax.inject.Singleton

@Module
object VisitorInfoModule {


    @Provides
    @Singleton
    fun provideFingerprintJSFactory(context:Context): FingerprintJSFactory {
        return FingerprintJSFactory(context)
    }

    @Provides
    @Singleton
    fun provideLocalVisitorInfoLocalDataSource(duaSharedPrefs: DuaSharedPrefs): VisitorInfoLocalDataSource {
        return VisitorInfoLocalDataSourceImpl(duaSharedPrefs)
    }

    @Provides
    @Singleton
    fun provideVisitorIdRepository(
        localDataSource: VisitorInfoLocalDataSource,
        fingerprintJSFactory: FingerprintJSFactory,
    ): VisitorInfoRepository {
        return VisitorInfoRepositoryImpl(
            localDataSource,
            fingerprintJSFactory,
        )
    }

    @Provides
    @Singleton
    fun provideVisitorIdUseCase(repository: VisitorInfoRepository): VisitorIdUseCase {
        return VisitorIdUseCase(repository)
    }
}
