package com.duaag.android.views

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.ImageViewerItemBinding

class ImagesViewerAdapter : RecyclerView.Adapter<ImagesViewerAdapter.ImageViewHolder>() {

    var images: ArrayList<String?> = ArrayList()
    var isLoading: Boolean = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val v = ImageViewerItemBinding.inflate(layoutInflater, parent, false)
        return ImageViewHolder(v)
    }

    override fun getItemCount(): Int {
        return images.size ?: 0
    }

    override fun onBindViewHolder(holder: ImageViewHolder, position: Int) {
        if (position < images.size && images[position] != null) {
            holder.bind(images[position]!!)
        }
    }

    fun setData(array: ArrayList<String>) {
        with(images) {
            clear()
            addAll(array)
            if (size > 0) {
                notifyDataSetChanged()
            }
        }
    }

    inner class ImageViewHolder(val binding: ImageViewerItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(url: String) {
            binding.url = url
        }
    }
}