package com.duaag.android.broadcasts

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.duaag.android.utils.NetworkChecker

class ConnectivityReceiver: BroadcastReceiver() {

    var connectivityReceiverListener: ConnectivityReceiverListener? = null

    override fun onReceive(context: Context?, intent: Intent?) {
        if (connectivityReceiverListener != null) {
            connectivityReceiverListener!!.onNetworkConnectionChanged(NetworkChecker.isNetworkConnected(context!!))
        }
    }


    interface ConnectivityReceiverListener {
        fun onNetworkConnectionChanged(isConnected: <PERSON>olean)
    }

}