package com.duaag.android.firebase

import android.app.ActivityManager
import android.app.ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
import android.app.ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationChannelGroup
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.PendingIntent.FLAG_IMMUTABLE
import android.app.PendingIntent.FLAG_MUTABLE
import android.app.PendingIntent.FLAG_UPDATE_CURRENT
import android.app.PendingIntent.getActivity
import android.app.PendingIntent.getService
import android.app.TaskStackBuilder
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.graphics.drawable.Icon
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.RingtoneManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import androidx.annotation.Nullable
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.Person
import androidx.core.graphics.drawable.IconCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.clevertap.android.sdk.CleverTapAPI
import com.clevertap.android.sdk.pushnotification.fcm.CTFcmMessageHandler
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.calls.CallActivity
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.CALL_ACCEPT_ACTION
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.CALL_CANCEL_ACTION
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.CALL_INCOMING_ACTION
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.CALL_RESPONSE_ACTION_KEY
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.VIDEO_CALL_BODY_KEY
import com.duaag.android.calls.broadast_receivers.CallNotificationActionReceiver.Companion.VIDEO_CALL_DATA_KEY
import com.duaag.android.calls.services.CallService.Companion.ACCEPT_CALL_REQUEST_CODE
import com.duaag.android.calls.services.CallService.Companion.CALL_CHANNEL_ID
import com.duaag.android.calls.services.CallService.Companion.CALL_CHANNEL_NAME
import com.duaag.android.calls.services.CallService.Companion.CANCEL_CALL_REQUEST_CODE
import com.duaag.android.calls.services.CallService.Companion.INCOMING_CALL_REQUEST_CODE
import com.duaag.android.chat.fragments.ConversationFragment
import com.duaag.android.chat.model.ConversationData
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationType
import com.duaag.android.chat.model.MessageModel
import com.duaag.android.firebase.NotificationConstants.CLEVERTAP_DEFAULT_CHANNEL_ID
import com.duaag.android.firebase.NotificationHelper.Utils.getCircleBitmap
import com.duaag.android.firebase.model.CallModel
import com.duaag.android.firebase.model.NotificationModel
import com.duaag.android.firebase.model.UserLikedYouNotificationResponse
import com.duaag.android.firebase.service.ReplyMessageService
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.models.InteractionType
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.getS3Url
import com.google.firebase.messaging.RemoteMessage
import timber.log.Timber
import java.util.Random
import kotlin.math.round


class NotificationHelper(val context: Context) : ContextWrapper(context) {
    private var mNotificationManager: NotificationManager? = null

    companion object {
        const val KEY_TEXT_REPLY = "key_text_reply"
        const val REPLAY_MESSAGE = "REPLAY_MESSAGE"
        const val CONVERSATION_MODEL = "CONVERSATION_MODEL"
        const val NOTIFICATION_ID = "NOTIFICATION_ID"
        const val UPDATE_DATA = "UPDATE_DATA"
        const val INCOMING_CALL_NOTIFICATION_ID = 166

        //  Group Summary Notification
        const val GROUP_KEY_LIKED_YOU = "com.duaag.android.LIKED_YOU"

        //use constant ID for notification used as group summary
        const val LIKED_YOU_SUMMARY_ID = 0


        private var manager: NotificationManager? = null
        private var notificationHelperInstance: NotificationHelper? = null

        fun getNotificationHelper(context: Context): NotificationHelper {
            if (notificationHelperInstance == null) {
                notificationHelperInstance = NotificationHelper(context)
            }

            return notificationHelperInstance as NotificationHelper
        }

        fun areNotificationsEnables(mContext: Context): Boolean {
            NotificationHelper(mContext)
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                (isNotificationChannelEnabled(
                    mContext,
                    NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU
                )
                        && isNotificationChannelEnabled(
                    mContext,
                    NotificationConstants.NOTIFICATION_ID_NEW_MATCH
                )
                        && isNotificationChannelEnabled(
                    mContext,
                    NotificationConstants.NOTIFICATION_ID_MESSAGE
                ) && NotificationManagerCompat.from(mContext).areNotificationsEnabled())
            } else {
                NotificationManagerCompat.from(mContext).areNotificationsEnabled()
            }
        }

        fun isMessageNotificationsEnables(mContext: Context): Boolean {
            NotificationHelper(mContext)
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                (isNotificationChannelEnabled(
                    mContext,
                    NotificationConstants.NOTIFICATION_ID_MESSAGE
                ) && NotificationManagerCompat.from(mContext).areNotificationsEnabled())
            } else {
                NotificationManagerCompat.from(mContext).areNotificationsEnabled()
            }
        }

        private fun isNotificationChannelEnabled(context: Context, channelId: String?): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (!TextUtils.isEmpty(channelId)) {
                    val manager =
                        context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                    val channel = manager.getNotificationChannel(channelId)
                    return channel.importance != NotificationManager.IMPORTANCE_NONE
                }
                false
            } else {
                NotificationManagerCompat.from(context).areNotificationsEnabled()
            }
        }

        fun clareAllNotifications() {
            val notificationManager =
                DuaApplication.instance.applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancelAll()
        }

        fun isApplicationInTheBackground(): Boolean {
            val appProcessInfo = ActivityManager.RunningAppProcessInfo();
            ActivityManager.getMyMemoryState(appProcessInfo);
            return !(appProcessInfo.importance == IMPORTANCE_FOREGROUND || appProcessInfo.importance == IMPORTANCE_VISIBLE)
        }
    }

    init {
        createNotificationChannelGroup()
        createChannels()
    }

    private fun getNotificationManager(): NotificationManager {
        if (manager == null) {
            manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        }
        return manager as NotificationManager
    }

    @Suppress("SameParameterValue", "UNUSED_PARAMETER")
    private fun createNotificationChannel(
        channel_name: String,
        @Nullable descriptionText: String,
        CHANNEL_ID: String,
        CHANNEL_GROUP_ID: String,
        sound_path: String?,
        NOTIFICATION_IMPORTANCE: Int?,
        sound_type: Int?
    ) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Create the NotificationChannel
                val importance = NotificationManager.IMPORTANCE_HIGH

                val mChannel = NotificationChannel(CHANNEL_ID, channel_name, importance)

//                if (sound_path != null) {
//                    val attributes: AudioAttributes = AudioAttributes.Builder()
//                            .setUsage(AudioAttributes.USAGE_MEDIA)
//                            .setUsage(AudioAttributes.USAGE_ALARM)
//                            .build()
//
//                    mChannel.setSound(Uri.parse(sound_path), attributes)
//                }
                mChannel.description = descriptionText
                mChannel.enableLights(true)
                mChannel.lightColor = Color.RED
                mChannel.enableVibration(true)
                mChannel.group = CHANNEL_GROUP_ID;
                mChannel.setShowBadge(true)

                // Register the channel with the system; you can't change the importance
                // or other notification behaviors after this
                getNotificationManager().createNotificationChannel(mChannel)
            }
        } catch (e: Exception) {
            Timber.tag("notificationHelper").d(e.message ?: "")
        }

    }

    fun createNotificationChannelGroup() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {

            val notificationChannelGroups = arrayListOf<NotificationChannelGroup>()
            notificationChannelGroups.add(
                NotificationChannelGroup(
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    "Default"
                )
            )
            val notificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannelGroups(notificationChannelGroups)
        }
    }

    fun createChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

            createNotificationChannel(
                context.getString(R.string.new_liked_you_notification), "",
                NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU,
                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                defaultSoundUri.toString(),
                NotificationManager.IMPORTANCE_HIGH, -1
            )

            createNotificationChannel(
                context.getString(R.string.new_match_notification), "",
                NotificationConstants.NOTIFICATION_ID_NEW_MATCH,
                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                defaultSoundUri.toString(),
                NotificationManager.IMPORTANCE_HIGH, -1
            )

            createNotificationChannel(
                context.getString(R.string.new_chat_notification), "",
                NotificationConstants.NOTIFICATION_ID_MESSAGE,
                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                defaultSoundUri.toString(),
                NotificationManager.IMPORTANCE_HIGH, -1
            )

            createNotificationChannel(
                context.getString(R.string.counter_reset_notification), "",
                NotificationConstants.NOTIFICATION_ID_COUNTER_RESET,
                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                defaultSoundUri.toString(),
                NotificationManager.IMPORTANCE_HIGH, -1
            )

            createNotificationChannel(
                context.getString(R.string.user_deleted_notification), "",
                NotificationConstants.NOTIFICATION_ID_USER_DELETED,
                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                defaultSoundUri.toString(),
                NotificationManager.IMPORTANCE_HIGH, -1
            )

            createNotificationChannel(
                context.getString(R.string.profile_completion_reminder_notification), "",
                NotificationConstants.NOTIFICATION_ID_PROFILE_COMPLETION_REMINDER,
                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                defaultSoundUri.toString(),
                NotificationManager.IMPORTANCE_HIGH, -1
            )

            createNotificationChannel(
                context.getString(R.string.general_notification_), "",
                NotificationConstants.NOTIFICATION_ID_GENERAL,
                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                defaultSoundUri.toString(),
                NotificationManager.IMPORTANCE_HIGH, -1
            )
        }

    }

    fun showNotification(
        context: Context,
        title: String?,
        body: String?,
        image:String? = null,
        notificationModel: NotificationModel,
        intent: Intent
    ) {
        try {
            //  Bitmap largeIcon = getImageForNotifications(nameOfNotifications);
            // String stringTypeofSound = getTypeOfSoundForPrayer(nameOfNotifications);
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
            intent.action = Intent.ACTION_MAIN
            intent.putExtra(
                DuaFirebaseMessagingService.ACTION,
                notificationModel.mRemoteMessage!!.data[DuaFirebaseMessagingService.ACTION]
            )
            intent.putExtra(
                DuaFirebaseMessagingService.JSON_DATA,
                notificationModel.mRemoteMessage!!.data[DuaFirebaseMessagingService.JSON_DATA]
            )
            mNotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            when (notificationModel.type) {
                NotificationType.MESSAGE -> showMessageNotificationV2(
                    context,
                    title,
                    body,
                    notificationModel,
                    intent
                )

                NotificationType.MATCH -> if(image.isNullOrEmpty()) showMatchNotification(
                    context,
                    title,
                    body,
                    notificationModel,
                    intent
                ) else showMatchNotificationWithImage(
                    context,
                    title,
                    body,
                    image,
                    intent
                )

                NotificationType.NEW_LIKE_RECEIVED -> if(image.isNullOrEmpty()) showLikeYouNotification(
                    context,
                    title,
                    body,
                    notificationModel,
                    intent
                ) else showLikeYouNotificationWithImage(
                    context,
                    title,
                    body,
                    image,
                    intent)

                NotificationType.UN_MATCHED -> {
                }

                NotificationType.COUNTER_RESET -> showCounterResetNotification(
                    context,
                    title,
                    body,
                    notificationModel,
                    intent
                )

                NotificationType.USER_DELETED -> {
                    Handler(Looper.getMainLooper()).postDelayed({
                        showUserDeleteNotification(
                            context,
                            title,
                            body,
                            notificationModel,
                            intent
                        )
                    },1000)
                }

                NotificationType.PROFILE_COMPLETION_REMINDER -> {
                   if(image.isNullOrEmpty()) showGeneralNotification(
                        context,
                        title,
                        body,
                        notificationModel,
                        intent,
                        R.string.profile_completion_reminder_notification,
                        NotificationConstants.NOTIFICATION_ID_PROFILE_COMPLETION_REMINDER
                    ) else showGeneralNotificationWithImage(
                       context,
                       title,
                       body,
                       image,
                       intent,
                       R.string.profile_completion_reminder_notification,
                       NotificationConstants.NOTIFICATION_ID_PROFILE_COMPLETION_REMINDER
                    )
                }
                NotificationType.NEW_REFERRAL_REWARD -> {
                    if (!HomeActivity.isActive) {
                       if(image.isNullOrEmpty()) showGeneralNotification(
                            context,
                            title,
                            body,
                            notificationModel,
                            intent,
                            R.string.general_notification_,
                            NotificationConstants.NOTIFICATION_ID_GENERAL
                        ) else showGeneralNotificationWithImage(
                           context,
                           title,
                           body,
                           image,
                           intent,
                           R.string.general_notification_,
                           NotificationConstants.NOTIFICATION_ID_GENERAL
                        )
                    }
                }
                NotificationType.IS_LIKED_MESSAGE_STATE_CHANGED,
                NotificationType.SETUP_ACCOUNT_CREDENTIALS,
                NotificationType.DISLIKE_INSTACHAT,
                NotificationType.UPDATE_REMOTE_CONFIG  -> {
                }

                NotificationType.VIDEOCALLS_NEWVIDEOCALL -> {
                    val incomingCallIntent = Intent(context, CallActivity::class.java)
                    incomingCallIntent.putExtra(
                        VIDEO_CALL_DATA_KEY,
                        notificationModel.data as CallModel
                    )
                    showIncomingCallNotification(incomingCallIntent)
                }
                NotificationType.NEW_LOCAL_SUBSCRIPTION -> showPremiumNotification(
                        context,
                        title,
                        body,
                        notificationModel,
                        intent
                )

                NotificationType.NONE,
                NotificationType.VERIFY_USER_ATTRIBUTE,
                NotificationType.BADGE_2,
                NotificationType.VERIFY_YOUR_IMAGE,
                NotificationType.DOWNLOAD_DATA,
                NotificationType.OPEN_PREMIUM_PAYWALL,
                NotificationType.PREMIUM_SPECIAL_OFFER,
                NotificationType.PREMIUM_PAY_1_GET_1,
                NotificationType.DISABLED_STATE_CHANGED,
                NotificationType.PUSH_BADGE2_VERIFICATION,
                NotificationType.IMPRESSIONS_REWARDED,
                NotificationType.INSTACHAT_LASTHOUR,
                NotificationType.RMOD_LASTHOUR,
                NotificationType.BOOST_FAILED,
                NotificationType.BOOST_SUCCESS,
                NotificationType.SHOW_DONT_LET_GO_OFFER,
                NotificationType.NEW_GROUPED_LIKES,
                NotificationType.EXTRA_SWIPES_REWARDED_MALE,
                NotificationType.REWARD_VIDEOS,
                NotificationType.FEATURED_USERS_RESET,
                NotificationType.RMOD_GENERATED,
                -> {
                    if(image.isNullOrEmpty()) showGeneralNotification(
                        context,
                        title,
                        body,
                        notificationModel,
                        intent,
                        R.string.general_notification_,
                        NotificationConstants.NOTIFICATION_ID_GENERAL
                    ) else showGeneralNotificationWithImage(
                        context,
                        title,
                        body,
                        image,
                        intent,
                        R.string.general_notification_,
                        NotificationConstants.NOTIFICATION_ID_GENERAL
                    )

                }
                else -> {}
            }

        } catch (e: Exception) {
            e.message
        }

    }

    private fun showLikeYouNotification(
        context: Context,
        title: String?,
        body: String?,
        notificationModel: NotificationModel,
        intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
                getActivity(
                        this,
                        notificationId,
                        intent,
                        FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
                )

        @Suppress("UNCHECKED_CAST")
        val isSuperLike: Boolean =
                (notificationModel.data as UserLikedYouNotificationResponse).data?.type == InteractionType.SUPER_LIKE.value
        val largeIcon = BitmapFactory.decodeResource(
                context.resources,
                if (isSuperLike) R.drawable.ic_notifcation_superlike else R.drawable.ic_notification_newlike
        )

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(R.string.new_liked_you_notification), "",
                    NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )

            val notification = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU
            )
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setSmallIcon(R.drawable.ic_dua_notification)
              //  .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setLargeIcon(largeIcon)
                .setAutoCancel(true)
                .setChannelId(NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU)
                .setContentIntent(pendingIntent)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                .setShowWhen(true)
                .setGroup(GROUP_KEY_LIKED_YOU)
                .setGroupAlertBehavior(NotificationCompat.GROUP_ALERT_SUMMARY)
                .build()
            mNotificationManager?.notify(notificationId, notification)

        } else {
            val mBuilder = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU
            )
                .setSmallIcon(R.drawable.ic_dua_notification)
                .setLargeIcon(largeIcon)
            //    .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setLights(Color.GREEN, 1, 1)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                .setContentTitle(title)
                .setContentText(body)
                .setGroup(GROUP_KEY_LIKED_YOU)
                .setGroupAlertBehavior(NotificationCompat.GROUP_ALERT_SUMMARY)
                .setShowWhen(true)
            /*.addAction(R.drawable.share, getString(R.string.share),
                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

            mBuilder.setSound(defaultSoundUri)

            mNotificationManager?.notify(notificationId, mBuilder.build())
        }

        val summaryNotification = NotificationCompat.Builder(
            applicationContext,
            NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU
        )
            .setContentTitle(title)
            //set content text to support devices running API level < 24
            .setContentText(body)
            .setSmallIcon(R.drawable.ic_dua_notification)
            //build summary info into InboxStyle template
            .setStyle(
                NotificationCompat.InboxStyle()
                    .setSummaryText("")
            )
            //specify which group this notification belongs to
            .setGroup(GROUP_KEY_LIKED_YOU)
            //set this notification as the summary for the group
            .setGroupSummary(true)
            .build()
        mNotificationManager?.notify(LIKED_YOU_SUMMARY_ID, summaryNotification)

    }

    private fun showLikeYouNotificationWithImage(
        context: Context,
        title: String?,
        body: String?,
        image: String?,
        intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
            getActivity(
                this,
                notificationId,
                intent,
                FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
            )

 /*       @Suppress("UNCHECKED_CAST")
        val isSuperLike: Boolean =
            (notificationModel.data as UserLikedYouNotificationResponse).data?.type == InteractionType.SUPER_LIKE.value
        val largeIcon = BitmapFactory.decodeResource(
            context.resources,
            if (isSuperLike) R.drawable.ic_notifcation_superlike else R.drawable.ic_notification_newlike
        )*/
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

        Glide.with(this)
            .asBitmap()
            .load(image)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?
                ) {
                    val roundedLargeImage = getCircleBitmap(resource)

                    if (Build.VERSION.SDK_INT >= 26) {

                        NotificationHelper.getNotificationHelper(applicationContext)
                            .createNotificationChannel(
                                context.getString(R.string.new_liked_you_notification), "",
                                NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU,
                                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                                defaultSoundUri.toString(),
                                NotificationManager.IMPORTANCE_HIGH, -1
                            )

                        val notification = NotificationCompat.Builder(
                            applicationContext,
                            NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU
                        )
                            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                            .setSmallIcon(R.drawable.ic_dua_notification)
                            //  .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                            .setLargeIcon(roundedLargeImage)
                            .setAutoCancel(true)
                            .setChannelId(NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU)
                            .setContentIntent(pendingIntent)
                            .setContentTitle(title)
                            .setContentText(body)
                            .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                            .setShowWhen(true)
                            .setGroup(GROUP_KEY_LIKED_YOU)
                            .setGroupAlertBehavior(NotificationCompat.GROUP_ALERT_SUMMARY)
                            .build()
                        mNotificationManager?.notify(notificationId, notification)

                    } else {
                        val mBuilder = NotificationCompat.Builder(
                            applicationContext,
                            NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU
                        )
                            .setSmallIcon(R.drawable.ic_dua_notification)
                            .setLargeIcon(roundedLargeImage)
                            //    .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                            .setAutoCancel(true)
                            .setLights(Color.GREEN, 1, 1)
                            .setContentIntent(pendingIntent)
                            .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                            .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                            .setContentTitle(title)
                            .setContentText(body)
                            .setGroup(GROUP_KEY_LIKED_YOU)
                            .setGroupAlertBehavior(NotificationCompat.GROUP_ALERT_SUMMARY)
                            .setShowWhen(true)
                        /*.addAction(R.drawable.share, getString(R.string.share),
                                createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

                        mBuilder.setSound(defaultSoundUri)

                        mNotificationManager?.notify(notificationId, mBuilder.build())
                    }

                    val summaryNotification = NotificationCompat.Builder(
                        applicationContext,
                        NotificationConstants.NOTIFICATION_ID_NEW_LIKED_YOU
                    )
                        .setContentTitle(title)
                        //set content text to support devices running API level < 24
                        .setContentText(body)
                        .setSmallIcon(R.drawable.ic_dua_notification)
                        //build summary info into InboxStyle template
                        .setStyle(
                            NotificationCompat.InboxStyle()
                                .setSummaryText("")
                        )
                        //specify which group this notification belongs to
                        .setGroup(GROUP_KEY_LIKED_YOU)
                        //set this notification as the summary for the group
                        .setGroupSummary(true)
                        .build()
                    mNotificationManager?.notify(LIKED_YOU_SUMMARY_ID, summaryNotification)

                }

                override fun onLoadCleared(placeholder: Drawable?) {
                }

            })
    }

    @Suppress("UNUSED_PARAMETER")
    private fun showMatchNotification(
        context: Context,
        title: String?,
        body: String?,
        notificationModel: NotificationModel,
        intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
                getActivity(
                        this,
                        notificationId,
                        intent,
                        FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
                )

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(R.string.new_match_notification), "",
                    NotificationConstants.NOTIFICATION_ID_NEW_MATCH,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )

            val notification = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_NEW_MATCH
            )
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setSmallIcon(R.drawable.ic_dua_notification)
                //.setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setChannelId(NotificationConstants.NOTIFICATION_ID_NEW_MATCH)
                .setContentIntent(pendingIntent)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                .setShowWhen(true)
                .build()
            mNotificationManager?.notify(notificationId, notification)

        } else {
            val mBuilder = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_NEW_MATCH
            )
                .setSmallIcon(R.drawable.ic_dua_notification)
               // .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setLights(Color.GREEN, 1, 1)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                .setContentTitle(title)
                .setContentText(body)
                .setShowWhen(true)
            /*.addAction(R.drawable.share, getString(R.string.share),
                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

            mBuilder.setSound(defaultSoundUri)

            mNotificationManager?.notify(notificationId, mBuilder.build())
        }
    }

    private fun showMatchNotificationWithImage(
        context: Context,
        title: String?,
        body: String?,
        image:String,
        intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
            getActivity(
                this,
                notificationId,
                intent,
                FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
            )

   /*     val largeIcon =
            BitmapFactory.decodeResource(context.resources, R.drawable.ic_notification_round_logo)*/

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        Glide.with(this)
            .asBitmap()
            .load(image)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?
                ) {
                  val roundedLargeImage = getCircleBitmap(resource)
                    if (Build.VERSION.SDK_INT >= 26) {

                        NotificationHelper.getNotificationHelper(applicationContext)
                            .createNotificationChannel(
                                context.getString(R.string.new_match_notification), "",
                                NotificationConstants.NOTIFICATION_ID_NEW_MATCH,
                                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                                defaultSoundUri.toString(),
                                NotificationManager.IMPORTANCE_HIGH, -1
                            )

                        val notification = NotificationCompat.Builder(
                            applicationContext,
                            NotificationConstants.NOTIFICATION_ID_NEW_MATCH
                        )
                            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                            .setSmallIcon(R.drawable.ic_dua_notification)
                            //.setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                            .setLargeIcon(roundedLargeImage)
                            .setAutoCancel(true)
                            .setChannelId(NotificationConstants.NOTIFICATION_ID_NEW_MATCH)
                            .setContentIntent(pendingIntent)
                            .setContentTitle(title)
                            .setContentText(body)
                            .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                            .setShowWhen(true)
                            .build()
                        mNotificationManager?.notify(notificationId, notification)

                    } else {
                        val mBuilder = NotificationCompat.Builder(
                            applicationContext,
                            NotificationConstants.NOTIFICATION_ID_NEW_MATCH
                        )
                            .setSmallIcon(R.drawable.ic_dua_notification)
                            // .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                            .setLargeIcon(roundedLargeImage)
                            .setAutoCancel(true)
                            .setLights(Color.GREEN, 1, 1)
                            .setContentIntent(pendingIntent)
                            .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                            .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                            .setContentTitle(title)
                            .setContentText(body)
                            .setShowWhen(true)
                        /*.addAction(R.drawable.share, getString(R.string.share),
                                createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

                        mBuilder.setSound(defaultSoundUri)

                        mNotificationManager?.notify(notificationId, mBuilder.build())
                    }
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                }

            })

    }

    @Suppress("UNUSED_PARAMETER")
    private fun showPremiumNotification(
        context: Context,
        title: String?,
        body: String?,
        notificationModel: NotificationModel,
        intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
            getActivity(
                this,
                notificationId,
                intent,
                FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
            )
        val largeIcon =
            BitmapFactory.decodeResource(context.resources, R.drawable.ic_premium_diamond)

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(R.string.new_match_notification), "",
                    NotificationConstants.NOTIFICATION_ID_GENERAL,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )

            val notification = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_NEW_MATCH
            )
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setSmallIcon(R.drawable.ic_dua_notification)
                // .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setLargeIcon(largeIcon)
                .setAutoCancel(true)
                .setChannelId(NotificationConstants.NOTIFICATION_ID_NEW_MATCH)
                .setContentIntent(pendingIntent)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                .setShowWhen(true)
                .build()
            mNotificationManager?.notify(notificationId, notification)

        } else {
            val mBuilder = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_NEW_MATCH
            )
                .setSmallIcon(R.drawable.ic_dua_notification)
               // .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setLargeIcon(largeIcon)
                .setAutoCancel(true)
                .setLights(Color.GREEN, 1, 1)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                .setContentTitle(title)
                .setContentText(body)
                .setShowWhen(true)
            /*.addAction(R.drawable.share, getString(R.string.share),
                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

            mBuilder.setSound(defaultSoundUri)

            mNotificationManager?.notify(notificationId, mBuilder.build())
        }
    }

    private fun showCounterResetNotification(
        context: Context, title: String?, body: String?, @Suppress(
            "UNUSED_PARAMETER"
        ) notificationModel: NotificationModel, intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
                getActivity(
                        this,
                        notificationId,
                        intent,
                        FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
                )

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(R.string.counter_reset_notification), "",
                    NotificationConstants.NOTIFICATION_ID_COUNTER_RESET,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )

            val notification = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_COUNTER_RESET
            )
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setSmallIcon(R.drawable.ic_dua_notification)
             //   .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setChannelId(NotificationConstants.NOTIFICATION_ID_COUNTER_RESET)
                .setContentIntent(pendingIntent)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                .setShowWhen(true)
                .build()
            mNotificationManager?.notify(notificationId, notification)

        } else {
            val mBuilder = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_COUNTER_RESET
            )
                .setSmallIcon(R.drawable.ic_dua_notification)
               // .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setLights(Color.GREEN, 1, 1)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                .setContentTitle(title)
                .setContentText(body)
                .setShowWhen(true)
            /*.addAction(R.drawable.share, getString(R.string.share),
                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

            mBuilder.setSound(defaultSoundUri)

            mNotificationManager?.notify(notificationId, mBuilder.build())
        }
    }

    @Deprecated(message = "Use showMessageNotificationV2 function")
    private fun showMessageNotification(
        context: Context,
        title: String?,
        body: String?,
        notificationModel: NotificationModel,
        intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
                getActivity(this, 0, intent, FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT)
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val conversationWebSocketModel = notificationModel.data as ConversationData
        val imageUrl = getS3Url(conversationWebSocketModel.conversationMember.pictureUrl)

        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(R.string.new_chat_notification), "",
                    NotificationConstants.NOTIFICATION_ID_MESSAGE,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )


            if (isApplicationInTheBackground()) {
                Glide.with(this)
                    .asBitmap()
                    .load(imageUrl)
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onResourceReady(
                            resource: Bitmap,
                            transition: Transition<in Bitmap>?
                        ) {
                            val notification = NotificationCompat.Builder(
                                applicationContext,
                                NotificationConstants.NOTIFICATION_ID_MESSAGE
                            )
                                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                                .setSmallIcon(R.drawable.ic_dua_notification)
//                                .setColor(
//                                    ContextCompat.getColor(
//                                        applicationContext,
//                                        R.color.dua_red_color
//                                    )
//                                )
                                .setAutoCancel(true)
                                .setChannelId(NotificationConstants.NOTIFICATION_ID_MESSAGE)
                                .setContentIntent(pendingIntent)
                                .setContentTitle(title)
                                .setContentText(body)
                                .setLargeIcon(getCircleBitmap(resource))
                                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                                .setShowWhen(true)
                                .build()
                            mNotificationManager?.notify(notificationId, notification)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                        }
                    })
            } else {

                Glide.with(this)
                    .asBitmap()
                    .load(imageUrl)
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onResourceReady(
                            resource: Bitmap,
                            transition: Transition<in Bitmap>?
                        ) {
                            if (ConversationFragment.isShowedConversationFragment && ConversationFragment.isChattingWithId == conversationWebSocketModel.conversationMember.id) {
                            } else {
                                val notification = NotificationCompat.Builder(
                                    applicationContext,
                                    NotificationConstants.NOTIFICATION_ID_MESSAGE
                                )
                                    .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                                    .setSmallIcon(R.drawable.ic_dua_notification)
//                                    .setColor(
//                                        ContextCompat.getColor(
//                                            applicationContext,
//                                            R.color.dua_red_color
//                                        )
//                                    )
                                    .setAutoCancel(true)
                                    .setChannelId(NotificationConstants.NOTIFICATION_ID_MESSAGE)
                                    .setContentIntent(pendingIntent)
                                    .setContentTitle(title)
                                    .setContentText(body)
                                    .setLargeIcon(getCircleBitmap(resource))
                                    .setShowWhen(true)
                                    .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                                    .addAction(
                                        getReplayAction(
                                            conversationWebSocketModel.conversationMember,
                                            notificationId
                                        )
                                    )
                                    .setSound(defaultSoundUri)
                                    .build()
                                // Issue the notification.
                                with(NotificationManagerCompat.from(context)) {
                                    mNotificationManager?.notify(notificationId, notification)
                                }
                            }

                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                        }

                    })
            }

        } else {
            if (isApplicationInTheBackground()) {
                Glide.with(this)
                    .asBitmap()
                    .load(imageUrl)
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onLoadCleared(placeholder: Drawable?) {
                        }

                        override fun onResourceReady(
                            resource: Bitmap,
                            transition: Transition<in Bitmap>?
                        ) {
                            val mBuilder = NotificationCompat.Builder(
                                applicationContext,
                                NotificationConstants.NOTIFICATION_ID_MESSAGE
                            )
                                .setSmallIcon(R.drawable.ic_dua_notification)
//                                .setColor(
//                                    ContextCompat.getColor(
//                                        applicationContext,
//                                        R.color.dua_red_color
//                                    )
//                                )
                                .setContentText(body)
                                .setAutoCancel(true)
                                .setLights(Color.GREEN, 1, 1)
                                .setContentIntent(pendingIntent)
                                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                                .setContentTitle(title)
                                .setLargeIcon(getCircleBitmap(resource))
                                .setShowWhen(true)
                            /*.addAction(R.drawable.share, getString(R.string.share),
                                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

                            mBuilder.setSound(defaultSoundUri)

                            mNotificationManager?.notify(notificationId, mBuilder.build())
                        }

                    })
            } else {
                Glide.with(this)
                    .asBitmap()
                    .load(imageUrl)
                    .into(object : CustomTarget<Bitmap>() {
                        override fun onLoadCleared(placeholder: Drawable?) {
                        }

                        override fun onResourceReady(
                            resource: Bitmap,
                            transition: Transition<in Bitmap>?
                        ) {
                            if (!(ConversationFragment.isShowedConversationFragment && ConversationFragment.isChattingWithId == conversationWebSocketModel.conversationMember.id)) {
                                val mBuilder = NotificationCompat.Builder(
                                    applicationContext,
                                    NotificationConstants.NOTIFICATION_ID_MESSAGE
                                )
                                    .setSmallIcon(R.drawable.ic_dua_notification)
//                                    .setColor(
//                                        ContextCompat.getColor(
//                                            applicationContext,
//                                            R.color.dua_red_color
//                                        )
//                                    )
                                    .setContentText(body)
                                    .setAutoCancel(true)
                                    .setLights(Color.GREEN, 1, 1)
                                    .setContentIntent(pendingIntent)
                                    .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                                    .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                                    .setContentTitle(title)
                                    .setLargeIcon(getCircleBitmap(resource))
                                    .setShowWhen(true)
                                    .addAction(
                                        getReplayAction(
                                            conversationWebSocketModel.conversationMember,
                                            notificationId
                                        )
                                    )

                                /*.addAction(R.drawable.share, getString(R.string.share),
                                        createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/
                                mBuilder.setSound(defaultSoundUri)


                                mNotificationManager?.notify(notificationId, mBuilder.build())
                            }
                        }

                    })
            }
        }
    }


    private fun showMessageNotificationV2(
        context: Context,
        title: String?,
        body: String?,
        notificationModel: NotificationModel,
        intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val conversationWebSocketModel = notificationModel.data as ConversationData
        val isInstaChatType =
            conversationWebSocketModel.conversationMember.type == ConversationType.INSTANT_CHAT.value
        val isFemaleUser = conversationWebSocketModel.conversationMember.gender != GenderType.MAN.value
        val isBlurredUrl =
            (isInstaChatType && isFemaleUser && (!DuaApplication.instance.isLoggedInUserPremium() && !DuaApplication.instance.isLoggedInUserFreeStarterExperience()))
        val imageUrl =
            getS3Url(if (isBlurredUrl) conversationWebSocketModel.conversationMember.bluredThumbnailUrl!!
            else conversationWebSocketModel.conversationMember.pictureUrl)

        val NOTIFICATION_ID_CONVERSATION_HASH_CODE = conversationWebSocketModel.conversationMember.id.hashCode()
        val pendingIntent = getActivity(this, NOTIFICATION_ID_CONVERSATION_HASH_CODE, intent, FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT)

        Timber.tag("imageUrl").e(imageUrl)
        if (!(ConversationFragment.isShowedConversationFragment && ConversationFragment.isChattingWithId == conversationWebSocketModel.conversationMember.userId)) {

            if (Build.VERSION.SDK_INT >= 26) {

                getNotificationHelper(applicationContext)
                        .createNotificationChannel(
                                context.getString(R.string.new_chat_notification), "",
                                NotificationConstants.NOTIFICATION_ID_MESSAGE,
                                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                                defaultSoundUri.toString(),
                                NotificationManager.IMPORTANCE_HIGH, -1
                        )

                Glide.with(this)
                        .asBitmap()
                        .load(imageUrl)
                        .into(object : CustomTarget<Bitmap>() {
                            override fun onResourceReady(
                                    resource: Bitmap,
                                    transition: Transition<in Bitmap>?
                            ) {

                                val statusBarNotification =
                                        findActiveNotification(
                                                NOTIFICATION_ID_CONVERSATION_HASH_CODE,
                                                conversationWebSocketModel.conversationMember.id
                                        )

                                if (statusBarNotification == null) {
                                    val senderPerson = Person.Builder().also {
                                        it.setName(title)
                                        it.setIcon(IconCompat.createWithBitmap(getCircleBitmap(resource)))
                                    }.build()
                                    val message = NotificationCompat.MessagingStyle.Message(
                                            body,
                                            conversationWebSocketModel.message.time,
                                            senderPerson
                                    )
                                    val notificationsStyle =
                                            NotificationCompat.MessagingStyle(senderPerson)
                                                    .addMessage(message)
                                    val notificationBuilder = NotificationCompat.Builder(
                                            applicationContext,
                                            NotificationConstants.NOTIFICATION_ID_MESSAGE
                                    )
                                            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                                            .setSmallIcon(R.drawable.ic_dua_notification)
//                                            .setColor(
//                                                    ContextCompat.getColor(
//                                                            applicationContext,
//                                                            R.color.dua_red_color
//                                                    )
//                                            )
                                            .setAutoCancel(true)
                                            .setChannelId(NotificationConstants.NOTIFICATION_ID_MESSAGE)
                                            .setContentIntent(pendingIntent)
                                            .setShowWhen(true)
                                            .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                                            .setStyle(notificationsStyle)
                                            .setLargeIcon(getCircleBitmap(resource))
                                            .setSound(defaultSoundUri)
                                    if (!isApplicationInTheBackground() && conversationWebSocketModel.conversationMember.type != ConversationType.INSTANT_CHAT.value) {
                                        notificationBuilder.addAction(
                                                getReplayAction(
                                                        conversationWebSocketModel.conversationMember,
                                                        NOTIFICATION_ID_CONVERSATION_HASH_CODE
                                                )
                                        )
                                    }

                                    // Issue the notification.
                                    with(NotificationManagerCompat.from(context)) {
                                        mNotificationManager?.notify(
                                                conversationWebSocketModel.conversationMember.id,
                                                NOTIFICATION_ID_CONVERSATION_HASH_CODE,
                                                notificationBuilder.build()
                                        )
                                    }
                                } else {
                                    updateConversationNotification(
                                            title,
                                            body,
                                            conversationWebSocketModel.message,
                                            resource,
                                            NOTIFICATION_ID_CONVERSATION_HASH_CODE
                                    )
                                }
                            }

                            override fun onLoadCleared(placeholder: Drawable?) {
                            }

                        })

            } else {
                Glide.with(this)
                        .asBitmap()
                        .load(imageUrl)
                        .into(object : CustomTarget<Bitmap>() {
                            override fun onLoadCleared(placeholder: Drawable?) {
                            }

                            override fun onResourceReady(
                                    resource: Bitmap,
                                    transition: Transition<in Bitmap>?
                            ) {

                                if (Build.VERSION.SDK_INT >= 24) {

                                    val statusBarNotification =
                                            findActiveNotification(
                                                    NOTIFICATION_ID_CONVERSATION_HASH_CODE,
                                                    conversationWebSocketModel.conversationMember.id
                                            )

                                    if (statusBarNotification == null) {
                                        val senderPerson = Person.Builder().also {
                                            it.setName(title)
                                            it.setIcon(
                                                    IconCompat.createWithBitmap(
                                                            getCircleBitmap(
                                                                    resource
                                                            )
                                                    )
                                            )
                                        }.build()
                                        val message = NotificationCompat.MessagingStyle.Message(
                                                body,
                                                conversationWebSocketModel.message.time,
                                                senderPerson
                                        )
                                        val notificationsStyle =
                                                NotificationCompat.MessagingStyle(senderPerson)
                                                        .addMessage(message)
                                        val mBuilder = NotificationCompat.Builder(
                                                applicationContext,
                                                NotificationConstants.NOTIFICATION_ID_MESSAGE
                                        )
                                                .setSmallIcon(R.drawable.ic_dua_notification)
//                                                .setColor(
//                                                        ContextCompat.getColor(
//                                                                applicationContext,
//                                                                R.color.dua_red_color
//                                                        )
//                                                )
                                                .setAutoCancel(true)
                                                .setLights(Color.GREEN, 1, 1)
                                                .setContentIntent(pendingIntent)
                                                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                                                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                                                .setStyle(notificationsStyle)
                                                .setShowWhen(true)
                                        if (!isApplicationInTheBackground() && conversationWebSocketModel.conversationMember.type != ConversationType.INSTANT_CHAT.value) {
                                            mBuilder.addAction(
                                                    getReplayAction(
                                                            conversationWebSocketModel.conversationMember,
                                                            NOTIFICATION_ID_CONVERSATION_HASH_CODE
                                                    )
                                            )
                                        }

                                        /*.addAction(R.drawable.share, getString(R.string.share),
                                            createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/
                                        mBuilder.setSound(defaultSoundUri)


                                        // Issue the notification.
                                        with(NotificationManagerCompat.from(context)) {
                                            mNotificationManager?.notify(
                                                    conversationWebSocketModel.conversationMember.id,
                                                    NOTIFICATION_ID_CONVERSATION_HASH_CODE,
                                                    mBuilder.build()
                                            )
                                        }

                                    } else {
                                        updateConversationNotification(
                                                title,
                                                body,
                                                conversationWebSocketModel.message,
                                                resource,
                                                NOTIFICATION_ID_CONVERSATION_HASH_CODE
                                        )
                                    }
                                } else {
                                    val mBuilder = NotificationCompat.Builder(
                                            applicationContext,
                                            NotificationConstants.NOTIFICATION_ID_MESSAGE
                                    )
                                            .setSmallIcon(R.drawable.ic_dua_notification)
//                                            .setColor(
//                                                    ContextCompat.getColor(
//                                                            applicationContext,
//                                                            R.color.dua_red_color
//                                                    )
//                                            )
                                            .setContentText(body)
                                            .setAutoCancel(true)
                                            .setLights(Color.GREEN, 1, 1)
                                            .setContentIntent(pendingIntent)
                                            .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                                            .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                                            .setContentTitle(title)
                                            .setLargeIcon(getCircleBitmap(resource))
                                            .setShowWhen(true)
                                    if (!isApplicationInTheBackground() && conversationWebSocketModel.conversationMember.type != ConversationType.INSTANT_CHAT.value) {
                                        mBuilder.addAction(
                                                getReplayAction(
                                                        conversationWebSocketModel.conversationMember,
                                                        notificationId
                                                )
                                        )
                                    }

                                    /*.addAction(R.drawable.share, getString(R.string.share),
                                        createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/
                                    mBuilder.setSound(defaultSoundUri)

                                    mNotificationManager?.notify(notificationId, mBuilder.build())
                                }

                            }

                        })
            }
        }
    }

    private fun findActiveNotification(
        conversationId: Int,
        notificationTag: String?
    ): Notification? {
        return (context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager)
            .activeNotifications.find { it.id == conversationId && it.tag == notificationTag }?.notification
    }

    @RequiresApi(Build.VERSION_CODES.N)
    private fun updateConversationNotification(
        title: String?,
        body: String?,
        model: MessageModel,
        bitmap: Bitmap,
        notificationId: Int
    ) {
        // Find notification that you want to update.
        val activeNotification =
            findActiveNotification(notificationId, model.conversationId) ?: return

        // Extract MessagingStyle object from the active notification.
        val activeStyle = NotificationCompat.MessagingStyle.extractMessagingStyleFromNotification(
            activeNotification
        )

        // Recover builder from the active notification.
        val recoveredBuilder = Notification.Builder.recoverBuilder(context, activeNotification)

        // The recoveredBuilder is Notification.Builder whereas the activeStyle is NotificationCompat.MessagingStyle.
        // It means you need to recreate the style as Notification.MessagingStyle to make it compatible with the builder.
        val newStyle = if (Build.VERSION.SDK_INT >= 28) {
            val senderPerson = android.app.Person.Builder().also {
                it.setName(title)
                it.setIcon(Icon.createWithBitmap(getCircleBitmap(bitmap)))
            }
            Notification.MessagingStyle(senderPerson.build())
        } else {
            @Suppress("DEPRECATION")
            Notification.MessagingStyle(title!!)
        }

        activeStyle?.messages?.forEach {
            @Suppress("DEPRECATION")
            newStyle.addMessage(
                Notification.MessagingStyle.Message(
                    it.text,
                    it.timestamp,
                    it.sender
                )
            )
        }

        // Add your reply to the new style.
        if (Build.VERSION.SDK_INT >= 28) {
            newStyle.addMessage(
                Notification.MessagingStyle.Message(
                    body.toString(),
                    System.currentTimeMillis(),
                    newStyle.user
                )
            )
        } else {
            @Suppress("DEPRECATION")
            newStyle.addMessage(
                Notification.MessagingStyle.Message(
                    body,
                    System.currentTimeMillis(),
                    title
                )
            )
        }

        // Set the new style to the recovered builder.
        recoveredBuilder.style = newStyle

        with(NotificationManagerCompat.from(context)) {
            mNotificationManager?.notify(
                model.conversationId,
                notificationId,
                recoveredBuilder.build()
            )
        }

    }

    private fun showUserDeleteNotification(
        context: Context,
        title: String?,
        body: String?,
        @Suppress("UNUSED_PARAMETER") notificationModel: NotificationModel,
        intent: Intent
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent = getActivity(this, notificationId, intent, FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT)


        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(R.string.counter_reset_notification), "",
                    NotificationConstants.NOTIFICATION_ID_USER_DELETED,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )

            val notification = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_USER_DELETED
            )
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setSmallIcon(R.drawable.ic_dua_notification)
             //   .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setChannelId(NotificationConstants.NOTIFICATION_ID_USER_DELETED)
                .setContentIntent(pendingIntent)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                .setShowWhen(true)
                .build()
            mNotificationManager?.notify(notificationId, notification)

        } else {
            val mBuilder = NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.NOTIFICATION_ID_USER_DELETED
            )
                .setSmallIcon(R.drawable.ic_dua_notification)
               // .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setLights(Color.GREEN, 1, 1)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                .setContentTitle(title)
                .setContentText(body)
                .setShowWhen(true)
            /*.addAction(R.drawable.share, getString(R.string.share),
                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

            mBuilder.setSound(defaultSoundUri)

            mNotificationManager?.notify(notificationId, mBuilder.build())
        }
    }

    fun showGeneralNotification(
        context: Context,
        title: String?,
        body: String?,
        @Suppress("UNUSED_PARAMETER") notificationModel: NotificationModel?,
        intent: Intent,
        channelName: Int,
        CHANNEL_ID: String
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
                getActivity(
                        this,
                        notificationId,
                        intent,
                        FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
                )

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(channelName), "",
                    CHANNEL_ID,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )

            val notification = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setSmallIcon(R.drawable.ic_dua_notification)
              //  .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setChannelId(CHANNEL_ID)
                .setContentIntent(pendingIntent)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                .setShowWhen(true)
                .build()
            mNotificationManager?.notify(notificationId, notification)

        } else {
            val mBuilder = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_dua_notification)
              //  .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setLights(Color.GREEN, 1, 1)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                .setContentTitle(title)
                .setContentText(body)
                .setShowWhen(true)
            /*.addAction(R.drawable.share, getString(R.string.share),
                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

            mBuilder.setSound(defaultSoundUri)

            mNotificationManager?.notify(notificationId, mBuilder.build())
        }
    }

    fun showGeneralNotificationWithImage(
        context: Context,
        title: String?,
        body: String?,
        image: String?,
        intent: Intent,
        channelName: Int,
        CHANNEL_ID: String
    ) {
        val notificationId = Random().nextInt(60000)
        val pendingIntent =
            getActivity(
                this,
                notificationId,
                intent,
                FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
            )


        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        Glide.with(this)
            .asBitmap()
            .load(image)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(
                    resource: Bitmap,
                    transition: Transition<in Bitmap>?
                ) {
                    val roundedLargeImage = getCircleBitmap(resource)

                    if (Build.VERSION.SDK_INT >= 26) {

                        NotificationHelper.getNotificationHelper(applicationContext)
                            .createNotificationChannel(
                                context.getString(channelName), "",
                                CHANNEL_ID,
                                NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                                defaultSoundUri.toString(),
                                NotificationManager.IMPORTANCE_HIGH, -1
                            )

                        val notification = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                            .setSmallIcon(R.drawable.ic_dua_notification)
                            //  .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                            .setLargeIcon(roundedLargeImage)
                            .setAutoCancel(true)
                            .setChannelId(CHANNEL_ID)
                            .setContentIntent(pendingIntent)
                            .setContentTitle(title)
                            .setContentText(body)
                            .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                            .setShowWhen(true)
                            .build()
                        mNotificationManager?.notify(notificationId, notification)

                    } else {
                        val mBuilder = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                            .setSmallIcon(R.drawable.ic_dua_notification)
                            .setLargeIcon(roundedLargeImage)
                            //  .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                            .setAutoCancel(true)
                            .setLights(Color.GREEN, 1, 1)
                            .setContentIntent(pendingIntent)
                            .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                            .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                            .setContentTitle(title)
                            .setContentText(body)
                            .setShowWhen(true)
                        /*.addAction(R.drawable.share, getString(R.string.share),
                                createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

                        mBuilder.setSound(defaultSoundUri)

                        mNotificationManager?.notify(notificationId, mBuilder.build())
                    }

                }

                override fun onLoadCleared(placeholder: Drawable?) {
                }

            })

    }

    fun dismissIncomingCallNotification() {
        mNotificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        mNotificationManager?.cancel(INCOMING_CALL_NOTIFICATION_ID)
    }

    fun showIncomingCallNotification(intent: Intent?) {
        var data: CallModel? = null
        if (intent != null && intent.extras != null) {
            data = intent.getParcelableExtra(VIDEO_CALL_DATA_KEY)
        }
        try {
            val acceptCallIntent = Intent(applicationContext, CallActivity::class.java)
            acceptCallIntent.putExtra(CALL_RESPONSE_ACTION_KEY, CALL_ACCEPT_ACTION)
            acceptCallIntent.putExtra(VIDEO_CALL_DATA_KEY, data)
            acceptCallIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            acceptCallIntent.action = CALL_ACCEPT_ACTION

            val acceptCallPendingIntent: PendingIntent =  TaskStackBuilder.create(this).run {
                addNextIntentWithParentStack(acceptCallIntent)
                getPendingIntent(ACCEPT_CALL_REQUEST_CODE, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
            }


            val incomingCallIntent = Intent(applicationContext, CallActivity::class.java)
            incomingCallIntent.putExtra(CALL_RESPONSE_ACTION_KEY, CALL_INCOMING_ACTION)
            incomingCallIntent.putExtra(VIDEO_CALL_DATA_KEY, data)
            incomingCallIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            incomingCallIntent.action = CALL_INCOMING_ACTION

            val incomingCallPendingIntent: PendingIntent = TaskStackBuilder.create(this).run {
                addNextIntentWithParentStack(incomingCallIntent)
                getPendingIntent(INCOMING_CALL_REQUEST_CODE, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
            }


            val cancelCallIntent = Intent(this, CallNotificationActionReceiver::class.java)
            cancelCallIntent.putExtra(CALL_RESPONSE_ACTION_KEY, CALL_CANCEL_ACTION)
            cancelCallIntent.putExtra(VIDEO_CALL_DATA_KEY, data)

            val cancelCallPendingIntent = PendingIntent.getBroadcast(
                this,
                CANCEL_CALL_REQUEST_CODE,
                cancelCallIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            createIncomingCallChannel()

            val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)

            val notificationBody = intent?.getStringExtra(VIDEO_CALL_BODY_KEY)
            val notification = NotificationCompat.Builder(this, CALL_CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_dua_notification)
                .setContentTitle(data?.caller?.name)
                .setContentText(notificationBody)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setCategory(NotificationCompat.CATEGORY_CALL)
                .addAction(R.color.green_500, getString(R.string.answer), acceptCallPendingIntent)
                .addAction(R.color.red_500, getString(R.string.decline), cancelCallPendingIntent)
                .setAutoCancel(true)
                .setOngoing(true)
                .setSound(defaultSoundUri)
                .setDefaults(Notification.DEFAULT_LIGHTS or Notification.DEFAULT_VIBRATE)
                .setFullScreenIntent(incomingCallPendingIntent, true)
                .build()

            mNotificationManager?.notify(INCOMING_CALL_NOTIFICATION_ID, notification)

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun createIncomingCallChannel() {
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)

        val mChannel: NotificationChannel
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mChannel = NotificationChannel(
                CALL_CHANNEL_ID,
                CALL_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            )
            mChannel.lightColor = Color.RED
            mChannel.enableLights(true)
            mChannel.description = "Call notifications"

            val audioAttributes = AudioAttributes.Builder()
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .setLegacyStreamType(AudioManager.STREAM_RING)
                .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                .build()

            mChannel.setSound(defaultSoundUri, audioAttributes)
            mNotificationManager?.createNotificationChannel(mChannel)
        }
    }

    fun showInstaChatVerifiedNotification(
        context: Context,
        title: String?,
        body: String?,
        @Suppress("UNUSED_PARAMETER") notificationModel: NotificationModel?,
        channelName: Int,
        CHANNEL_ID: String
    ) {
        mNotificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val notificationId = Random().nextInt(60000)

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(channelName), "",
                    CHANNEL_ID,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )

            val notification = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setSmallIcon(R.drawable.ic_dua_notification)
             //   .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setChannelId(CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                .setShowWhen(true)
                .build()
            mNotificationManager?.notify(notificationId, notification)

        } else {
            val mBuilder = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_dua_notification)
            //    .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setLights(Color.GREEN, 1, 1)
                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                .setContentTitle(title)
                .setContentText(body)
                .setShowWhen(true)
            /*.addAction(R.drawable.share, getString(R.string.share),
                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

            mBuilder.setSound(defaultSoundUri)

            mNotificationManager?.notify(notificationId, mBuilder.build())
        }
    }

    fun showInAppProductBought(
        context: Context,
        title: String?,
        body: String?,
        @Suppress("UNUSED_PARAMETER") notificationModel: NotificationModel?,
        channelName: Int,
        CHANNEL_ID: String,
        drawable: Int
    ) {
        mNotificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val notificationId = Random().nextInt(60000)
        val largeIcon = BitmapFactory.decodeResource(context.resources, drawable)

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        if (Build.VERSION.SDK_INT >= 26) {

            NotificationHelper.getNotificationHelper(applicationContext)
                .createNotificationChannel(
                    context.getString(channelName), "",
                    CHANNEL_ID,
                    NotificationConstants.NOTIFICATION_GROUP_DEFAULT,
                    defaultSoundUri.toString(),
                    NotificationManager.IMPORTANCE_HIGH, -1
                )

            val notification = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setSmallIcon(R.drawable.ic_dua_notification)
              //  .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setLargeIcon(largeIcon)
                .setAutoCancel(true)
                .setChannelId(CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_HIGH)   // heads-up
                .setShowWhen(true)
                .build()
            mNotificationManager?.notify(notificationId, notification)

        } else {
            val mBuilder = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_dua_notification)
                .setLargeIcon(largeIcon)
            //    .setColor(ContextCompat.getColor(applicationContext, R.color.dua_red_color))
                .setAutoCancel(true)
                .setLights(Color.GREEN, 1, 1)
                .setPriority(NotificationCompat.PRIORITY_MAX)   // heads-up
                .setVibrate(longArrayOf(100, 100, 100, 100, 100))
                .setContentTitle(title)
                .setContentText(body)
                .setShowWhen(true)
            /*.addAction(R.drawable.share, getString(R.string.share),
                    createShareIntentNotificationForPrayer(nameOfNotifications + " " + timeOfNotifications, "\n" + charSequenceContentText));*/

            mBuilder.setSound(defaultSoundUri)

            mNotificationManager?.notify(notificationId, mBuilder.build())
        }
    }

    private fun getReplayAction(
        conversationModel: ConversationModel,
        notificationId: Int
    ): NotificationCompat.Action {
        val replyLabel: String = getString(R.string.reply)
        val remoteInput: androidx.core.app.RemoteInput =
            androidx.core.app.RemoteInput.Builder(KEY_TEXT_REPLY).run {
                setLabel(replyLabel)
                build()
            }

        // Build a PendingIntent for the reply action to trigger.
        val replyPendingIntent: PendingIntent =
                getService(
                        applicationContext,
                        notificationId,
                        getReplayIntent(conversationModel, notificationId),
                        FLAG_MUTABLE or FLAG_UPDATE_CURRENT
                )

        return NotificationCompat.Action.Builder(
            R.drawable.ic_send,
            getString(R.string.reply), replyPendingIntent
        )
            .addRemoteInput(remoteInput)
            .build()

    }

    private fun getReplayIntent(conversationModel: ConversationModel, notificationId: Int): Intent {
        val intent = Intent(applicationContext, ReplyMessageService::class.java)
        intent.putExtra(CONVERSATION_MODEL, conversationModel)
        intent.putExtra(NOTIFICATION_ID, notificationId)
        return intent
    }

//    private fun getCircleBitmap(bitmap: Bitmap): Bitmap? {
//        val output = Bitmap.createBitmap(bitmap.width,
//                bitmap.height, Bitmap.Config.ARGB_8888)
//        val canvas = Canvas(output)
//        val color = Color.RED
//        val paint = Paint()
//        val rect = Rect(0, 0, bitmap.width, bitmap.height)
//        val rectF = RectF(rect)
//        paint.isAntiAlias = true
//        canvas.drawARGB(0, 0, 0, 0)
//        paint.color = color
//        canvas.drawOval(rectF, paint)
//        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
//        canvas.drawBitmap(bitmap, rect, rect, paint)
//        bitmap.recycle()
//        return output
//    }

    object Utils {
        fun getCircleBitmap(bitmap: Bitmap): Bitmap {
            val output: Bitmap
            val srcRect: Rect
            val dstRect: Rect
            val r: Float
            val width = bitmap.width
            val height = bitmap.height
            if (width > height) {
                output = Bitmap.createBitmap(height, height, Bitmap.Config.ARGB_8888)
                val left = (width - height) / 2
                val right = left + height
                srcRect = Rect(left, 0, right, height)
                dstRect = Rect(0, 0, height, height)
                r = height / 2.toFloat()
            } else {
                output = Bitmap.createBitmap(width, width, Bitmap.Config.ARGB_8888)
                val top = (height - width) / 2
                val bottom = top + width
                srcRect = Rect(0, top, width, bottom)
                dstRect = Rect(0, 0, width, width)
                r = width / 2.toFloat()
            }
            val canvas = Canvas(output)
            val color = -0xbdbdbe
            val paint = Paint()
            paint.isAntiAlias = true
            canvas.drawARGB(0, 0, 0, 0)
            paint.color = color
            canvas.drawCircle(r, r, r, paint)
            paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
            canvas.drawBitmap(bitmap, srcRect, dstRect, paint)
            return output
        }
    }

    fun showClevertapNotification(message: RemoteMessage) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CleverTapAPI.createNotificationChannel(
                applicationContext,
                CLEVERTAP_DEFAULT_CHANNEL_ID,
                getString(R.string.clevertap_default_channel_name),
                "",
                NotificationManager.IMPORTANCE_HIGH,
                true
            )
        }

        CTFcmMessageHandler().createNotification(applicationContext, message)
    }
}
