package com.duaag.android.firebase.model

import com.google.gson.annotations.SerializedName

data class UserMatchNotificationResponse(
    @SerializedName("receiverUserId")
    val receiverUserId: String,
    @SerializedName("data") val data: UserMatchesNotificationModel,
    @SerializedName("initiatorData") val initiatorData: InitiatorData? = null
) {


    data class InitiatorData(
        @SerializedName("sentSuperMatch") val sentSuperMatch: Boolean?,
        @SerializedName("id") val id: Int?,
        @SerializedName("receivedSuperMatch") val receivedSuperMatch: Boolean?,
        @SerializedName("time") val time: String?,
        @SerializedName("user") val user: UserData?,
        @SerializedName("seen") val seen: Boolean?
    )

    data class UserData(
        @SerializedName("firstName") val firstName: String?,
        @SerializedName("lastName") val lastName: String?,
        @SerializedName("gender") val gender: String?,
        @SerializedName("cognitoUserId") val cognitoUserId: String?,
        @SerializedName("profile") val profile: ProfileData?,
        @SerializedName("hasBadge1") val hasBadge1: Boolean?,
        @SerializedName("id") val id: Int?,
        @SerializedName("community") val community: String?,
        @SerializedName("premiumType") val premiumType: String?,
        @SerializedName("age") val age: Int?,
        @SerializedName("badge2") val badge2: String?
    )

    data class ProfileData(
        @SerializedName("bluredThumbnailUrl") val bluredThumbnailUrl: String?,
        @SerializedName("pictureUrl") val pictureUrl: String?,
        @SerializedName("thumbnailUrl") val thumbnailUrl: String?
    )

}