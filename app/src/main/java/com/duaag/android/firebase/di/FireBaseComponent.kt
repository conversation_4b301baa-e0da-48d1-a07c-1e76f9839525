package com.duaag.android.firebase.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.firebase.DuaFirebaseMessagingService
import dagger.Subcomponent

// Definition of a Dagger subcomponent
@ActivityScope
@Subcomponent()
interface FireBaseComponent {

    // Factory to create instances of RegistrationComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): FireBaseComponent
    }

    // Classes that can be injected by this Component
    fun inject(firebaseMessagingService: DuaFirebaseMessagingService)
}