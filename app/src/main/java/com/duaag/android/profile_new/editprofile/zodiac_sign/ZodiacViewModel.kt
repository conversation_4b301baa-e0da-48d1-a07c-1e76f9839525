package com.duaag.android.profile_new.editprofile.zodiac_sign

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.duaag.android.base.models.UserModel
import com.duaag.android.user.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

class ZodiacViewModel @Inject constructor(
    val userRepository: UserRepository,
) : ViewModel() {


    private val _uiData : MutableStateFlow<ZodiacUiData> = MutableStateFlow(ZodiacUiData())
    val uiData : LiveData<ZodiacUiData> get() = _uiData.asLiveData()

    fun updateUiData(user: UserModel?) {
      _uiData.update {
          it.copy(isZodiacSwitchEnabled = user?.hasPublicZodiacSign == true, zodiacSign = user?.userZodiacSign())
      }

    }

    fun showZodiacSigns() {
        _uiData.update {
            it.copy(areZodiacSignsShown = !it.areZodiacSignsShown)
        }
    }

    fun onZodiacSignSelected(zodiacSignDataItem: ZodiacSignDataItem) {
      //  _uiData.update { it.copy(zodiacSign = (zodiacSignDataItem as ZodiacSignDataItem.SignItem).zodiacSign.sign) }
        updateZodiacState(_uiData.value.isZodiacSwitchEnabled,(zodiacSignDataItem as ZodiacSignDataItem.SignItem).zodiacSign.sign)

    }

    private fun updateZodiacState(checked: Boolean, sign: ZodiacSign) {
        viewModelScope.launch {
            userRepository.updateZodiacSign(hasPublicZodiacSign = checked, zodiacSign = sign.stringValue)
        }
    }

    fun manageZodiacState(isEnabled: Boolean) {
        _uiData.value.zodiacSign?.let {
            updateZodiacState(
                isEnabled,
                it
            )
        }
    }
}
data class ZodiacSignItem(val sign: ZodiacSign,var isSelected: Boolean = false)
data class ZodiacUiData(
    val isZodiacSwitchEnabled: Boolean = false,
    val zodiacSign: ZodiacSign? = null,
    val areZodiacSignsShown: Boolean = false,
) {
    val zodiacSignsList : List<ZodiacSignItem> = ZodiacSign.values().map { ZodiacSignItem(sign = it, isSelected = it == zodiacSign) }

}