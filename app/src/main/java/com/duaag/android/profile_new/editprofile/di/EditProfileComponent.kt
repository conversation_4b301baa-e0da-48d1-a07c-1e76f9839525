package com.duaag.android.profile_new.editprofile.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.profile_new.editprofile.*
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment
import com.duaag.android.profile_new.editprofile.my_information.MyInformationFragment
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment
import com.duaag.android.profile_new.editprofile.profile_progress.ProfileProgressFragment
import com.duaag.android.profile_new.editprofile.zodiac_sign.ZodiacFragment
import dagger.Subcomponent

// Definition of a Dagger subcomponent
@ActivityScope
@Subcomponent(modules = [EditProfileViewModelModule::class])
interface EditProfileComponent {
    // Factory to create instances of RegistrationComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): EditProfileComponent
    }
    // Classes that can be injected by this Component
    fun inject(activity: EditProfileActivity)
    fun inject(fragment: EditProfileFragment)
    fun inject(fragment: AdditionalInfoFragment)
    fun inject(searchableFragment: MyInformationSearchableFragment)
    fun inject(fragment: MyInformationFragment)
    fun inject(fragment: SearchFragment)
    fun inject(fragment: ProfileProgressFragment)
    fun inject(fragment: ZodiacFragment)
    fun inject(fragment: AddUserHeightFragment)


}