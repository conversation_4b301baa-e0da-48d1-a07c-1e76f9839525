package com.duaag.android.profile_new.models

import com.duaag.android.R
import com.duaag.android.counters.data.models.CounterEntity
import com.duaag.android.counters.data.models.CounterNameEnum
import com.duaag.android.home.models.GooglePlayPackage

enum class InAppProduct(val type: String){
    BOOST("boost"),
    IMPRESSIONS("impressions"),
    FLIGHTS("flights"),
    UNDOS("undos"),
    INSTACHATS("instachats"),
}

class ProfileInAppPackagesModel(
    val index: Int,
    val image: Int,
    val packageType: Int,
    val packageName: GooglePlayPackage,
    var price: String? = null,
)

fun getInAppProductFromType(type: InAppProduct): ProfileInAppPackagesModel{
    return when(type){
        InAppProduct.BOOST ->  ProfileInAppPackagesModel(1, R.drawable.ic_impressions_icon, R.string.impressions, GooglePlayPackage.IMPRESSIONS_PACKAGE1)
        InAppProduct.IMPRESSIONS ->  ProfileInAppPackagesModel(1, R.drawable.ic_impressions_icon, R.string.impressions, GooglePlayPackage.IMPRESSIONS_PACKAGE1)
        InAppProduct.FLIGHTS ->  ProfileInAppPackagesModel(1, R.drawable.ic_impressions_icon, R.string.impressions, GooglePlayPackage.IMPRESSIONS_PACKAGE1)
        InAppProduct.UNDOS ->  ProfileInAppPackagesModel(1, R.drawable.ic_impressions_icon, R.string.impressions, GooglePlayPackage.IMPRESSIONS_PACKAGE1)
        InAppProduct.INSTACHATS ->  ProfileInAppPackagesModel(1, R.drawable.ic_impressions_icon, R.string.impressions, GooglePlayPackage.IMPRESSIONS_PACKAGE1)
    }
}
val freemiumInAppPackagesModels: List<ProfileInAppPackagesModel> by lazy {
    listOf(
        ProfileInAppPackagesModel(1,
            R.drawable.ic_impressions_icon,
            R.string.impressions,
            GooglePlayPackage.IMPRESSIONS_PACKAGE1),
        ProfileInAppPackagesModel(2,
            R.drawable.ic_instachat_icon,
            R.string.instachats_string,
            GooglePlayPackage.INSTACHATS_PACKAGE1),
        ProfileInAppPackagesModel(3,
            R.drawable.ic_flights_icon,
            R.string.flights,
            GooglePlayPackage.FLYING_PACKAGE1),
        ProfileInAppPackagesModel(4,
            R.drawable.ic_undo_icon,
            R.string.undo_title,
            GooglePlayPackage.UNDO_PACKAGE1)
    )
}


val premiumMaleInAppPackagesModels: List<ProfileInAppPackagesModel>
        by lazy {
            listOf(

                ProfileInAppPackagesModel(1,
                    R.drawable.ic_impressions_icon,
                    R.string.impressions,
                    GooglePlayPackage.IMPRESSIONS_PACKAGE1),
                ProfileInAppPackagesModel(2,
                    R.drawable.ic_instachat_icon,
                    R.string.instachats_string,
                    GooglePlayPackage.INSTACHATS_PACKAGE1))
        }

fun getPackageList(
    list: List<String>?,
    userCounters: List<CounterEntity>?,
): List<ProfileInAppPackagesModel> {
    if (list.isNullOrEmpty()) return emptyList()
    val consumables = mutableListOf<ProfileInAppPackagesModel>()
    list.forEach {
        when {
            InAppProduct.BOOST.type == it &&
                    (userCounters?.firstOrNull { counter ->
                        counter.name == CounterNameEnum.BOOST.value
                    }?.configuration?.isUnlimited ?: false).not() -> {
                consumables.add(ProfileInAppPackagesModel(1,
                    R.drawable.ic_boost_small_icon,
                    R.string.boost,
                    GooglePlayPackage.BOOST_PACKAGE1))
            }
            InAppProduct.IMPRESSIONS.type == it &&
                    (userCounters?.firstOrNull { counter ->
                        counter.name == CounterNameEnum.INTERACTION.value
                    }?.configuration?.isUnlimited ?: false).not() -> {
                consumables.add(ProfileInAppPackagesModel(2,
                    R.drawable.ic_trophy_small,
                    R.string.impressions,
                    GooglePlayPackage.IMPRESSIONS_PACKAGE1))
            }
            InAppProduct.INSTACHATS.type == it &&
                    (userCounters?.firstOrNull { counter ->
                        counter.name == CounterNameEnum.INSTACHAT.value
                    }?.configuration?.isUnlimited ?: false).not() -> {
                consumables.add(ProfileInAppPackagesModel(3,
                    R.drawable.ic_instachat_small,
                    R.string.instachats_string,
                    GooglePlayPackage.INSTACHATS_PACKAGE1))
            }
            InAppProduct.FLIGHTS.type == it &&
                    (userCounters?.firstOrNull { counter ->
                        counter.name == CounterNameEnum.FLY.value
                    }?.configuration?.isUnlimited ?: false).not() -> {
                consumables.add(ProfileInAppPackagesModel(4,
                    R.drawable.ic_airplane_small,
                    R.string.flights,
                    GooglePlayPackage.FLYING_PACKAGE1))
            }
            InAppProduct.UNDOS.type == it &&
                    (userCounters?.firstOrNull { counter ->
                        counter.name == CounterNameEnum.UNDO.value
                    }?.configuration?.isUnlimited ?: false).not() -> {
                consumables.add(ProfileInAppPackagesModel(5,
                    R.drawable.ic_undo_small,
                    R.string.undo_title,
                    GooglePlayPackage.UNDO_PACKAGE1))
            }
        }
    }

    return consumables
}




