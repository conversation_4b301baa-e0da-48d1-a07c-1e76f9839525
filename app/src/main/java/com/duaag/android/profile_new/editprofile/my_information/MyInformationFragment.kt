package com.duaag.android.profile_new.editprofile.my_information

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.core.widget.addTextChangedListener
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentMyInformationBinding
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoEventProperties
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoSourceValues
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoTypeValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.profile_new.editprofile.EditProfileActivity
import com.duaag.android.profile_new.editprofile.EditProfileConstants
import com.duaag.android.profile_new.editprofile.EditProfileConstants.MyInformationConstants
import com.duaag.android.profile_new.editprofile.EditProfileConstants.NOT_SEARCHABLE_TYPE
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment.MyInformationSearchableType
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.updateLocale
import javax.inject.Inject


class MyInformationFragment : Fragment() {
    private var type: String? = MyInformationConstants.JOB_EDUCATION
    private var _binding: FragmentMyInformationBinding? = null
    private val binding get() = _binding!!

    private lateinit var types: Map<String?, MyInformationSearchableType>

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ShareEditProfileViewModel>({ activity as EditProfileActivity }) { viewModelFactory }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            type = it.getString(NOT_SEARCHABLE_TYPE)
        }
        types = mapOf(MyInformationConstants.JOB_EDUCATION to MyInformationSearchableType.JOB_EDUCATION, MyInformationConstants.DESCRIPTION to MyInformationSearchableType.DESCRIPTION)
    }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as EditProfileActivity).editProfileComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        _binding = FragmentMyInformationBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.type = types.getValue(type)

        when (types.getValue(type)) {
            MyInformationSearchableType.JOB_EDUCATION -> {
                val job = viewModel.jobInfo
                val educations = viewModel.educationInfo

                val jobModel = viewModel.userProfile.value?.profile?.jobs
                val educationModel = viewModel.userProfile.value?.profile?.educations

                viewModel.updateJobAndEducation.observe(viewLifecycleOwner) {
                    if (it) {
                        if (job != binding.editTextInputProfile.text.toString() ||
                            educations != binding.editTextInputProfile2.text.toString() ||
                            jobModel != job ||
                            educationModel != educations
                        ) {

                            viewModel.updateUser(
                                jobCategoryValue = binding.editTextInputProfile.text.toString(),
                                educationCategoryValue = binding.editTextInputProfile2.text.toString()
                            ).observe(viewLifecycleOwner) { result ->
                                when (result) {
                                    is Result.Success -> {
                                        //Show this when update user api returns ‘freeExperienceIsGranted=true’ and ‘FreeStarterExperienceUntil=date’
                                        if(result.data.freeExperienceIsGranted &&
                                            result.data.freeStarterExperienceUntil != null &&
                                            System.currentTimeMillis() < result.data.freeStarterExperienceUntil) {
                                            DuaAccount.shouldShowPremiumLiteApprovedDialog = true

                                            viewModel.syncUserCounters()
                                        }


                                        binding.editProgress.visibility = View.GONE
                                        viewModel.onProfileProgressUpdated()
                                        updateLocalUser()

                                        if (binding.editTextInputProfile.text.isNotEmpty() && (job != binding.editTextInputProfile.text.toString())) {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.JOB_TITLE.value
                                                )
                                            )

                                            sendClevertapEvent(
                                                ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                                AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                                AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.JOB_TITLE.value
                                            ))
                                        }

                                        if (binding.editTextInputProfile2.text.isNotEmpty() && (educations != binding.editTextInputProfile2.text.toString())) {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.JOB_TITLE.value
                                                )
                                            )

                                            sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                                AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                                AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.JOB_TITLE.value
                                            ))
                                        }

                                        if (binding.editTextInputProfile.text.isEmpty() && (job != binding.editTextInputProfile.text.toString())) {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.JOB_TITLE.value
                                                )
                                            )

                                            sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                                AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.JOB_TITLE.value
                                            ))
                                        }

                                        if (binding.editTextInputProfile2.text.isEmpty() && (educations != binding.editTextInputProfile2.text.toString())) {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.JOB_TITLE.value
                                                )
                                            )

                                            sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                                AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.JOB_TITLE.value
                                            ))
                                        }

                                    }
                                    is Result.Error -> {
                                        binding.editProgress.visibility = View.GONE
                                        ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                        logError(ErrorStatus.UPDATE_MY_INFORMATION)
                                    }
                                    Result.Loading -> {
                                        binding.editProgress.visibility = View.VISIBLE
                                    }
                                }
                            }
                        } else {
                            if(viewModel.fromBackPress != true) {
                                findNavController().navigateSafer(
                                    R.id.action_jobEducationFragment_to_activitiesFragment,
                                    bundleOf(EditProfileConstants.MY_INFORMATION_TYPE to EditProfileConstants.MyInformationSearchableConstants.ACTIVITIES)
                                )
                            } else {
                                viewModel.fromBackPress = false
                                (requireActivity() as EditProfileActivity).getNavController().popBackStack(R.id.editProfileFragment, false)

                            }

                        }
                    }
                }

                viewModel.userProfile.observe(viewLifecycleOwner) { userModel ->
                    val jobUser = userModel.profile.jobs
                    val education = userModel.profile.educations
                    binding.editTextInputProfile2.setText(education)
                    binding.editTextInputProfile.setText(jobUser)
                }

                var jobText = ""
                var educationText = ""

                binding.editTextInputProfile.addTextChangedListener {
                    jobText = it.toString()
                    viewModel.jobInfo = jobText
                    if (jobText.isNotEmpty()) {
                        checkFabState(MyInformationSearchableType.JOB_EDUCATION)
                        viewModel.setFabNextVisibility(FloatingActionButtonVisibility.SHOWN)
                    }
                    if (jobText.isEmpty() || educationText.isEmpty()) {
                        viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    }
                    viewModel.checkPercentageForMyInformationChoices()
                }

                binding.editTextInputProfile2.addTextChangedListener {
                    educationText = it.toString()
                    viewModel.educationInfo = educationText
                    if (educationText.isNotEmpty()) {
                        checkFabState(MyInformationSearchableType.JOB_EDUCATION)
                        viewModel.setFabNextVisibility(FloatingActionButtonVisibility.SHOWN)
                    }
                    if (educationText.isEmpty() || jobText.isEmpty()) {
                        viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    }
                    viewModel.checkPercentageForMyInformationChoices()
                }
            }

            MyInformationSearchableType.DESCRIPTION -> {
                val description = viewModel.descriptionInfo
                val descriptionModel = viewModel.userProfile.value?.profile?.description

                viewModel.updateDescription.observe(viewLifecycleOwner) {
                    if (it) {
                        if (description != binding.editTextInputProfile.text.toString() || descriptionModel != description) {

                            viewModel.updateUser(
                                jobCategory = "description",
                                jobCategoryValue = binding.editTextInputProfile.text.toString()
                            ).observe(viewLifecycleOwner) { result ->
                                when (result) {
                                    is Result.Success -> {
                                        //Show this when update user api returns ‘freeExperienceIsGranted=true’ and ‘FreeStarterExperienceUntil=date’
                                        if(result.data.freeExperienceIsGranted &&
                                            result.data.freeStarterExperienceUntil != null &&
                                            System.currentTimeMillis() < result.data.freeStarterExperienceUntil) {
                                            DuaAccount.shouldShowPremiumLiteApprovedDialog = true

                                            viewModel.syncUserCounters()
                                        }


                                        updateLocalUser("description")
                                        viewModel.onProfileProgressUpdated()
                                        binding.editProgress.visibility = View.GONE

                                        if (binding.editTextInputProfile.text.isNotEmpty() && (description != binding.editTextInputProfile.text.toString())) {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.DESCRIPTION.value
                                                )
                                            )

                                            sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                                AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                                AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.DESCRIPTION.value
                                            ))
                                        }

                                        if (binding.editTextInputProfile.text.isEmpty() && (description != binding.editTextInputProfile.text.toString())) {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.DESCRIPTION.value
                                                )
                                            )

                                            sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                                AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.DESCRIPTION.value
                                            ))
                                        }
                                    }
                                    is Result.Error -> {
                                        binding.editProgress.visibility = View.GONE
                                        ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                        logError(ErrorStatus.UPDATE_MY_INFORMATION)
                                    }
                                    Result.Loading -> {
                                        binding.editProgress.visibility = View.VISIBLE
                                    }
                                }
                            }

                        } else findNavController().navigateSafer(R.id.action_descriptionFragment_to_editProfileFragment)
                    }
                }

                viewModel.userProfile.observe(viewLifecycleOwner) { model ->
                    if(model == null)
                        return@observe

                    binding.editTextInputProfile.setText(model.profile.description)
                }

                binding.editTextInputProfile.doOnTextChanged { text, _, _, _ ->
                    viewModel.descriptionInfo = text.toString()
                    binding.charCount.text = text?.count().toString()
                    if (!text.isNullOrEmpty()) {
                        viewModel.setFabNextVisibility(FloatingActionButtonVisibility.SHOWN)
                    } else {
                        viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    }
                    viewModel.checkPercentageForMyInformationChoices()
                }
            }

            else -> { }
        }

        return binding.root
    }

    private fun updateLocalUser(s: String? = null) {
        viewModel.userProfile.observe(viewLifecycleOwner) {
            val user = it
            if (s.isNullOrEmpty()) {
                user.profile.jobs = binding.editTextInputProfile.text.toString().trim()
                user.profile.educations = binding.editTextInputProfile2.text.toString().trim()
                viewModel.updateUser(user)
                if(viewModel.fromBackPress != true){
                    findNavController().navigateSafer(
                        R.id.action_jobEducationFragment_to_activitiesFragment,
                        bundleOf(EditProfileConstants.MY_INFORMATION_TYPE to EditProfileConstants.MyInformationSearchableConstants.ACTIVITIES)
                    )
                } else {
                    viewModel.fromBackPress = false
                    (requireActivity() as EditProfileActivity).getNavController().popBackStack(R.id.editProfileFragment, false)
                }

                viewModel.checkIfJobDescriptionCompleted(MyInformationSearchableType.JOB_EDUCATION)
            } else {
                user.profile.description = binding.editTextInputProfile.text.toString().trim()
                viewModel.updateUser(user)
                if(viewModel.fromBackPress != true){
                    findNavController().navigateSafer(R.id.action_descriptionFragment_to_editProfileFragment)

                } else {
                    viewModel.fromBackPress = false
                    (requireActivity() as EditProfileActivity).getNavController().popBackStack(R.id.editProfileFragment, false)
                }
                viewModel.checkIfJobDescriptionCompleted(MyInformationSearchableType.DESCRIPTION)
            }

        }
    }


    override fun onResume() {
        super.onResume()
        if (types.getValue(type) == MyInformationSearchableType.JOB_EDUCATION) {
            (requireActivity() as EditProfileActivity).setToolbarTitle(requireContext().getString(R.string.job_title_and_education))
            checkFabState(types.getValue(type))
        } else {
            (requireActivity() as EditProfileActivity).setToolbarTitle(requireContext().getString(R.string.description))
            checkFabState(types.getValue(type))
        }
    }

    private fun checkFabState(value: MyInformationSearchableType) {
        if (value == MyInformationSearchableType.JOB_EDUCATION) {
            val job = binding.editTextInputProfile.text.toString()
            val education = binding.editTextInputProfile2.text.toString()
            if (job.isNotEmpty() && education.isNotEmpty()) viewModel.setFabNextVisibility(FloatingActionButtonVisibility.SHOWN) else viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
        } else if (value == MyInformationSearchableType.DESCRIPTION) {
            val description = binding.editTextInputProfile.text.toString()
            if (description.isNotEmpty()) viewModel.setFabNextVisibility(FloatingActionButtonVisibility.SHOWN) else viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}