package com.duaag.android.profile_new

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.widget.NestedScrollView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.application.DuaApplication.Companion.BILLING_AVAILABLE
import com.duaag.android.base.models.UserModel
import com.duaag.android.chat.viewmodel.LikedYouViewModel
import com.duaag.android.clevertap.ClevertapAddPhotoSourceValues
import com.duaag.android.clevertap.ClevertapBoostActivationSource
import com.duaag.android.clevertap.ClevertapEditProfileSourceValues
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapFiltersEventSourceValues
import com.duaag.android.clevertap.ClevertapGhostSourceValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.UploadImageSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.CountersInfoCardBinding
import com.duaag.android.databinding.ProfileFragmentBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.adapter.GetInteractionsAdapter
import com.duaag.android.home.adapter.GetInteractionsAdapter.Companion.PROFILE
import com.duaag.android.home.adapter.GetInteractionsAdapter.Companion.PROFILE_SMALL
import com.duaag.android.home.fragments.ActivateBoostDialog
import com.duaag.android.home.fragments.UnhideProfileDialog
import com.duaag.android.home.fragments.UserProfileFragment
import com.duaag.android.home.fragments.VerificationInProgressDialog
import com.duaag.android.home.fragments.VerificationOptions
import com.duaag.android.home.fragments.VerificationType
import com.duaag.android.home.fragments.VerifyYourProfileDialog
import com.duaag.android.home.models.GetInteractionsModel
import com.duaag.android.home.models.PremiumOfferTypes
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsAddPhotoSourceValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.NEW_IMAGES_REQUEST
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.OPENED_FROM
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion.UPLOAD_IMAGE_SOURCE
import com.duaag.android.manage_pictures.viewmodels.ProfileImagesViewModel
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.fragments.PremiumBenefitsDialogFragment
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.models.premiumBenefitsBigItemsFemale
import com.duaag.android.premium_subscription.models.premiumBenefitsBigItemsMale
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.profile_builder.ProfileBuilderActivity
import com.duaag.android.profile_new.adapters.ProfileInAppPackagesAdapter
import com.duaag.android.profile_new.editprofile.EditProfileActivity
import com.duaag.android.profile_new.models.InAppProduct
import com.duaag.android.profile_new.models.ProfileInAppPackagesModel
import com.duaag.android.profile_new.models.getPackageList
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.fragments.Badge2Status.APPROVED
import com.duaag.android.settings.fragments.Badge2Status.NOT_APPROVED
import com.duaag.android.settings.fragments.Badge2Status.NULL
import com.duaag.android.settings.fragments.Badge2Status.PROCESSING
import com.duaag.android.settings.fragments.account_settings.ghost_mode.IncognitoModeDialogFragment
import com.duaag.android.settings.fragments.account_settings.ghost_mode.IncognitoModeType
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.imageCircle
import com.duaag.android.utils.livedata.observeOnce
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.setMargin
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.utils.updateLocale
import com.duaag.android.uxcam.sendUxCamEvent
import com.duaag.android.views.MarginItemDecoration
import com.duaag.android.views.OutOfImpressionsDialog
import com.duaag.android.broadcast.DuaEvent
import com.duaag.android.broadcast.EventBus
import com.google.android.material.tabs.TabLayoutMediator
import com.skydoves.balloon.ArrowOrientation
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.BalloonAnimation
import io.purchasely.models.PLYPlan
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import sendVerifyYourProfileInitiatedAnalyticsEvent
import sendVerifyYourProfilePopupAnalyticsEvent
import timber.log.Timber
import javax.inject.Inject


class ProfileFragment : Fragment(), VerifyYourProfileDialog.VerifyYourProfileDialogListener,IncognitoModeDialogFragment.IncognitoModeDialogListener {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val likedYouViewModel by viewModels<LikedYouViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val profileViewModel by viewModels<ProfileViewModel>({ activity as HomeActivity }) { viewModelFactory }
    private val profileImagesViewModel by viewModels<ProfileImagesViewModel> { viewModelFactory }

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    companion object {
        const val TAG = "NewProfileFragment"
        private const val REQUEST_CODE_EDIT_PROFILE_ACTIVITY = 14
        private const val REQUEST_CODE_SETTINGS_ACTIVITY = 15
    }

    private var _binding: ProfileFragmentBinding? = null
    private val binding get() = _binding
    private var fadeAnimator: ObjectAnimator? = null


    private var tabLayoutMediator: TabLayoutMediator? = null

    private val premiumBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            val isPremium = intent?.getBooleanExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, false)
                ?: false
            if (isPremium) {
                homeViewModel.fetchUserProfile()
                upgradedToPremium()
                homeViewModel.afterPremiumVerified()
                likedYouViewModel.initData()
            }
        }
    }

    private val billingAvailableBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            val isAvailable = intent?.getBooleanExtra(BILLING_AVAILABLE, false) ?: false
            if (isAvailable) {
                initializeUIBasedOnUserPremium()
                initializeInAppPackagesRecyclerView()
                showPremiumBenefits()
            }
        }
    }

    private val premiumOfferAvailableBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            val hasOffer = intent?.getBooleanExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, false) ?: false
            if (hasOffer) {
                initializeUIBasedOnUserPremium()
            }
        }
    }

    private val items by lazy {
        if (homeViewModel.userProfile.value?.gender == GenderType.WOMAN.value) {
            premiumBenefitsBigItemsFemale
        } else {
            premiumBenefitsBigItemsMale
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as HomeActivity).homeComponent.inject(this)
        Timber.tag("VIEWMODEL").d(homeViewModel.toString())
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): NestedScrollView? {
        _binding = ProfileFragmentBinding.inflate(inflater)

        (activity as HomeActivity).recheckIfBillingIsAvailable()

        binding?.root?.setOnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                val rect = Rect()
                binding?.completedProfileBtn?.getGlobalVisibleRect(rect)
                if (!rect.contains(event.rawX.toInt(), event.rawY.toInt())) {
                    hideProfileCompletedProgressBar()
                }
                false
            } else {
                false
            }
        }

        binding?.addImageBtn?.setOnSingleClickListener {
            openManagePicturesActivity()
            if(homeViewModel.userProfile.value?.profile?.isShadowBanned == true) {
                sendAddPhotoEvents()
            }

            hideProfileCompletedProgressBar()
        }

        binding?.profileImage?.setOnClickListener {
            openMyProfile()

            hideProfileCompletedProgressBar()
        }

        binding?.shareAppIv?.setOnSingleClickListener(1000L) {
            val outOfImpressionsDialog = OutOfImpressionsDialog.newInstance(inviteType = OutOfImpressionsDialog.InviteType.DEFAULT)
            outOfImpressionsDialog.show(childFragmentManager, "outOfImpressionsDialog")

            hideProfileCompletedProgressBar()
        }

        binding?.editProfileButton?.setPadding(34, 4, 34, 4)


        binding?.settingsButton?.setOnSingleClickListener {
            sendClevertapEvent(ClevertapEventEnum.SETTINGS_SCREENVIEW,
                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(homeViewModel.userProfile.value),
                    ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapEditProfileSourceValues.PROFILE.value)
            )
            sendUxCamEvent(ClevertapEventEnum.SETTINGS_SCREENVIEW,
                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(homeViewModel.userProfile.value),
                    ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapEditProfileSourceValues.PROFILE.value)
            )
            startActivityForResult(
                Intent(requireContext(), SettingsActivity::class.java),
                REQUEST_CODE_SETTINGS_ACTIVITY
            )

            hideProfileCompletedProgressBar()
        }

        binding?.editProfileButton?.setOnSingleClickListener {
            openEditProfileActivity(ClevertapEditProfileSourceValues.PROFILE)
            hideProfileCompletedProgressBar()
        }

        binding?.badgeImg?.setOnSingleClickListener {
            if ((homeViewModel.badge2 == APPROVED || homeViewModel.badge2 == PROCESSING) && homeViewModel.isUserVerified()) {
                if (homeViewModel.badge2 != PROCESSING) {
                    showFullVerifyBubbleLayout(true)
                } else {
                    showFullVerifyBubbleLayout(false)
                }

                return@setOnSingleClickListener
            }
            val type: VerificationType = when (homeViewModel.badge2) {
                PROCESSING -> {
                    if (!homeViewModel.isUserVerified()) VerificationType.ALL_IMAGE_PROCESSING else VerificationType.IMAGE_VERIFICATION_PROCESSING
                }
                APPROVED -> VerificationType.PHONE_EMAIL_VERIFICATION
                NOT_APPROVED -> if (homeViewModel.isUserVerified()) VerificationType.IMAGE_VERIFICATION else VerificationType.ALL
                NULL -> {
                    if (!homeViewModel.isUserVerified()) VerificationType.ALL else VerificationType.IMAGE_VERIFICATION
                }
            }

            homeViewModel.accountModel.observeOnce(viewLifecycleOwner) {
                val fragment = VerificationOptions.newInstance(object :
                    VerificationOptions.VerificationOptionsClickListener {
                    override fun onImageVerificationClicked(
                        button: View,
                        fragment: VerificationOptions
                    ) {
                       findNavController().navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,
                           bundleOf(EVENT_SOURCE to ClevertapVerificationSourceValues.BADGE.value)
                       )

                        fragment.dismiss()

                        val eventPremiumType =
                            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                        sendVerifyYourProfileInitiatedAnalyticsEvent(
                            ClevertapVerificationSourceValues.BADGE.value,
                            eventPremiumType
                        )

                        firebaseLogEvent(
                            FirebaseAnalyticsEventsName.INITIATE_IMAGE_VERIFICATION, mapOf(
                                FirebaseAnalyticsParameterName.IMAGE_VERIFICATION_INITIATE_PROFILE.value to 1L
                            )
                        )
                    }

                    override fun onPhoneEmailVerificationClicked(
                        authMethod: AuthMethod?,
                        fragment: VerificationOptions
                    ) {
                        goToVerificationBadge1(authMethod)
                        fragment.dismiss()
                    }

                }, type, it)

                fragment.show(childFragmentManager, "VerificationOptions")
            }

            hideProfileCompletedProgressBar()
        }

        binding?.userLocation?.setOnClickListener {
            homeViewModel.showFilter(ClevertapFiltersEventSourceValues.PROFILE.value)
            firebaseLogEvent(FirebaseAnalyticsEventsName.FILTER_BUTTONCLICK)

            hideProfileCompletedProgressBar()
        }

        homeViewModel.continueUserGuide.observe(viewLifecycleOwner) {
//            showUserGuide(2)
        }

        homeViewModel.openFilterGuide.observe(viewLifecycleOwner) {
            openFilterBottomSheet()
        }

        homeViewModel.boostStatusChanged.observe(viewLifecycleOwner) {
            initializeInAppPackagesRecyclerView()
        }

        homeViewModel.specialOfferReceived.observe(viewLifecycleOwner) {
            initializeUIBasedOnUserPremium()
        }

        profileViewModel.planPrices.observe(viewLifecycleOwner) { areSet ->
            setPrices(profileViewModel.getSmallestPlans())
        }

        initializeUIBasedOnUserPremium()
        initializeInAppPackagesRecyclerView()
        initializeCompletedProfileProgressbar()
        showPremiumBenefits()
        setObservers()
        getCoordinates()
        registerReceivers()
        sendProfileScreenViewEvent()

        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        registerEventBus()
    }

    private fun setPrices(productDetailsList: List<PLYPlan?>) {
        val adapter = binding?.inAppConsumables?.adapter as ProfileInAppPackagesAdapter
        val newList = mutableListOf<ProfileInAppPackagesModel>()

        productDetailsList.forEach { product ->
            if (product != null) {
                val item =
                    adapter.items.find { it.packageName.productId == product.getProductId() }
                item?.let {
                    it.price = getString(R.string.from) + " " + product.localizedPrice()
                    newList.add(it)
                }
                Timber.tag(TAG).d("onBillingSetupFinished: $item")
            }
        }
        newList.sortBy { it.index }
        adapter.setNewItems(newList)
        adapter.notifyDataSetChanged()

    }

    private fun sendProfileScreenViewEvent() {
        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

        val user = homeViewModel.userProfile.value
        val profileProgressBar = if(user?.profilePercentage == 100) null else user?.profilePercentage

        sendClevertapEvent(
                ClevertapEventEnum.PROFILE_SCREENVIEW, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.PROFILE_PROGRESS_BAR.propertyName to profileProgressBar)
        )

        firebaseLogEvent(
                FirebaseAnalyticsEventsName.PROFILE_SCREENVIEW, mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                FirebaseAnalyticsParameterName.PROFILE_PROGRESS_BAR.value to profileProgressBar)
        )
    }

    private fun initializeUIBasedOnUserPremium() {
        val constraintGlobalLayout: ConstraintLayout = binding?.inAppConsumables?.parent as ConstraintLayout
        val constraintGlobalSet = ConstraintSet()
        constraintGlobalSet.clone(constraintGlobalLayout)


        //When user is premium
        if (homeViewModel.userProfile.value?.premiumType != null) {
            binding?.inAppConsumables?.visibility = View.GONE
            binding?.benefitsContainer?.root?.visibility = View.GONE
            binding?.premiumOverlay?.visibility = View.VISIBLE
            binding?.premiumIndicator?.visibility = View.VISIBLE
            binding?.inviteFriendContainer?.root?.visibility = View.GONE

            val showDontLetGoOffer = homeViewModel.dontLetGoOfferAvailable()

            if (showDontLetGoOffer && DuaApplication.instance.getBillingAvailable()) {
                showSpecialOfferView(PremiumOfferTypes.DONT_LET_GO.offerType)
            } else {
                binding?.specialOfferContainer?.root?.visibility = View.GONE

                binding?.premiumContainer?.youArePremiumDescription?.text = getString(
                    R.string.enjoy_duapremium_to_the_fullest,
                    getString(R.string.app_name)
                )
                binding?.premiumContainer?.root?.visibility = View.VISIBLE
                binding?.premiumContainer?.premiumOverviewBtn?.setOnClickListener {
                    firebaseLogEvent(FirebaseAnalyticsEventsName.PREMIUM_OVERVIEW)

                    val dialogFragment = PremiumBenefitsDialogFragment()
                    val ft = fragmentManager?.beginTransaction()
                    if (ft != null) {
                        dialogFragment.show(ft, "dialog")
                    }

                    hideProfileCompletedProgressBar()
                }
            }

            if (DuaApplication.instance.getBillingAvailable()) {
                constraintGlobalSet.connect(R.id.in_app_consumables, ConstraintSet.BOTTOM, R.id.premium_container, ConstraintSet.TOP, 0)

                binding?.inAppConsumables?.visibility = View.VISIBLE
            }

        } else {
            //When user is not premium
            binding?.premiumOverlay?.visibility = View.GONE
            binding?.premiumIndicator?.visibility = View.GONE
            binding?.premiumContainer?.root?.visibility = View.GONE

            //When billing is available
            if(DuaApplication.instance.getBillingAvailable()){
                constraintGlobalSet.connect(R.id.in_app_consumables, ConstraintSet.BOTTOM, R.id.benefits_container, ConstraintSet.TOP, 0)

                binding?.inAppConsumables?.visibility = View.VISIBLE
                binding?.inviteFriendContainer?.root?.visibility = View.GONE

                val showSpecialDaysOffer = homeViewModel.specialOfferAvailable()
                if(showSpecialDaysOffer) {
                    showSpecialOfferView(homeViewModel.getOfferType())
                } else {
                    binding?.benefitsContainer?.root?.visibility = View.VISIBLE
                    binding?.benefitsContainer?.premiumBtn?.setOnClickListener {
                        requireActivity().openPremiumPaywall(
                            BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM,
                            ClevertapEventSourceValues.PROFILE,
                            placementId = PurchaselyPlacement.PROFILE_GO_PREMIUM_BUTTON.id,
                            userModel = homeViewModel.userProfile.value
                        )

                        hideProfileCompletedProgressBar()
                    }
                }
            } else{
                //When billing is not available
                constraintGlobalSet.connect(R.id.in_app_consumables, ConstraintSet.BOTTOM, R.id.invite_friend_container, ConstraintSet.TOP, 0)

                val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                val eventSourceValue = ClevertapEventSourceValues.HOME.value

                sendClevertapEvent(
                    ClevertapEventEnum.INVITE_A_FRIEND_SCREENVIEW, mapOf(
                        ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                        ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSourceValue))


                binding?.inviteFriendContainer?.root?.visibility = View.VISIBLE
                binding?.inAppConsumables?.visibility = View.GONE

                binding?.inviteFriendContainer?.inviteFriendBtn?.setOnClickListener {
                    (requireActivity() as HomeActivity).checkShareCompat()
                    firebaseLogEvent(
                        FirebaseAnalyticsEventsName.REFER_FRIENDS_BUTTONCLICK_HOME, mapOf(
                            FirebaseAnalyticsParameterName.REFER_FRIENDS_HOME_COUNT.value to 1L))
                }
            }
        }

        val padding = convertDpToPixel(32f, requireContext()).toInt()
        binding?.inAppConsumables?.setPadding(0, padding, 0, padding)

    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initializeCompletedProfileProgressbar() {
        homeViewModel.userProfile.value?.let {
            val hasProgressBarBeenShown = (requireActivity() as HomeActivity).hasProfileCompletedProgressShown
            if(it.profilePercentage < 100 && !hasProgressBarBeenShown) {
                (requireActivity() as HomeActivity).hasProfileCompletedProgressShown = true

                binding?.completedProfileBtn?.visibility = View.VISIBLE

                val progressView = binding?.profileCircle
                progressView?.setProgress(it.profilePercentage.toFloat())
                progressView?.setRounded(true)
                progressView?.setProgressColor(ContextCompat.getColor(requireContext(), R.color.pink_500))
                progressView?.setProgressBackgroundColor(ContextCompat.getColor(requireContext(), R.color.border))
                progressView?.setProgressWidth(convertDpToPixel(4f, requireContext()))

                val percentageText = "${it.profilePercentage}%"
                binding?.percentageText?.text = percentageText

                binding?.completedProfileBtn?.setOnClickListener {
                    hideProfileCompletedProgressBar()
                    openEditProfileActivity(ClevertapEditProfileSourceValues.PROFILE_PROGRESS_BAR)
                }
            }
        }
    }

    private fun hideProfileCompletedProgressBar() {
        binding?.completedProfileBtn?.visibility = View.GONE
    }

    private fun showSpecialOfferView(offerType: String?) {
        val title = when(offerType){
            PremiumOfferTypes.SPECIAL_DAYS.offerType -> getString(R.string.a_gift_for_you)
            PremiumOfferTypes.DONT_LET_GO.offerType -> getString(R.string.a_gift_for_you)
            PremiumOfferTypes.PAY_1_GET_1.offerType -> getString(R.string.pay_1_month_get_2)
            else -> ""
        }

        val description = when(offerType){
            PremiumOfferTypes.SPECIAL_DAYS.offerType -> getString(R.string.special_offer_just_for_you)
            PremiumOfferTypes.DONT_LET_GO.offerType -> getString(R.string.special_offer_just_for_you)
            PremiumOfferTypes.PAY_1_GET_1.offerType -> getString(R.string.special_offer_just_for_you)
            else -> ""
        }

        if(offerType == PremiumOfferTypes.PAY_1_GET_1.offerType){
            binding?.specialOfferContainer?.specialOfferBackground?.visibility = View.INVISIBLE
            binding?.specialOfferContainer?.specialOfferAnimation?.setAnimation(R.raw.pay_1_get_1)
        }else {
            binding?.specialOfferContainer?.specialOfferBackground?.visibility = View.VISIBLE
            binding?.specialOfferContainer?.specialOfferAnimation?.setAnimation(R.raw.gift_animation)
        }

        binding?.inviteFriendContainer?.root?.visibility = View.GONE
        binding?.benefitsContainer?.root?.visibility = View.GONE
        binding?.premiumContainer?.root?.visibility = View.GONE
        binding?.specialOfferContainer?.root?.visibility = View.VISIBLE
        binding?.specialOfferContainer?.specialOfferMainTitle?.visibility = View.GONE

        binding?.specialOfferContainer?.specialOfferTitle?.text = title
        binding?.specialOfferContainer?.specialOfferDesc?.text = description

        binding?.specialOfferContainer?.getItNowBtn?.setOnClickListener {


            when (homeViewModel.getOfferType()) {
                PremiumOfferTypes.DONT_LET_GO.offerType -> {
                    firebaseLogEvent(FirebaseAnalyticsEventsName.REDEEM_OFFER_INITIATED_PROFILE)
                }
//                PremiumOfferTypes.PAY_1_GET_1.offerType -> {
//                    firebaseLogEvent(FirebaseAnalyticsEventsName.GET_2FOR1_BUTTONCLICK_PROFILE)
//                }
            }


            if(NetworkChecker.isNetworkConnected(requireContext())) {
                requireActivity().openPremiumPaywall(
                    BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM,
                    ClevertapEventSourceValues.PROFILE,
                    placementId = PurchaselyPlacement.DONT_LET_GO.id,
                    userModel = homeViewModel.userProfile.value
                )
            } else {
                ToastUtil.toast(getString(R.string.no_internet_connection))
            }

            hideProfileCompletedProgressBar()
        }

       setSpecialOfferConstraints()

        //when the in-app consumables is empty
        if (getPackageList(RemoteConfigUtils.getConsumables(), homeViewModel.userCounters).isEmpty()) {
            val constraintOfferLayout: ConstraintLayout? = binding?.mainCl
            val constraintOfferSet = ConstraintSet()
            constraintOfferSet.clone(constraintOfferLayout)

            binding?.specialOfferContainer?.specialOfferMainTitle?.visibility = View.VISIBLE

            val animParams = binding?.specialOfferContainer?.specialOfferAnimation?.layoutParams
            animParams?.width = convertDpToPixel(70f, requireContext()).toInt()
            animParams?.height = convertDpToPixel(50f, requireContext()).toInt()
            binding?.specialOfferContainer?.specialOfferAnimation?.layoutParams = animParams

            val heartsParams = binding?.specialOfferContainer?.specialOfferBackground?.layoutParams
            heartsParams?.width = convertDpToPixel(280f, requireContext()).toInt()
            heartsParams?.height = convertDpToPixel(160f, requireContext()).toInt()
            binding?.specialOfferContainer?.specialOfferDesc?.setPadding(0, convertDpToPixel(8f, requireContext()).toInt(), 0, convertDpToPixel(40f, requireContext()).toInt())
            binding?.specialOfferContainer?.specialOfferBackground?.layoutParams = heartsParams
            binding?.specialOfferContainer?.specialOfferMainTitle?.setPadding(0, convertDpToPixel(90f, requireContext()).toInt(), 0, convertDpToPixel(40f, requireContext()).toInt())
            binding?.specialOfferContainer?.specialOfferDesc?.setPadding(0, convertDpToPixel(8f, requireContext()).toInt(), 0, convertDpToPixel(40f, requireContext()).toInt())
            binding?.specialOfferContainer?.specialOfferTitle?.setPadding(0, convertDpToPixel(24f, requireContext()).toInt(), 0, 0)
            binding?.specialOfferContainer?.getItNowBtn?.setMargin(0f, 0f, 0f, 40f)

            constraintOfferSet.clear(R.id.special_offer_container, ConstraintSet.VERTICAL)
            constraintOfferSet.connect(R.id.special_offer_container, ConstraintSet.TOP, R.id.shadow_banned_container, ConstraintSet.BOTTOM, 0)
            constraintOfferSet.connect(R.id.special_offer_container, ConstraintSet.BOTTOM, R.id.parent, ConstraintSet.BOTTOM, 0)
            constraintOfferSet.applyTo(constraintOfferLayout)
        }
    }

    private fun setSpecialOfferConstraints() {
        val constraintOfferLayout: ConstraintLayout? = binding?.mainCl
        val constraintOfferSet = ConstraintSet()
        constraintOfferSet.clone(constraintOfferLayout)

        val animParam = binding?.specialOfferContainer?.specialOfferAnimation?.layoutParams
        animParam?.width = convertDpToPixel(60f, requireContext()).toInt()
        animParam?.height = convertDpToPixel(40f, requireContext()).toInt()
        binding?.specialOfferContainer?.specialOfferAnimation?.layoutParams = animParam

        val heartsParam = binding?.specialOfferContainer?.specialOfferBackground?.layoutParams
        heartsParam?.width = convertDpToPixel(235f, requireContext()).toInt()
        heartsParam?.height = convertDpToPixel(135f, requireContext()).toInt()
        binding?.specialOfferContainer?.specialOfferBackground?.layoutParams = heartsParam

        constraintOfferSet.clear(R.id.special_offer_container, ConstraintSet.TOP)
        constraintOfferSet.clear(R.id.special_offer_animation, ConstraintSet.TOP)

        if(homeViewModel.userProfile.value?.profile?.isShadowBanned == true)
            constraintOfferSet.connect(R.id.special_offer_container, ConstraintSet.TOP, R.id.shadow_banned_container, ConstraintSet.BOTTOM, convertDpToPixel(24f, requireContext()).toInt())
        else
            constraintOfferSet.connect(R.id.special_offer_container, ConstraintSet.TOP, R.id.in_app_consumables, ConstraintSet.BOTTOM, 0)

        constraintOfferSet.clear(R.id.get_it_now_btn, ConstraintSet.TOP)
        constraintOfferSet.connect(R.id.get_it_now_btn, ConstraintSet.TOP, R.id.special_offer_desc, ConstraintSet.BOTTOM, convertDpToPixel(24f, requireContext()).toInt())
        constraintOfferSet.applyTo(constraintOfferLayout)
    }

    private fun showPremiumBenefits() {
        val list = mutableListOf<GetInteractionsModel>()
        items.forEach { list.add(GetInteractionsModel(it.image,it.title, it.description, null).apply { descriptionText = it.descriptionTxt }) }
        val viewHolderSize = if((binding?.inAppConsumables?.adapter?.itemCount ?: 0) == 0) PROFILE else PROFILE_SMALL

        binding?.benefitsContainer?.premiumBenefits?.adapter = GetInteractionsAdapter(list, viewHolderSize, homeViewModel)
        binding?.benefitsContainer?.tabLayout?.let { tabLayout ->
            binding?.benefitsContainer?.premiumBenefits?.let { viewPager ->
                tabLayoutMediator = TabLayoutMediator(tabLayout, viewPager) { _, _ ->}
                tabLayoutMediator?.attach()
            }
        }
    }

    private fun registerEventBus() {
        viewLifecycleOwner.lifecycleScope.launch {
            EventBus.events.collect { event ->
                Timber.tag("EVENT_BUS").d("received event: $event")

                when (event) {
                    is DuaEvent.PremiumUpgraded -> {
                        val isPremium = event.isPremium
                        if (isPremium) {
                            homeViewModel.fetchUserProfile()
                            upgradedToPremium()
                            homeViewModel.afterPremiumVerified()
                            likedYouViewModel.initData()
                        }
                    }

                    is DuaEvent.BillingAvailable -> {
                        val isAvailable = event.isAvailable
                        if (isAvailable) {
                            initializeUIBasedOnUserPremium()
                            initializeInAppPackagesRecyclerView()
                            showPremiumBenefits()
                        }
                    }

                    is DuaEvent.PremiumOfferAvailable -> {
                        val hasOffer = event.hasOffer
                        if (hasOffer) {
                            initializeUIBasedOnUserPremium()
                        }
                    }

                    is DuaEvent.CountersFetched -> {
                        binding?.inAppConsumables?.adapter?.notifyDataSetChanged()
                    }
                }
            }
        }
    }

    private fun registerReceivers() {
        //register recieveer for premium subscription events
        val premiumSubscriptionFilter = IntentFilter(PremiumActivity.PREMIUM_INTENT)
        val billingAvailableFilter = IntentFilter(PremiumActivity.BILLING_AVAILABLE_INTENT)
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(premiumBroadcastReceiver, premiumSubscriptionFilter)
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(billingAvailableBroadcastReceiver, billingAvailableFilter)

        val premiumOfferFilter = IntentFilter(PremiumActivity.PREMIUM_OFFER_INTENT)
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(premiumOfferAvailableBroadcastReceiver, premiumOfferFilter)

        Timber.tag(TAG).d("registerReceiver: Receiver ON")
    }

    private fun unregisterReceivers() {
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(premiumBroadcastReceiver)
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(premiumOfferAvailableBroadcastReceiver)
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(billingAvailableBroadcastReceiver)

        Timber.tag(TAG).d("registerReceiver: Receiver OFF")
    }

    private fun goToVerificationBadge1(authMethod: AuthMethod?) {
        authMethod?.let {
            val intent = Intent(requireActivity(), SettingsActivity::class.java)

            when (it) {
                AuthMethod.EMAIL, AuthMethod.GOOGLE -> {
                    intent.putExtra(
                        SettingsActivity.SETTINGS_ACTIVITY_START_DESTINATION_INTENT,
                        "Email_Verification"
                    )
                    startActivity(intent)
                }
                AuthMethod.PHONE, AuthMethod.FACEBOOK -> {
                    intent.putExtra(
                        SettingsActivity.SETTINGS_ACTIVITY_START_DESTINATION_INTENT,
                        "Phone_Verification"
                    )
                    startActivity(intent)
                }
            }
        }
    }

    private fun showFullVerifyBubbleLayout(isFullVerified: Boolean) {
        val balloon = Balloon.Builder(requireContext())
            .setLayout(if (isFullVerified) R.layout.full_verified_layout else R.layout.processing_verified_bubble_layout)
            .setArrowSize(16)
            .setArrowAlignAnchorPaddingRatio(convertDpToPixel(5f, requireContext()))
            .setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR)
            .setArrowOrientation(ArrowOrientation.TOP)
            .setArrowPosition(0.5f)
            .setCornerRadius(10f)
            .setElevation(1)
            .setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.modal_popup))
            .setBalloonAnimation(BalloonAnimation.OVERSHOOT)
            .setLifecycleOwner(viewLifecycleOwner)
            .build()
        binding?.badgeApproved?.let { balloon.showAlignBottom(it) }
    }

    private fun setObservers() {
        homeViewModel.userProfile.observe(viewLifecycleOwner) {
            it?.let {
                Timber.tag(TAG).d("user:")
                updateUserUI(it)
            }
        }

        homeViewModel.isFromGuidelines.observe(viewLifecycleOwner) {
            if (it) openManagePicturesActivity()
        }
    }

    private fun updateUserUI(user: UserModel) {
        binding?.ghostModeProfile?.root?.let { setVisibility(it, user.profile.isGhost) }
        binding?.ghostModeProfile?.button?.setOnSingleClickListener {
            if (!NetworkChecker.isNetworkConnected(requireContext())) {
                ToastUtil.toast(getString(R.string.no_internet_connection))
                return@setOnSingleClickListener
            }
            profileViewModel.toggleGhostMode(
                isChecked = false,
                eventSource = ClevertapGhostSourceValues.PROFILE)

            hideProfileCompletedProgressBar()
        }
        binding?.profileImage?.let { imageCircle(it, user.profile.pictureUrl, blurred = user.profile.isShadowBanned ) }
        binding?.nameAndAge?.text = "${user.firstName}, ${user.age}"

        bindShadowTooltip(user.profile.isShadowBanned)

        val isInFlyMode = user.isInFlyMode()
        val locationImage =  if (isInFlyMode) R.drawable.ic_fly_profile else R.drawable.ic_location
        binding?.userLocation?.text = if (user.profile.address.isNullOrEmpty()) "-" else user.profile.address
        binding?.userLocation?.setCompoundDrawablesWithIntrinsicBounds(
            ContextCompat.getDrawable(requireContext(), locationImage), null, null, null
        )

        bindProfileBadge(user)
        checkShadowBannedUser(user.profile.isShadowBanned)
    }

    private fun bindShadowTooltip(isShadowBanned: Boolean) {
        val visibility = if(isShadowBanned) View.VISIBLE else View.GONE
        lifecycleScope.launch {
            delay(1000)
            withContext(Dispatchers.Main) {
                binding?.tooltipContainer?.visibility = visibility
                binding?.angle?.visibility = visibility
            }
        }
    }

    private fun openEditProfileActivity(source: ClevertapEditProfileSourceValues) {
        val intent = Intent(requireContext(), EditProfileActivity::class.java)
        intent.putExtra(EditProfileActivity.EDIT_PROFILE_SOURCE, source.value)
        startActivityForResult(intent, REQUEST_CODE_EDIT_PROFILE_ACTIVITY)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            REQUEST_CODE_SETTINGS_ACTIVITY -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    if (data.hasExtra(SettingsActivity.HAS_NAME_CHANGED) ||
                        data.hasExtra(SettingsActivity.HAS_AGE_CHANGED)
                    ) {
                        updateUserUI(homeViewModel.userProfile.value!!)
                    }
                    if (data.hasExtra(SettingsActivity.HAS_GENDER_CHANGED))
                        homeViewModel.reloadRecommendedUsers()

                    if (data.hasExtra(SettingsActivity.HAS_RESTORED_PREMIUM) ||
                        data.hasExtra(SettingsActivity.HAS_UPGRADED_TO_PREMIUM)) {
                        homeViewModel.fetchUserProfile()
                        upgradedToPremium()
                        homeViewModel.afterPremiumVerified()
                        likedYouViewModel.initData()
                        homeViewModel.getCardsAndFeaturedProfilesFromAPi(
                            fromTouch = false,
                            applyCardsImmediately = false,
                            fetchCards = false,
                            fetchFeaturedProfiles = true
                        )
                    }
                }
            }
            REQUEST_CODE_EDIT_PROFILE_ACTIVITY -> {
                if (resultCode == Activity.RESULT_OK) {
                    homeViewModel.navigatedToProfileFromRadar()
                }
            }

        }
    }

    private fun openMyProfile() {
        homeViewModel.userProfile.value?.let {
            val userProfileFragment = UserProfileFragment.newInstance(
                RecommendedUserModel.convertToRecommendedUserModel(it, false),
                _isMatchUser = false,
                _isMyProfile = true,
                _profileVisitSource = ""
            )
            if (childFragmentManager.findFragmentByTag("myProfile") == null) {
                userProfileFragment.show(childFragmentManager, "myProfile")
            }
        }
    }

    private fun openManagePicturesActivity() {
        val intent = Intent(context, ManagePicturesActivity::class.java)
        intent.putExtra(UPLOAD_IMAGE_SOURCE, arguments?.getString(OPENED_FROM) ?: UploadImageSourceValues.PROFILE.value)
        startActivityForResult(intent, NEW_IMAGES_REQUEST)
    }

    private fun bindProfileBadge(user: UserModel?) {
        val badgeApproved = binding?.badgeApproved
        val badgeProcessing = binding?.badgeProcessing
        when (user?.badge2) {
            APPROVED.status -> {
                badgeApproved?.setImageDrawable(
                    ContextCompat.getDrawable(
                        badgeApproved.context,
                        R.drawable.ic_image_verification
                    )
                )
                badgeApproved?.visibility = View.VISIBLE
                badgeProcessing?.visibility = View.INVISIBLE
                badgeApproved?.alpha = 1f
                fadeAnimator?.cancel()
            }
            PROCESSING.status -> {
                badgeApproved?.setImageDrawable(
                    ContextCompat.getDrawable(
                        badgeApproved.context,
                        R.drawable.ic_image_verification
                    )
                )
                badgeProcessing?.setImageDrawable(
                    ContextCompat.getDrawable(
                        badgeProcessing.context,
                        R.drawable.ic_image_verification_processinng
                    )
                )
                badgeApproved?.visibility = View.VISIBLE
                badgeProcessing?.visibility = View.VISIBLE
                badgeApproved?.alpha = 1f
                fadeAnimator = ObjectAnimator.ofFloat(badgeApproved, "alpha", 1f, 0f).apply {
                    duration = 800
                    repeatCount = ObjectAnimator.INFINITE
                    repeatMode = ObjectAnimator.REVERSE
                }

                fadeAnimator?.start()
            }
            NOT_APPROVED.status -> {
                badgeApproved?.setImageDrawable(
                    ContextCompat.getDrawable(
                        badgeApproved.context,
                        R.drawable.ic_image_verification_failed
                    )
                )
                badgeApproved?.visibility = View.VISIBLE
                badgeProcessing?.visibility = View.INVISIBLE
                badgeApproved?.alpha = 1f
                fadeAnimator?.cancel()
            }
            NULL.status -> {
                badgeProcessing?.visibility = View.INVISIBLE
                badgeApproved?.alpha = 1f
                fadeAnimator?.cancel()
                if (user.hasBadge1) {
                    badgeApproved?.setImageDrawable(
                        ContextCompat.getDrawable(
                            badgeApproved.context,
                            R.drawable.ic_verification_badge_black_1
                        )
                    )
                    badgeApproved?.visibility = View.VISIBLE
                } else {
                    badgeApproved?.setImageDrawable(
                        ContextCompat.getDrawable(
                            badgeApproved.context,
                            R.drawable.ic_no_badge
                        )
                    )
                    badgeApproved?.visibility = View.VISIBLE
                    badgeProcessing?.visibility = View.INVISIBLE
                }
            }
            else -> {
                badgeProcessing?.visibility = View.INVISIBLE
                badgeApproved?.alpha = 1f
                fadeAnimator?.cancel()
                if (user?.hasBadge1 == true) {
                    badgeApproved?.setImageDrawable(
                        ContextCompat.getDrawable(
                            badgeApproved.context,
                            R.drawable.ic_verification_badge_black_1
                        )
                    )
                    badgeApproved?.visibility = View.VISIBLE
                } else {
                    badgeApproved?.setImageDrawable(
                        ContextCompat.getDrawable(
                            badgeApproved.context,
                            R.drawable.ic_no_badge
                        )
                    )
                    badgeApproved?.visibility = View.VISIBLE
                    badgeProcessing?.visibility = View.INVISIBLE
                }
            }
        }
    }

    fun upgradedToPremium() {
        if (homeViewModel.userProfile.value?.premiumType != null) {
            homeViewModel.clearDontLetGoData()
            initializeUIBasedOnUserPremium()
            updateUserUI(homeViewModel.userProfile.value!!)
            initializeInAppPackagesRecyclerView()
        }
    }

    private fun initializeInAppPackagesRecyclerView() {
        binding?.inAppConsumables?.adapter = null
        binding?.inAppConsumables?.layoutManager = null

        val hasItemDecorations = binding?.inAppConsumables?.itemDecorationCount != 0
        if(hasItemDecorations)
            binding?.inAppConsumables?.removeItemDecorationAt(0)

        val items = generateInAppItems()
        when (items.size) {
            1 -> binding?.inAppConsumables?.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            2 -> binding?.inAppConsumables?.layoutManager = GridLayoutManager(context, 2, GridLayoutManager.VERTICAL, false)
            else -> {
                binding?.inAppConsumables?.addItemDecoration(
                    MarginItemDecoration(convertDpToPixel(16f, requireContext()).toInt())
                )
                binding?.inAppConsumables?.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            }
        }
        val adapter = ProfileInAppPackagesAdapter(
            items = items,
            homeViewModel = homeViewModel,
            onClickListener = object : ProfileInAppPackagesAdapter.OnClickListener {
                override fun onItemClick(profileInAppPackagesModel: ProfileInAppPackagesModel) {
                    when (profileInAppPackagesModel.packageType) {
                        R.string.impressions -> {

                            requireActivity().openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.PROFILE,
                                placementId = PurchaselyPlacement.DYNAMIC_IMPRESSIONS.id,
                                userModel = homeViewModel.userProfile.value
                            )
                        }
                        R.string.instachats_string -> {

                            requireActivity().openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.PROFILE,
                                placementId = PurchaselyPlacement.DYNAMIC_INSTACHAT.id,
                                userModel = homeViewModel.userProfile.value
                            )

                        }
                        R.string.flights -> {

                            requireActivity().openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.PROFILE,
                                placementId = PurchaselyPlacement.DYNAMIC_FLIGHT.id,
                                userModel = homeViewModel.userProfile.value
                            )
                        }
                        R.string.undo_title -> {

                            requireActivity().openPremiumPaywall(
                                eventSourceClevertap = ClevertapEventSourceValues.PROFILE,
                                placementId = PurchaselyPlacement.DYNAMIC_UNDO.id,
                                userModel = homeViewModel.userProfile.value
                            )
                        }
                        R.string.boost -> {
                            if (homeViewModel.hasRemainingBoosts() && !homeViewModel.hasActiveBoost()) {
                                when (homeViewModel.userProfile.value?.badge2) {
                                    APPROVED.status -> {
                                        val user = homeViewModel.userProfile.value
                                        user?.let {
                                            if (user.isInvisible()) {
                                                UnhideProfileDialog.newInstance()
                                                        .show(childFragmentManager, "UnhideProfileDialog")
                                            } else if(user.profile.isGhost) {
                                                IncognitoModeDialogFragment.showDisable(requireContext(),childFragmentManager)
                                                return@let
                                            } else {
                                                ActivateBoostDialog.newInstance(
                                                    ClevertapBoostActivationSource.PROFILE)
                                                        .show(childFragmentManager, "ActivateBoostDialog")
                                            }
                                        }
                                    }
                                    PROCESSING.status -> {
                                        VerificationInProgressDialog.showVerificationInProgressDialog(
                                            childFragmentManager
                                        )
                                    }
                                    else -> {
                                        val eventPremiumType =
                                            getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                                        sendVerifyYourProfilePopupAnalyticsEvent(
                                            eventPremiumType,
                                            ClevertapVerificationSourceValues.TO_BOOST.value
                                        )

                                        VerifyYourProfileDialog.showVerifyYourProfileDialog(
                                            childFragmentManager,
                                            VerifyYourProfileDialog.VerifyProfileFromEnum.BOOST
                                        )
                                    }
                                }
                            } else if(!homeViewModel.hasRemainingBoosts() && !homeViewModel.hasActiveBoost()){

                                requireActivity().openPremiumPaywall(
                                    eventSourceClevertap = ClevertapEventSourceValues.PROFILE,
                                    placementId = PurchaselyPlacement.DYNAMIC_BOOST.id,
                                    userModel = homeViewModel.userProfile.value
                                )
                            }

                        }


                    }
                }

                override fun onInfoButtonClick(profileInAppPackagesModel: ProfileInAppPackagesModel, position:ImageButton) {
                    val binding1: CountersInfoCardBinding? = DataBindingUtil.inflate(
                        layoutInflater,
                        R.layout.counters_info_card,
                        null,
                        false
                    )

                    when (profileInAppPackagesModel.packageType) {
                        R.string.impressions -> {
                            binding1?.inAppConsumablesName?.setText(R.string.impressions)

                            if (homeViewModel.getRemainingLikes() < 0) {
                                binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, 0)
                            } else {
                                binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, homeViewModel.getRemainingLikes())
                            }
                        }

                        R.string.instachats_string -> {
                            binding1?.inAppConsumablesName?.setText(R.string.insta_chats_string)

                            if (homeViewModel.getRemainingInstachats() < 0) {
                                binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, 0)
                            } else {
                                binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, homeViewModel.getRemainingInstachats())
                            }
                        }

                        R.string.flights -> {

                            binding1?.inAppConsumablesName?.setText(R.string.flights)

                            if (homeViewModel.getRemainingFlights() < 0) {
                                binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, 0)
                            } else {
                                binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, homeViewModel.getRemainingFlights())
                            }
                        }

                        R.string.undo_title -> {
                            binding1?.inAppConsumablesName?.setText(R.string.undo_title)

                            if (homeViewModel.getRemainingUndo() < 0) {
                                binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, 0)
                            } else {
                                binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, homeViewModel.getRemainingUndo())

                            }
                    }
                            R.string.boost -> {
                                binding1?.inAppConsumablesName?.setText(R.string.boost)
                                if (homeViewModel.getRemainingBoosts() < 0) {
                                    binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, 0)
                                } else {
                                    binding1?.inAppConsumablesCount?.text = getString(R.string.counters_left_an, homeViewModel.getRemainingBoosts())
                                }
                            }

                    }

                    val balloon = binding1?.root?.let {
                        Balloon.Builder(requireContext())
                            .setLayout(it)
                            .setIsVisibleArrow(false)
                            .setBackgroundDrawableResource(R.drawable.impression_gradient)
                            .setElevation(1)
                            .setMarginTop(-10)
                            .setBalloonAnimation(BalloonAnimation.NONE)
                            .setLifecycleOwner(viewLifecycleOwner)
                            .build()
                    }
                    balloon?.showAlignBottom(position)
                }
            }
        )
        adapter.setNewItems(items)
        binding?.inAppConsumables?.adapter = adapter

        if (profileViewModel.arePricesMissing()) {
            profileViewModel.getPurchaselyPackages()
        }

    }

    private fun generateInAppItems(): List<ProfileInAppPackagesModel> {
        val userGender = homeViewModel.userProfile.value?.gender
        val isFreemium = homeViewModel.userProfile.value?.premiumType == null

        val items = if(isFreemium) {
            getPackageList(RemoteConfigUtils.getConsumables(), homeViewModel.userCounters)
        } else {
            val isAllowedNewLimits = RemoteConfigUtils.isAllowedToUseNewPremiumLimits()

            if(userGender == GenderType.MAN.value){
                if (isAllowedNewLimits) {
                    getPackageList(listOf(InAppProduct.BOOST.type, InAppProduct.INSTACHATS.type), homeViewModel.userCounters)
                } else {
                    getPackageList(listOf(InAppProduct.BOOST.type, InAppProduct.IMPRESSIONS.type, InAppProduct.INSTACHATS.type), homeViewModel.userCounters)
                }
            } else {
                getPackageList(listOf(InAppProduct.BOOST.type), homeViewModel.userCounters)
            }
        }

        items.forEach { it.price = when(it.packageName.productId) {
                profileViewModel.boostSmallestPrice?.getProductId() -> getString(R.string.from) + " " + profileViewModel.boostSmallestPrice?.localizedPrice()
                profileViewModel.impressionsSmallestPrice?.getProductId() -> getString(R.string.from) + " " + profileViewModel.impressionsSmallestPrice?.localizedPrice()
                profileViewModel.instachatsSmallestPrice?.getProductId() -> getString(R.string.from) + " " + profileViewModel.instachatsSmallestPrice?.localizedPrice()
                profileViewModel.undosSmallestPrice?.getProductId() -> getString(R.string.from) + " " + profileViewModel.undosSmallestPrice?.localizedPrice()
                profileViewModel.flightsSmallestPrice?.getProductId() -> getString(R.string.from) + " " + profileViewModel.flightsSmallestPrice?.localizedPrice()
                else -> ""
            }
        }
        return items
    }

    fun getCoordinates() {
//        binding.root.viewTreeObserver?.addOnGlobalLayoutListener(
//            object : ViewTreeObserver.OnGlobalLayoutListener {
//                override fun onGlobalLayout() {
//                    binding.root.viewTreeObserver!!.removeOnGlobalLayoutListener(this)
//
//                    //TODO logic when to show this
//                    if ((requireActivity() as HomeActivity).isSigningUpShowGuide
//                        && !duaSharedPrefs.getHasShownUserGuideInProfile()
//                    )
//                        showUserGuide()
//                }
//            })
    }

    fun showUserGuide(step: Int = 0) {
//        val dialogFragment = NewProfileGuideDialog()
//        dialogFragment.arguments = bundleOf(
//            BTN_FILTER_COORDINATES_X to binding.btnFilter.x,
//            BTN_FILTER_COORDINATES_Y to binding.btnFilter.y,
//            BTN_EDIT_COORDINATES_Y to binding.editProfileButton.y + binding.toolbarId.height,
//            BTN_EDIT_COORDINATES_X to binding.editProfileButton.x,
//            BTN_PHOTO_COORDINATES_Y to binding.addImageBtn.y + binding.toolbarId.height,
//            BTN_PHOTO_COORDINATES_X to binding.addImageBtn.x,
//            USER_GUIDE_STEP to step,
//        )
//
//        Timber.tag("CAMERAPOSITION").d("NEWPROFILEDIALOG cameraImagebtn.y: ${binding.cameraImagebtn.y}")
//        Timber.tag("CAMERAPOSITION").d("NEWPROFILEDIALOG editProfileButton: ${binding.editProfileButton.y}")
//        val ft = childFragmentManager.beginTransaction()
//        dialogFragment.show(ft, "dialog")
    }

    private fun openFilterBottomSheet() {
       homeViewModel.showFilter(ClevertapFiltersEventSourceValues.PROFILE.value)
    }

    private fun checkShadowBannedUser(isShadowBanned: Boolean) {
        val addBackGround:Int = if (isShadowBanned) {
            R.drawable.profile_buttons_bg_middle_border_color
        } else {
            R.drawable.profile_buttons_bg_middle
        }
        val plusIcon: Int = if (isShadowBanned) {
            R.drawable.ic_plus_icon_disable
        } else {
            R.drawable.ic_plus_icon
        }
        binding?.addImageBtn?.setBackgroundResource(addBackGround)
        binding?.addImageBtn?.setImageResource(plusIcon)

        binding?.inAppConsumables?.isVisible = !isShadowBanned && DuaApplication.instance.getBillingAvailable()
        binding?.shadowBannedContainer?.root?.isVisible = isShadowBanned
        binding?.shadowBannedContainer?.shdbButton?.setOnClickListener {
            openManagePicturesActivity()
            firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_PHOTO)
            sendClevertapEvent(ClevertapEventEnum.CHANGE_PHOTO)
            sendAddPhotoEvents()

            hideProfileCompletedProgressBar()
        }
    }
    private fun sendAddPhotoEvents() {
        sendClevertapEvent(
            ClevertapEventEnum.ADD_PHOTO,
            mapOf(ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapAddPhotoSourceValues.PROFILE.value)
        )
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.ADD_PHOTO,
            mapOf(FirebaseAnalyticsParameterName.EVENT_SOURCE.value to FirebaseAnalyticsAddPhotoSourceValues.PROFILE.value)
        )
    }

    override fun onVerifyYouProfileClicked(
        openedFrom: VerifyYourProfileDialog.VerifyProfileFromEnum,
        eventName: String?
    ) {
        when(openedFrom) {
            VerifyYourProfileDialog.VerifyProfileFromEnum.BOOST -> {
                val eventPremiumType =
                    getPremiumTypeEventProperty(homeViewModel.userProfile.value)

                sendVerifyYourProfileInitiatedAnalyticsEvent(
                    ClevertapVerificationSourceValues.TO_BOOST.value,
                    eventPremiumType
                )

             findNavController().navigateSafer(R.id.action_global_verifyProfileWithBadge2PopUp,
                 bundleOf(EVENT_SOURCE to ClevertapVerificationSourceValues.TO_BOOST.value)
             )

            }
            else -> {}
        }


    }


    override fun onDestroyView() {
        super.onDestroyView()
        unregisterReceivers()

        (binding?.inAppConsumables?.adapter as? ProfileInAppPackagesAdapter)?.countDownTimer?.cancel()
        binding?.inAppConsumables?.adapter = null

        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        _binding = null
    }

    override fun onButtonClick(dialog: DialogFragment, type: IncognitoModeType) {
        if(type == IncognitoModeType.DISABLE) {
            homeViewModel.toggleGhostMode(
                isChecked = false,
                eventSource = ClevertapGhostSourceValues.BOOST_PROFILE)
            dialog.dismissAllowingStateLoss()
            sendDeactivateGhostModePopupEvent(homeViewModel.userProfile.value)
        }
    }
    private fun sendDeactivateGhostModePopupEvent(userProfile: UserModel?) {
        sendClevertapEvent(
            event = ClevertapEventEnum.DEACTIVATE_GHOST_MODE_POPUP,
            properties = mapOf(
                ClevertapEventPropertyEnum.GHOST_SOURCE.propertyName to ClevertapGhostSourceValues.BOOST_PROFILE.value,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    userProfile
                ),
            )
        )
        //Since the GA property values are the same with the clevertap property values, we use the same variables
        firebaseLogEvent(
            eventName = FirebaseAnalyticsEventsName.DEACTIVATE_GHOST_MODE_POPUP,
            params = mapOf(
                ClevertapEventPropertyEnum.GHOST_SOURCE.propertyName to ClevertapGhostSourceValues.BOOST_PROFILE.value,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    userProfile
                )
            )
        )
    }
    override fun onDismiss(dialog: DialogFragment, type: IncognitoModeType) {
        /*not used*/
    }

}
