package com.duaag.android.profile_new.editprofile

data class Fields(
    val isJobCompleted: Boolean = false,
    val isEducationCompleted: Boolean = false,
    val isActivitiesCompleted: Boolean = false,
    val isHometownCompleted: <PERSON>olean = false,
    val isLanguagesCompleted: <PERSON>olean = false,
    val isDescriptionCompleted: <PERSON>olean = false,
    val isLookingForCompleted: <PERSON>olean = false,
    val isSmokeCompleted: <PERSON>ole<PERSON> = false,
    val isPetsCompleted: Boolean = false,
    val isReligionCompleted: Boolean = false,
    val isHaveChildrenCompleted: <PERSON>olean = false,
    val isWantChildrenCompleted: <PERSON>olean = false,

    ) {
    fun getMyInformationMembersList(): List<Boolean> {
        return listOf(isJobCompleted, isActivitiesCompleted, isHometownCompleted, isLanguagesCompleted, isDescriptionCompleted)
    }

    fun getAdditionalInfoMembersList(): List<Boolean> {
        return listOf(isLookingForCompleted, isSmokeCompleted, isPetsCompleted, isReligionCompleted, isHaveChildrenCompleted, isWantChildrenCompleted)
    }
}