package com.duaag.android.profile_new.adapters

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.databinding.PremiumOrInviteAFriendBinding
import com.duaag.android.utils.getStringPlaceHolder
import com.duaag.android.utils.setMargin
import com.duaag.android.utils.setOnSingleClickListener

class GetInteractionsModel1(val image: Int, val title: Int, val description: Int, val buttonText: Int)

class PremiumOrInviteFriendsAdapter(val clickListener: OnClickListener) : RecyclerView.Adapter<PremiumOrInviteFriendsAdapter.PremiumOrInviteFriendsAdapterAdapter>() {
    private var billingAvailable = DuaApplication.instance.getBillingAvailable()


    val items: List<GetInteractionsModel1>
        get() = if (!billingAvailable) {
            listOf(GetInteractionsModel1(R.drawable.ic_heartgroups, R.string.invite_your_friends, R.string.invite_a_friend_and_get_1_flight, R.string.invite_a_friend))
        } else {
            listOf(GetInteractionsModel1(R.drawable.ic_diamond_user, R.string.dua_premium_1, R.string.enjoy_duapremium_to_the_fullest, R.string.overview))
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PremiumOrInviteFriendsAdapterAdapter {
        val binding: PremiumOrInviteAFriendBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), R.layout.premium_or_invite_a_friend, parent, false)
        return PremiumOrInviteFriendsAdapterAdapter(binding)
    }


    @SuppressLint("StringFormatMatches")
    override fun onBindViewHolder(holder: PremiumOrInviteFriendsAdapterAdapter, position: Int) {
        holder.binding.getInteractionsModel = items[position]

        if(billingAvailable) {
            holder.binding.title.text = getStringPlaceHolder(holder.itemView.context.getString(R.string.dua_premium_1, holder.itemView.context.getString(R.string.app_name)), holder.itemView.context.getString(R.string.dua_premium_2_an), "", R.color.title_primary, holder.itemView.context,R.font.tt_norms_pro_bold)
            holder.binding.description.text = holder.itemView.context.getString(R.string.enjoy_duapremium_to_the_fullest, holder.itemView.context.getString(R.string.app_name))
            holder.binding.title.textSize = 22F
            holder.binding.image.setMargin(0F,4F,0F,0F)
            holder.binding.title.setMargin(0F,0F,0F,10F)
            holder.binding.description.setMargin(30F,0F,30F,26F)
        }else{
            holder.binding.title.text = holder.itemView.context.getString(R.string.invite_your_friends)
            holder.binding.description.text = holder.itemView.context.getString(R.string.invite_a_friend_and_get_1_flight,"100","1","1","1")
            holder.binding.title.textSize = 13F
            holder.binding.image.setMargin(0F,4F,0F,7F)
            holder.binding.title.setMargin(0F,0F,0F,8F)
            holder.binding.description.setMargin(30F,0F,30F,16F)
            holder.binding.description.minLines = 2
        }

    }

    override fun getItemCount(): Int = items.size

  inner class PremiumOrInviteFriendsAdapterAdapter(val binding: PremiumOrInviteAFriendBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.inviteBtn.setOnSingleClickListener(3000L){
                val model = items[adapterPosition]
                clickListener.onItemClick(model)
                bind(model)
        } }
        fun bind(getInteractionsModel1: GetInteractionsModel1) {
            binding.getInteractionsModel = getInteractionsModel1
            binding.executePendingBindings()
        }

    }

    interface OnClickListener {
        fun onItemClick(getInteractionsModel1: GetInteractionsModel1)
    }
}