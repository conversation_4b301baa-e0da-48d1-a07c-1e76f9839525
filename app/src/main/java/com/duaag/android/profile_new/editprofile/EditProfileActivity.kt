package com.duaag.android.profile_new.editprofile

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.Navigation
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.NavigationUI
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEditProfileSourceValues
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.ActivityEditProfile2Binding
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.firebase.NotificationType
import com.duaag.android.firebase.model.NotificationModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.profile_new.editprofile.di.EditProfileComponent
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.enableButton
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.livedata.observeOnce
import com.duaag.android.utils.vibrateTap
import com.google.android.material.floatingactionbutton.FloatingActionButton
import javax.inject.Inject


class EditProfileActivity : AppCompatActivity() {

    companion object {
        const val CONNECT_INSTAGRAM_INTENT = "CONNECT_INSTAGRAM_INTENT"

        const val EDIT_PROFILE_SOURCE = "edit_profile_source"
    }


    private var _binding: ActivityEditProfile2Binding? = null
    private val binding get() = _binding!!

    lateinit var editProfileComponent: EditProfileComponent

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    private val viewModel by viewModels<ShareEditProfileViewModel> { viewModelFactory }
    private lateinit var navController: NavController
    private lateinit var appBarConfiguration: AppBarConfiguration

    @Inject
    lateinit var duaAccount: DuaAccount

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        editProfileComponent = (application as DuaApplication).appComponent.editProfileComponent().create()
        editProfileComponent.inject(this)
        _binding = DataBindingUtil.setContentView(this, R.layout.activity_edit_profile2)
        intent?.getBooleanExtra(CONNECT_INSTAGRAM_INTENT,false)?.let {
            if(it) {
                viewModel.setInstagramClicked()
            }
        }
        binding.lifecycleOwner = this
        setSupportActionBar(binding.toolbar)

        setUpNav()
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        binding.let {
            it.lifecycleOwner = this
            it.toolbar.setNavigationOnClickListener {
                hideKeyboard(it)
                if (navController.currentDestination?.id == R.id.editProfileFragment) {
                    onBackPressed()
                } else {
                    viewModel.fromBackPress = true
                    navController.currentDestination?.id?.let { it1 -> updateTags(null, it1) }
                    if(navController.currentDestination?.id != R.id.jobEducationFragment && navController.currentDestination?.id != R.id.descriptionFragment){
                        navController.popBackStack(R.id.editProfileFragment, false)
                    }
                }
            }
            it.fabNext.enableButton()
            it.fabBack.enableButton(R.color.border)
            binding.fabBack.setImageDrawable(ContextCompat.getDrawable(this, R.drawable.ic_baseline_arrow_back_black))
        }

        viewModel.deleteUser.observe(this, Observer {
            duaAccount.deleteAllData()
        })

        viewModel.userProfile.observeOnce(this, Observer { user ->
            if(user == null)
                return@Observer

            viewModel.jobInfo = user.profile.jobs ?: ""
            viewModel.educationInfo = user.profile.educations ?: ""
            viewModel.descriptionInfo = user.profile.description ?: ""
        })

        setUpFabButtonsObservers(binding.fabBack, binding.fabNext)

        viewModel.progressBarPercentage.observe(this) {
            binding.typePercentage.progress = it
        }

        viewModel.nextFabState.observe(this) {
            when (it) {
                FloatingActionButtonVisibility.SHOWN -> {
                    binding.fabNext.enableButton()

                }
                FloatingActionButtonVisibility.HIDDEN -> {

                }
                FloatingActionButtonVisibility.DISABLED -> {

                }
                FloatingActionButtonVisibility.NEUTRAL -> {
                    binding.fabNext.enableButton(R.color.border)
                    binding.fabNext.setImageDrawable(
                        ContextCompat.getDrawable(
                            this,
                            R.drawable.ic_arrow_forw_normal
                        )
                    )
                }
                null -> {
                }
            }

        }

        viewModel.backFabState.observe(this) {
            when (it) {
                FloatingActionButtonVisibility.SHOWN -> {
                    binding.fabBack.enableButton(R.color.border)
                    binding.fabBack.setImageDrawable(
                        ContextCompat.getDrawable(
                            this,
                            R.drawable.ic_baseline_arrow_back_black
                        )
                    )
                }
                FloatingActionButtonVisibility.HIDDEN -> {
                    binding.fabBack.visibility = View.INVISIBLE
                }
                FloatingActionButtonVisibility.DISABLED -> {

                }
                FloatingActionButtonVisibility.NEUTRAL -> {

                }
                null -> {
                }
            }

        }

        setHapticFeedbackObserver()

        sendScreenViewedAnalyticsEvents()
    }

    private fun setHapticFeedbackObserver() {
        viewModel.hapticFeedback.observe(this, {
            if (it) {
                vibrateTap()
            }
        })
    }

    private fun setUpFabButtonsObservers(fabBack: FloatingActionButton?, fabNext: FloatingActionButton?) {
        fabNext?.setOnClickListener {
            navController.currentDestination?.id?.let { it1 -> updateTags(fabNext, it1) }
        }

        fabBack?.setOnClickListener {
            this.hideKeyboard(fabBack)
            navController.popBackStack()
        }
    }

    private fun updateTags(fabNext: FloatingActionButton?,currentDestination: Int) {
        when (currentDestination) {

            R.id.lookingForFragment -> {
                viewModel.setLoadingResultForField(6)
                viewModel.onLookingForUpdated()
            }

            R.id.smokingFragment -> {
                viewModel.setLoadingResultForField(7)
                viewModel.onSmokingUpdated()
            }

            R.id.addUserHeightBottomSheet2 -> {
                viewModel.onHeightUpdated()
            }

            R.id.petsFragment -> {
                viewModel.setLoadingResultForField(3)
                viewModel.onPetsUpdated()
            }

            R.id.religionFragment -> {
                viewModel.setLoadingResultForField(4)
                viewModel.onReligionUpdated()

            }

            R.id.haveChildrenFragment -> {
                viewModel.setLoadingResultForField(8)
                viewModel.onHaveChildrenUpdated()
            }

            R.id.wantChildrenFragment -> {
                viewModel.setLoadingResultForField(9)
                viewModel.onWantChildrenUpdated()
            }

            R.id.activitiesFragment -> {
                viewModel.setLoadingResultForField(1)
                viewModel.onActivitiesUpdated()
            }

            R.id.hometownFragment -> {
                viewModel.setLoadingResultForField(5)
                viewModel.onHometownUpdated()
            }

            R.id.jobEducationFragment -> {
                if (fabNext != null) {
                    this.hideKeyboard(fabNext)
                }
                viewModel.onJobAndEducationUpdated()
            }

            R.id.languagesFragment -> {
                viewModel.setLoadingResultForField(2)
                viewModel.onLanguagesUpdated()
            }

            R.id.descriptionFragment -> {
                if (fabNext != null) {
                    this.hideKeyboard(fabNext)
                }
                viewModel.onDescriptionUpdated()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        binding.toolbarTitle.text = getString(R.string.edit_my_profile)
    }

    private fun sendScreenViewedAnalyticsEvents() {
        val editProfileSource = intent.getStringExtra(EDIT_PROFILE_SOURCE) ?: ClevertapEditProfileSourceValues.PROFILE.value

        val eventPremiumType = getPremiumTypeEventProperty(viewModel.userProfile.value)

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.EDIT_PROFILE, mapOf(
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                FirebaseAnalyticsParameterName.EDIT_PROFILE_SOURCE.value to editProfileSource
            ))

        sendClevertapEvent(
            ClevertapEventEnum.EDIT_PROFILE, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.EDIT_PROFILE_SOURCE.propertyName to editProfileSource
            ))
    }

    private fun setUpNav() {
        navController = Navigation.findNavController(this, R.id.nav_edit_profile)
        appBarConfiguration = AppBarConfiguration.Builder()
                .setFallbackOnNavigateUpListener {
                    // Trigger the Activity's navigate up functionality
                    super.onSupportNavigateUp()
                }.build()
        NavigationUI.setupActionBarWithNavController(this, navController, appBarConfiguration)
        navController.addOnDestinationChangedListener { _, destination, _ ->
            if (destination.id == R.id.searchFragment || destination.id == R.id.searchFragmentAdditionalinfo) {
                supportActionBar?.hide()
                supportActionBar?.setDisplayHomeAsUpEnabled(false)


            } else {
                supportActionBar?.setDisplayHomeAsUpEnabled(true)
                supportActionBar?.show()
            }
         //   setToolbarInfoVisibility(destination)
            if (destination.id == R.id.editProfileFragment ||
                destination.id == R.id.searchFragment ||
                destination.id == R.id.searchFragmentAdditionalinfo ||
                destination.id==R.id.profileProgressFragment ||
                destination.id == R.id.zodiacFragment) binding.fabsContainer.visibility = View.GONE else binding.fabsContainer.visibility = View.VISIBLE
            when (destination.id) {
                R.id.editProfileFragment -> {
                    hideKeyboard()
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.DISABLED)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_close_black_24dp)
                    if(!toolbarInfoHasClickListener()!!){
                    setToolbarInfoClicklistener {
                        getNavController().navigate(R.id.profileProgressFragment)
                    }}
                }
                R.id.lookingForFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.HIDDEN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForAdditionalChoices()
                }
                R.id.addUserHeightBottomSheet2 -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForAdditionalChoices()
                }
                R.id.smokingFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForAdditionalChoices()
                }
                R.id.petsFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForAdditionalChoices()
                }
                R.id.religionFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForAdditionalChoices()
                }
                R.id.haveChildrenFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForAdditionalChoices()
                }
                R.id.wantChildrenFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForAdditionalChoices()
                }

                R.id.activitiesFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForMyInformationChoices()
                }

                R.id.hometownFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForMyInformationChoices()
                }
                R.id.languagesFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForMyInformationChoices()
                }
                R.id.jobEducationFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.HIDDEN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForMyInformationChoices()
                }
                R.id.descriptionFragment -> {
                    viewModel.setFabBackVisibility(FloatingActionButtonVisibility.SHOWN)
                    viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)
                    viewModel.checkPercentageForMyInformationChoices()
                }
                R.id.searchFragment -> {
                }

                R.id.zodiacFragment -> {
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)

                }

                R.id.profileProgressFragment -> {
                    binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_left_arrow)

                }
            }
        }
    }

    private fun setToolbarInfoVisibility(destination: NavDestination) {
        binding.toolbarInfo.visibility = if (destination.id == R.id.editProfileFragment) View.VISIBLE else View.GONE
    }

    override fun onSupportNavigateUp(): Boolean {
        return NavigationUI.navigateUp(navController, appBarConfiguration) || super.onSupportNavigateUp()
    }

    fun updateUser(userModel: UserModel) {
        viewModel.updateUser(userModel)
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(onUpdateDataReceiver)
        _binding = null
    }

    private val onUpdateDataReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val extras = intent.extras
            if (extras != null) {
                if (extras.containsKey("action") && extras.containsKey("jsonData"))
                    onDataUpdateFromNotification(NotificationModel(extras))
            } else if (intent.getStringExtra("action") != null && intent.getStringExtra("jsonData") != null) {
                Bundle().apply {
                    putString("action", intent.getStringExtra("action"))
                    putString("jsonData", intent.getStringExtra("jsonData"))
                    onDataUpdateFromNotification(NotificationModel(this))
                }
            }
        }
    }

    private fun onDataUpdateFromNotification(notificationModel: NotificationModel) {
        if (notificationModel.type == NotificationType.USER_DELETED) {
            viewModel.userDelete()
        }
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this).registerReceiver(onUpdateDataReceiver,
                IntentFilter(NotificationHelper.UPDATE_DATA)
        )
    }

    fun setToolbarTitle(title: String) {
        binding.toolbarTitle.text = title
    }

    fun toolbarInfoHasClickListener(): Boolean? {
        return binding.toolbarInfo.hasOnClickListeners()
    }

    fun setToolbarInfoClicklistener(listener: View.OnClickListener?) {
        binding.toolbarInfo.setOnClickListener(listener)
    }

    fun getNavController(): NavController {
        return navController
    }

    override fun onBackPressed() {
        when (val currentDestinationId = navController.currentDestination?.id) {
            R.id.searchFragment, R.id.searchFragmentAdditionalinfo -> {
                navController.popBackStack()
                return
            }
            R.id.editProfileFragment -> {
                super.onBackPressed()
            }
            else -> {
                viewModel.fromBackPress = true
                currentDestinationId?.let { updateTags(null, it) }

                if (currentDestinationId != R.id.jobEducationFragment &&
                    currentDestinationId != R.id.descriptionFragment) {
                    navController.popBackStack(R.id.editProfileFragment, false)
                }
            }
        }

    }

}
