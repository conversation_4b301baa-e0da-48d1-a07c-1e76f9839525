package com.duaag.android.profile_new.adapters

import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.InappPackagesMultipleItemsBinding
import com.duaag.android.databinding.InappPackagesSingleItemsBinding
import com.duaag.android.databinding.InappPackagesTwoItemsBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.profile_new.models.ProfileInAppPackagesModel
import java.util.concurrent.TimeUnit

class ProfileInAppPackagesAdapter(
    var items: List<ProfileInAppPackagesModel>,
    var homeViewModel: HomeViewModel,
    val onClickListener: OnClickListener
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val SINGLE_ITEM = 0
        const val TWO_ITEMS = 1
        const val MULTIPLE_ITEMS = 2
    }

    var countDownTimer: CountDownTimer? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            SINGLE_ITEM -> {
                val binding = InappPackagesSingleItemsBinding.inflate(layoutInflater, parent, false)
                SingleInAppPackageViewHolder(binding)
            }
            TWO_ITEMS -> {
                val binding = InappPackagesTwoItemsBinding.inflate(layoutInflater, parent, false)
                TwoInAppItemsPackageViewHolder(binding)
            }
            else -> {
                val binding = InappPackagesMultipleItemsBinding.inflate(layoutInflater, parent, false)
                MultipleInAppItemsPackageViewHolder(binding)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            SINGLE_ITEM -> (holder as SingleInAppPackageViewHolder).bind(items[position])
            TWO_ITEMS -> (holder as TwoInAppItemsPackageViewHolder).bind(items[position])
            else -> (holder as MultipleInAppItemsPackageViewHolder).bind(items[position])
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when(itemCount){
            1 -> SINGLE_ITEM
            2 -> TWO_ITEMS
            else -> MULTIPLE_ITEMS
        }
    }

    override fun getItemCount(): Int = items.size


    inner class SingleInAppPackageViewHolder(val binding: InappPackagesSingleItemsBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.container.setOnClickListener {
                val model = items[bindingAdapterPosition]
                onClickListener.onItemClick(model)
            }
            binding.infoBtn.setOnClickListener {
                val model = items[bindingAdapterPosition]
                onClickListener.onInfoButtonClick(model,binding.infoBtn)
            }
        }

        fun bind(inAppModel: ProfileInAppPackagesModel) {
            binding.inAppImage.setImageResource(inAppModel.image)
            binding.inAppTitle.setText(inAppModel.packageType)
            if(inAppModel.packageType == R.string.boost && (homeViewModel.hasActiveBoost() || homeViewModel.isInCalculatingBoostStatus())) {
                countDownTimer(binding.priceTxt,inAppModel)
            }else if(homeViewModel.hasRemainingBoosts() && inAppModel.packageType == R.string.boost){
                binding.priceTxt.setText(R.string.tap_to_activate_boost)
            }else {
                binding.priceTxt.text = inAppModel.price
            }
        }
    }

    inner class TwoInAppItemsPackageViewHolder(val binding: InappPackagesTwoItemsBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.container.setOnClickListener {
                val model = items[bindingAdapterPosition]
                onClickListener.onItemClick(model)
            }
            binding.infoBtn.setOnClickListener {
                val model = items[bindingAdapterPosition]
                onClickListener.onInfoButtonClick(model,binding.infoBtn)
            }
        }

        fun bind(inAppModel: ProfileInAppPackagesModel) {
            binding.inAppImage.setImageResource(inAppModel.image)
            binding.inAppTitle.setText(inAppModel.packageType)
            if(inAppModel.packageType == R.string.boost && (homeViewModel.hasActiveBoost() || homeViewModel.isInCalculatingBoostStatus())) {
                countDownTimer(binding.priceTxt,inAppModel)
            }else if(homeViewModel.hasRemainingBoosts() && inAppModel.packageType == R.string.boost){
                binding.priceTxt.setText(R.string.tap_to_activate_boost)
            }else {
                binding.priceTxt.text = inAppModel.price
            }
        }
    }

    inner class MultipleInAppItemsPackageViewHolder(val binding: InappPackagesMultipleItemsBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.container.setOnClickListener {
                val model = items[bindingAdapterPosition]
                onClickListener.onItemClick(model)
            }
            binding.infoBtn.setOnClickListener {
                val model = items[bindingAdapterPosition]
                onClickListener.onInfoButtonClick(model,binding.infoBtn)
            }
        }

        fun bind(inAppModel: ProfileInAppPackagesModel) {
            binding.inAppImage.setImageResource(inAppModel.image)
            binding.inAppTitle.setText(inAppModel.packageType)
            if(inAppModel.packageType == R.string.boost && (homeViewModel.hasActiveBoost() || homeViewModel.isInCalculatingBoostStatus())) {
                countDownTimer(binding.priceTxt,inAppModel)
            }else if(homeViewModel.hasRemainingBoosts() && inAppModel.packageType == R.string.boost){
                binding.priceTxt.setText(R.string.tap_to_activate_boost)
            }else {
                binding.priceTxt.text = inAppModel.price
            }
        }
    }


    fun countDownTimer(textView: TextView,inAppModel: ProfileInAppPackagesModel) {

        if (homeViewModel.isInCalculatingBoostStatus()) {
            textView.text = textView.context.getString(R.string.calculating_boost)
            return
        }

        if(countDownTimer == null) {
            countDownTimer = object : CountDownTimer(
                (homeViewModel.userProfile.value?.profile?.boostedUntilTime
                    ?: 0L) - System.currentTimeMillis(), 1000
            ) {
                override fun onTick(p0: Long) {
                    var millisUntilFinished = p0

                    val hours = TimeUnit.MILLISECONDS.toHours(millisUntilFinished)
                    millisUntilFinished -= TimeUnit.HOURS.toMillis(hours)

                    val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                    millisUntilFinished -= TimeUnit.MINUTES.toMillis(minutes)

                    val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished)

                    textView.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
                }

                override fun onFinish() {
                    textView.text = textView.context.getString(R.string.calculating_boost)
                }
            }
        }
        countDownTimer?.start()
    }
    interface OnClickListener {
        fun onItemClick(profileInAppPackagesModel: ProfileInAppPackagesModel)
        fun onInfoButtonClick(profileInAppPackagesModel: ProfileInAppPackagesModel, position: ImageButton)
    }

    fun setNewItems(newItems: List<ProfileInAppPackagesModel>) {
        items = newItems
    }
}



