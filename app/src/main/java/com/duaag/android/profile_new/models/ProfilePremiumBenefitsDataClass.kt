package com.duaag.android.profile_new.models

import com.duaag.android.R

class ProfilePremiumBenefitsDataClass(val image: Int, val title: Int)

val premiumBenefitsItemsFemale: List<ProfilePremiumBenefitsDataClass>
    get() = listOf(
            ProfilePremiumBenefitsDataClass(R.drawable.ic_impressions_icon, R.string.unlimited_impressions_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_instachat_icon, R.string.unlimited_instachats_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_flights_icon, R.string.unlimited_flights_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_undo_icon, R.string.unlimited_undo_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.no_ads),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_blurred_icon, R.string.unblurred_liked_you),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_more_pictures_icon, R.string.up_to_six_images_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_unlimited_calls_icon, R.string.ten_hours_per_month)
    )

fun getProfilePremiumBenefitsListFemale() = premiumBenefitsItemsFemale


val premiumBenefitsItemsMale: List<ProfilePremiumBenefitsDataClass>
    get() = listOf(
            ProfilePremiumBenefitsDataClass(R.drawable.ic_impressions_icon, R.string.impressions_premium_500),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_instachat_icon, R.string.five_instachats_daily_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_flights_icon, R.string.unlimited_flights_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_undo_icon, R.string.unlimited_undo_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_no_ads_icon, R.string.no_ads),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_blurred_icon, R.string.unblurred_liked_you),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_more_pictures_icon, R.string.up_to_six_images_premium),
            ProfilePremiumBenefitsDataClass(R.drawable.ic_unlimited_calls_icon, R.string.ten_hours_per_month)
    )

fun getProfilePremiumBenefitsListMale() = premiumBenefitsItemsMale

