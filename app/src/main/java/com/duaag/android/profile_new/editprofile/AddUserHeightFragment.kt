package com.duaag.android.profile_new.editprofile

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.databinding.AddUserHeightFragmentBinding
import com.duaag.android.home.models.SetHeightScreenState
import com.duaag.android.home.viewmodels.SetHeightViewModel
import com.duaag.android.profile_new.editprofile.EditProfileConstants.ADDITIONAL_INFO_TYPE
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.updateLocale
import kotlinx.coroutines.launch
import javax.inject.Inject

class AddUserHeightFragment : Fragment() {


    private var _binding: AddUserHeightFragmentBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val setHeightViewModel by viewModels<SetHeightViewModel> { viewModelFactory }
    private val sharedViewModel: ShareEditProfileViewModel by viewModels({ activity as EditProfileActivity }) { viewModelFactory }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as EditProfileActivity).editProfileComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AddUserHeightFragmentBinding.inflate(inflater, container, false)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val isValidInput = validateHeight(binding.heightInput.text.toString().toIntOrNull())
        setValidUi(isValidInput)

        sharedViewModel.userProfile.value?.profile?.height?.let { height ->
            binding.heightInput.setText(height.toString())
            binding.heightInput.setSelection(binding.heightInput.length())
        }

        binding.heightInput.doOnTextChanged { text, _, _, _ ->
            val isValid = validateHeight(text.toString().toIntOrNull())
            setValidUi(isValid)
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                setHeightViewModel.setHeightScreenState.collect {
                    when(it) {
                        is SetHeightScreenState.Success -> {
                            binding.editProgress.visibility = View.GONE

                            setHeightViewModel.setInitialHeight()
                            navigate()
                        }
                        is SetHeightScreenState.Error -> {
                            binding.editProgress.visibility = View.GONE
                        }
                        is SetHeightScreenState.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                        is SetHeightScreenState.InitialState -> {
                            binding.editProgress.visibility = View.GONE
                        }
                    }
                }
            }
        }

        sharedViewModel.updateHeight.observe(viewLifecycleOwner) {
            val profileHeight = sharedViewModel.userProfile.value?.profile?.height
            val textField = binding.heightInput.text.toString().toIntOrNull()
            if(profileHeight != textField && (textField == null || validateHeight(textField))) {
                setHeightViewModel.addHeight(textField)
            } else if (textField == null || validateHeight(textField)) {
                navigate()
            }
        }
    }

    fun navigate() {
        if(sharedViewModel.fromBackPress != true){
            findNavController().navigateSafer(
                R.id.action_addUserHeightBottomSheet2_to_smokingFragment,
                bundleOf(ADDITIONAL_INFO_TYPE to EditProfileConstants.AdditionalInfoConstants.SMOKE)
            )
        } else sharedViewModel.fromBackPress = false
    }

    private fun validateHeight(height: Int?): Boolean {
        val validHeight = try {
            height in 80..250
        } catch (ex: Exception) {
            ex.printStackTrace()
            false
        }

        return validHeight
    }

    private fun setValidUi(validHeight: Boolean) {
        if(validHeight) {
            sharedViewModel.setFabNextVisibility(FloatingActionButtonVisibility.SHOWN)
        } else {
            sharedViewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        _binding = null
    }
}