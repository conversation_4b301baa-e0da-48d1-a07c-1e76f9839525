package com.duaag.android.profile_new.editprofile.my_information

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.models.UpdateTagsModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentMyInformationSearchableBinding
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoEventProperties
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoSourceValues
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoTypeValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.profile_new.editprofile.EditProfileActivity
import com.duaag.android.profile_new.editprofile.EditProfileConstants
import com.duaag.android.profile_new.editprofile.EditProfileConstants.ITEMS_TYPE_TO_SEARCH
import com.duaag.android.profile_new.editprofile.EditProfileConstants.MY_INFORMATION_TYPE
import com.duaag.android.profile_new.editprofile.EditProfileConstants.MyInformationSearchableConstants
import com.duaag.android.profile_new.editprofile.EditProfileConstants.SEARCHABLE_TYPE
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment.MyInformationSearchableType.ACTIVITIES
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment.MyInformationSearchableType.HOMETOWN
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment.MyInformationSearchableType.LANGUAGES
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import kotlinx.parcelize.Parcelize
import javax.inject.Inject


class MyInformationSearchableFragment : Fragment() {
    @Parcelize
    enum class MyInformationSearchableType(val typeId: Int) : Parcelable {
        ACTIVITIES(1), HOMETOWN(5), LANGUAGES(2), JOB_EDUCATION(-1), DESCRIPTION(-2)
    }

    private var type: String? = MyInformationSearchableConstants.ACTIVITIES

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ShareEditProfileViewModel>({ activity as EditProfileActivity }) { viewModelFactory }
    private var _binding: FragmentMyInformationSearchableBinding? = null
    private val binding get() = _binding!!

    private var adapter: MyInformationChoicesAdapter? = null
    private lateinit var types: Map<String?, MyInformationSearchableType>
    private lateinit var fallbackChoices: List<MyInformationChoicesModel>
    private var initialSelectedItems: MutableList<Int> = mutableListOf()


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as EditProfileActivity).editProfileComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            type = it.getString(MY_INFORMATION_TYPE)
        }
        types = mapOf(MyInformationSearchableConstants.ACTIVITIES to ACTIVITIES,
                MyInformationSearchableConstants.HOMETOWN to HOMETOWN,
                MyInformationSearchableConstants.LANGUAGES to LANGUAGES)

        viewModel.getSelectedChoices(types.getValue(type)).apply {
            val newItems = this?.map { it.id } ?: emptyList()
            initialSelectedItems.addAll(newItems.filterNot { initialSelectedItems.contains(it) })
        }

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        _binding = FragmentMyInformationSearchableBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner

        binding.type = types.getValue(type)

        viewModel.setDefaultValueForUpdatedValues(value = types.getValue(type))
        fallbackChoices = viewModel.getChoices(types.getValue(type))!!



        binding.searchTextView.setOnSingleClickListener {
            val argument = it.tag as String
            when (types.getValue(type)) {

                ACTIVITIES -> it.findNavController().navigateSafer(R.id.action_activitiesFragment_to_searchFragment, bundleOf(SEARCHABLE_TYPE to argument, ITEMS_TYPE_TO_SEARCH to ACTIVITIES))

                LANGUAGES -> it.findNavController().navigateSafer(R.id.action_languagesFragment_to_searchFragment, bundleOf(SEARCHABLE_TYPE to argument, ITEMS_TYPE_TO_SEARCH to LANGUAGES))

                HOMETOWN -> it.findNavController().navigateSafer(R.id.action_hometownFragment_to_searchFragment, bundleOf(SEARCHABLE_TYPE to argument, ITEMS_TYPE_TO_SEARCH to HOMETOWN))

                else -> {
                }
            }
        }

        adapter = MyInformationChoicesAdapter(MyInformationChoicesAdapter.MyInformationChoicesClickListener { model, position ->
            when (model.type) {
                ACTIVITIES, LANGUAGES -> {
                    viewModel.checkMultipleSelected(model, model.type, position)
                    viewModel.checkPercentageForMyInformationChoices()
                    checkForFabState()
                }
                else -> {
                    viewModel.checkSelected(model, model.type, position)
                    viewModel.checkPercentageForMyInformationChoices()
                    checkForFabState()
                }
            }
        })

        when (val type = types.getValue(type)) {
            ACTIVITIES -> {
                viewModel.navigateMyInformationActivities.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {
                            binding.editProgress.visibility = View.GONE
                            if(viewModel.fromBackPress != true) {
                                if (viewModel.shouldShowHometown()) {
                                    findNavController().navigateSafer(R.id.action_activitiesFragment_to_hometownFragment,
                                        bundleOf(MY_INFORMATION_TYPE to MyInformationSearchableConstants.HOMETOWN))
                                } else {
                                    findNavController().navigateSafer(R.id.action_activitiesFragment_to_languagesFragment,
                                        bundleOf(MY_INFORMATION_TYPE to MyInformationSearchableConstants.LANGUAGES))
                                }
                            } else viewModel.fromBackPress = false

                        }
                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_MY_INFORMATION)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }
                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.activitiesChoices.observe(viewLifecycleOwner) {
                    adapter?.submitList(it)
                }

                viewModel.updateActivities.observe(viewLifecycleOwner) {
                    val ids = mutableListOf<Int>()
                    if (it) {
                        viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }
                        if (initialSelectedItems == ids) {
                            viewModel.setSuccessResultForField(1)
                        }
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(1, ids))

                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.ACTIVITIES.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.ACTIVITIES.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.ACTIVITIES.value
                                    )
                                )

                                sendClevertapEvent(
                                    ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.ACTIVITIES.value
                                ))
                            }
                        }
                    }

                }

            }

            LANGUAGES -> {
                viewModel.navigateMyInformationLanguages.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {
                            binding.editProgress.visibility = View.GONE
                            if(viewModel.fromBackPress != true){
                                findNavController().navigateSafer(
                                    R.id.action_languagesFragment_to_descriptionFragment,
                                    bundleOf(EditProfileConstants.NOT_SEARCHABLE_TYPE to EditProfileConstants.MyInformationConstants.DESCRIPTION)
                                )
                            } else viewModel.fromBackPress = false

                        }

                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_MY_INFORMATION)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }

                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.languageChoices.observe(viewLifecycleOwner) {
                    adapter?.submitList(it)
                }
                viewModel.updateLanguage.observe(viewLifecycleOwner) {
                    val ids = arrayListOf<Int>()
                    if (it) {
                       viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }

                        if (initialSelectedItems == ids) {
                            viewModel.setSuccessResultForField(2)
                        }
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(2, ids))
                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.LANGUAGES.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.LANGUAGES.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.LANGUAGES.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.LANGUAGES.value
                                ))

                            }
                        }
                    }

                }
            }

            HOMETOWN -> {
                viewModel.navigateMyInformationHometown.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {
                            binding.editProgress.visibility = View.GONE
                            if(viewModel.fromBackPress != true){
                                findNavController().navigateSafer(
                                    R.id.action_hometownFragment_to_languagesFragment,
                                    bundleOf(MY_INFORMATION_TYPE to MyInformationSearchableConstants.LANGUAGES)
                                )
                            } else viewModel.fromBackPress = false

                        }

                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_MY_INFORMATION)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }

                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.hometownChoices.observe(viewLifecycleOwner) {
                    adapter?.submitList(it)
                }
                viewModel.updateHometown.observe(viewLifecycleOwner) {
                    val ids = arrayListOf<Int>()
                    if (it) {
                        viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }
                        if (initialSelectedItems == ids) viewModel.setSuccessResultForField(5)
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(5, ids))

                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.HOMETOWN.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.HOMETOWN.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.HOMETOWN.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.HOMETOWN.value
                                ))
                            }
                        }
                    }

                }
            }

            else -> {
            }
        }

        val recyclerView = binding.recyclerViewOptions
        recyclerView.adapter = adapter

        return binding.root
    }

    private fun checkForFabState() {
        val selectedChoices = viewModel.getSelectedChoices(types.getValue(type))
        if (selectedChoices != null) {
            if (selectedChoices.isNotEmpty()) {
                viewModel.setFabNextVisibility(FloatingActionButtonVisibility.SHOWN)
            } else {
                viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
            }
        }
    }


    override fun onResume() {
        super.onResume()
        hideKeyboard()
        checkForFabState()
        when (types.getValue(type)) {
            ACTIVITIES -> (requireActivity() as EditProfileActivity).setToolbarTitle(requireContext().getString(R.string.activities))
            LANGUAGES -> (requireActivity() as EditProfileActivity).setToolbarTitle(requireContext().getString(R.string.languages))
            HOMETOWN -> (requireActivity() as EditProfileActivity).setToolbarTitle(requireContext().getString(R.string.hometown))
            else -> {
            }
        }

    }

    override fun onDestroyView() {
        when (val result = viewModel.getVariableByType(types.getValue(type))) {
            is Resource.Error -> {
                if (result.data == -2) {
                    if (findNavController().currentDestination!!.id != R.id.searchFragment)
                        viewModel.setChoices(fallbackChoices, types.getValue(type))
                }
            }
            else -> {
            }
        }
        super.onDestroyView()
        _binding = null
        adapter = null
    }
}

