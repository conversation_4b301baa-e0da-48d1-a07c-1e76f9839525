package com.duaag.android.profile_new.editprofile.profile_progress

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.databinding.FragmentProfileProgressBinding
import com.duaag.android.profile_new.editprofile.EditProfileActivity
import com.duaag.android.profile_new.editprofile.EditProfileConstants
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.getStringPlaceHolder
import com.duaag.android.utils.updateLocale
import javax.inject.Inject


class ProfileProgressFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    private val viewModel by viewModels<ShareEditProfileViewModel>({ activity as EditProfileActivity }) { viewModelFactory }
    private var _binding: FragmentProfileProgressBinding? = null
    private val binding get() = _binding!!

    private var adapter: ProfileProgressAdapter? = null
    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as EditProfileActivity).editProfileComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        _binding = FragmentProfileProgressBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner

        binding.title.text = getStringPlaceHolder(getString(R.string.profile_progress_lay_title_1),getString(R.string.profile_progress_lay_title_2_an,getString(R.string.app_name)),getString(R.string.profile_progress_lay_title_3),R.color.description_primary,requireContext(),R.font.tt_norms_pro_normal)


        adapter = ProfileProgressAdapter(ProfileProgressAdapter.ProfileProgressItemClickListener { model ->
            if (model.isFilled) return@ProfileProgressItemClickListener

            when (model.type) {
          /*      1 -> {
                    requireActivity().setResult(Activity.RESULT_OK)
                    requireActivity().finish()
                }
                2 -> {
                    val intent = Intent(requireActivity(), SettingsActivity::class.java)

                            intent.putExtra(
                                SettingsActivity.SETTINGS_ACTIVITY_START_DESTINATION_INTENT,
                                "Account_Verification"
                            )
                            startActivity(intent)

                    requireActivity().finish()
                }
                3 -> {
                    findNavController().navigate(R.id.action_profileProgressFragment_to_jobEducationFragment, bundleOf(EditProfileConstants.NOT_SEARCHABLE_TYPE to "job_education"))
                }
                4 -> {
                    findNavController().navigate(R.id.action_profileProgressFragment_to_lookingForFragment, bundleOf(EditProfileConstants.ADDITIONAL_INFO_TYPE to "looking_for"))
                }
            */

                EditProfileConstants.ProgressItemType.PROFILE_PICTURE -> {
                    requireActivity().setResult(Activity.RESULT_OK)
                    requireActivity().finish()
                }
                EditProfileConstants.ProgressItemType.BADGES -> {
                    val intent = Intent(requireActivity(), SettingsActivity::class.java)

                    intent.putExtra(
                        SettingsActivity.SETTINGS_ACTIVITY_START_DESTINATION_INTENT,
                        "Account_Verification"
                    )
                    startActivity(intent)

                    requireActivity().finish()
                }
                EditProfileConstants.ProgressItemType.MY_INFORMATION -> {
                    findNavController().navigate(R.id.action_profileProgressFragment_to_jobEducationFragment, bundleOf(EditProfileConstants.NOT_SEARCHABLE_TYPE to "job_education"))

                }
                EditProfileConstants.ProgressItemType.ADDITIONAL_INFORMATION -> {
                    findNavController().navigate(R.id.action_profileProgressFragment_to_lookingForFragment, bundleOf(EditProfileConstants.ADDITIONAL_INFO_TYPE to "looking_for"))

                }
            }

        })
        viewModel.profileProgressItems.observe(viewLifecycleOwner, {
            adapter?.submitList(it)
        })
        val recyclerView = binding.recyclerViewProfileProgress
        recyclerView.setHasFixedSize(true)
        recyclerView.adapter = adapter


        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        adapter = null
    }

}