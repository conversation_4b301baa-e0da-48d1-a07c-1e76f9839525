package com.duaag.android.profile_new.editprofile


import android.animation.ObjectAnimator
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.BitmapFactory
import android.graphics.BitmapShader
import android.graphics.Shader
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.fragment.BaseFragment
import com.duaag.android.base.models.INSTAGRAM_STATUS_CONNECTED
import com.duaag.android.base.models.INSTAGRAM_STATUS_DISCONNECTED
import com.duaag.android.base.models.INSTAGRAM_STATUS_TOKEN_REFRESH_ERROR
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEditProfileSourceValues
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum.ACTUAL_PROFILE_PERCENTAGE
import com.duaag.android.clevertap.ClevertapEventPropertyEnum.EDIT_PROFILE_SOURCE
import com.duaag.android.clevertap.ClevertapEventPropertyEnum.PREMIUM_TYPE
import com.duaag.android.clevertap.ClevertapEventPropertyEnum.PREVIOUS_PROFILE_PERCENTAGE
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapUserPropertyEnum
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.updateUserProfileInClevertap
import com.duaag.android.databinding.FragmentEditProfileBinding
import com.duaag.android.exceptions.PremiumException
import com.duaag.android.home.models.DeepLinkScreenNames
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.instagram.InstagramAuthenticationDialog
import com.duaag.android.instagram.adapters.InstagramImagesAdapter
import com.duaag.android.launcher.SplashActivity.Companion.DEEP_LINK_SCREENS
import com.duaag.android.launcher.SplashActivity.Companion.SCREENS_INTENT_FILTER
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.premium_subscription.showBillingNotAvailable
import com.duaag.android.profile_new.editprofile.EditProfileConstants.ADDITIONAL_INFO_TYPE
import com.duaag.android.profile_new.editprofile.EditProfileConstants.MY_INFORMATION_TYPE
import com.duaag.android.profile_new.editprofile.EditProfileConstants.NOT_SEARCHABLE_TYPE
import com.duaag.android.profile_new.editprofile.profile_progress.ProfileProgressItem
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.livedata.observeOnce
import com.duaag.android.utils.setMargin
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.ImageViewerDialog
import com.giphy.sdk.ui.utils.px
import com.google.android.material.switchmaterial.SwitchMaterial
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import sendVerifyYourProfileInitiatedAnalyticsEvent
import timber.log.Timber
import javax.inject.Inject


class EditProfileFragment : BaseFragment() {


    companion object {
        const val TAG = "EditProfileTag"
    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ShareEditProfileViewModel>({ activity as EditProfileActivity }) { viewModelFactory }
    private var _binding: FragmentEditProfileBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as EditProfileActivity).editProfileComponent.inject(this)
    }

    override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentEditProfileBinding.inflate(inflater)
        binding.let {
            it.lifecycleOwner = viewLifecycleOwner
            it.viewModel = viewModel

            setPercentageTextColor()

            viewModel.requestUserProfile.observe(viewLifecycleOwner) {
                if (it) {
                    viewModel.userRepository.getUserProfile().observe(viewLifecycleOwner) {
                        when (it) {
                            is Result.Success -> {

                                if (viewModel.updatePercentage) {



                                    val editProfileSource = (activity?.intent?.getStringExtra(EditProfileActivity.EDIT_PROFILE_SOURCE) ?: ClevertapEditProfileSourceValues.PROFILE.value)

                                    val eventPremiumType = getPremiumTypeEventProperty(it.data)

                                    sendClevertapEvent(ClevertapEventEnum.COMPLETED_PROFILE_PERCENTAGE,
                                        mapOf(
                                            PREMIUM_TYPE.propertyName to eventPremiumType,
                                            EDIT_PROFILE_SOURCE.propertyName to editProfileSource,
                                            PREVIOUS_PROFILE_PERCENTAGE.propertyName to viewModel.userProfile.value?.profilePercentage,
                                            ACTUAL_PROFILE_PERCENTAGE.propertyName to it.data.profilePercentage))

                                    when (it.data.profilePercentage) {
                                        in 1..20 -> {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.COMPLETED_PROFILE_PERCENTAGE,
                                                mapOf(
                                                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                                    FirebaseAnalyticsParameterName.ZERO_TWENTY.value to 1L,
                                                    FirebaseAnalyticsParameterName.EDIT_PROFILE_SOURCE.value to editProfileSource
                                                )
                                            )
                                        }
                                        in 21..40 -> {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.COMPLETED_PROFILE_PERCENTAGE,
                                                mapOf(
                                                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                                    FirebaseAnalyticsParameterName.TWENTYONE_FORTY.value to 1L,
                                                    FirebaseAnalyticsParameterName.EDIT_PROFILE_SOURCE.value to editProfileSource
                                                )
                                            )
                                        }
                                        in 41..60 -> {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.COMPLETED_PROFILE_PERCENTAGE,
                                                mapOf(
                                                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                                    FirebaseAnalyticsParameterName.FORTYONE_SIXTY.value to 1L,
                                                    FirebaseAnalyticsParameterName.EDIT_PROFILE_SOURCE.value to editProfileSource
                                                )
                                            )


                                        }
                                        in 61..80 -> {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.COMPLETED_PROFILE_PERCENTAGE,
                                                mapOf(
                                                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                                    FirebaseAnalyticsParameterName.SIXTYONE_EIGHTY.value to 1L,
                                                    FirebaseAnalyticsParameterName.EDIT_PROFILE_SOURCE.value to editProfileSource
                                                )
                                            )
                                        }
                                        in 81..100 -> {
                                            firebaseLogEvent(
                                                FirebaseAnalyticsEventsName.COMPLETED_PROFILE_PERCENTAGE,
                                                mapOf(
                                                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                                    FirebaseAnalyticsParameterName.EIGHTYONE_ONEHUNDRED.value to 1L,
                                                    FirebaseAnalyticsParameterName.EDIT_PROFILE_SOURCE.value to editProfileSource
                                                )
                                            )

                                        }
                                    }
                                } else viewModel.getUpdatedPercentage()

                                viewModel.setProfileProgress(it.data.profilePercentage.toString())
                                initVerificationUiAndLogic(it.data)


                                val percentageKey = ClevertapUserPropertyEnum.PROFILE_COMPLETION_PERCENTAGE.value
                                val percentageValue = it.data.profilePercentage
                                val clevertapUserProperties = mapOf(percentageKey to percentageValue)
                                updateUserProfileInClevertap(clevertapUserProperties)
                            }
                            is Result.Error -> {
                            }
                            Result.Loading -> {
                            }
                        }
                    }
                }
            }

            viewModel.userProfile.observe(viewLifecycleOwner) {
                it?.let { userModel ->
                    initVerificationUiAndLogic(userModel)
                    setHometownVisibility()
                    bindZodiacAddTextView(userModel)
                    viewModel.updatePremiumTagData(it)
                }
            }
            viewModel.dataUi
                .observe(viewLifecycleOwner){uiData->
                    setVisibility(binding.premiumTag, uiData.premiumTagData?.premiumType == null)
                    binding.premiumModeSwitch.isChecked = uiData.premiumTagData?.showPremiumTag ?: false
                    if(uiData.premiumTagData?.error is PremiumException) {
                        viewModel.updatePremiumTagState(error = null)
                        requireActivity().openPremiumPaywall(
                            eventSourceClevertap = ClevertapEventSourceValues.PREMIUM_SUBSCRIPTION_PREMIUM_BADGE,
                            placementId = PurchaselyPlacement.PREMIUM_SUBSCRIPTION_PREMIUM_BADGE.id,
                            userModel = viewModel.userProfile.value
                        )
                    }
                }
            it.cardZodiac.setOnSingleClickListener{v->
             v.findNavController().navigate(
                 R.id.zodiacFragment
             )
            }
            it.cardJob.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.jobEducationFragment, bundleOf(
                        NOT_SEARCHABLE_TYPE to (argument as String)
                )
                )
            }

            it.cardActivities.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.activitiesFragment,
                        bundleOf(MY_INFORMATION_TYPE to (argument as String))
                )
            }

            it.cardHometown.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.hometownFragment,
                        bundleOf(MY_INFORMATION_TYPE to (argument as String))
                )
            }

            it.cardLanguages.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.languagesFragment,
                        bundleOf(MY_INFORMATION_TYPE to (argument as String))
                )
            }

            it.cardDescription.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.descriptionFragment, bundleOf(
                        NOT_SEARCHABLE_TYPE to (argument as String)
                )
                )
            }

            it.cardLookingFor.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.lookingForFragment, bundleOf(
                        ADDITIONAL_INFO_TYPE to (argument as String)
                )
                )
            }

            it.cardHeight.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(R.id.addUserHeightBottomSheet2)
            }

            it.cardSmoking.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.smokingFragment,
                        bundleOf(ADDITIONAL_INFO_TYPE to (argument as String))
                )
            }

            it.cardPets.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.petsFragment,
                        bundleOf(ADDITIONAL_INFO_TYPE to (argument as String))
                )
            }

            it.cardReligion.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.religionFragment,
                        bundleOf(ADDITIONAL_INFO_TYPE to (argument as String))
                )
            }

            it.cardHaveChildren.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.haveChildrenFragment,
                        bundleOf(ADDITIONAL_INFO_TYPE to (argument as String))
                )
            }

            it.cardWantChildren.setOnSingleClickListener { v ->
                val argument = v.tag
                v.findNavController().navigate(
                        R.id.wantChildrenFragment,
                        bundleOf(ADDITIONAL_INFO_TYPE to (argument as String))
                )
            }

            viewModel.profileProgress.observe(viewLifecycleOwner) {
                val percentage = it.plus("%")
                binding.percentage.text = percentage
            }

            viewModel.instagramClicked.observe(viewLifecycleOwner) {
                binding.scrollView.let { scroll ->
                    lifecycleScope.launchWhenResumed {
                        scroll.post { scroll.scrollTo(0, scroll.bottom) }
                        binding.cardInstagram.performClick()
                    }
                }
            }
        }

        setupInstagramConnect()
        initBaseFunctions()
        observeProfileProgressItemState()
        return binding.root
    }

    private fun observeProfileProgressItemState() {
        val emptyProgressItems: ArrayList<ProfileProgressItem?> = arrayListOf()

        viewModel.profileProgressItems.observeOnce(viewLifecycleOwner) { user ->
            if (user == null)
                return@observeOnce

            emptyProgressItems.addAll(user)
        }

        viewModel.userProfile.observe(viewLifecycleOwner) { user ->
            if(user == null) return@observe

            val isVerified = user.hasBadge1
            val hasBadge2: Boolean =
                user.badge2 == Badge2Status.PROCESSING.status || user.badge2 == Badge2Status.APPROVED.status

            updateProgressItem(EditProfileConstants.ProgressItemType.PROFILE_PICTURE, emptyProgressItems,user.profile.pictures.size >= 2)
            updateProgressItem(EditProfileConstants.ProgressItemType.BADGES, emptyProgressItems,isVerified && hasBadge2)

        }

        viewModel.areFieldsCompleted.observe(viewLifecycleOwner) {
            val listOfMyInformationFields = it.getMyInformationMembersList()
            val listOfAdditionalInfoFields = it.getAdditionalInfoMembersList()
            updateProgressItem(EditProfileConstants.ProgressItemType.MY_INFORMATION,emptyProgressItems, !listOfMyInformationFields.contains(false))
            updateProgressItem(EditProfileConstants.ProgressItemType.ADDITIONAL_INFORMATION, emptyProgressItems,!listOfAdditionalInfoFields.contains(false))

            viewModel.setProfileProgressItems(emptyProgressItems)
        }

        binding.cardPremium.setOnSingleClickListener{
            binding.premiumModeSwitch.performClick()
        }

        binding.premiumModeSwitch.setOnSingleClickListener {view ->
            if(!DuaApplication.instance.getBillingAvailable() && viewModel.userProfile.value?.premiumType == null){
                requireActivity().showBillingNotAvailable(
                    title = getString(R.string.billings_not_available_title_an),
                    message = getString(R.string.billings_not_available_desc_an)
                )
                (view as? SwitchMaterial)?.apply {
                    this.isChecked = false
                }

                return@setOnSingleClickListener
            }
            viewModel.togglePremiumTag(binding.premiumModeSwitch.isChecked)
        }
    }

    private fun bindZodiacAddTextView(userModel: UserModel?) {
        val zodiacSign = userModel?.zodiacSign
        val hasPublicZodiacSign = userModel?.hasPublicZodiacSign
        if (!zodiacSign.isNullOrEmpty() && hasPublicZodiacSign == true) {
            binding.cardZodiacAddTextview.text = zodiacSign.replaceFirstChar { it.uppercase() }
            binding.cardZodiacAddTextview.setTextColor(ContextCompat.getColor(requireContext(), R.color.description_primary))
            binding.arrowRightZodiac.visibility = View.VISIBLE
            binding.cardZodiacAddTextview.setMargin(end = 4F)
        } else {
            binding.cardZodiacAddTextview.text = requireContext().getString(R.string.add)
            binding.cardZodiacAddTextview.setTextColor(ContextCompat.getColor(requireContext(), R.color.pink_500))
            binding.arrowRightZodiac.visibility = View.GONE
            binding.cardZodiacAddTextview.setMargin(end = 12F)
        }
    }

    override fun initialize() {
    }

    override fun setToolbar() {
    }

    override fun observeErrors() {
    }

    override fun setCallBacks() {

    }
    private fun updateProgressItem(type: EditProfileConstants.ProgressItemType, items: ArrayList<ProfileProgressItem?>, isFilled: Boolean): ArrayList<ProfileProgressItem?> {
        val item = items.find { it?.type == type }
        item?.let {
            val index = items.indexOf(it)
            items[index] = it.copy(isFilled = isFilled)
        }
        return items
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        (requireActivity() as EditProfileActivity).setToolbarInfoClicklistener(null)
    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as EditProfileActivity).setToolbarTitle(getString(R.string.edit_my_profile))
    }

    private fun setupInstagramConnect() {
        binding.instagramMedia.visibility = View.GONE

        viewModel.instagramMedia.observe(viewLifecycleOwner) { list ->
            binding.instagramMedia.visibility =
                if (list.isNullOrEmpty()) View.GONE else View.VISIBLE
            binding.instagramView.visibility = if (list.isNullOrEmpty()) View.VISIBLE else View.GONE

            list?.let {
                if (list.size >= 8) {
                    binding.instagramMedia.layoutManager = GridLayoutManager(
                        requireContext(),
                        2,
                        GridLayoutManager.HORIZONTAL,
                        false
                    )
                } else {
                    binding.instagramMedia.layoutManager = LinearLayoutManager(
                        requireContext(),
                        LinearLayoutManager.HORIZONTAL,
                        false
                    )
                }

                binding.instagramMedia.adapter =
                    InstagramImagesAdapter(InstagramImagesAdapter.EDIT_PROFILE_VIEW,
                        object : InstagramImagesAdapter.InstagramImageClickListener {
                            override fun onImageClick(images: List<String>, index: Int) {
                                ImageViewerDialog.newInstance(images.take(30), index).show(
                                    childFragmentManager,
                                    "ImageViewer"
                                )
                            }
                        })
                (binding.instagramMedia.adapter as InstagramImagesAdapter).setData(it.take(30))
            }
        }

        viewModel.userProfile.observe(viewLifecycleOwner) { user ->
            if(user == null)
                return@observe

            when (user.instagramStatus) {
                INSTAGRAM_STATUS_CONNECTED -> {
                    binding.addInstagram.text = getString(R.string.disconnect)
                    binding.addInstagram.setTextColor(
                        ContextCompat.getColor(
                            requireContext(),
                            R.color.description_secondary
                        )
                    )
                    binding.instagramTitle.text = getString(R.string.instagram)
                    binding.instagramText.visibility = View.GONE

                    val params =
                        binding.imageViewInstagram.layoutParams as ConstraintLayout.LayoutParams
                    params.topMargin = convertDpToPixel(32f, requireContext()).toInt()
                    binding.imageViewInstagram.layoutParams = params

                    binding.imageViewInstagram.setBackgroundResource(R.color.transparent)
                    binding.imageViewInstagram.setImageResource(R.drawable.ic_instagramloggo)
                    setDisconnectInstagramClickListener()
                }
                INSTAGRAM_STATUS_DISCONNECTED -> {
                    //instagramDisconnectedUiState()
                    hideInstagramUi()
                }
                INSTAGRAM_STATUS_TOKEN_REFRESH_ERROR -> {
                    binding.addInstagram.text = getString(R.string.connect)
                    binding.instagramTitle.text = getString(R.string.connect_with_instagram)
                    binding.instagramText.visibility = View.VISIBLE

                    val params =
                        binding.imageViewInstagram.layoutParams as ConstraintLayout.LayoutParams
                    params.topMargin = convertDpToPixel(16f, requireContext()).toInt()
                    binding.imageViewInstagram.layoutParams = params

                    binding.imageViewInstagram.setImageResource(R.drawable.ic_instagram_outline)
                    setConnectInstagramClickListener()

                    showInstagramReconnectDialog()
                }
                null-> {
                    hideInstagramUi()
                }
                else -> {
                 //   instagramDisconnectedUiState()
                    hideInstagramUi()
                }
            }
        }

        viewModel.instagramTokenRefreshFailed.observe(viewLifecycleOwner, Observer {
            showInstagramReconnectDialog()
        })

        if (viewModel.userProfile.value?.instagramStatus == INSTAGRAM_STATUS_CONNECTED && viewModel.instagramMedia.value.isNullOrEmpty()) {
            viewModel.getInstagramMedia()
        }

    }

    private fun hideInstagramUi() {
        binding.instagramMedia.visibility = View.GONE
        binding.cardInstagram.visibility = View.GONE
    }

    private fun setConnectInstagramClickListener() {
        binding.cardInstagram.setOnSingleClickListener { v ->
            val dialog = InstagramAuthenticationDialog(requireContext()) {
                viewModel.connectInstagram(it)
                Timber.tag("INSTAGRAM").d("INSTAGRAM: $it")
            }
            dialog.show()
        }
    }

    private fun setPercentageTextColor() {
        try {
            val bitmap = BitmapFactory.decodeResource(resources, R.drawable.text_color_gradient)
            val shader = BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
            binding.percentage.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            binding.percentage.paint.shader = shader
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setDisconnectInstagramClickListener() {
        binding.cardInstagram.setOnSingleClickListener { v ->
            val builder = AlertDialog.Builder(requireContext())
            builder.setMessage(R.string.are_you_sure_you_want_to_disconnect_from_instagram)
            builder.setPositiveButton(R.string.yes) { dialogInterface, i ->
                viewModel.disconnectInstagram()
            }
            builder.setNegativeButton(R.string.no, null)

            val dialog = builder.create()
            dialog.setOnShowListener { dialog1: DialogInterface? ->
                dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(
                        ContextCompat.getColor(
                                requireContext(),
                                R.color.pink_500
                        )
                )
                dialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(
                        ContextCompat.getColor(
                                requireContext(),
                                R.color.pink_500
                        )
                )
            }
            dialog.show()
        }
    }

    fun showInstagramReconnectDialog() {
        val builder = AlertDialog.Builder(requireContext())
        builder.setMessage(R.string.instagram_token_refresh_error)

        builder.setPositiveButton(R.string.connect) { dialogInterface, i ->
            val dialog = InstagramAuthenticationDialog(requireContext()) {
                lifecycleScope.launch {
                    viewModel.reconnectInstagram(it)
                            .catch { e -> e.printStackTrace() }
                            .collect { Timber.tag(TAG).d(it.toString()) }
                }
                Timber.tag("INSTAGRAM").d("INSTAGRAM: $it")
            }
            dialog.show()
        }

        builder.setNegativeButton(R.string.cancel) { dialogInterface, i ->
            lifecycleScope.launch {
                viewModel.disconnectInstagram()

            }
        }

        val dialog = builder.create()
        dialog.setOnShowListener { dialog1: DialogInterface? ->
            dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(
                    ContextCompat.getColor(
                            requireContext(),
                            R.color.pink_500
                    )
            )
            dialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(
                    ContextCompat.getColor(
                            requireContext(),
                            R.color.pink_500
                    )
            )
        }
        dialog.show()
    }

    private fun instagramDisconnectedUiState() {
        binding.addInstagram.text = getString(R.string.connect)
        binding.instagramTitle.text = getString(R.string.connect_with_instagram)
        binding.instagramText.visibility = View.VISIBLE

        val params = binding.imageViewInstagram.layoutParams as ConstraintLayout.LayoutParams
        params.topMargin = convertDpToPixel(16f, requireContext()).toInt()
        binding.imageViewInstagram.layoutParams = params

        binding.imageViewInstagram.setImageResource(R.drawable.ic_instagram_outline)
        setConnectInstagramClickListener()
    }

    private fun initVerificationUiAndLogic(userModel: UserModel) {
        val hasBadge1 = userModel.hasBadge1
        val badge2 = userModel.badge2
        val badge2Status: Badge2Status = when (badge2) {
            Badge2Status.APPROVED.status -> Badge2Status.APPROVED
            Badge2Status.PROCESSING.status -> Badge2Status.PROCESSING
            Badge2Status.NOT_APPROVED.status -> Badge2Status.NOT_APPROVED
            Badge2Status.NULL.status -> Badge2Status.NULL
            else -> Badge2Status.NULL
        }
        initBadge1Badge2View(hasBadge1, badge2Status)
        setProfileVersificationConstants(hasBadge1, badge2Status)

        //Handle clicks
        binding.imageVerificationLayout.setOnClickListener {
            if (badge2Status != Badge2Status.APPROVED && badge2Status != Badge2Status.PROCESSING) {

                val eventPremiumType = getPremiumTypeEventProperty(viewModel.userProfile.value)

                sendVerifyYourProfileInitiatedAnalyticsEvent(
                    ClevertapVerificationSourceValues.EDIT_PROFILE.value,
                    eventPremiumType
                )

                firebaseLogEvent(
                        FirebaseAnalyticsEventsName.INITIATE_IMAGE_VERIFICATION, mapOf(
                        FirebaseAnalyticsParameterName.IMAGE_VERIFICATION_INITIATE_INFORMATION.value to 1L)
                )

                val intent = Intent(SCREENS_INTENT_FILTER)
                intent.apply {
                    `package` = requireContext().packageName
                }
                intent.putExtra(DEEP_LINK_SCREENS, DeepLinkScreenNames.VERIFY_IMAGE.value)
                intent.putExtra(EVENT_SOURCE, ClevertapVerificationSourceValues.EDIT_PROFILE.value)
                requireActivity().sendBroadcast(intent)

                requireActivity().finish()
            }
        }

        binding.badge1VerificationLayout.setOnClickListener {
            if (!hasBadge1 && viewModel.authMethod != null) {
                val intent = Intent(requireActivity(), SettingsActivity::class.java)

                when (viewModel.authMethod) {
                    AuthMethod.EMAIL -> {
                        intent.putExtra(
                                SettingsActivity.SETTINGS_ACTIVITY_START_DESTINATION_INTENT,
                                "Email_Verification"
                        )
                        startActivity(intent)
                    }
                    AuthMethod.PHONE, AuthMethod.FACEBOOK -> {
                        intent.putExtra(
                                SettingsActivity.SETTINGS_ACTIVITY_START_DESTINATION_INTENT,
                                "Phone_Verification"
                        )
                        startActivity(intent)
                    }
                    else -> {}
                }
            }
        }

    }

    private fun initBadge1Badge2View(hasBadge1: Boolean, badge2Status: Badge2Status) {

        //imageVerification

        if (badge2Status == Badge2Status.NULL) {
            binding.fragmeBadge2.visibility = View.GONE
            binding.imageViewBadge2.visibility = View.VISIBLE
        } else {
            binding.fragmeBadge2.visibility = View.VISIBLE
            binding.imageViewBadge2.visibility = View.INVISIBLE
        }

        when (badge2Status) {
            Badge2Status.APPROVED -> {
                binding.badgeApproved.setImageResource(R.drawable.ic_image_verification)
                binding.badgeApproved.visibility = View.VISIBLE
                binding.badgeProcessing.visibility = View.INVISIBLE
                binding.badgeApproved.alpha = 1f

                binding.addBadge2.text = getString(R.string.verified_string)
                binding.addBadge2.setTextColor(ContextCompat.getColor(requireContext(), R.color.others))
                binding.addBadge2.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
            }
            Badge2Status.NOT_APPROVED -> {
                binding.badgeApproved.setImageResource(R.drawable.ic_image_verification_failed)
                binding.badgeApproved.visibility = View.VISIBLE
                binding.badgeProcessing.visibility = View.INVISIBLE
                binding.badgeApproved.alpha = 1f

                binding.addBadge2.text = getString(R.string.verify)
                binding.addBadge2.setTextColor(ContextCompat.getColor(requireContext(), R.color.pink_500))
                binding.addBadge2.compoundDrawableTintList = ColorStateList.valueOf(ContextCompat.getColor(requireContext(), R.color.pink_500))
            }
            Badge2Status.PROCESSING -> {
                binding.badgeApproved.setImageDrawable(binding.badgeApproved.context?.let { ContextCompat.getDrawable(it, R.drawable.ic_image_verification) })
                binding.badgeProcessing.setImageDrawable(binding.badgeProcessing.context?.let { ContextCompat.getDrawable(it, R.drawable.ic_image_verification_processinng) })
                binding.badgeApproved.visibility = View.VISIBLE
                binding.badgeProcessing.visibility = View.VISIBLE
                binding.badgeProcessing.alpha = 1f

                binding.addBadge2.text = getString(R.string.processing)
                binding.addBadge2.setTextColor(ContextCompat.getColor(requireContext(), R.color.gray_200))
                binding.addBadge2.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
                val fadeAnimator2 = ObjectAnimator.ofFloat(binding.badgeApproved, "alpha", 1f, 0f).apply {
                    duration = 800
                    repeatCount = ObjectAnimator.INFINITE
                    repeatMode = ObjectAnimator.REVERSE
                }

                fadeAnimator2.start()
            }
            Badge2Status.NULL -> {
                binding.addBadge2.text = getString(R.string.verify)
                binding.addBadge2.setTextColor(ContextCompat.getColor(requireContext(), R.color.pink_500))
                binding.addBadge2.compoundDrawableTintList = ColorStateList.valueOf(ContextCompat.getColor(requireContext(),R.color.pink_500))
            }
        }

        //Badge1 verification
        if (hasBadge1) {
            binding.imageViewBadge1.background = null
            binding.addBadge1.text = getString(R.string.verified_string)
            binding.addBadge1.setTextColor(ContextCompat.getColor(requireContext(), R.color.others))
            binding.addBadge1.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
            binding.badge1Title.text = getString(R.string.verified)
        } else {
            binding.addBadge1.text = getString(R.string.verify)
            binding.addBadge1.setTextColor(ContextCompat.getColor(requireContext(), R.color.pink_500))
            binding.addBadge1.compoundDrawableTintList = ColorStateList.valueOf(ContextCompat.getColor(requireContext(),R.color.pink_500))

            viewModel.accountModel.observe(viewLifecycleOwner) { accountModel ->
                accountModel?.let {
                    when {
                        it.phone == null -> {
                            binding.badge1Title.text = getString(R.string.phone_verification)
                            viewModel.authMethod = AuthMethod.PHONE
                        }
                        it.email == null -> {
                            binding.badge1Title.text = getString(R.string.email_verification)
                            viewModel.authMethod = AuthMethod.EMAIL
                        }
                    }
                }
            }

        }
    }

    private fun setProfileVersificationConstants(hasBadge1: Boolean, badge2Status: Badge2Status) {
        //global parent
        val constraintGlobalLayout: ConstraintLayout? = binding.parentLayout
        val constraintGlobalSet = ConstraintSet()
        constraintGlobalSet.clone(constraintGlobalLayout)
        if (hasBadge1 && (badge2Status != Badge2Status.NOT_APPROVED && badge2Status != Badge2Status.NULL)) {

            //card_verification
            constraintGlobalSet.connect(
                    R.id.card_verification,
                    ConstraintSet.TOP,
                    R.id.card_want_children,
                    ConstraintSet.BOTTOM,
                    44.px
            )
            //my_information
            constraintGlobalSet.connect(
                    R.id.textView4,
                    ConstraintSet.TOP,
                    R.id.card_premium,
                    ConstraintSet.BOTTOM,
               44.px
            )
            //card_instagram
            constraintGlobalSet.connect(
                    R.id.card_instagram,
                    ConstraintSet.TOP,
                    R.id.card_verification,
                    ConstraintSet.BOTTOM,
          0
            )
            //card_zodiac_title
            constraintGlobalSet.connect(
                R.id.card_zodiac_title,
                ConstraintSet.TOP,
                R.id.textView15,
                ConstraintSet.BOTTOM,
           44.px
            )

            //card_zodiac
            constraintGlobalSet.connect(
                R.id.card_zodiac,
                ConstraintSet.TOP,
                R.id.card_zodiac_title,
                ConstraintSet.BOTTOM,
                16.px
            )

            //card_premium
            constraintGlobalSet.connect(
                R.id.card_premium,
                ConstraintSet.TOP,
                R.id.card_premium_title,
                ConstraintSet.BOTTOM,
                16.px
            )

            //my_information
            constraintGlobalSet.connect(
                R.id.textView4,
                ConstraintSet.TOP,
                R.id.card_premium,
                ConstraintSet.BOTTOM,
                44.px
            )


            constraintGlobalSet.applyTo(constraintGlobalLayout)
        } else {
            //card_verification
            constraintGlobalSet.connect(
                    R.id.card_verification,
                    ConstraintSet.TOP,
                    R.id.textView15,
                    ConstraintSet.BOTTOM,
                    0
            )
            //textView4
            constraintGlobalSet.connect(
                    R.id.textView4,
                    ConstraintSet.TOP,
                    R.id.card_zodiac,
                    ConstraintSet.BOTTOM,
           44.px
            )
            //card_instagram
            constraintGlobalSet.connect(
                    R.id.card_instagram,
                    ConstraintSet.TOP,
                    R.id.card_want_children,
                    ConstraintSet.BOTTOM,
                    0
            )

            //card_zodiac_title
            constraintGlobalSet.connect(
                R.id.card_zodiac_title,
                ConstraintSet.TOP,
                R.id.card_verification,
                ConstraintSet.BOTTOM,
        44.px
            )

            //card_zodiac_title
            constraintGlobalSet.connect(
                R.id.card_zodiac,
                ConstraintSet.TOP,
                R.id.card_zodiac_title,
                ConstraintSet.BOTTOM,
                16.px
            )
            //card_zodiac_title
            constraintGlobalSet.connect(
                R.id.card_premium,
                ConstraintSet.TOP,
                R.id.card_premium_title,
                ConstraintSet.BOTTOM,
                16.px
            )
        }

        //inside verified card  parent

        val constraintVerifiedLayout: ConstraintLayout? = binding.insideVerificationParent
        val constraintVerifiedSet = ConstraintSet()
        constraintVerifiedSet.clone(constraintVerifiedLayout)

        if ((badge2Status == Badge2Status.NOT_APPROVED || badge2Status == Badge2Status.NULL) || hasBadge1) {
            constraintVerifiedSet.connect(
                    R.id.image_verification_layout,
                    ConstraintSet.TOP,
                    R.id.parent,
                    ConstraintSet.TOP,
                    0
            )

            constraintVerifiedSet.connect(
                    R.id.badge1_verification_layout,
                    ConstraintSet.TOP,
                    R.id.image_verification_layout,
                    ConstraintSet.BOTTOM,
                    0
            )
            constraintVerifiedSet.applyTo(constraintVerifiedLayout)

        } else {
            constraintVerifiedSet.connect(
                    R.id.image_verification_layout,
                    ConstraintSet.TOP,
                    R.id.badge1_verification_layout,
                    ConstraintSet.BOTTOM,
                    0
            )

            constraintVerifiedSet.connect(
                    R.id.badge1_verification_layout,
                    ConstraintSet.TOP,
                    R.id.parent,
                    ConstraintSet.TOP,
                    0
            )
            constraintVerifiedSet.applyTo(constraintVerifiedLayout)
        }


        binding.cardVerification.visibility = View.VISIBLE
    }

    fun setHometownVisibility() {
       binding.cardHometown.isVisible = viewModel.shouldShowHometown()
    }
}