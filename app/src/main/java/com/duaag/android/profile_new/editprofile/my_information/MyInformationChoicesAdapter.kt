package com.duaag.android.profile_new.editprofile.my_information

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.MyInformationChoiceItemBinding

class MyInformationChoicesAdapter(private val clickListener: MyInformationChoicesClickListener) : ListAdapter<MyInformationChoicesModel, MyInformationChoicesAdapter.OptionsViewHolder>(OptionsDiffCallback()) {

    override fun onCurrentListChanged(previousList: MutableList<MyInformationChoicesModel>, currentList: MutableList<MyInformationChoicesModel>) {
        super.onCurrentListChanged(previousList, currentList)
        notifyDataSetChanged()
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OptionsViewHolder {
        val binding: MyInformationChoiceItemBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), R.layout.my_information_choice_item, parent, false)
        return OptionsViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OptionsViewHolder, position: Int) {
        holder.bind(getItem(holder.adapterPosition), clickListener)
    }

    inner class OptionsViewHolder(private var binding: MyInformationChoiceItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: MyInformationChoicesModel, clickListener: MyInformationChoicesClickListener) {
            binding.item = item
            binding.clickListener = clickListener
            binding.position=currentList.indexOf(item)
            binding.executePendingBindings()
        }

    }

    class MyInformationChoicesClickListener(val clickListener: (model: MyInformationChoicesModel, position: Int) -> Unit) {
        fun onClick(model: MyInformationChoicesModel, position: Int) = clickListener(model, position)
    }

    class OptionsDiffCallback : DiffUtil.ItemCallback<MyInformationChoicesModel>() {
        override fun areItemsTheSame(oldItem: MyInformationChoicesModel, newItem: MyInformationChoicesModel): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: MyInformationChoicesModel, newItem: MyInformationChoicesModel): Boolean {
            return oldItem == newItem
        }

    }

}