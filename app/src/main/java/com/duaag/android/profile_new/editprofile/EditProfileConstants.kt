package com.duaag.android.profile_new.editprofile

import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.profile_new.editprofile.profile_progress.ProfileProgressItem

object EditProfileConstants {

    const val ADDITIONAL_INFO_TYPE = "additional_info_type"
    const val MY_INFORMATION_TYPE = "my_information_type"
    const val NOT_SEARCHABLE_TYPE = "not_searchable_type"
    const val SEARCHABLE_TYPE = "searchable_type"
    const val ITEMS_TYPE_TO_SEARCH = "items_type_to_search"

    object AdditionalInfoConstants {
        const val LOOKING_FOR = "looking_for"
        const val SMOKE = "smoking"
        const val PETS = "pets"
        const val RELIGION = "religion"
        const val HAVE_CHILDREN = "have_children"
        const val WANT_CHILDREN = "want_children"
    }

    object MyInformationSearchableConstants {
        const val ACTIVITIES = "activities"
        const val HOMETOWN = "hometown"
        const val LANGUAGES = "languages"
    }
    enum class ProgressItemType(val index: Int) {
        PROFILE_PICTURE(0),
        BADGES(1),
        MY_INFORMATION(2),
        ADDITIONAL_INFORMATION(3),
    }
    object MyInformationConstants {
        const val JOB_EDUCATION = "job_education"
        const val DESCRIPTION = "description"
    }

    fun getProfileProgressItems(): List<ProfileProgressItem> {
        val context = DuaApplication.instance.applicationContext
        return listOf(
            ProfileProgressItem(
                id = 1,
                title = context.getString(R.string.profile_progress_title),
                description = context.getString(R.string.profile_progress_desc, context.getString(R.string.app_name)),
                progressText = context.getString(R.string.profile_progress),
                isFilled = false,
                type = ProgressItemType.PROFILE_PICTURE
            ),
            ProfileProgressItem(
                id = 2,
                title = context.getString(R.string.profile_progress_title_2),
                description = context.getString(R.string.profile_progress_desc_2, "30%"),
                progressText = context.getString(R.string.profile_progress_2),
                isFilled = false,
                type = ProgressItemType.BADGES
            ),
            ProfileProgressItem(
                id = 3,
                title = context.getString(R.string.profile_progress_title_3),
                description = context.getString(R.string.profile_progress_desc_3, "24%"),
                progressText = context.getString(R.string.profile_progress_3),
                isFilled = false,
                type = ProgressItemType.MY_INFORMATION
            ),
            ProfileProgressItem(
                id = 4,
                title = context.getString(R.string.profile_progress_title_4),
                description = context.getString(R.string.profile_progress_desc_4, "25%"),
                progressText = context.getString(R.string.profile_progress_4),
                isFilled = false,
                type = ProgressItemType.ADDITIONAL_INFORMATION
            ),
        )
    }


}