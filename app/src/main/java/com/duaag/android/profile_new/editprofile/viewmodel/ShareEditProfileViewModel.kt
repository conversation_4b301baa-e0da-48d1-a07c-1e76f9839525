package com.duaag.android.profile_new.editprofile.viewmodel

import androidx.lifecycle.*
import com.duaag.android.R
import com.duaag.android.api.UserService
import com.duaag.android.api.Resource
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.*
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapPremiumTagStatusValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.counters.domain.SyncUserCountersUseCase
import com.duaag.android.di.ActivityScope
import com.duaag.android.exceptions.InstagramConnectException
import com.duaag.android.exceptions.InstagramTokenRefreshException
import com.duaag.android.instagram.models.InstagramMediaModel
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoEventProperties
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoSourceValues
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoTypeValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.profile_new.editprofile.EditProfileConstants.getProfileProgressItems
import com.duaag.android.profile_new.editprofile.Fields
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoChoicesModel
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType.*
import com.duaag.android.profile_new.editprofile.my_information.MyInformationChoicesModel
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment.MyInformationSearchableType
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment.MyInformationSearchableType.*
import com.duaag.android.profile_new.editprofile.profile_progress.ProfileProgressItem
import com.duaag.android.profile_new.models.UpdateUserResponse
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.user.DuaAccount
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.livedata.SingleLiveData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Named

@ActivityScope
class ShareEditProfileViewModel @Inject constructor(
    @Named("private") var userService: UserService,
    var userRepository: UserRepository,
    private val syncUserCountersUseCase: SyncUserCountersUseCase
) : ViewModel() {


    var fromBackPress: Boolean? = false
    val userProfile = userRepository.user
    var accountModel = userRepository.getAccount()
    var authMethod: AuthMethod? = null

    private val userProfileFlow = userRepository.userFlow()

    private val _deleteUser: SingleLiveData<Boolean> = SingleLiveData()
    val deleteUser: LiveData<Boolean>
        get() = _deleteUser

    private var _lookingForAdditionalInfoChoices: MutableLiveData<List<AdditionalInfoChoicesModel>> =
        MutableLiveData(mutableListOf())
    val lookingForAdditionalInfoChoices: LiveData<List<AdditionalInfoChoicesModel>>
        get() = _lookingForAdditionalInfoChoices

    private var _smokeAdditionalInfoChoices: MutableLiveData<List<AdditionalInfoChoicesModel>> =
        MutableLiveData(mutableListOf())
    val smokeAdditionalInfoChoices: LiveData<List<AdditionalInfoChoicesModel>>
        get() = _smokeAdditionalInfoChoices

    private var _petsAdditionalInfoChoices: MutableLiveData<List<AdditionalInfoChoicesModel>> =
        MutableLiveData(mutableListOf())
    val petsAdditionalInfoChoices: LiveData<List<AdditionalInfoChoicesModel>>
        get() = _petsAdditionalInfoChoices

    private var _religionAdditionalInfoChoices: MutableLiveData<List<AdditionalInfoChoicesModel>> =
        MutableLiveData(mutableListOf())
    val religionAdditionalInfoChoices: LiveData<List<AdditionalInfoChoicesModel>>
        get() = _religionAdditionalInfoChoices

    private var _haveChildrenAdditionalInfoChoices: MutableLiveData<List<AdditionalInfoChoicesModel>> =
        MutableLiveData(mutableListOf())
    val haveChildrenAdditionalInfoChoices: LiveData<List<AdditionalInfoChoicesModel>>
        get() = _haveChildrenAdditionalInfoChoices

    private var _wantChildrenAdditionalInfoChoices: MutableLiveData<List<AdditionalInfoChoicesModel>> =
        MutableLiveData(mutableListOf())
    val wantChildrenAdditionalInfoChoices: LiveData<List<AdditionalInfoChoicesModel>>
        get() = _wantChildrenAdditionalInfoChoices

    private var _activitiesChoices: MutableLiveData<List<MyInformationChoicesModel>> =
        MutableLiveData(mutableListOf())
    val activitiesChoices: LiveData<List<MyInformationChoicesModel>>
        get() = _activitiesChoices

    private var _hometownChoices: MutableLiveData<List<MyInformationChoicesModel>> =
        MutableLiveData(mutableListOf())
    val hometownChoices: LiveData<List<MyInformationChoicesModel>>
        get() = _hometownChoices

    private var _languagesChoices: MutableLiveData<List<MyInformationChoicesModel>> =
        MutableLiveData(mutableListOf())
    val languageChoices: LiveData<List<MyInformationChoicesModel>>
        get() = _languagesChoices

    private var _areFieldsCompleted: MutableLiveData<Fields> = MutableLiveData(Fields())
    val areFieldsCompleted: LiveData<Fields>
        get() = _areFieldsCompleted

    private var _progressBarPercentage: MutableLiveData<Int> = MutableLiveData(0)
    val progressBarPercentage: LiveData<Int>
        get() = _progressBarPercentage

    private val _nextFabState: SingleLiveData<FloatingActionButtonVisibility> = SingleLiveData()
    val nextFabState: LiveData<FloatingActionButtonVisibility>
        get() = _nextFabState

    private val _backFabState: SingleLiveData<FloatingActionButtonVisibility> = SingleLiveData()
    val backFabState: LiveData<FloatingActionButtonVisibility>
        get() = _backFabState

    private val _updateJobAndEducation: SingleLiveData<Boolean> = SingleLiveData()
    val updateJobAndEducation: LiveData<Boolean>
        get() = _updateJobAndEducation

    private val _updateDescription: SingleLiveData<Boolean> = SingleLiveData()
    val updateDescription: LiveData<Boolean>
        get() = _updateDescription

    private val _updateLookingFor: SingleLiveData<Boolean> = SingleLiveData()
    val updateLookingFor: LiveData<Boolean>
        get() = _updateLookingFor

    private val _updateHeight: SingleLiveData<Boolean> = SingleLiveData()
    val updateHeight: LiveData<Boolean>
        get() = _updateHeight

    private val _updateSmoking: SingleLiveData<Boolean> = SingleLiveData()
    val updateSmoking: LiveData<Boolean>
        get() = _updateSmoking

    private val _updatePets: SingleLiveData<Boolean> = SingleLiveData()
    val updatePets: LiveData<Boolean>
        get() = _updatePets

    private val _updateReligion: SingleLiveData<Boolean> = SingleLiveData()
    val updateReligion: LiveData<Boolean>
        get() = _updateReligion

    private val _updateHaveChildren: SingleLiveData<Boolean> = SingleLiveData()
    val updateHaveChildren: LiveData<Boolean>
        get() = _updateHaveChildren

    private val _updateWantChildren: SingleLiveData<Boolean> = SingleLiveData()
    val updateWantChildren: LiveData<Boolean>
        get() = _updateWantChildren

    private val _updateActivities: SingleLiveData<Boolean> = SingleLiveData()
    val updateActivities: LiveData<Boolean>
        get() = _updateActivities

    private val _updateHometown: SingleLiveData<Boolean> = SingleLiveData()
    val updateHometown: LiveData<Boolean>
        get() = _updateHometown

    private val _updateLanguage: SingleLiveData<Boolean> = SingleLiveData()
    val updateLanguage: LiveData<Boolean>
        get() = _updateLanguage

    var jobInfo: String = ""
    var educationInfo: String = ""
    var descriptionInfo: String = ""

    private val _navigateAdditionalInfoLookingFor: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateAdditionalInfoLookingFor: LiveData<Resource<Int>>
        get() = _navigateAdditionalInfoLookingFor

    private val _navigateAdditionalInfoSmoking: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateAdditionalInfoSmoking: LiveData<Resource<Int>>
        get() = _navigateAdditionalInfoSmoking

    private val _navigateAdditionalInfoPets: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateAdditionalInfoPets: LiveData<Resource<Int>>
        get() = _navigateAdditionalInfoPets

    private val _navigateAdditionalInfoReligion: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateAdditionalInfoReligion: LiveData<Resource<Int>>
        get() = _navigateAdditionalInfoReligion

    private val _navigateAdditionalInfoHaveChildren: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateAdditionalInfoHaveChildren: LiveData<Resource<Int>>
        get() = _navigateAdditionalInfoHaveChildren

    private val _navigateAdditionalInfoWantChildren: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateAdditionalInfoWantChildren: LiveData<Resource<Int>>
        get() = _navigateAdditionalInfoWantChildren

    private val _navigateMyInformationActivities: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateMyInformationActivities: LiveData<Resource<Int>>
        get() = _navigateMyInformationActivities

    private val _navigateMyInformationLanguages: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateMyInformationLanguages: LiveData<Resource<Int>>
        get() = _navigateMyInformationLanguages

    private val _navigateMyInformationHometown: SingleLiveData<Resource<Int>> = SingleLiveData()
    val navigateMyInformationHometown: LiveData<Resource<Int>>
        get() = _navigateMyInformationHometown

    private val _hapticFeedback: SingleLiveData<Boolean> = SingleLiveData()
    val hapticFeedback: LiveData<Boolean>
        get() = _hapticFeedback


    private var _profileProgressItems: MutableLiveData<List<ProfileProgressItem?>> =
        MutableLiveData(mutableListOf())
    val profileProgressItems: LiveData<List<ProfileProgressItem?>>
        get() = _profileProgressItems

    private val _profileProgress: MutableLiveData<String> = MutableLiveData("0")
    val profileProgress: LiveData<String>
        get() = _profileProgress

    private val _requestUserProfile: SingleLiveData<Boolean> = SingleLiveData()
    val requestUserProfile: LiveData<Boolean>
        get() = _requestUserProfile

    private val _instagramTokenRefreshFailed: SingleLiveData<Void> = SingleLiveData()
    val instagramTokenRefreshFailed: LiveData<Void>
        get() = _instagramTokenRefreshFailed

    private var _instagramMedia: MutableLiveData<List<InstagramMediaModel>?> =
        MutableLiveData(mutableListOf())
    val instagramMedia: LiveData<List<InstagramMediaModel>?>
        get() = _instagramMedia

    private var _instagramClicked: SingleLiveData<Void> = SingleLiveData()
    val instagramClicked: LiveData<Void>
        get() = _instagramClicked

    var updatePercentage = false

    private val _dataUi : MutableStateFlow<EditProfileUIData> = MutableStateFlow(EditProfileUIData())
    val dataUi : LiveData<EditProfileUIData> get() = _dataUi.asLiveData()
    fun getUpdatedPercentage() {
        updatePercentage = true
    }

    private var _selectedAdditionalInfoChoices: MutableLiveData<ArrayList<AdditionalInfoChoicesModel>> =
        MutableLiveData(arrayListOf())
    private var _selectedMyInformationChoices: MutableLiveData<ArrayList<MyInformationChoicesModel>> =
        MutableLiveData(arrayListOf())

    init {
        getTags()
        _profileProgressItems.value = getProfileProgressItems()
        setProfileProgress(userProfile.value?.profilePercentage.toString())
        //this is temporary until we have time to find a better way to do this to reduce API calls
        onProfileProgressUpdated()
    }

    fun setProfileProgress(percentage: String) {
        _profileProgress.value = percentage
    }

    fun setInstagramClicked() {
        _instagramClicked.call()
    }

    fun userDelete() {
        _deleteUser.value = true
    }

    private fun setLookingForChoices(items: List<AdditionalInfoChoicesModel>) {
        _lookingForAdditionalInfoChoices.value = items
    }

    private fun setSmokeChoices(items: List<AdditionalInfoChoicesModel>) {
        _smokeAdditionalInfoChoices.value = items
    }

    private fun setPetsChoices(items: List<AdditionalInfoChoicesModel>) {
        _petsAdditionalInfoChoices.value = items
    }

    private fun setReligionChoices(items: List<AdditionalInfoChoicesModel>) {
        _religionAdditionalInfoChoices.value = items
    }

    private fun setHaveChildrenChoices(items: List<AdditionalInfoChoicesModel>) {
        _haveChildrenAdditionalInfoChoices.value = items
    }

    private fun setWantChildrenChoices(items: List<AdditionalInfoChoicesModel>) {
        _wantChildrenAdditionalInfoChoices.value = items
    }

    private fun setActivitiesChoices(items: List<MyInformationChoicesModel>) {
        _activitiesChoices.value = items
    }

    private fun setHometownChoices(items: List<MyInformationChoicesModel>) {
        _hometownChoices.value = items
    }

    private fun setLanguageChoices(items: List<MyInformationChoicesModel>) {
        _languagesChoices.value = items
    }

    fun updateUser(userModel: UserModel) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.updateUser(userModel)
        }
    }

    fun updateUserTags(model: UpdateTagsModel) {
        viewModelScope.launch(Dispatchers.IO) {
            val user = userProfile.value

            userRepository.updateUserTags(user, model)
                .catch {
                    withContext(Dispatchers.Main) {
                        setErrorResultForFields(model.typeId)
                    }
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {
                            //Show this when update user api returns ‘freeExperienceIsGranted=true’ and ‘FreeStarterExperienceUntil=date’
                            if(it.data.updatedUserData.freeExperienceIsGranted &&
                                it.data.updatedUserData.freeStarterExperienceUntil != null &&
                                System.currentTimeMillis() < it.data.updatedUserData.freeStarterExperienceUntil) {
                                DuaAccount.shouldShowPremiumLiteApprovedDialog = true

                                syncUserCounters()
                            }

                            onProfileProgressUpdated()
                            withContext(Dispatchers.Main) {
                                setSuccessResultForField(model.typeId)
                            }
                        }

                        is Resource.Error -> {
                            withContext(Dispatchers.Main) {
                                setErrorResultForFields(model.typeId)
                            }
                        }
                        is Resource.Loading -> {
                            withContext(Dispatchers.Main) {
                                setLoadingResultForField(model.typeId)
                            }
                        }
                    }

                }
        }
    }

    fun syncUserCounters() {
        viewModelScope.launch(Dispatchers.IO) {
            syncUserCountersUseCase.invoke()
                .catch { e -> Timber.e(e) }
                .collect { Timber.d("Counters updated") }
        }
    }

    fun updateUser(
        jobCategory: String = "jobs",
        educationCategory: String = "educations",
        jobCategoryValue: String? = null,
        educationCategoryValue: String? = null
    ): LiveData<Result<UpdateUserResponse>> {

        return when {
            !jobCategoryValue.isNullOrEmpty() && !educationCategoryValue.isNullOrEmpty() -> {
                val params = mapOf(
                    "profile" to mapOf(
                        jobCategory to jobCategoryValue,
                        educationCategory to educationCategoryValue
                    )
                )
                userRepository.updateUser(params)
            }
            !educationCategoryValue.isNullOrEmpty() && jobCategoryValue.isNullOrEmpty() -> {
                val params = mapOf(
                    "profile" to mapOf(
                        educationCategory to educationCategoryValue,
                        jobCategory to jobCategoryValue
                    )
                )
                userRepository.updateUser(params)
            }
            educationCategoryValue.isNullOrEmpty() && !jobCategoryValue.isNullOrEmpty() -> {
                val params = mapOf(
                    "profile" to mapOf(
                        jobCategory to jobCategoryValue,
                        educationCategory to educationCategoryValue
                    )
                )
                userRepository.updateUser(params)
            }
            else -> {
                val params = mapOf(
                    "profile" to mapOf(
                        jobCategory to jobCategoryValue,
                        educationCategory to educationCategoryValue
                    )
                )
                userRepository.updateUser(params)
            }
        }


    }

    internal fun checkSelected(it: Any, type: Any, position: Int) {
        if (it is AdditionalInfoChoicesModel) {
            val itemsAdditionalInfo = arrayListOf<AdditionalInfoChoicesModel>()
            val additionalInfoType = type as AdditionalInfoType
            when (additionalInfoType) {
                LOOKING_FOR -> _lookingForAdditionalInfoChoices.value?.let { it1 ->
                    itemsAdditionalInfo.addAll(it1)
                }
                SMOKE -> _smokeAdditionalInfoChoices.value?.let { it1 ->
                    itemsAdditionalInfo.addAll(it1)
                }

                PETS -> _petsAdditionalInfoChoices.value?.let { it1 ->
                    itemsAdditionalInfo.addAll(it1)
                }

                RELIGION -> _religionAdditionalInfoChoices.value?.let { it1 ->
                    itemsAdditionalInfo.addAll(it1)
                }

                HAVE_CHILDREN -> _haveChildrenAdditionalInfoChoices.value?.let { it1 ->
                    itemsAdditionalInfo.addAll(it1)
                }

                WANT_CHILDREN -> _wantChildrenAdditionalInfoChoices.value?.let { it1 ->
                    itemsAdditionalInfo.addAll(it1)
                }

            }
            _selectedAdditionalInfoChoices.value =
                itemsAdditionalInfo.filter { it.isSelected } as ArrayList<AdditionalInfoChoicesModel>
            if (!itemsAdditionalInfo.isNullOrEmpty()) {
                if (!itemsAdditionalInfo[position].isSelected) {
                    for ((index, value) in itemsAdditionalInfo.withIndex())
                        itemsAdditionalInfo[index] = value.copy(isSelected = value.id == it.id)
                } else {
                    itemsAdditionalInfo[position] =
                        it.copy(isSelected = !itemsAdditionalInfo[position].isSelected)

                }
                setChoices(itemsAdditionalInfo, additionalInfoType)
            }
        } else {
            val itemsMyInformation = arrayListOf<MyInformationChoicesModel>()
            val myInformationType = type as MyInformationSearchableType
            when (myInformationType) {
                ACTIVITIES -> _activitiesChoices.value?.let { it1 ->
                    itemsMyInformation.addAll(it1)
                }
                HOMETOWN -> _hometownChoices.value?.let { it1 ->
                    itemsMyInformation.addAll(it1)
                }
                LANGUAGES -> _languagesChoices.value?.let { it1 ->
                    itemsMyInformation.addAll(it1)
                }
                else -> {
                }
            }
            _selectedMyInformationChoices.value =
                itemsMyInformation.filter { it.isSelected } as ArrayList<MyInformationChoicesModel>
            if (!itemsMyInformation.isNullOrEmpty()) {
                if (!itemsMyInformation[position].isSelected) {
                    for ((index, value) in itemsMyInformation.withIndex()) {
                        itemsMyInformation[index] =
                            value.copy(isSelected = value.id == (it as MyInformationChoicesModel).id)
                    }
                } else {
                    itemsMyInformation[position] =
                        (it as MyInformationChoicesModel).copy(isSelected = !itemsMyInformation[position].isSelected)
                }
                setChoices(itemsMyInformation, myInformationType)
            }
        }
    }

    internal fun checkMultipleSelected(it: Any, position1: Any, position: Int) {
        if (it is AdditionalInfoChoicesModel) {
            val items = arrayListOf<AdditionalInfoChoicesModel>()
            val type = position1 as AdditionalInfoType
            when (type) {
                LOOKING_FOR -> getChoices(type)?.let { it1 ->
                    items.addAll(it1)
                }
                SMOKE -> getChoices(type)?.let { it1 ->
                    items.addAll(it1)
                }
                PETS -> getChoices(type)?.let { it1 ->
                    items.addAll(it1)
                }
                RELIGION -> getChoices(type)?.let { it1 ->
                    items.addAll(it1)
                }
                HAVE_CHILDREN -> getChoices(type)?.let { it1 ->
                    items.addAll(it1)
                }
                WANT_CHILDREN -> getChoices(type)?.let { it1 ->
                    items.addAll(it1)
                }
            }

            _selectedAdditionalInfoChoices.value =
                items.filter { it.isSelected } as ArrayList<AdditionalInfoChoicesModel>
            if ((_selectedAdditionalInfoChoices.value as ArrayList<AdditionalInfoChoicesModel>).size < 7 || items[position].isSelected) {
                items[position] = it.let { model ->
                    val isSelected = model.isSelected
                    model.copy(isSelected = !isSelected)
                }
                setChoices(items.sortedWith(compareBy({ !it.isSelected }, { it.name })), type)
                _selectedAdditionalInfoChoices.value =
                    items.filter { it.isSelected } as ArrayList<AdditionalInfoChoicesModel>
            } else {
                _hapticFeedback.value = true
            }
        } else {
            val items = arrayListOf<MyInformationChoicesModel>()
            val type = position1 as MyInformationSearchableType

            when (type) {
                ACTIVITIES, HOMETOWN, LANGUAGES -> getChoices(type)?.let { choices ->
                    items.addAll(choices)
                }
                JOB_EDUCATION, DESCRIPTION -> {
                }
            }

            _selectedMyInformationChoices.value =
                items.filter { it.isSelected } as ArrayList<MyInformationChoicesModel>
            if ((_selectedMyInformationChoices.value as ArrayList<MyInformationChoicesModel>).size < 7 || items[position].isSelected) {
                val selectedItem = items[position].let { model ->
                    model.copy(isSelected = !model.isSelected)
                }
                items[position] = selectedItem

                val sortedList = items.sortedWith(compareBy({ !it.isSelected }, { it.name }))
                setChoices(sortedList, type)
                _selectedMyInformationChoices.value =
                    items.filter { it.isSelected } as ArrayList<MyInformationChoicesModel>
            } else {
                _hapticFeedback.value = true
            }
        }
    }

    fun setChoices(items: List<AdditionalInfoChoicesModel>, type: AdditionalInfoType) {
        when (type) {
            LOOKING_FOR -> setLookingForChoices(items)
            SMOKE -> setSmokeChoices(items)
            PETS -> setPetsChoices(items)
            RELIGION -> setReligionChoices(items)
            HAVE_CHILDREN -> setHaveChildrenChoices(items)
            WANT_CHILDREN -> setWantChildrenChoices(items)
        }
        checkForCompletedAdditionalInfoFields(type)
    }

    fun setChoices(items: List<MyInformationChoicesModel>, type: MyInformationSearchableType) {
        when (type) {
            ACTIVITIES -> setActivitiesChoices(items)
            HOMETOWN -> setHometownChoices(items)
            LANGUAGES -> setLanguageChoices(items)
            else -> {
            }
        }
        checkForCompletedMyInformationFields(type)
    }

    fun getChoices(type: AdditionalInfoType): List<AdditionalInfoChoicesModel>? {
        return when (type) {
            LOOKING_FOR -> _lookingForAdditionalInfoChoices.value
            SMOKE -> _smokeAdditionalInfoChoices.value
            PETS -> _petsAdditionalInfoChoices.value
            RELIGION -> _religionAdditionalInfoChoices.value
            HAVE_CHILDREN -> _haveChildrenAdditionalInfoChoices.value
            WANT_CHILDREN -> _wantChildrenAdditionalInfoChoices.value
        }

    }

    fun getChoices(type: MyInformationSearchableType): List<MyInformationChoicesModel>? {
        return when (type) {
            ACTIVITIES -> _activitiesChoices.value
            HOMETOWN -> _hometownChoices.value
            LANGUAGES -> _languagesChoices.value
            else -> emptyList()
        }

    }

    fun checkIfJobDescriptionCompleted(type: MyInformationSearchableType?) {
        val fields = _areFieldsCompleted.value
        val copy: Fields?
        if (type == DESCRIPTION) {
            val descriptionCompleted = userProfile.value?.profile?.description
            copy = when {
                !descriptionCompleted.isNullOrEmpty() -> {

                    fields?.copy(isDescriptionCompleted = true)
                }
                else -> {
                    fields?.copy(isDescriptionCompleted = false)
                }

            }
            _areFieldsCompleted.value = copy
        } else if (type == JOB_EDUCATION) {
            val jobCompleted = userProfile.value?.profile?.jobs
            val educationCompleted = userProfile.value?.profile?.educations
            copy = when {
                !jobCompleted.isNullOrEmpty() -> when {
                    !educationCompleted.isNullOrEmpty() -> {
                        fields?.copy(isJobCompleted = true)
                    }
                    else -> {
                        fields?.copy(isJobCompleted = false)
                    }
                }
                else -> {
                    fields?.copy(isJobCompleted = false)
                }
            }
            _areFieldsCompleted.value = copy
        }

    }

    private fun checkForCompletedMyInformationFields(type: MyInformationSearchableType) {
        val fields = _areFieldsCompleted.value
        val copy: Fields?
        val selectedFields: List<MyInformationChoicesModel>? = getSelectedChoices(type)

        copy = if (selectedFields?.size!! > 0) {

            when (type) {
                ACTIVITIES -> fields?.copy(isActivitiesCompleted = true)
                HOMETOWN -> fields?.copy(isHometownCompleted = true)
                LANGUAGES -> fields?.copy(isLanguagesCompleted = true)
                else -> fields?.copy(isDescriptionCompleted = true)
            }
        } else {
            when (type) {
                ACTIVITIES -> fields?.copy(isActivitiesCompleted = false)
                HOMETOWN -> fields?.copy(isHometownCompleted = false)
                LANGUAGES -> fields?.copy(isLanguagesCompleted = false)
                else -> fields?.copy(isDescriptionCompleted = false)
            }
        }
        _areFieldsCompleted.value = copy

    }

    private fun checkForCompletedAdditionalInfoFields(type: AdditionalInfoType) {
        val fields = _areFieldsCompleted.value
        val copy: Fields?
        val selectedFields: List<AdditionalInfoChoicesModel>? = getSelectedChoices(type)
        when {
            selectedFields?.size!! > 0 -> {
                copy = when (type) {
                    LOOKING_FOR -> fields?.copy(isLookingForCompleted = true)
                    SMOKE -> fields?.copy(isSmokeCompleted = true)
                    PETS -> fields?.copy(isPetsCompleted = true)
                    RELIGION -> fields?.copy(isReligionCompleted = true)
                    HAVE_CHILDREN -> fields?.copy(isHaveChildrenCompleted = true)
                    WANT_CHILDREN -> fields?.copy(isWantChildrenCompleted = true)
                }
            }
            else -> {
                copy = when (type) {
                    LOOKING_FOR -> fields?.copy(isLookingForCompleted = false)
                    SMOKE -> fields?.copy(isSmokeCompleted = false)
                    PETS -> fields?.copy(isPetsCompleted = false)
                    RELIGION -> fields?.copy(isReligionCompleted = false)
                    HAVE_CHILDREN -> fields?.copy(isHaveChildrenCompleted = false)
                    WANT_CHILDREN -> fields?.copy(isWantChildrenCompleted = false)
                }
            }
        }
        _areFieldsCompleted.value = copy

    }

    fun getSelectedChoices(type: AdditionalInfoType): List<AdditionalInfoChoicesModel>? {
        return when (type) {
            LOOKING_FOR -> _lookingForAdditionalInfoChoices.value?.filter { it.isSelected }
            SMOKE -> _smokeAdditionalInfoChoices.value?.filter { it.isSelected }
            PETS -> _petsAdditionalInfoChoices.value?.filter { it.isSelected }
            RELIGION -> _religionAdditionalInfoChoices.value?.filter { it.isSelected }
            HAVE_CHILDREN -> _haveChildrenAdditionalInfoChoices.value?.filter { it.isSelected }
            WANT_CHILDREN -> _wantChildrenAdditionalInfoChoices.value?.filter { it.isSelected }
        }
    }

    fun getSelectedChoices(type: MyInformationSearchableType): List<MyInformationChoicesModel>? {
        return when (type) {
            ACTIVITIES -> _activitiesChoices.value?.filter { it.isSelected }
            HOMETOWN -> _hometownChoices.value?.filter { it.isSelected }
            LANGUAGES -> _languagesChoices.value?.filter { it.isSelected }
            JOB_EDUCATION -> {
                null
            }
            DESCRIPTION -> null
        }
    }

    fun checkPercentageForAdditionalChoices() {
        var count = 0
        if (!getSelectedChoices(LOOKING_FOR).isNullOrEmpty()) {
            count++
        }
        if (!getSelectedChoices(SMOKE).isNullOrEmpty()) {
            count++
        }
        if (!getSelectedChoices(PETS).isNullOrEmpty()) {
            count++
        }
        if (!getSelectedChoices(RELIGION).isNullOrEmpty()) {
            count++
        }
        if (!getSelectedChoices(HAVE_CHILDREN).isNullOrEmpty()) {
            count++
        }
        if (!getSelectedChoices(WANT_CHILDREN).isNullOrEmpty()) {
            count++
        }
        if(userProfile.value?.profile?.height != null) {
            count++
        }

        _progressBarPercentage.value = count.times(100f / 7).toInt()
    }

    fun checkPercentageForMyInformationChoices() {
        var count = 0

        val shouldShowHometown = shouldShowHometown()
        val times = if (shouldShowHometown) 17 else 20
        
        if (!getSelectedChoices(ACTIVITIES).isNullOrEmpty()) {
            count++
        }
        if (shouldShowHometown && !getSelectedChoices(HOMETOWN).isNullOrEmpty()) {
            count++
        }
        if (!getSelectedChoices(LANGUAGES).isNullOrEmpty()) {
            count++
        }

        if (!jobInfo.isNullOrEmpty()) {
            count++
        }

        if (!educationInfo.isNullOrEmpty()) {
            count++
        }

        if (!descriptionInfo.isNullOrEmpty()) {
            count++
        }

        Timber.tag("EDIT_PROFILE").d("checkPercentageForMyInformationChoices: $count")

        _progressBarPercentage.value = count.times(times)
    }

    fun shouldShowHometown():Boolean {
        val albanianCommunityId = "al"
         return userProfile.value?.communityInfo?.id == albanianCommunityId
    }

    private fun getUserTags() {
        myInformationTags()
        additionalInfoTags()

    }

    private fun additionalInfoTags(type: AdditionalInfoType? = null) {
        if (type == null) {
            additionalInfoTags(PETS)
            additionalInfoTags(RELIGION)
            additionalInfoTags(LOOKING_FOR)
            additionalInfoTags(SMOKE)
            additionalInfoTags(HAVE_CHILDREN)
            additionalInfoTags(WANT_CHILDREN)
        } else {
            viewModelScope.launch(Dispatchers.IO) {
                userProfileFlow.collect {
                    if(it == null) return@collect

                    val tags = it.tags?.filter { model ->
                        when (type) {
                            PETS -> model.tagTypeId == 3
                            RELIGION -> model.tagTypeId == 4
                            LOOKING_FOR -> model.tagTypeId == 6
                            SMOKE -> model.tagTypeId == 7
                            HAVE_CHILDREN -> model.tagTypeId == 8
                            WANT_CHILDREN -> model.tagTypeId == 9
                        }
                    }

                    val items = arrayListOf<AdditionalInfoChoicesModel>()
                    when (type) {
                        PETS -> _petsAdditionalInfoChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }
                        }
                        RELIGION -> _religionAdditionalInfoChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }
                        }
                        LOOKING_FOR -> _lookingForAdditionalInfoChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }
                        }
                        SMOKE -> _smokeAdditionalInfoChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }
                        }
                        HAVE_CHILDREN -> _haveChildrenAdditionalInfoChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }
                        }
                        WANT_CHILDREN -> _wantChildrenAdditionalInfoChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }
                        }
                    }

                    tags?.forEach { tagItem ->
                        items.find { it.id == tagItem.tagItemId }?.isSelected = true

                    }
                    withContext(Dispatchers.Main) {
                        setChoices(items.filter { !it.isExcluded() }.sortedByDescending { it.isSelected }, type)
                    }

                }

            }
        }
    }

    private fun myInformationTags(type: MyInformationSearchableType? = null) {

        if (type == null) {
            myInformationTags(ACTIVITIES)
            myInformationTags(HOMETOWN)
            myInformationTags(LANGUAGES)
        } else {
            viewModelScope.launch(Dispatchers.IO) {
                userProfileFlow.collect {
                    if(it == null) return@collect

                    val tags = it.tags?.filter { model ->
                        when (type) {
                            ACTIVITIES -> model.tagTypeId == 1
                            HOMETOWN -> model.tagTypeId == 5
                            LANGUAGES -> model.tagTypeId == 2
                            else -> {
                                return@collect
                            }
                        }
                    }
                    val items = arrayListOf<MyInformationChoicesModel>()
                    when (type) {
                        ACTIVITIES -> _activitiesChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }

                        }
                        HOMETOWN -> _hometownChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }

                        }
                        LANGUAGES -> _languagesChoices.value.let { it2 ->
                            it2?.let { items.addAll(it) }

                        }
                        else -> return@collect
                    }

                    tags?.forEach { tagItem ->
                        items.find { it.id == tagItem.tagItemId }?.isSelected = true

                    }
                    withContext(Dispatchers.Main) {
                        setChoices(items.filter { !it.isExcluded() }.sortedByDescending { it.isSelected }, type)
//                        checkForCompletedAdditionalInfoFields()
                    }

                }

            }
        }
    }

    private fun getTags() {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.tagsFlow()
                .catch {

                }
                .collect {
                    val tags = it as? List<TagTypeModel>
                    val activities = tags?.find { model -> model.id == 1 }
                    val activitiesChoices = activities?.items?.map { tagModel ->
                        tagModel.asMyInformationChoicesModel()
                    }

                    val languages = tags?.find { tagTypes -> tagTypes.id == 2 }
                    val languagesChoices = languages?.items?.map { tagModel ->
                        tagModel.asMyInformationChoicesModel()
                    }
                    val pets = tags?.find { tagTypes -> tagTypes.id == 3 }
                    val petsChoices = pets?.items?.map { tagModel ->
                        tagModel.asAdditionalInfoChoicesModel()
                    }
                    val religion = tags?.find { tagTypes -> tagTypes.id == 4 }
                    val religionChoices = religion?.items?.map { tagModel ->
                        tagModel.asAdditionalInfoChoicesModel()
                    }
                    val homeTown = tags?.find { tagTypes -> tagTypes.id == 5 }
                    val homeTownChoices = homeTown?.items?.map { tagModel ->
                        tagModel.asMyInformationChoicesModel()
                    }
                    val lookingFor = tags?.find { tagTypes -> tagTypes.id == 6 }
                    val lookingForChoices = lookingFor?.items?.map { tagModel ->
                        tagModel.asAdditionalInfoChoicesModel()
                    }

                    val smoking = tags?.find { tagTypes -> tagTypes.id == 7 }
                    val smokingChoices = smoking?.items?.map { tagModel ->
                        tagModel.asAdditionalInfoChoicesModel()
                    }
                    val haveChildren = tags?.find { tagTypes -> tagTypes.id == 8 }
                    val haveChildrenChoices = haveChildren?.items?.map { tagModel ->
                        tagModel.asAdditionalInfoChoicesModel()
                    }
                    val wantChildren = tags?.find { tagTypes -> tagTypes.id == 9 }
                    val wantChildrenChoices = wantChildren?.items?.map { tagModel ->
                        tagModel.asAdditionalInfoChoicesModel()
                    }

                    withContext(Dispatchers.Main) {
                        if (religionChoices != null) {
                            setReligionChoices(religionChoices)
                        }
                        if (petsChoices != null) {
                            setPetsChoices(petsChoices)
                        }
                        if (languagesChoices != null) {
                            setLanguageChoices(languagesChoices)
                        }
                        if (activitiesChoices != null) {
                            setActivitiesChoices(activitiesChoices)
                        }
                        if (homeTownChoices != null) {
                            setHometownChoices(homeTownChoices)
                        }
                        if (lookingForChoices != null) {
                            setLookingForChoices(lookingForChoices)
                        }
                        if (smokingChoices != null) {
                            setSmokeChoices(smokingChoices)
                        }
                        if (haveChildrenChoices != null) {
                            setHaveChildrenChoices(haveChildrenChoices)
                        }
                        if (wantChildrenChoices != null) {
                            setWantChildrenChoices(wantChildrenChoices)
                        }
                        checkIfJobDescriptionCompleted(JOB_EDUCATION)
                        checkIfJobDescriptionCompleted(DESCRIPTION)
                        getUserTags()
                    }
                }
        }

    }

    fun setFabNextVisibility(value: FloatingActionButtonVisibility) {
        _nextFabState.value = value
    }

    fun setFabBackVisibility(value: FloatingActionButtonVisibility) {
        _backFabState.value = value
    }

    fun onLookingForUpdated() {
        _updateLookingFor.value = true
    }

    fun onSmokingUpdated() {
        _updateSmoking.value = true
    }

    fun onHeightUpdated() {
        _updateHeight.value = true
    }

    fun onPetsUpdated() {
        _updatePets.value = true
    }

    fun onReligionUpdated() {
        _updateReligion.value = true
    }

    fun onHaveChildrenUpdated() {
        _updateHaveChildren.value = true
    }

    fun onWantChildrenUpdated() {
        _updateWantChildren.value = true
    }

    fun onActivitiesUpdated() {
        _updateActivities.value = true
    }

    fun onHometownUpdated() {
        _updateHometown.value = true
    }

    fun onLanguagesUpdated() {
        _updateLanguage.value = true
    }

    fun onJobAndEducationUpdated() {
        _updateJobAndEducation.value = true
    }

    fun onDescriptionUpdated() {
        _updateDescription.value = true
    }

    fun setDefaultValueForUpdatedValues(errorValue: Int = -2, value: AdditionalInfoType) {
        when (value) {
            LOOKING_FOR -> _navigateAdditionalInfoLookingFor.value = Resource.Error(errorValue)
            SMOKE -> _navigateAdditionalInfoSmoking.value = Resource.Error(errorValue)
            PETS -> _navigateAdditionalInfoPets.value = Resource.Error(errorValue)
            RELIGION -> _navigateAdditionalInfoReligion.value = Resource.Error(errorValue)
            HAVE_CHILDREN -> _navigateAdditionalInfoHaveChildren.value = Resource.Error(errorValue)
            WANT_CHILDREN -> _navigateAdditionalInfoWantChildren.value = Resource.Error(errorValue)
        }

    }

    fun setDefaultValueForUpdatedValues(errorValue: Int = -2, value: MyInformationSearchableType) {
        when (value) {
            ACTIVITIES -> _navigateMyInformationActivities.value = Resource.Error(errorValue)
            LANGUAGES -> _navigateMyInformationLanguages.value = Resource.Error(errorValue)
            HOMETOWN -> _navigateMyInformationHometown.value = Resource.Error(errorValue)
            else -> {
            }
        }

    }

    fun setSuccessResultForField(typeId: Int) {
        when (typeId) {
            1 -> _navigateMyInformationActivities.value = Resource.Success(typeId)
            2 -> _navigateMyInformationLanguages.value = Resource.Success(typeId)
            3 -> _navigateAdditionalInfoPets.value = Resource.Success(typeId)
            4 -> _navigateAdditionalInfoReligion.value = Resource.Success(typeId)
            5 -> _navigateMyInformationHometown.value = Resource.Success(typeId)
            6 -> _navigateAdditionalInfoLookingFor.value = Resource.Success(typeId)
            7 -> _navigateAdditionalInfoSmoking.value = Resource.Success(typeId)
            8 -> _navigateAdditionalInfoHaveChildren.value = Resource.Success(typeId)
            9 -> _navigateAdditionalInfoWantChildren.value = Resource.Success(typeId)
        }
    }

    private fun setErrorResultForFields(typeId: Int, value: Int = 0) {
        when (typeId) {
            1 -> _navigateMyInformationActivities.value = Resource.Error(value)
            2 -> _navigateMyInformationLanguages.value = Resource.Error(value)
            3 -> _navigateAdditionalInfoPets.value = Resource.Error(value)
            4 -> _navigateAdditionalInfoReligion.value = Resource.Error(value)
            5 -> _navigateMyInformationHometown.value = Resource.Error(value)
            6 -> _navigateAdditionalInfoLookingFor.value = Resource.Error(value)
            7 -> _navigateAdditionalInfoSmoking.value = Resource.Error(value)
            8 -> _navigateAdditionalInfoHaveChildren.value = Resource.Error(value)
            9 -> _navigateAdditionalInfoWantChildren.value = Resource.Error(value)
        }
    }

    fun setLoadingResultForField(typeId: Int) {
        when (typeId) {
            1 -> _navigateMyInformationActivities.value = Resource.Loading
            2 -> _navigateMyInformationLanguages.value = Resource.Loading
            3 -> _navigateAdditionalInfoPets.value = Resource.Loading
            4 -> _navigateAdditionalInfoReligion.value = Resource.Loading
            5 -> _navigateMyInformationHometown.value = Resource.Loading
            6 -> _navigateAdditionalInfoLookingFor.value = Resource.Loading
            7 -> _navigateAdditionalInfoSmoking.value = Resource.Loading
            8 -> _navigateAdditionalInfoHaveChildren.value = Resource.Loading
            9 -> _navigateAdditionalInfoWantChildren.value = Resource.Loading
        }
    }

    fun getVariableByType(value: AdditionalInfoType): Resource<Int>? {
        return when (value) {
            LOOKING_FOR -> _navigateAdditionalInfoLookingFor.value
            SMOKE -> _navigateAdditionalInfoSmoking.value
            PETS -> _navigateAdditionalInfoPets.value
            RELIGION -> _navigateAdditionalInfoReligion.value
            HAVE_CHILDREN -> _navigateAdditionalInfoHaveChildren.value
            WANT_CHILDREN -> _navigateAdditionalInfoWantChildren.value
        }
    }

    fun getVariableByType(value: MyInformationSearchableType): Resource<Int>? {
        return when (value) {
            ACTIVITIES -> _navigateMyInformationActivities.value
            LANGUAGES -> _navigateMyInformationLanguages.value
            HOMETOWN -> _navigateMyInformationHometown.value
            else -> null
        }
    }

    fun setProfileProgressItems(items: ArrayList<ProfileProgressItem?>) {
        _profileProgressItems.value = items
    }

    fun onProfileProgressUpdated() {
        _requestUserProfile.postValue(true)
    }

    fun getInstagramMedia(isFromConnection: Boolean = false) {
        userProfile.value?.cognitoUserId?.let {
            val user = userProfile.value

            viewModelScope.launch(Dispatchers.IO) {
                userRepository.getUserInstagramMedia(user, it)
                    .catch { e ->
                        when (e) {
                            is InstagramTokenRefreshException -> {
                                _instagramTokenRefreshFailed.postValue(null)
                            }
                            else -> e.printStackTrace()
                        }

                    }
                    .collect {
                        when (it) {
                            is Resource.Success -> {
                                _instagramMedia.postValue(it.data.media?.data)

                                if (isFromConnection) {
                                    onProfileProgressUpdated()

                                    firebaseLogEvent(
                                        FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                            AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                            AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.INSTAGRAM.value
                                        )
                                    )

                                    sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.INSTAGRAM.value
                                    ))
                                }
                            }
                            else -> {
                            }
                        }
                    }
            }
        }
    }

    fun connectInstagram(code: String) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.connectInstagram(code)
                .catch { e ->
                    when (e) {
                        is InstagramConnectException -> {
                            ToastUtil.toast(
                                DuaApplication.instance.applicationContext.getString(
                                    R.string.instagram_account_already_connected,
                                    DuaApplication.instance.applicationContext.getString(R.string.app_name)
                                )
                            )
                        }
                        else -> e.printStackTrace()
                    }
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {
                            getInstagramMedia(true)

                            val premiumType = getPremiumTypeEventProperty(userProfile.value)

                            sendClevertapEvent(
                                ClevertapEventEnum.INSTAGRAM_CONNECTED,
                                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType)
                            )
                        }
                        else -> {
                        }
                    }
                }
        }
    }

    fun disconnectInstagram() {
        viewModelScope.launch(Dispatchers.IO) {
            val profile = userProfile.value?.copy(
                instagramToken = InstagramToken(null, null, null),
                instagramStatus = INSTAGRAM_STATUS_DISCONNECTED
            )

            userRepository.disconnectInstagram(profile)
                .catch { e ->
                    e.printStackTrace()
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {
                            _instagramMedia.postValue(null)

                            onProfileProgressUpdated()

                            firebaseLogEvent(
                                FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.INSTAGRAM.value
                                )
                            )

                            sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.INSTAGRAM.value
                            ))

                            val premiumType = getPremiumTypeEventProperty(userProfile.value)

                            sendClevertapEvent(
                                ClevertapEventEnum.INSTAGRAM_DISCONNECTED,
                                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumType)
                            )
                        }
                        else -> {
                        }
                    }
                }
        }
    }

    suspend fun reconnectInstagram(code: String) = flow<Resource<ResponseBody>> {
        userRepository.reconnectInstagram(code)
            .catch { e ->
                e.printStackTrace()
            }
            .collect {
                emit(it)
            }
    }
    fun updatePremiumTagData(user: UserModel){
        _dataUi.update {
            it.copy(
                premiumTagData = it.premiumTagData?.copy(
                    showPremiumTag = user.profile.showPremiumBadge == true,
                    premiumType = user.premiumType))
        }
    }
    fun togglePremiumTag(isChecked: Boolean) {
        val params = mapOf("profile" to mapOf("showPremiumBadge" to isChecked))
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.showPremiumBadge(isChecked,params)
                .catch {e->
                    e.printStackTrace()
                    withContext(Dispatchers.Main) {
                     updatePremiumTagState(e)
                    }
                }
                .collect{
                    if(it is Resource.Success) {
                        sendPremiumTagEvents(userProfile.value,isChecked)
                    }
                }
        }    }

    private fun sendPremiumTagEvents(user: UserModel?,status:Boolean) {
       val premiumTagStatus = if(status) ClevertapPremiumTagStatusValues.SHOW.value else ClevertapPremiumTagStatusValues.HIDE.value
        val community = user?.communityInfo?.communityName

        sendClevertapEvent(ClevertapEventEnum.PREMIUM_BADGE,
            mapOf(
                ClevertapEventPropertyEnum.STATUS.propertyName to premiumTagStatus,
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to community
            )
        )

        firebaseLogEvent(FirebaseAnalyticsEventsName.PREMIUM_BADGE,
            mapOf(
                FirebaseAnalyticsParameterName.STATUS.value to premiumTagStatus,
                FirebaseAnalyticsParameterName.COMMUNITY.value to community
            )
        )
    }

    fun updatePremiumTagState(error: Throwable?) {
        _dataUi.update {
            it.copy(
                premiumTagData = it.premiumTagData?.copy(error = error)
            )
        }
    }
}
data class PremiumTagUIData(
    val showPremiumTag: Boolean = false,
    val premiumType: String? = null,
    val upgradedToPremium: Boolean = false,
    val error: Throwable? = null
)
data class EditProfileUIData(
    val premiumTagData: PremiumTagUIData? = PremiumTagUIData()

)
