package com.duaag.android.profile_new.editprofile.additional_info

import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment

data class AdditionalInfoChoicesModel(
    val id: Int,
    val name: String,
    var isSelected: Boolean = false,
    var deletedAt: String? = null,
    val type: AdditionalInfoFragment.AdditionalInfoType
) {
    fun isExcluded(): Boolean = deletedAt != null && !isSelected

}
