package com.duaag.android.profile_new.models

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName


@Keep
data class UpdateUserResponse(
    @SerializedName("counterConfigurationNames")
    val counterConfigurationNames: CounterConfigurationNames?,
    @SerializedName("freeExperienceIsGranted")
    val freeExperienceIsGranted: Bo<PERSON>an,
    @SerializedName("freeStarterExperienceUntil")
    val freeStarterExperienceUntil: Long? = null,
)

@Keep
data class CounterConfigurationNames(
    @SerializedName("birthdayCounterConfigurationName")
    val birthdayCounterConfigurationName: String,
    @SerializedName("boostCounterConfigurationName")
    val boostCounterConfigurationName: String,
    @SerializedName("communityCounterConfigurationName")
    val communityCounterConfigurationName: String,
    @SerializedName("flyCounterConfigurationName")
    val flyCounterConfigurationName: String,
    @SerializedName("genderChangeCounterConfigurationName")
    val genderChangeCounterConfigurationName: String,
    @SerializedName("instachatCounterConfigurationName")
    val instachatCounterConfigurationName: String,
    @SerializedName("interactionCounterConfigurationName")
    val interactionCounterConfigurationName: String,
    @SerializedName("nameChangeCounterConfigurationName")
    val nameChangeCounterConfigurationName: String,
    @SerializedName("superlikeCounterConfigurationName")
    val superlikeCounterConfigurationName: String,
    @SerializedName("unblurCounterConfigurationName")
    val unblurCounterConfigurationName: String,
    @SerializedName("undoCounterConfigurationName")
    val undoCounterConfigurationName: String
)