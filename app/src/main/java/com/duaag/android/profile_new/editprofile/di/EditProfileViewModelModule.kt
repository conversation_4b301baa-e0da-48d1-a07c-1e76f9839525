package com.duaag.android.profile_new.editprofile.di

import androidx.lifecycle.ViewModel
import com.duaag.android.di.ViewModelKey
import com.duaag.android.home.viewmodels.SetHeightViewModel
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.profile_new.editprofile.zodiac_sign.ZodiacViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

@Module
abstract class EditProfileViewModelModule {
    @Binds
    @IntoMap
    @ViewModelKey(ShareEditProfileViewModel::class)
    abstract fun bindViewModel(myViewModel: ShareEditProfileViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(ZodiacViewModel::class)
    abstract fun bindZodiacViewModel(zodiacViewModel: ZodiacViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(SetHeightViewModel::class)
    abstract fun bindSetHeightViewModel(setHeightViewModel: SetHeightViewModel): ViewModel

}