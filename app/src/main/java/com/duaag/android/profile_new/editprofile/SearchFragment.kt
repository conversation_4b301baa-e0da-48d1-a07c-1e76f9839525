package com.duaag.android.profile_new.editprofile

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.SearchView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.databinding.FragmentSearchBinding
import com.duaag.android.profile_new.editprofile.EditProfileConstants.ITEMS_TYPE_TO_SEARCH
import com.duaag.android.profile_new.editprofile.EditProfileConstants.SEARCHABLE_TYPE
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment.MyInformationSearchableType.*
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoChoicesAdapter
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoChoicesModel
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment
import com.duaag.android.profile_new.editprofile.my_information.MyInformationChoicesAdapter
import com.duaag.android.profile_new.editprofile.my_information.MyInformationChoicesModel
import com.duaag.android.profile_new.editprofile.my_information.MyInformationSearchableFragment
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.showKeyboardFrom
import com.duaag.android.utils.updateLocale
import java.util.*
import javax.inject.Inject


class SearchFragment : Fragment() {
    enum class SearchType {
        ADDITIONAL_INFO, MY_INFORMATION
    }

    private var _binding: FragmentSearchBinding? = null
    private val binding get() = _binding!!

    private var adapter: Any? = null
    private var searchType: String? = "additional_info"
    private var itemsTypeToSearch: Any? = AdditionalInfoFragment.AdditionalInfoType.PETS

    private lateinit var searchTypes: Map<String?, SearchType>


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ShareEditProfileViewModel>({activity as EditProfileActivity}) { viewModelFactory }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as EditProfileActivity).editProfileComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            searchType = it.getString(SEARCHABLE_TYPE)
            itemsTypeToSearch = it.getParcelable(ITEMS_TYPE_TO_SEARCH)
        }
        searchTypes = mapOf("additional_info" to SearchType.ADDITIONAL_INFO, "my_information" to SearchType.MY_INFORMATION)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        _binding = FragmentSearchBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner
        when (searchTypes.getValue(searchType)) {

            SearchType.MY_INFORMATION -> {


                adapter = MyInformationChoicesAdapter(MyInformationChoicesAdapter.MyInformationChoicesClickListener { model, position ->
                    hideKeyboard()
                    when (model.type) {
                        ACTIVITIES, LANGUAGES -> viewModel.checkMultipleSelected(model, model.type, position)
                        else -> viewModel.checkSelected(model, model.type, position)
                    }
                })
                val recyclerView = binding.searchResults
                recyclerView.adapter = adapter as MyInformationChoicesAdapter
                when (itemsTypeToSearch) {
                    ACTIVITIES -> {
                        binding.searchView.queryHint = requireContext().getString(R.string.search_your_activities)
                        viewModel.activitiesChoices.observe(viewLifecycleOwner) { options ->
                            (adapter as MyInformationChoicesAdapter).submitList(options)
                            binding.searchView.setOnQueryTextListener(
                                MyInformationQueryListener(
                                    options,
                                    ACTIVITIES
                                )
                            )
                        }
                    }
                    HOMETOWN -> {
                        binding.searchView.queryHint = requireContext().getString(R.string.search_your_hometown)
                        viewModel.hometownChoices.observe(viewLifecycleOwner) { options ->
                            (adapter as MyInformationChoicesAdapter).submitList(options)
                            binding.searchView.setOnQueryTextListener(
                                MyInformationQueryListener(
                                    options,
                                    HOMETOWN
                                )
                            )
                        }
                    }
                    LANGUAGES -> {
                        binding.searchView.queryHint = requireContext().getString(R.string.search_your_languages)
                        viewModel.languageChoices.observe(viewLifecycleOwner) { options ->
                            (adapter as MyInformationChoicesAdapter).submitList(options)
                            binding.searchView.setOnQueryTextListener(
                                MyInformationQueryListener(
                                    options,
                                    LANGUAGES
                                )
                            )
                        }
                    }
                }

            }
            SearchType.ADDITIONAL_INFO -> {

                adapter = AdditionalInfoChoicesAdapter(AdditionalInfoChoicesAdapter.AdditionalInfoChoicesClickListener { model, position ->
                    hideKeyboard()
                    when (model.type) {
                        AdditionalInfoFragment.AdditionalInfoType.PETS -> viewModel.checkMultipleSelected(model, model.type, position)
                        else -> viewModel.checkSelected(model, model.type, position)
                    }

                })
                val recyclerView = binding.searchResults
                recyclerView.adapter = adapter as AdditionalInfoChoicesAdapter
                when (itemsTypeToSearch) {
                    AdditionalInfoFragment.AdditionalInfoType.PETS -> {
                        binding.searchView.queryHint = requireContext().getString(R.string.search_your_pet)
                        viewModel.petsAdditionalInfoChoices.observe(viewLifecycleOwner) { options ->
                            (adapter as AdditionalInfoChoicesAdapter).submitList(options)
                            binding.searchView.setOnQueryTextListener(
                                AdditionalFragmentOnQueryListener(
                                    options,
                                    AdditionalInfoFragment.AdditionalInfoType.PETS
                                )
                            )
                        }
                    }
                    AdditionalInfoFragment.AdditionalInfoType.RELIGION -> {
                        binding.searchView.queryHint = getString(R.string.search_your_religion)
                        viewModel.religionAdditionalInfoChoices.observe(viewLifecycleOwner) { options ->
                            (adapter as AdditionalInfoChoicesAdapter).submitList(options)
                            binding.searchView.setOnQueryTextListener(
                                AdditionalFragmentOnQueryListener(
                                    options,
                                    AdditionalInfoFragment.AdditionalInfoType.RELIGION
                                )
                            )
                        }
                    }
                }


            }
        }


            binding.backButton.setOnClickListener {
                requireActivity().onBackPressed()
            }


        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        adapter = null
    }
    inner class MyInformationQueryListener(private val options: List<MyInformationChoicesModel>, private val type: MyInformationSearchableFragment.MyInformationSearchableType) : SearchView.OnQueryTextListener {
        override fun onQueryTextSubmit(query: String?): Boolean {
            val options=options
            val sortedOptions = options.sortedWith(compareBy({ !it.name.contains(query?.toLowerCase(Locale.getDefault())?.capitalize(Locale.getDefault()).toString()) }, { !it.isSelected }))
            viewModel.setChoices(sortedOptions, type)
            return false
        }

        override fun onQueryTextChange(newText: String?): Boolean {
            val options=options
            val sortedOptions = options.sortedWith(compareBy({ !it.name.contains(newText?.toLowerCase(Locale.getDefault())?.capitalize(Locale.getDefault()).toString()) }, { !it.isSelected }))
            viewModel.setChoices(sortedOptions, type)
            return false
        }
    }

    inner class AdditionalFragmentOnQueryListener(private val options: List<AdditionalInfoChoicesModel>, private val type: AdditionalInfoFragment.AdditionalInfoType) : SearchView.OnQueryTextListener {
        override fun onQueryTextSubmit(query: String?): Boolean {
            val options=options
            val sortedOptions = options.sortedWith(compareBy({ !it.name.contains(query?.toLowerCase(Locale.getDefault())?.capitalize(Locale.getDefault()).toString()) }, { !it.isSelected }))
            viewModel.setChoices(sortedOptions, type)
            return false
        }

        override fun onQueryTextChange(newText: String?): Boolean {

            val options=options
            val sortedOptions = options.sortedWith(compareBy({ !it.name.contains(newText?.toLowerCase(Locale.getDefault())?.capitalize(Locale.getDefault()).toString()) }, { !it.isSelected }))
            viewModel.setChoices(sortedOptions, type)
            return false

        }
    }

    override fun onResume() {
        super.onResume()
        binding.searchView.requestFocus()
        showKeyboardFrom(requireContext())
    }
}
