package com.duaag.android.profile_new.editprofile.additional_info

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.AdditionalInfoChoiceItemBinding


class AdditionalInfoChoicesAdapter(private val clickListenerAdditionalInfo: AdditionalInfoChoicesClickListener) : ListAdapter<AdditionalInfoChoicesModel, AdditionalInfoChoicesAdapter.ChoicesViewHolder>(ChoicesDiffCallback()) {

    override fun onCurrentListChanged(previousList: MutableList<AdditionalInfoChoicesModel>, currentList: MutableList<AdditionalInfoChoicesModel>) {
        super.onCurrentListChanged(previousList, currentList)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChoicesViewHolder {
        val binding: AdditionalInfoChoiceItemBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), R.layout.additional_info_choice_item, parent, false)
        return ChoicesViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ChoicesViewHolder, position: Int) {
        holder.bind(getItem(position), clickListenerAdditionalInfo)
    }

    inner class ChoicesViewHolder(private var binding: AdditionalInfoChoiceItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: AdditionalInfoChoicesModel, clickListenerAdditionalInfo: AdditionalInfoChoicesClickListener) {
            binding.item = item
            binding.clickListener = clickListenerAdditionalInfo
            binding.position=currentList.indexOf(item)
            binding.executePendingBindings()
        }

    }

    class AdditionalInfoChoicesClickListener(val clickListener: (modelAdditionalInfo: AdditionalInfoChoicesModel, position: Int) -> Unit) {
        fun onClick(modelAdditionalInfo: AdditionalInfoChoicesModel, position: Int) = clickListener(modelAdditionalInfo, position)
    }

    class ChoicesDiffCallback : DiffUtil.ItemCallback<AdditionalInfoChoicesModel>() {
        override fun areItemsTheSame(oldItem: AdditionalInfoChoicesModel, newItem: AdditionalInfoChoicesModel): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: AdditionalInfoChoicesModel, newItem: AdditionalInfoChoicesModel): Boolean {
            return oldItem == newItem
        }

    }

}