package com.duaag.android.profile_new.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.ProfilePremiumBenefitsItemsBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.profile_new.models.ProfilePremiumBenefitsDataClass
import com.duaag.android.profile_new.models.getProfilePremiumBenefitsListFemale
import com.duaag.android.profile_new.models.getProfilePremiumBenefitsListMale
import com.duaag.android.utils.GenderType

class ProfileBenefitsPremiumAdapter (homeViewModel: HomeViewModel) : RecyclerView.Adapter<ProfileBenefitsPremiumAdapter.ProfileBenefitsPremiumAdapterViewHolder>() {

        private var user = homeViewModel.userProfile.value?.gender
    
        private val items
            get() = if (user == GenderType.WOMAN.value) {
                getProfilePremiumBenefitsListFemale()
            } else {
                getProfilePremiumBenefitsListMale()
            }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProfileBenefitsPremiumAdapterViewHolder {
            val binding: ProfilePremiumBenefitsItemsBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), R.layout.profile_premium_benefits_items, parent, false)
            return ProfileBenefitsPremiumAdapterViewHolder(binding)
        }

        override fun onBindViewHolder(holder: ProfileBenefitsPremiumAdapterViewHolder, position: Int) {
            holder.binding.profilePremiumBenefitsList = items[position]
        }

        override fun getItemCount(): Int = items.size

        class ProfileBenefitsPremiumAdapterViewHolder(val binding: ProfilePremiumBenefitsItemsBinding) : RecyclerView.ViewHolder(binding.root) {
            fun bind(profilePremiumBenefitsDataClass: ProfilePremiumBenefitsDataClass) {
                binding.profilePremiumBenefitsList = profilePremiumBenefitsDataClass
                binding.executePendingBindings()
            }
        }
    }
