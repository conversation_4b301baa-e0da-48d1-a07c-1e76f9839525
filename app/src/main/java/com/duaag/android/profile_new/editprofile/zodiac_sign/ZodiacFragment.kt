package com.duaag.android.profile_new.editprofile.zodiac_sign

import android.content.Context
import androidx.lifecycle.ViewModelProvider
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.View
import androidx.fragment.app.viewModels
import com.duaag.android.R
import com.duaag.android.databinding.FragmentZodiacBinding
import com.duaag.android.profile_new.editprofile.EditProfileActivity
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.utils.setOnSingleClickListener
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.google.android.material.switchmaterial.SwitchMaterial
import timber.log.Timber
import javax.inject.Inject

class ZodiacFragment : Fragment(R.layout.fragment_zodiac) {
    // Scoped to the lifecycle of the fragment's view (between onCreateView and onDestroyView)
    private var binding: FragmentZodiacBinding? = null

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val sharedEditProfileViewModel: ShareEditProfileViewModel by viewModels({ activity as EditProfileActivity }) { viewModelFactory }
    private val viewModel: ZodiacViewModel by viewModels { viewModelFactory }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        (requireActivity() as EditProfileActivity).editProfileComponent.inject(this)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val binding = FragmentZodiacBinding.bind(view)
        this.binding = binding
        sharedEditProfileViewModel.userProfile.observe(viewLifecycleOwner){
            viewModel.updateUiData(it)
        }
        val adapter = ZodiacSignsAdapter(onZodiacSignSelectedCallback = {
            viewModel.onZodiacSignSelected(it)
        })
       viewModel.uiData.observe(viewLifecycleOwner){
           binding.zodiacSwitch.isChecked = it.isZodiacSwitchEnabled
           binding.zodiacSwitch.text = it.zodiacSign?.stringValue?.replaceFirstChar { it.uppercase() }
           binding.zodiacChooseSignImageview.rotation = if(it.areZodiacSignsShown) 180f else 0f

           adapter.submitList(it.zodiacSignsList.map { ZodiacSignDataItem.SignItem(it) })
           binding.zodiacRecyclerview.visibility = if(it.areZodiacSignsShown) View.VISIBLE else View.GONE

       }

        binding.zodiacSwitch.setOnSingleClickListener {
            viewModel.manageZodiacState((it as SwitchMaterial).isChecked)
         //   viewModel.updateZodiacState(!(it as SwitchMaterial).isChecked)
        }

        binding.zodiacChooseSignTextview.setOnSingleClickListener{
            viewModel.showZodiacSigns()
        }

        binding.zodiacChooseSignImageview.setOnSingleClickListener {
            viewModel.showZodiacSigns()
        }

        //initialize recyclerview with its layout manager
        val flexLayoutManager =
            FlexboxLayoutManager(binding.root.context, FlexDirection.ROW, FlexWrap.WRAP)
        flexLayoutManager.justifyContent = JustifyContent.FLEX_START
        binding.zodiacRecyclerview.layoutManager = flexLayoutManager
        binding.zodiacRecyclerview.adapter = adapter

    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as EditProfileActivity).setToolbarTitle(requireContext().getString(R.string.zodiac_edit_profile))
    }
    override fun onDestroyView() {
        // Consider not storing the binding instance in a field, if not needed.
        binding = null
        super.onDestroyView()
    }

}