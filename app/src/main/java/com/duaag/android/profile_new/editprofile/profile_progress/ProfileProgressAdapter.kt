package com.duaag.android.profile_new.editprofile.profile_progress

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.ProfileProgressItemBinding

class ProfileProgressAdapter(private val clickListener: ProfileProgressItemClickListener) : ListAdapter<ProfileProgressItem, ProfileProgressAdapter.ProfileProgressViewHolder>(ProfileProgressItemDiffCallback()) {

    inner class ProfileProgressViewHolder constructor(private var binding: ProfileProgressItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ProfileProgressItem, clickListener: ProfileProgressItemClickListener) {
            binding.item = item
            binding.clickListener=clickListener
            binding.executePendingBindings()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProfileProgressViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding = ProfileProgressItemBinding.inflate(layoutInflater, parent, false)
        return ProfileProgressViewHolder(binding)
    }


    override fun onBindViewHolder(holder: ProfileProgressViewHolder, position: Int) {
        holder.bind(getItem(position), clickListener)

    }

    class ProfileProgressItemClickListener(val clickListener: (model: ProfileProgressItem) -> Unit) {
        fun onClick(model: ProfileProgressItem) = clickListener(model)
    }

    class ProfileProgressItemDiffCallback : DiffUtil.ItemCallback<ProfileProgressItem>() {
        override fun areItemsTheSame(oldItem: ProfileProgressItem, newItem: ProfileProgressItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: ProfileProgressItem, newItem: ProfileProgressItem): Boolean {
            return oldItem == newItem
        }

    }

}