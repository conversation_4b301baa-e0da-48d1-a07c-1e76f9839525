package com.duaag.android.profile_new.editprofile.zodiac_sign;

enum class ZodiacSign(val stringValue: String) {
    ARIES("aries"),
    TAURUS("taurus"),
    GEMINI("gemini"),
    CANCER("cancer"),
    LEO("leo"),
    VIRGO("virgo"),
    LIBRA("libra"),
    SCORPIO("scorpio"),
    SAGITTARIUS("sagittarius"),
    CAPRICORN("capricorn"),
    AQUARIUS("aquarius"),
    PISCES("pisces");

    companion object {
        fun fromStringValue(stringValue: String?): ZodiacSign? {
            return values().find { it.stringValue == stringValue }
        }
    }
}
