package com.duaag.android.profile_new

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.duaag.android.api.Resource
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapGhostSourceValues
import com.duaag.android.clevertap.ClevertapGhostStatusValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.di.ActivityScope
import com.duaag.android.home.viewmodels.InAppPackagesViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.livedata.SingleLiveData
import io.purchasely.ext.Purchasely
import io.purchasely.models.PLYPlan
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@ActivityScope
class ProfileViewModel @Inject constructor(
    private val userRepository: UserRepository,
    ) : ViewModel() {

    var boostSmallestPrice: PLYPlan? = null
    var impressionsSmallestPrice: PLYPlan? = null
    var flightsSmallestPrice: PLYPlan? = null
    var undosSmallestPrice: PLYPlan? = null
    var instachatsSmallestPrice: PLYPlan? = null

    fun getSmallestPlans(): List<PLYPlan?> {
        return listOf(
            boostSmallestPrice,
            impressionsSmallestPrice,
            flightsSmallestPrice,
            undosSmallestPrice,
            instachatsSmallestPrice
        )
    }

    fun arePricesMissing() = boostSmallestPrice == null || impressionsSmallestPrice == null || instachatsSmallestPrice == null || undosSmallestPrice == null || flightsSmallestPrice == null

    private val _planPrices: SingleLiveData<Void> = SingleLiveData()
    val planPrices: LiveData<Void>
        get() = _planPrices

    fun getPurchaselyPackages() {
        if (boostSmallestPrice != null || impressionsSmallestPrice != null || flightsSmallestPrice != null || undosSmallestPrice != null || instachatsSmallestPrice != null)
            return
        Purchasely.allProducts(
            onSuccess = { products ->
                boostSmallestPrice =
                    products.firstOrNull { it.vendorId == InAppPackagesViewModel.boostsProductId }?.plans?.firstOrNull { it.vendorId == InAppPackagesViewModel.boostFirstPackage }
                impressionsSmallestPrice =
                    products.firstOrNull { it.vendorId == InAppPackagesViewModel.impressionsProductId }?.plans?.firstOrNull { it.vendorId == InAppPackagesViewModel.impressionsFirstPackage }
                instachatsSmallestPrice =
                    products.firstOrNull { it.vendorId == InAppPackagesViewModel.instachatsProductId }?.plans?.firstOrNull { it.vendorId == InAppPackagesViewModel.instachatFirstPackage }
                flightsSmallestPrice =
                    products.firstOrNull { it.vendorId == InAppPackagesViewModel.flightsProductId }?.plans?.firstOrNull { it.vendorId == InAppPackagesViewModel.flightFirstPackage }
                undosSmallestPrice =
                    products.firstOrNull { it.vendorId == InAppPackagesViewModel.undosProductId }?.plans?.firstOrNull { it.vendorId == InAppPackagesViewModel.undoFirstPackage }

                _planPrices.call()

            },
            onError = { throwable ->
                Timber.tag("Purchasely").d("throwable ${throwable.message}")
                //display an error
            }
        )

    }

    fun toggleGhostMode(isChecked: Boolean, eventSource: ClevertapGhostSourceValues){
        val params = mapOf("profile" to mapOf("isGhost" to isChecked))
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.ghostMode(isChecked,params)
                .catch {e->
                    e.printStackTrace()
                }
                .collect{
                    //we only send events here as per UI we observe user model and act upon it
                    sendGhostModeEvents(isChecked, eventSource,userRepository.user.value )
                }
        }
    }

    private fun sendGhostModeEvents(
        isChecked: Boolean,
        eventSource: ClevertapGhostSourceValues,
        userProfile: UserModel?
    ) {
        val ghostStatus =
            if (isChecked) ClevertapGhostStatusValues.ON.value else ClevertapGhostStatusValues.OFF.value
        val ghostSource =
            if (userProfile?.profile?.isInvisible == true) ClevertapGhostSourceValues.HIDE_PROFILE.value else eventSource.value
        sendClevertapEvent(
            event = ClevertapEventEnum.GHOST,
            properties = mapOf(
                ClevertapEventPropertyEnum.GHOST_STATUS.propertyName to ghostStatus,
                ClevertapEventPropertyEnum.GHOST_SOURCE.propertyName to ghostSource,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    userProfile
                ),
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to userProfile?.communityInfo?.communityName
            )
        )
        //Since the GA property values are the same with the clevertap property values, we use the same variables
        firebaseLogEvent(
            eventName = FirebaseAnalyticsEventsName.GHOST,
            params = mapOf(
                ClevertapEventPropertyEnum.GHOST_STATUS.propertyName to ghostStatus,
                ClevertapEventPropertyEnum.GHOST_SOURCE.propertyName to ghostSource,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    userProfile
                )
            )
        )
    }

}