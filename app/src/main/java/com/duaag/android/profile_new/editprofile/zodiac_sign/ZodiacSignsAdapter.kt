package com.duaag.android.profile_new.editprofile.zodiac_sign

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.ZodiacTagItemBinding
import com.duaag.android.utils.setOnSingleClickListener

class ZodiacSignsAdapter(private val onZodiacSignSelectedCallback: (ZodiacSignDataItem)-> Unit) : ListAdapter<ZodiacSignDataItem, RecyclerView.ViewHolder>(ZodiacSignDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
         return  ZodiacSignItemViewHolder.from(parent)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as ZodiacSignItemViewHolder).bind(getItem(position) as ZodiacSignDataItem.SignItem,onZodiacSignSelectedCallback)
    }

    class ZodiacSignItemViewHolder private constructor(val binding: ZodiacTagItemBinding) :
        RecyclerView.ViewHolder(binding.root){
        companion object {
            fun from(parent: ViewGroup) : ZodiacSignItemViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding =
                    ZodiacTagItemBinding.inflate(layoutInflater,parent,false)
                return ZodiacSignItemViewHolder(binding)
            }
        }

        fun bind(
            item: ZodiacSignDataItem.SignItem,
            onZodiacSignSelectedCallback: (ZodiacSignDataItem) -> Unit,
        ) {
            binding.titleText.text = item.zodiacSign.sign.stringValue.replaceFirstChar { it.uppercase() }
            binding.card.setCardBackgroundColor(
                ContextCompat.getColor(
                    binding.root.context,
                    if (item.zodiacSign.isSelected) R.color.dua_red_color else R.color.transparent
                )
            )
            binding.titleText.setTextColor(
                if (item.zodiacSign.isSelected) ContextCompat.getColor(
                    binding.root.context,
                    R.color.gray_50
                ) else ContextCompat.getColor(binding.root.context, R.color.title_primary)
            )
            binding.card.strokeColor = if (item.zodiacSign.isSelected) ContextCompat.getColor(
                binding.root.context,
                R.color.transparent
            ) else ContextCompat.getColor(binding.root.context, R.color.border)
            binding.card.setOnSingleClickListener {
                onZodiacSignSelectedCallback(item)
            }
        }
    }

}

sealed class ZodiacSignDataItem{
    data class SignItem(val zodiacSign: ZodiacSignItem) : ZodiacSignDataItem() {
        override val id: Int?
            get() = Int.MIN_VALUE
    }

    abstract val id : Int?
}

class ZodiacSignDiffCallback : DiffUtil.ItemCallback<ZodiacSignDataItem>() {
    override fun areItemsTheSame(
        oldItem: ZodiacSignDataItem,
        newItem: ZodiacSignDataItem
    ): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(
        oldItem: ZodiacSignDataItem,
        newItem: ZodiacSignDataItem
    ): Boolean {
        return oldItem == newItem
    }
}