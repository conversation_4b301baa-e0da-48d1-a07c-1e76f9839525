package com.duaag.android.profile_new.editprofile.additional_info

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.models.UpdateTagsModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentAdditionalInfoBinding
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoEventProperties
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoSourceValues
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoTypeValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.profile_new.editprofile.EditProfileActivity
import com.duaag.android.profile_new.editprofile.EditProfileConstants
import com.duaag.android.profile_new.editprofile.EditProfileConstants.AdditionalInfoConstants
import com.duaag.android.profile_new.editprofile.EditProfileConstants.ITEMS_TYPE_TO_SEARCH
import com.duaag.android.profile_new.editprofile.EditProfileConstants.SEARCHABLE_TYPE
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoChoicesAdapter.AdditionalInfoChoicesClickListener
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType.HAVE_CHILDREN
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType.LOOKING_FOR
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType.PETS
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType.RELIGION
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType.SMOKE
import com.duaag.android.profile_new.editprofile.additional_info.AdditionalInfoFragment.AdditionalInfoType.WANT_CHILDREN
import com.duaag.android.profile_new.editprofile.viewmodel.ShareEditProfileViewModel
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.convertDpToPx
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.updateLocale
import kotlinx.parcelize.Parcelize
import javax.inject.Inject


class AdditionalInfoFragment : Fragment() {

    @Parcelize
    enum class AdditionalInfoType(val typeId: Int) : Parcelable {
        LOOKING_FOR(6), SMOKE(7), PETS(3), RELIGION(4), HAVE_CHILDREN(8), WANT_CHILDREN(9),
    }


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<ShareEditProfileViewModel>({activity as EditProfileActivity}) { viewModelFactory }
    private var _binding: FragmentAdditionalInfoBinding? = null
    private val binding get() = _binding!!
    private var adapterAdditionalInfo: AdditionalInfoChoicesAdapter? = null
    private var type: String? = AdditionalInfoConstants.LOOKING_FOR
    lateinit var types: Map<String?, AdditionalInfoType>

    private lateinit var fallbackChoices: List<AdditionalInfoChoicesModel>
    private var initialSelectedItems: MutableList<Int> = mutableListOf()


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as EditProfileActivity).editProfileComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        arguments?.let {
            type = it.getString(EditProfileConstants.ADDITIONAL_INFO_TYPE)
        }
        types = mapOf(
                AdditionalInfoConstants.LOOKING_FOR to LOOKING_FOR,
                AdditionalInfoConstants.SMOKE to SMOKE,
                AdditionalInfoConstants.PETS to PETS,
                AdditionalInfoConstants.RELIGION to RELIGION,
                AdditionalInfoConstants.HAVE_CHILDREN to HAVE_CHILDREN,
                AdditionalInfoConstants.WANT_CHILDREN to WANT_CHILDREN
        )
        viewModel.getSelectedChoices(types.getValue(type)).apply {
            val newItems = this?.map { it.id } ?: emptyList()
            initialSelectedItems.addAll(newItems.filterNot { initialSelectedItems.contains(it) })
        }
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        _binding = FragmentAdditionalInfoBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.type = types.getValue(type)
        viewModel.setDefaultValueForUpdatedValues(value = types.getValue(type))
        fallbackChoices = viewModel.getChoices(types.getValue(type))!!

        adapterAdditionalInfo = AdditionalInfoChoicesAdapter(AdditionalInfoChoicesClickListener { model, position ->
            when (model.type) {
                LOOKING_FOR, SMOKE, HAVE_CHILDREN, WANT_CHILDREN, RELIGION -> {
                    viewModel.checkSelected(model, model.type, position)
                    viewModel.checkPercentageForAdditionalChoices()
                    checkForFabState()
                }
                else -> {
                    viewModel.checkMultipleSelected(model, model.type, position)
                    viewModel.checkPercentageForAdditionalChoices()
                    checkForFabState()
                }

            }
        })

        binding.searchTextView.setOnClickListener {
            val argument = it.tag as String
            when (types.getValue(type)) {
                PETS -> it.findNavController().navigate(R.id.action_petsFragment_to_searchFragmentAdditionalinfo, bundleOf(SEARCHABLE_TYPE to argument, ITEMS_TYPE_TO_SEARCH to PETS))

                RELIGION -> it.findNavController().navigate(R.id.action_religionFragment_to_searchFragmentAdditionalinfo, bundleOf(SEARCHABLE_TYPE to argument, ITEMS_TYPE_TO_SEARCH to RELIGION))

                else -> {
                }
            }

        }

        when (val type = types.getValue(type)) {
            PETS -> {
                viewModel.navigateAdditionalInfoPets.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {
                            binding.editProgress.visibility = View.GONE
                            if (viewModel.fromBackPress != true){
                                findNavController().navigateSafer(
                                    R.id.action_petsFragment_to_religionFragment,
                                    bundleOf(EditProfileConstants.ADDITIONAL_INFO_TYPE to AdditionalInfoConstants.RELIGION)
                                )
                            } else viewModel.fromBackPress = false
                        }

                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
//                                viewModel.checkForCompletedAdditionalInfoFields(type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_ADDITIONAL_INFOS)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }

                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.petsAdditionalInfoChoices.observe(viewLifecycleOwner) {
                    adapterAdditionalInfo?.submitList(it)
                }

                viewModel.updatePets.observe(viewLifecycleOwner) {
                    val ids = arrayListOf<Int>()
                    if (it) {
                        viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }
                        if (initialSelectedItems == ids) viewModel.setSuccessResultForField(3)
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(3, ids))

                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.PETS.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.PETS.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.PETS.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.PETS.value
                                ))
                            }
                        }

                    }

                }
            }

            LOOKING_FOR -> {
                viewModel.navigateAdditionalInfoLookingFor.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {
                            binding.editProgress.visibility = View.GONE
                            if(viewModel.fromBackPress != true){
                                findNavController().navigateSafer(
                                    R.id.action_lookingForFragment_to_addUserHeightBottomSheet2
                                )
                            } else viewModel.fromBackPress = false
                        }

                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
//                                viewModel.checkForCompletedAdditionalInfoFields(type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_ADDITIONAL_INFOS)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }

                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.lookingForAdditionalInfoChoices.observe(viewLifecycleOwner) {
                    adapterAdditionalInfo?.submitList(it)
                }
                viewModel.updateLookingFor.observe(viewLifecycleOwner) {
                    val ids = arrayListOf<Int>()
                    if (it) {
                        viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }
                        if (initialSelectedItems == ids) viewModel.setSuccessResultForField(6)
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(6, ids))

                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.LOOKING_FOR.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.LOOKING_FOR.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.LOOKING_FOR.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.LOOKING_FOR.value
                                ))
                            }
                        }
                    }

                }
            }

            SMOKE -> {
                viewModel.navigateAdditionalInfoSmoking.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {

                            binding.editProgress.visibility = View.GONE
                            if(viewModel.fromBackPress != true){
                                findNavController().navigateSafer(
                                    R.id.action_smokingFragment_to_petsFragment,
                                    bundleOf(EditProfileConstants.ADDITIONAL_INFO_TYPE to AdditionalInfoConstants.PETS)
                                )
                            } else viewModel.fromBackPress = false
                        }

                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_ADDITIONAL_INFOS)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }

                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.smokeAdditionalInfoChoices.observe(viewLifecycleOwner) {
                    adapterAdditionalInfo?.submitList(it)
                }
                viewModel.updateSmoking.observe(viewLifecycleOwner) {
                    val ids = arrayListOf<Int>()
                    if (it) {
                        viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }
                        if (initialSelectedItems == ids) viewModel.setSuccessResultForField(7)
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(7, ids))

                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.SMOKING.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.SMOKING.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.SMOKING.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.SMOKING.value
                                ))
                            }
                        }
                    }

                }
            }

            RELIGION -> {
                viewModel.navigateAdditionalInfoReligion.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {
                            binding.editProgress.visibility = View.GONE
                            if(viewModel.fromBackPress != true) {
                                findNavController().navigateSafer(
                                    R.id.action_religionFragment_to_childrenFragment,
                                    bundleOf(EditProfileConstants.ADDITIONAL_INFO_TYPE to AdditionalInfoConstants.HAVE_CHILDREN)
                                )
                            } else viewModel.fromBackPress = false

                        }

                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_ADDITIONAL_INFOS)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }

                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.religionAdditionalInfoChoices.observe(viewLifecycleOwner) {
                    adapterAdditionalInfo?.submitList(it)
                }
                viewModel.updateReligion.observe(viewLifecycleOwner) {
                    val ids = arrayListOf<Int>()
                    if (it) {
                        viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }
                        if (initialSelectedItems == ids) viewModel.setSuccessResultForField(4)
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(4, ids))

                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.RELIGION.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.RELIGION.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.RELIGION.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.RELIGION.value
                                ))
                            }
                        }
                    }

                }
            }

            HAVE_CHILDREN -> {
                // Set both width and height to 120dp
                binding.imageFragment.layoutParams.width = convertDpToPx(120f).toInt()
                binding.imageFragment.layoutParams.height = convertDpToPx(120f).toInt()
                binding.imageFragment.requestLayout()

                viewModel.navigateAdditionalInfoHaveChildren.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {
                            binding.editProgress.visibility = View.GONE
                            if(viewModel.fromBackPress != true){
                                findNavController().navigateSafer(
                                    R.id.action_haveChildrenFragment_to_wantChildrenFragment,
                                    bundleOf(EditProfileConstants.ADDITIONAL_INFO_TYPE to AdditionalInfoConstants.WANT_CHILDREN)
                                )

                            } else viewModel.fromBackPress = false
                        }

                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_ADDITIONAL_INFOS)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }

                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.haveChildrenAdditionalInfoChoices.observe(viewLifecycleOwner) {
                    adapterAdditionalInfo?.submitList(it)
                }
                viewModel.updateHaveChildren.observe(viewLifecycleOwner) {
                    val ids = arrayListOf<Int>()
                    if (it) {
                        viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }
                        if (initialSelectedItems == ids) viewModel.setSuccessResultForField(8)
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(8, ids))

                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.HAVE_CHILDREN.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.HAVE_CHILDREN.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.HAVE_CHILDREN.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.HAVE_CHILDREN.value
                                ))
                            }
                        }
                    }

                }
            }

            WANT_CHILDREN -> {
                // Set both width and height to 120dp
                binding.imageFragment.layoutParams.width = convertDpToPx(120f).toInt()
                binding.imageFragment.layoutParams.height = convertDpToPx(120f).toInt()
                binding.imageFragment.requestLayout()

                viewModel.navigateAdditionalInfoWantChildren.observe(viewLifecycleOwner) {
                    when (it) {
                        is Resource.Success -> {
                            binding.editProgress.visibility = View.GONE
                            if(viewModel.fromBackPress != true){
                                findNavController().navigateSafer(R.id.action_wantChildrenFragment_to_editProfileFragment)

                            } else viewModel.fromBackPress = false
                        }

                        is Resource.Error -> {
                            if (it.data == 0) {
                                binding.editProgress.visibility = View.GONE
                                viewModel.setChoices(fallbackChoices, type)
                                ToastUtil.toast(requireContext().getString(R.string.smthg_went_wrong))
                                logError(ErrorStatus.UPDATE_ADDITIONAL_INFOS)
                            } else if (it.data == -2) {
                                viewModel.setChoices(fallbackChoices, type)
                            }
                        }

                        Resource.Loading -> {
                            binding.editProgress.visibility = View.VISIBLE
                        }
                    }
                }
                viewModel.wantChildrenAdditionalInfoChoices.observe(viewLifecycleOwner) {
                    adapterAdditionalInfo?.submitList(it)
                }
                viewModel.updateWantChildren.observe(viewLifecycleOwner) {
                    val ids = arrayListOf<Int>()
                    if (it) {
                        viewModel.getSelectedChoices(type).apply {
                            this?.forEach { model ->
                                ids.add(model.id)
                            }
                        }
                        if (initialSelectedItems == ids) viewModel.setSuccessResultForField(9)
                        else {
                            viewModel.updateUserTags(UpdateTagsModel(9, ids))

                            if (ids.isEmpty()) {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.REMOVE_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.WANT_CHILDREN.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.REMOVE_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.WANT_CHILDREN.value
                                ))
                            } else {
                                firebaseLogEvent(
                                    FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                                        AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                        AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.WANT_CHILDREN.value
                                    )
                                )

                                sendClevertapEvent(ClevertapEventEnum.ADD_PROFILE_INFORMATION,mapOf(
                                    AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.EDIT_PROFILE.value,
                                    AddProfileInfoEventProperties.INFO_TYPE.value to AddProfileInfoTypeValues.WANT_CHILDREN.value
                                ))
                            }
                        }
                    }

                }
            }
        }


        val recyclerView = binding.recyclerViewChoices
        recyclerView.adapter = adapterAdditionalInfo


        return binding.root
    }

    override fun onResume() {
        super.onResume()
        hideKeyboard()
        (requireActivity() as EditProfileActivity).setToolbarTitle(requireContext().getString(R.string.edit_your_information))
        checkForFabState()
    }

    override fun onDestroyView() {
        when (val result = viewModel.getVariableByType(types.getValue(type))) {
            is Resource.Success -> {

            }
            is Resource.Error -> {
                if (result.data == -2) {
                    viewModel.setChoices(fallbackChoices, types.getValue(type))

                }
            }
            Resource.Loading -> {

            }
            null -> {

            }
        }
        super.onDestroyView()
        _binding = null
        adapterAdditionalInfo = null
    }

    private fun checkForFabState() {
        val selectedChoices = viewModel.getSelectedChoices(types.getValue(type))
        if (selectedChoices != null) {
            if (selectedChoices.isNotEmpty()) {
                viewModel.setFabNextVisibility(FloatingActionButtonVisibility.SHOWN)
            } else {
                viewModel.setFabNextVisibility(FloatingActionButtonVisibility.NEUTRAL)
            }

        }
    }
}
