package com.duaag.android.logevents.firebaseanalytics

import android.os.Bundle
import com.duaag.android.BuildConfig
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.UserModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName.SIGN_IN_SOURCE
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName.SIGN_UP_OR_SIGN_IN_MEDIUM
import com.duaag.android.signup.models.AuthMethod
import com.google.firebase.Firebase
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.analytics
import com.google.firebase.analytics.logEvent

const val shouldSendEventOnDev = true
      val environmentsWithoutEvents = arrayOf("dev","qa")
fun firebaseLogEvent(eventName: FirebaseAnalyticsEventsName, params: Map<String?, Any?>){
    if (environmentsWithoutEvents.contains(BuildConfig.FLAVOR_environment) && !shouldSendEventOnDev ) return
    val paramsWithCommunityId = mutableMapOf<String?, Any?>()
    paramsWithCommunityId.putAll(params)
    paramsWithCommunityId[FirebaseAnalyticsParameterName.COMMUNITY.value] = DuaApplication.instance.getUserCommunityId()

   Firebase.analytics.logEvent(eventName.value){
       for ((key, value) in paramsWithCommunityId) {
           when (value) {
               is Int -> key?.let { param(it, value.toLong()) }
               is Long -> key?.let { param(it, value) }
               is String -> key?.let { param(it, value) }
               is Boolean -> key?.let { param(it, if(value) 1 else 0) }
           }
       }
   }
}

fun firebaseLogEvent(eventName: String, params: Map<String?, Any>){
    if (environmentsWithoutEvents.contains(BuildConfig.FLAVOR_environment) && !shouldSendEventOnDev ) return
    val paramsWithCommunityId = mutableMapOf<String?, Any?>()
    paramsWithCommunityId.putAll(params)
    paramsWithCommunityId[FirebaseAnalyticsParameterName.COMMUNITY.value] = DuaApplication.instance.getUserCommunityId()

    Firebase.analytics.logEvent(eventName){
       for ((key, value) in paramsWithCommunityId) {
           when (value) {
               is Long -> key?.let { param(it, value) }
               is String -> key?.let { param(it, value) }
               is Boolean -> key?.let { param(it, if(value) 1 else 0) }
           }
       }
   }
}

fun firebaseLogEvent(eventName: FirebaseAnalyticsEventsName){
    if (environmentsWithoutEvents.contains(BuildConfig.FLAVOR_environment) && !shouldSendEventOnDev ) return
    val paramsWithCommunityId = mutableMapOf<String?, Any?>()
    paramsWithCommunityId[FirebaseAnalyticsParameterName.COMMUNITY.value] = DuaApplication.instance.getUserCommunityId()

    Firebase.analytics.logEvent(eventName.value) {
        for ((key, value) in paramsWithCommunityId) {
            when (value) {
                is Long -> key?.let { param(it, value) }
                is String -> key?.let { param(it, value) }
                is Boolean -> key?.let { param(it, if(value) 1 else 0) }
            }
        }
    }
}

fun firebaseLogEvent(eventName: String){
    if (environmentsWithoutEvents.contains(BuildConfig.FLAVOR_environment) && !shouldSendEventOnDev ) return
    val paramsWithCommunityId = mutableMapOf<String?, Any?>()
    paramsWithCommunityId[FirebaseAnalyticsParameterName.COMMUNITY.value] = DuaApplication.instance.getUserCommunityId()

    Firebase.analytics.logEvent(eventName) {
        for ((key, value) in paramsWithCommunityId) {
            when (value) {
                is Long -> key?.let { param(it, value) }
                is String -> key?.let { param(it, value) }
                is Boolean -> key?.let { param(it, if(value) 1 else 0) }
            }
        }
    }
}

fun firebaseScreenLogEvent(screen:String) {
    if (environmentsWithoutEvents.contains(BuildConfig.FLAVOR_environment) && !shouldSendEventOnDev ) return
    val bundle = Bundle()
    bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, screen)
    bundle.putString(FirebaseAnalytics.Param.SCREEN_CLASS, screen)
    bundle.putString(FirebaseAnalyticsParameterName.COMMUNITY.value,DuaApplication.instance.getUserCommunityId())

    Firebase.analytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
}

fun logSignUpEvent(authMethod: AuthMethod?, eventName: FirebaseAnalyticsEventsName,signInSourceValues: FirebaseAnalyticsSignInSourceValues?=null) {
    if (authMethod == null) return

    if (signInSourceValues != null) {
        firebaseLogEvent(eventName, mapOf(SIGN_UP_OR_SIGN_IN_MEDIUM.value to authMethod.methodName,
        SIGN_IN_SOURCE.value to signInSourceValues))
    } else {
        firebaseLogEvent(eventName, mapOf(SIGN_UP_OR_SIGN_IN_MEDIUM.value to authMethod.methodName))
    }
}
fun setUserId(cognitoUserid:String){
    Firebase.analytics.setUserId(cognitoUserid)
}

fun resetUserId() {
    Firebase.analytics.setUserId(null)
}

fun sendFirebaseCardResultEvent(isCardWithResult:Boolean, userProfile:UserModel?) {
    val minAge = userProfile?.filter?.minAge
    val maxAge = userProfile?.filter?.maxAge
    val rangeRadius = userProfile?.filter?.radius
    val locationFilter = userProfile?.profile?.address
    val isRadiusExtended = userProfile?.filter?.areFiltersExtended ?: false

    firebaseLogEvent(FirebaseAnalyticsEventsName.CARD_RESULTS,
        mapOf(FirebaseAnalyticsParameterName.IS_CARD_WITH_RESULTS.value to isCardWithResult,
            FirebaseAnalyticsParameterName.MIN_AGE_FILTERED.value to minAge,
            FirebaseAnalyticsParameterName.MAX_AGE_FILTERED.value to maxAge,
            FirebaseAnalyticsParameterName.RANGE_RADIUS_FILTERED.value to rangeRadius,
            FirebaseAnalyticsParameterName.LOCATION_FILTERED.value to locationFilter,
            FirebaseAnalyticsParameterName.IS_RADIUS_EXTENDED.value to isRadiusExtended))
}

fun signInWithSpottedEvent(
    authMethod: AuthMethod?, eventName: FirebaseAnalyticsEventsName, setupSource: String?,
    signInSourceValues: FirebaseAnalyticsSignInSourceValues?
) {
    if(authMethod == null) return

    val parameterName: String = when(authMethod){
        AuthMethod.EMAIL -> {
            FirebaseAnalyticsParameterName.EMAIL.value
        }
        AuthMethod.PHONE -> {
            FirebaseAnalyticsParameterName.PHONE.value
        }
        AuthMethod.FACEBOOK-> {
            FirebaseAnalyticsParameterName.FACEBOOK.value
        }
        AuthMethod.GOOGLE-> {
            FirebaseAnalyticsParameterName.GOOGLE.value
        }
    }

    if(setupSource != null){
    firebaseLogEvent(eventName, mapOf(parameterName to 1L,
        FirebaseAnalyticsParameterName.SETUP_SOURCE.value to setupSource))
    }else{
        firebaseLogEvent(eventName, mapOf(parameterName to 1L,
            FirebaseAnalyticsParameterName.SIGN_IN_SOURCE.value to signInSourceValues?.value))
    }
}



