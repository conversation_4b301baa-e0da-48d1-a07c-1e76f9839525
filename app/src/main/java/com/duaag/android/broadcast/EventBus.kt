package com.duaag.android.broadcast

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import timber.log.Timber

object EventBus {
    private val coroutineScope = CoroutineScope(Dispatchers.Default)

    private val _events = MutableSharedFlow<DuaEvent>()
    val events = _events.asSharedFlow()

    fun sendEvent(event: DuaEvent) {
        coroutineScope.launch {
            Timber.tag("EVENT_BUS").d("sending event: $event")
            _events.emit(event)
        }
    }
}