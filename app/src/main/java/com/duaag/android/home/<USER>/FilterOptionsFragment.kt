package com.duaag.android.home.fragments

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.SeekBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.models.Filter
import com.duaag.android.base.models.UserModel
import com.duaag.android.change_location.ChangeLocationActivity
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapVerificationSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendAddInfoInitiatedEvent
import com.duaag.android.databinding.FragmentFilterBottomsheetBinding
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.fragments.AddEthnicitiesBottomSheet.Companion.CHOSEN_ITEMS
import com.duaag.android.home.fragments.AddUserHeightBottomSheet.Companion.USER_HEIGHT_CHANGED
import com.duaag.android.home.fragments.FilterItemTagPickerBottomSheet.Companion.FILTER_EXTENDED
import com.duaag.android.home.fragments.FilterItemTagPickerBottomSheet.Companion.FILTER_TYPE
import com.duaag.android.home.fragments.FilterItemTagPickerBottomSheet.Companion.PROFILE_EDIT
import com.duaag.android.home.models.FilterType
import com.duaag.android.home.viewmodels.FilterOptionsUiData
import com.duaag.android.home.viewmodels.FilterOptionsViewModel
import com.duaag.android.home.viewmodels.FilterOptionsViewModelFactory
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.home.viewmodels.provideFactory
import com.duaag.android.image_verification.fragments.VerifyProfileWithBadge2PopUp.Companion.EVENT_SOURCE
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.PremiumActivity.Companion.PREMIUM_INTENT
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.premium_subscription.showBillingNotAvailable
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.convertDpToPixel
import com.duaag.android.utils.setMargin
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.material_range_bar.RangeBar
import kotlinx.coroutines.launch
import sendVerifyYourProfileInitiatedAnalyticsEvent
import javax.inject.Inject


class FilterOptionsFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var filterOptionsViewModelFactory: FilterOptionsViewModelFactory

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs


    private val homeViewModel by viewModels<HomeViewModel>({ activity as HomeActivity }) { viewModelFactory }

    private val viewModel by navGraphViewModels<FilterOptionsViewModel>(R.id.nav_graph_filter) {
        provideFactory(
        filterOptionsViewModelFactory,
        requireArguments().getString(ARG_EVENT_SOURCE) ?: "",
    ) }

    private var userModel: UserModel? = null
    private var firstTimeCity: String? = null

    private var _binding: FragmentFilterBottomsheetBinding? = null
    private val binding get() = _binding!!

    private val premiumBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val isPremium = intent?.getBooleanExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, false)
                ?: false
            if (isPremium) {
                updateUIState(viewModel.dataUi.value)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateLocale(context)
        (requireActivity() as HomeActivity).homeComponent.inject(this)
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentFilterBottomsheetBinding.inflate(inflater)
        binding.toolbar.setNavigationIcon(R.drawable.ic_close_icon)
        binding.toolbar.setNavigationOnClickListener {
            popBackStack()
        }
        homeViewModel.userProfile.observe(viewLifecycleOwner) { user ->
            if(user == null) return@observe

            userModel = user
//            binding.textviewYoursValue.text = user.communityInfo?.communityName ?: "..."
           
            updateLocationUI(
                context = requireContext(),
                binding = binding,
                isFlyMode = user.isInFlyMode()
            )
            
            if (user.premiumType == null) {
                binding.advancedFiltersDesc.visibility = View.VISIBLE
            } else {
                binding.advancedFiltersDesc.visibility = View.GONE
            }
            
            //verify_profile
            val isVerified = user.badge2.equals(Badge2Status.APPROVED.status)
            setVisibility(binding.verifiedUserContainer, isVerified)
            setVisibility(binding.nonVerifiedUserContainer, !isVerified)

            if (user.communityInfo?.canFiltersBeExtended == true) {
                binding.extendedRange.visibility = View.VISIBLE
                binding.extendedRange.isChecked = user.filter.areFiltersExtended ?: false
                binding.extendedRange.setMargin(end = convertDpToPixel(8f, requireContext()))

                //  toggleRadiusSeekbar(it.filter.areFiltersExtended == true)
            }

            val chosenLocationString = if (user.profile.address.isNullOrEmpty()) "-" else user.profile.address
            binding.chosenLocation.text = chosenLocationString
            checkWhenCityChanged()
        }

        setCallbacks()

        registerReceivers()

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val navBackStackEntry = findNavController().getBackStackEntry(R.id.filterOptionsFragment)
        val savedStateHandle = navBackStackEntry.savedStateHandle

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                savedStateHandle.getStateFlow(USER_HEIGHT_CHANGED, false).collect { result ->
                    if (result) {
                        onUserHeightChanged()
                        savedStateHandle.remove<Boolean>("userHeight")
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.dataUi.collect { uiData ->
                    updateUIState(uiData)
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.filtersUpdated.collect {
                    homeViewModel.updateFilterUser(true)
                    findNavController().navigateUp()
                }
            }
        }
    }

    private fun updateUIState(uiData: FilterOptionsUiData) {
            val filter = uiData.modifiedData?.filter ?: Filter(
                100,
                18,
                40,
                minHeight = 80,
                maxHeight = 250
            )

            val defaultFilters = userModel?.getDefaultFilters()

            val showResetHeight = userModel?.profile?.height != null && (
                    filter.minHeight != defaultFilters?.minHeight ||
                            filter.maxHeight != defaultFilters?.maxHeight ||
                            filter.heightExtended != defaultFilters?.heightExtended)

            updateHeightComponent(
                infoRoot = binding.addInfoHeight.root,
                componentRoot = binding.heightContainer,
                hasInfo = userModel?.profile?.height != null,
                resetButton = binding.heightReset,
                showResetBtn = showResetHeight
            )

            updateComponent(
                infoRoot = binding.addInfoLookingFor.root,
                componentRoot = binding.addLookingForComponent.root,
                hasInfo = uiData.hasUserLookingForInfo,
                list = uiData.modifiedData?.filter?.lookingForList,
                placeholder = binding.addLookingForComponent.addInfoPlaceholder,
                resetButton = binding.lookingForReset
            )

            updateComponent(
                infoRoot = binding.addInfoLanguages.root,
                componentRoot = binding.addLanguagesComponent.root,
                hasInfo = uiData.hasUserLanguagesInfo,
                list = uiData.modifiedData?.filter?.languagesList,
                placeholder = binding.addLanguagesComponent.addInfoPlaceholder,
                resetButton = binding.languageReset
            )

            updateComponent(
                infoRoot = binding.addInfoReligion.root,
                componentRoot = binding.addReligionComponent.root,
                hasInfo = uiData.hasUserReligionInfo,
                list = uiData.modifiedData?.filter?.religionsList,
                placeholder = binding.addReligionComponent.addInfoPlaceholder,
                resetButton = binding.religionReset
            )

//            updateCommunitiesData(uiData, filter)

            updateAdvancedFiltersHeader()

            binding.radiusSeekBar.progress = filter.radius
            binding.distanceDesc.text =
                getString(R.string.up_to_caption_km_away, filter.radius.toString())

            binding.rangeAgeTxt.text = getAgeRangeText(filter.minAge, filter.maxAge)
            binding.heightDesc.text =
                getHeightDescription(filter.minHeight, filter.maxHeight)

            binding.heightSwitch.apply {
                isEnabled = !(filter.minHeight == 80 && filter.maxHeight == 250)
                isChecked = filter.heightExtended == true
            }

            updateExtendedFilters(binding, filter)

            if(!uiData.isUpdatingRangeBars) {
                updateRangeBars(
                    minHeight = filter.minHeight ?: 80,
                    maxHeight = filter.maxHeight ?: 250,
                    minAge = filter.minAge,
                    maxAge = filter.maxAge
                )
            }
            binding.heightBlocker.isVisible = homeViewModel.userProfile.value?.premiumType == null

            updateVerifiedProfilesComponent(uiData)

            binding.applyBtn.isEnabled = uiData.initialData != uiData.modifiedData

            toggleLoadingVisibility(uiData.isLoading)
        }

    private fun updateAdvancedFiltersHeader() {
        val isPremium = homeViewModel.userProfile.value?.premiumType != null
        if(isPremium) {
            binding.advancedFiltersDiamond.isVisible = false
            binding.advancedFiltersDesc.isVisible = false
        } else {
            binding.advancedFiltersDiamond.isVisible = true
            binding.advancedFiltersDesc.isVisible = true
        }
    }

//    private fun updateCommunitiesData(uiData: FilterOptionsUiData, filter: Filter) {
//        val chosenCommunities = uiData.modifiedData?.filter?.communitiesList?.toMutableList()
//        chosenCommunities?.removeAll { it == userModel?.communityInfo?.id }
//        val items = viewModel.allCommunities?.filter { chosenCommunities?.contains(it.id) ?: false }
//        val stringItems = items?.joinToString(", ") { it.communityName }?.ifEmpty { getString(R.string.add_more_ethnicities_label) } ?: getString(R.string.add_more_ethnicities_label)
//        binding.ethnicitiesPlaceholder.text = stringItems
//
//        binding.ethnicitySwitch.isChecked = filter.isGlobal
//
//        val addImage = if(items.isNullOrEmpty())
//            R.drawable.ic_plus_gray
//        else
//            R.drawable.ic_pen_v2
//        binding.ethnicitiesAddBtn.setImageResource(addImage)
//
//        val showResetButton = !chosenCommunities.isNullOrEmpty() || filter.isGlobal
//        binding.ethnicityReset.isVisible = showResetButton
//    }

    private fun setCallbacks() {
        binding.heightBlocker.setOnSingleClickListener {
            showPremiumFeaturePopup()
        }

        binding.heightRangeBar.setOnRangeBarChangeListener(object : RangeBar.OnRangeBarChangeListener {
            override fun onRangeChangeListener(
                rangeBar: RangeBar?,
                leftPinIndex: Int,
                rightPinIndex: Int,
                leftPinValue: String?,
                rightPinValue: String?
            ) {
                viewModel.updateHeight(
                    minHeight = leftPinValue?.toInt(),
                    maxHeight = rightPinValue?.toInt()
                )
            }

            override fun onTouchStarted(rangeBar: RangeBar?) = Unit

            override fun onTouchEnded(rangeBar: RangeBar?) = Unit

        })

        binding.radiusSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                viewModel.updateRadius(progress)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
            }

        })

        binding.ageRangeBar.setOnRangeBarChangeListener(object : RangeBar.OnRangeBarChangeListener {
            override fun onTouchEnded(rangeBar: RangeBar?) {
            }

            override fun onRangeChangeListener(
                rangeBar: RangeBar?,
                leftPinIndex: Int,
                rightPinIndex: Int,
                leftPinValue: String?,
                rightPinValue: String?
            ) {
                viewModel.updateAge(minAge = leftPinValue?.toInt(), maxAge = rightPinValue?.toInt())
            }

            override fun onTouchStarted(rangeBar: RangeBar?) {
            }
        })

        binding.extendedRange.setOnCheckedChangeListener { _, isChecked ->
            viewModel.extendRange(isExtended = isChecked)
        }

//        binding.ethnicitySwitch.setOnCheckedChangeListener { _, isChecked ->
//            viewModel.updateIsGlobalState(isChecked)
//        }

        binding.heightSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.extendHeight(isExtended = isChecked)
        }

        binding.verifyProfileBtn.setOnSingleClickListener {
            val eventPremiumType = getPremiumTypeEventProperty(userModel)
            sendVerifyYourProfileInitiatedAnalyticsEvent(
                ClevertapVerificationSourceValues.ADVANCED_FILTERS.value,
                eventPremiumType
            )

            findNavController().navigate(R.id.action_global_verifyProfileWithBadge2PopUp,
                bundleOf(EVENT_SOURCE to ClevertapVerificationSourceValues.ADVANCED_FILTERS.value)
            )
        }

        binding.verifiedProfilesSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            if (buttonView.isPressed) {
                if (homeViewModel.userProfile.value?.premiumType != null) {
                    viewModel.updateVerifiedProfiles(isChecked)
                } else {
                    showPremiumFeaturePopup()
                    viewModel.updateVerifiedProfiles(!isChecked)
                }
            }
        }

        binding.addInfoHeight.addInfoBtn.setOnSingleClickListener {
            findNavController().navigate(R.id.action_filterOptionsFragment_to_addUserHeightBottomSheet)
            sendAddInfoInitiatedEvent(FilterType.HEIGHT, userModel)
        }

        binding.addInfoLookingFor.addInfoBtn.setOnSingleClickListener {
            val lookingForExtended = viewModel.getModifiedFilter()?.lookingForExtended
            findNavController().navigate(R.id.action_filterOptionsFragment_to_filterItemTagPickerBottomSheet,
                bundleOf(
                    FILTER_TYPE to FilterType.LOOKING_FOR,
                    FilterItemTagPickerBottomSheet.CHOSEN_ITEMS to null,
                    FILTER_EXTENDED to lookingForExtended,
                    PROFILE_EDIT to true
                )
            )

            sendAddInfoInitiatedEvent(FilterType.LOOKING_FOR, userModel)
        }

        binding.addLookingForComponent.addInfoContainer.setOnSingleClickListener {
            if(homeViewModel.userProfile.value?.premiumType != null) {
                val chosenItems = viewModel.getModifiedFilter()?.lookingForList
                val lookingForExtended = viewModel.getModifiedFilter()?.lookingForExtended
                findNavController().navigate(
                    R.id.action_filterOptionsFragment_to_filterItemTagPickerBottomSheet,
                    bundleOf(
                        FILTER_TYPE to FilterType.LOOKING_FOR,
                        FilterItemTagPickerBottomSheet.CHOSEN_ITEMS to chosenItems,
                        FILTER_EXTENDED to lookingForExtended
                    )
                )
            } else {
                showPremiumFeaturePopup()
            }
        }

        binding.addInfoLanguages.addInfoBtn.setOnSingleClickListener {
            val languagesExtended = viewModel.getModifiedFilter()?.languagesExtended
            findNavController().navigate(R.id.action_filterOptionsFragment_to_filterItemTagPickerBottomSheet,
                bundleOf(
                    FILTER_TYPE to FilterType.LANGUAGES,
                    FilterItemTagPickerBottomSheet.CHOSEN_ITEMS to null,
                    FILTER_EXTENDED to languagesExtended,
                    PROFILE_EDIT to true
                )
            )

            sendAddInfoInitiatedEvent(FilterType.LANGUAGES, userModel)
        }

        binding.addLanguagesComponent.addInfoContainer.setOnSingleClickListener {
            if(homeViewModel.userProfile.value?.premiumType != null) {
                val chosenItems = viewModel.getModifiedFilter()?.languagesList
                val languagesExtended = viewModel.getModifiedFilter()?.languagesExtended
                findNavController().navigate(
                    R.id.action_filterOptionsFragment_to_filterItemTagPickerBottomSheet,
                    bundleOf(
                        FILTER_TYPE to FilterType.LANGUAGES,
                        FilterItemTagPickerBottomSheet.CHOSEN_ITEMS to chosenItems,
                        FILTER_EXTENDED to languagesExtended
                    )
                )
            } else {
                showPremiumFeaturePopup()
            }
        }

        binding.addInfoReligion.addInfoBtn.setOnSingleClickListener {
            val religionsExtended = viewModel.getModifiedFilter()?.religionsExtended
            findNavController().navigate(R.id.action_filterOptionsFragment_to_filterItemTagPickerBottomSheet,
                bundleOf(
                    FILTER_TYPE to FilterType.RELIGION,
                    FilterItemTagPickerBottomSheet.CHOSEN_ITEMS to null,
                    FILTER_EXTENDED to religionsExtended,
                    PROFILE_EDIT to true
                )
            )

            sendAddInfoInitiatedEvent(FilterType.RELIGION, userModel)
        }

        binding.addReligionComponent.addInfoContainer.setOnSingleClickListener {
            if(homeViewModel.userProfile.value?.premiumType != null) {
                val chosenItems = viewModel.getModifiedFilter()?.religionsList
                val religionsExtended = viewModel.getModifiedFilter()?.religionsExtended
                findNavController().navigate(
                    R.id.action_filterOptionsFragment_to_filterItemTagPickerBottomSheet,
                    bundleOf(
                        FILTER_TYPE to FilterType.RELIGION,
                        FilterItemTagPickerBottomSheet.CHOSEN_ITEMS to chosenItems,
                        FILTER_EXTENDED to religionsExtended
                    )
                )
            } else {
                showPremiumFeaturePopup()
            }
        }

        binding.locationContainer.setOnSingleClickListener {
            val intent = Intent(context, ChangeLocationActivity::class.java)
            intent.putExtra("UserModel", homeViewModel.userProfile.value)
            requireActivity().startActivityForResult(
                intent,
                ChangeLocationActivity.CHANGE_LOCATION_SUCCESS
            )
        }

//        binding.otherEthnicitiesContainer.setOnSingleClickListener {
//            val id = R.id.action_filterOptionsFragment_to_addEthnicitiesBottomSheet
//            val ethnicities = viewModel.getModifiedFilter()?.communitiesList?.filterNot { it == userModel?.communityInfo?.id }
//            findNavController().navigate(id, bundleOf(CHOSEN_ITEMS to ethnicities))
//        }

        binding.applyBtn.setOnSingleClickListener {
            viewModel.updateUserFilter()
        }

//        binding.ethnicityReset.setOnSingleClickListener {
//            viewModel.updateEthnicities(emptyList())
//            viewModel.updateIsGlobalState(false)
//        }
        binding.verifiedProfilesReset.setOnSingleClickListener {
            viewModel.updateVerifiedProfiles(false)
        }
        binding.heightReset.setOnSingleClickListener {
            val minHeightDefault = userModel?.getDefaultFilters()?.minHeight
            val maxHeightDefault = userModel?.getDefaultFilters()?.maxHeight
            val heightDefault = userModel?.getDefaultFilters()?.heightExtended
            if(minHeightDefault != null && maxHeightDefault != null && heightDefault != null) {
                viewModel.updateHeight(minHeightDefault, maxHeightDefault, false)
                viewModel.extendHeight(heightDefault)
            }
        }
        binding.lookingForReset.setOnSingleClickListener {
            val lookingForExtended = userModel?.getDefaultFilters()?.lookingForExtended
            if(lookingForExtended != null)
                viewModel.updateLookingFar(emptyList(), lookingForExtended)
        }
        binding.languageReset.setOnSingleClickListener {
            val languagesExtended = userModel?.getDefaultFilters()?.languagesExtended
            if(languagesExtended != null)
                viewModel.updateLanguages(emptyList(), languagesExtended)
        }
        binding.religionReset.setOnSingleClickListener {
            val religionsExtended = userModel?.getDefaultFilters()?.religionsExtended
            if(religionsExtended != null)
                viewModel.updateReligion(emptyList(), religionsExtended)
        }

    }

    private fun registerReceivers() {
        val premiumSubscriptionFilter = IntentFilter(PREMIUM_INTENT)
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(premiumBroadcastReceiver, premiumSubscriptionFilter)
    }

    private fun toggleLoadingVisibility(showLoading: Boolean = false) {
        if(showLoading) {
            binding.applyBtn.visibility = View.INVISIBLE
            binding.progressBar.visibility = View.VISIBLE
        } else {
            binding.applyBtn.visibility = View.VISIBLE
            binding.progressBar.visibility = View.GONE
        }
    }

    private fun updateVerifiedProfilesComponent(uiData: FilterOptionsUiData) {
        val isVerified = uiData.modifiedData?.filter?.verified ?: false
        binding.verifiedProfilesSwitch.isChecked = isVerified
        binding.verifiedProfilesReset.isVisible = isVerified
    }

    private fun updateComponent(
        infoRoot: View,
        componentRoot: View,
        hasInfo: Boolean,
        list: List<Int>? = emptyList(),
        placeholder: TextView,
        resetButton: Button
    ) {
        setVisibility(resetButton, hasInfo && !list.isNullOrEmpty())
        setVisibility(infoRoot, !hasInfo)
        setVisibility(componentRoot, hasInfo)
        val items = viewModel.tagList?.flatMap { it.items }?.filter { list?.contains(it.id) ?: false }
        val stringItems = items?.joinToString(", ") { it.title }?.ifEmpty { getString(R.string.add) } ?: getString(R.string.add)
        placeholder.text = stringItems
        val addImage = if(items.isNullOrEmpty())
            R.drawable.ic_plus_gray
        else
            R.drawable.ic_pen_v2
        componentRoot.findViewById<ImageButton>(R.id.btn_add_info).setImageResource(addImage)
    }

    private fun updateHeightComponent(
        infoRoot: View,
        componentRoot: View,
        hasInfo: Boolean,
        resetButton: Button,
        showResetBtn: Boolean
    ) {
        if(RemoteConfigUtils.isHeightFilterEnabled()) {
            setVisibility(resetButton, showResetBtn)
            setVisibility(infoRoot, !hasInfo)
            setVisibility(componentRoot, hasInfo)
        } else {
            setVisibility(binding.heightTitle, false)
            setVisibility(resetButton, false)
            setVisibility(infoRoot, false)
            setVisibility(componentRoot, false)
        }
    }

    private fun getAgeRangeText(minAge: Int, maxAge: Int): String {
        return if (maxAge >= 60) {
            getString(R.string.between_caption_range, "${minAge}", "$maxAge+")
        } else {
            getString(R.string.between_caption_range, minAge.toString(), maxAge.toString())
        }
    }

    private fun getHeightDescription(minHeight: Int?, maxHeight: Int?): String {
        return if (minHeight == 80 && maxHeight == 250) {
            getString(R.string.any_height_caption)
        } else {
            "$minHeight ${getString(R.string.to_caption)} $maxHeight cm"
        }
    }

    private fun updateExtendedFilters(binding: FragmentFilterBottomsheetBinding, filter: Filter) {
        val extendedRange = filter.areFiltersExtended == true
        binding.extendedRange.isChecked = extendedRange
    }

    private fun updateRangeBars(
        minHeight: Int,
        maxHeight: Int,
        minAge: Int,
        maxAge: Int,
    ) {
        updateHeightRangeBar(minHeight, maxHeight)
        updateAgeRangeBar(minAge, maxAge)
    }

    private fun updateHeightRangeBar(
        minHeight: Int,
        maxHeight: Int,
    ) {
        binding.heightRangeBar.setRangePinsByValue(minHeight.toFloat(), maxHeight.toFloat())
    }

    private fun updateAgeRangeBar(
        minAge: Int,
        maxAge: Int,
    ) {
        val filteredMinAge = minAge.coerceIn(18, 60)
        val filteredMaxAge = maxAge.coerceIn(18, 60)
        binding.ageRangeBar.setRangePinsByValue(filteredMinAge.toFloat(), filteredMaxAge.toFloat())
    }

    private fun updateLocationUI(
        context: Context,
        isFlyMode: Boolean,
        binding: FragmentFilterBottomsheetBinding
    ) {
        val locationString =
            if (isFlyMode) R.string.fly_location_status else R.string.current_location
        val locationDrawable = if (isFlyMode) ContextCompat.getDrawable(
            context,
            R.drawable.airplane_outline
        ) else ContextCompat.getDrawable(context, R.drawable.ic_location_icon)
        binding.locationTxt.text = getString(locationString)
        binding.imgLocation.setImageDrawable(locationDrawable)
    }


    private fun checkWhenCityChanged() {
        if (firstTimeCity != null) {
            if (firstTimeCity != binding.chosenLocation.text.toString()) {
                /* binding.cancelButton.visibility = View.INVISIBLE
                 binding.cancelButton.isEnabled = false
                 binding.doneBtn.visibility = View.VISIBLE*/
            }
        } else {
            firstTimeCity = binding.chosenLocation.text.toString()
        }
    }

    private fun showPremiumFeaturePopup() {
        if(!DuaApplication.instance.getBillingAvailable()) {
            requireActivity().showBillingNotAvailable(
                title = getString(R.string.billings_not_available_title_an),
                message = getString(R.string.billings_not_available_desc_an)
            )
        } else {
            requireActivity().openPremiumPaywall(
                eventSourceClevertap = ClevertapEventSourceValues.ADVANCED_FILTERS,
                placementId = PurchaselyPlacement.ADVANCED_FILTERS.id,
                userModel = homeViewModel.userProfile.value
            )
        }
    }

    fun popBackStack() {
        (requireActivity() as HomeActivity).navController.popBackStack()
    }

    override fun onDestroyView() {
        super.onDestroyView()

        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(premiumBroadcastReceiver)

        _binding = null
    }

    fun onUserHeightChanged() {
        val filter = viewModel.getModifiedFilter()
        val defaultFilters = userModel?.getDefaultFilters()
        if(filter == null || defaultFilters == null) return

        val showResetHeight = userModel?.profile?.height != null &&
                (filter.minHeight != defaultFilters.minHeight ||
                        filter.maxHeight != defaultFilters.maxHeight ||
                        filter.heightExtended != defaultFilters.heightExtended)

        updateHeightComponent(
            infoRoot = binding.addInfoHeight.root,
            componentRoot = binding.heightContainer,
            hasInfo = homeViewModel.userProfile.value?.profile?.height != null,
            resetButton = binding.heightReset,
            showResetBtn = showResetHeight
        )
    }


    companion object {
        const val ARG_EVENT_SOURCE = "arg_event_source"

    }
}