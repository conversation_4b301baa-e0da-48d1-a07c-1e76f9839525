package com.duaag.android.home.fragments

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.duaag.android.clevertap.ClevertapAddPhotoSourceValues
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentShadowBannedDialogBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsAddPhotoSourceValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.signup.fragment.guidelines.DialogClickListener
import com.duaag.android.signup.fragment.guidelines.GuidelinesDialogFragment
import com.duaag.android.utils.imageUrlCircleFullResolution
import com.duaag.android.utils.setOnSingleClickListener
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class ShadowBannedDialogFragment : BottomSheetDialogFragment(), DialogClickListener {

    private var _binding: FragmentShadowBannedDialogBinding? = null
    private val binding get() = _binding

    private val imageUrl: String? by lazy { arguments?.getString(IMAGE_URL) }
    private val premiumType: String? by lazy { arguments?.getString(PREMIUM_TYPE) }
    private var listener: ShadowBannedDialog.ShadowBannedDialogListener? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            BottomSheetBehavior.from<FrameLayout?>(bottomSheet!!).state =
                BottomSheetBehavior.STATE_EXPANDED
            d.dismissWithAnimation = true
        }
        return dialog
    }
    override fun onAttach(context: Context) {
        super.onAttach(context)
        try {
            listener = parentFragment as ShadowBannedDialog.ShadowBannedDialogListener
        } catch (e: ClassCastException) {
            throw ClassCastException(
                (context.toString() +
                        " must implement GhostModeDialogListener")
            )
        }
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        _binding = FragmentShadowBannedDialogBinding.inflate(inflater, container, false)
        return binding?.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding?.image?.let { imageUrlCircleFullResolution(it,imageUrl) }
        binding?.addPhotoBtn?.setOnSingleClickListener{
          listener?.changePhoto()
            firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_PHOTO)
            sendClevertapEvent(ClevertapEventEnum.CHANGE_PHOTO)
            sendAddPhotoEvents()
            dismissAllowingStateLoss()
        }
        binding?.seeGuidelinesTxt?.setOnSingleClickListener{
             val dialog= GuidelinesDialogFragment.newInstance(premiumTypeEventProperty = premiumType)
            dialog.show(childFragmentManager,"GuidelinesDialog")
        }
    }
    private fun sendAddPhotoEvents() {
        sendClevertapEvent(
            ClevertapEventEnum.ADD_PHOTO,
            mapOf(ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to ClevertapAddPhotoSourceValues.BOTTOM_SHEET.value)
        )
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.ADD_PHOTO,
            mapOf(FirebaseAnalyticsParameterName.EVENT_SOURCE.value to FirebaseAnalyticsAddPhotoSourceValues.BOTTOM_SHEET.value)
        )
    }
    companion object {
        const val IMAGE_URL = "image_url"
        const val PREMIUM_TYPE = "premium_type"

        fun newInstance(imageUrl: String,premiumType: String?): ShadowBannedDialogFragment =
            ShadowBannedDialogFragment().apply {
                arguments = Bundle().apply {
                    putString(IMAGE_URL, imageUrl)
                    putString(PREMIUM_TYPE,premiumType)
                }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onDetach() {
        super.onDetach()
        listener = null
    }

    override fun onButtonClicked() {
       binding?.addPhotoBtn?.performClick()
    }
}