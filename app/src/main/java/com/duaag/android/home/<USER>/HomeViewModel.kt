package com.duaag.android.home.viewmodels

import android.annotation.SuppressLint
import android.location.Location
import android.net.Uri
import android.os.Handler
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.android.billingclient.api.Purchase
import com.applovin.sdk.AppLovinSdk
import com.appsflyer.AppsFlyerProperties
import com.clevertap.android.sdk.CleverTapAPI
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.ads.RewardAdType
import com.duaag.android.api.InteractionErrorBody
import com.duaag.android.api.InteractionErrorType
import com.duaag.android.api.ListState
import com.duaag.android.api.Resource
import com.duaag.android.api.ResourceV2
import com.duaag.android.api.Result
import com.duaag.android.api.UserService
import com.duaag.android.api.socket.ChatWebSocket
import com.duaag.android.api.socket.model.ErrorChatWebSocketModel
import com.duaag.android.api.socket.model.LikeYouWebSocketModel
import com.duaag.android.api.socket.model.LikedMessageStateChanged
import com.duaag.android.api.socket.model.MatchesWebSocketModel
import com.duaag.android.api.socket.model.NewConversationTokenModel
import com.duaag.android.api.socket.model.UpdateTypingStateChanged
import com.duaag.android.api.socket.model.UserDeletedWebSocketModel
import com.duaag.android.api.socket.model.UserUnmatchedWebSocketModel
import com.duaag.android.api.socket.model.WebSocketMessageModel
import com.duaag.android.api.socket.model.mapToMessageModel
import com.duaag.android.application.DuaApplication
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.AppsflyerEventsPropertyEnum
import com.duaag.android.appsflyer.AppsflyerSpecialOfferValueEnum
import com.duaag.android.appsflyer.domain.AppsFlyerBackendManager
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.appsflyer.validateAndLogInAppPurchase
import com.duaag.android.base.models.AccountModel
import com.duaag.android.base.models.CommunityInfo
import com.duaag.android.base.models.INSTAGRAM_STATUS_DISCONNECTED
import com.duaag.android.base.models.INSTAGRAM_STATUS_TOKEN_REFRESH_ERROR
import com.duaag.android.base.models.InstagramToken
import com.duaag.android.base.models.PremiumEnum
import com.duaag.android.base.models.Profile
import com.duaag.android.base.models.UpdateTagsModel
import com.duaag.android.base.models.UserModel
import com.duaag.android.change_location.models.LocationModel
import com.duaag.android.change_location.models.LocationType
import com.duaag.android.change_location.repositories.LocationsRepository
import com.duaag.android.chat.ChatRepository
import com.duaag.android.chat.model.ConversationData
import com.duaag.android.chat.model.ConversationModel
import com.duaag.android.chat.model.ConversationToken
import com.duaag.android.chat.model.ConversationType
import com.duaag.android.chat.model.ConversationWebSocketModel
import com.duaag.android.chat.model.LikeMessageModel
import com.duaag.android.chat.model.MessageModel
import com.duaag.android.chat.model.MessageType
import com.duaag.android.chat.model.UserLikesModel
import com.duaag.android.chat.model.UserMatchesModel
import com.duaag.android.chat.repositories.LikedYouRepository
import com.duaag.android.clevertap.ClevertapBoostActivationSource
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapFiltersEventSourceValues
import com.duaag.android.clevertap.ClevertapGhostSourceValues
import com.duaag.android.clevertap.ClevertapGhostStatusValues
import com.duaag.android.clevertap.ClevertapMatchTypeValues
import com.duaag.android.clevertap.ClevertapUserPropertyEnum
import com.duaag.android.clevertap.PremiumBadgeValues
import com.duaag.android.clevertap.SwipeSourceValues
import com.duaag.android.clevertap.areUserPropertiesSynced
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.linkCleverTapAppsFlyer
import com.duaag.android.clevertap.onLoginCleverTap
import com.duaag.android.clevertap.pushUserProfileClevertap
import com.duaag.android.clevertap.sendClevertapCardResultEvent
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.sendPremiumPurchasedEvent
import com.duaag.android.clevertap.sendRestorePremiumEvent
import com.duaag.android.clevertap.sendRmodRemovedEvent
import com.duaag.android.clevertap.updateUserProfileInClevertap
import com.duaag.android.counters.data.models.CounterEntity
import com.duaag.android.counters.domain.CalculateIntervalTimeInHourUseCase
import com.duaag.android.counters.domain.GetLocalCountersUseCase
import com.duaag.android.counters.domain.GetUserCountersByConfNamesUseCase
import com.duaag.android.counters.domain.UpdateUserCounterUseCase
import com.duaag.android.di.ActivityScope
import com.duaag.android.exceptions.LimitReachedException
import com.duaag.android.exceptions.NoConnectivityException
import com.duaag.android.exceptions.PaymentAlreadyConsumedException
import com.duaag.android.exceptions.PremiumSyncException
import com.duaag.android.exceptions.SubscriptionConflictException
import com.duaag.android.exceptions.UserDeletedExistException
import com.duaag.android.exceptions.UserNotExistException
import com.duaag.android.fingerprint_pro.domain.usecase.VisitorIdUseCase
import com.duaag.android.firebase.NotificationConstants
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.firebase.NotificationRepository
import com.duaag.android.firebase.model.ConsumableRewardGivenModel
import com.duaag.android.firebase.model.DislikeInstaChatModel
import com.duaag.android.firebase.model.UserLikedYouNotificationResponse
import com.duaag.android.firebase.model.UserUnMatchedModel
import com.duaag.android.home.fragments.HomeFragment.Companion.CARDSTAG
import com.duaag.android.home.interfaces.NotificationsReceivedInterface
import com.duaag.android.home.models.BoostFailedException
import com.duaag.android.home.models.DontLetGoOfferAvailableResponseModel
import com.duaag.android.home.models.GooglePlayPackage
import com.duaag.android.home.models.HasChatModel
import com.duaag.android.home.models.HomeScreenType
import com.duaag.android.home.models.InteractionBody
import com.duaag.android.home.models.InteractionType
import com.duaag.android.home.models.InteractionsResponse
import com.duaag.android.home.models.LimitReachedModel
import com.duaag.android.home.models.LimitReachedScreenSource
import com.duaag.android.home.models.PaymentType
import com.duaag.android.home.models.PaymentVerifyErrorBody
import com.duaag.android.home.models.PaymentVerifyErrorType
import com.duaag.android.home.models.PremiumOfferTypes
import com.duaag.android.home.models.ProfileActivitiesModel
import com.duaag.android.home.models.ProfileActivitiesTypeCardSupport
import com.duaag.android.home.models.ProfileInfoModel
import com.duaag.android.home.models.ProfileInfoTypeCardSupport
import com.duaag.android.home.models.RecommendedUserModel
import com.duaag.android.home.models.VerifyPaymentModel
import com.duaag.android.home.models.asConversationModel
import com.duaag.android.home.profile_activities_card.data.ProfileActivitiesCardRepository
import com.duaag.android.home.profile_info_card.data.ProfileInfoCardRepository
import com.duaag.android.instagram.models.InstagramMediaResponse
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoEventProperties
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoSourceValues
import com.duaag.android.logevents.firebaseanalytics.AddProfileInfoTypeValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.sendFirebaseCardResultEvent
import com.duaag.android.premium_subscription.PurchaselyManager
import com.duaag.android.premium_subscription.models.InAppPurchaseItemInfoModel
import com.duaag.android.premium_subscription.models.SpecialOfferDataModel
import com.duaag.android.profile_new.models.UpdateUserResponse
import com.duaag.android.recommender.domain.model.FeaturedProfileModel
import com.duaag.android.recommender.domain.model.RmodModel
import com.duaag.android.recommender.domain.use_case.GetCardsAndFeaturedProfilesUseCase
import com.duaag.android.recommender.presentation.FeaturedProfilesState
import com.duaag.android.rewards.ConsumableRewardBaseDialog
import com.duaag.android.rewards.highlight.boost.BoostHighlightData
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.settings.fragments.language.domain.UpdateLanguageAndTagsUseCase
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.DuaAccount
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.firstPartOfConversationId
import com.duaag.android.utils.livedata.SingleLiveData
import com.duaag.android.utils.secondPartOfConversationId
import com.duaag.android.broadcast.DuaEvent
import com.duaag.android.broadcast.EventBus
import com.google.gson.Gson
import com.uxcam.UXCam
import com.yuyakaido.android.cardstackview.Direction
import io.purchasely.ext.Purchasely
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.retryWhen
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Named
import kotlin.collections.set
import kotlin.ranges.contains

@ActivityScope
class HomeViewModel @Inject constructor(
    @Named("private") val userService: UserService,
    val duaSharedPrefs: DuaSharedPrefs,
    val userRepository: UserRepository,
    val likedYouRepository: LikedYouRepository,
    val chatRepository: ChatRepository,
    val locationsRepository: LocationsRepository,
    val profileInfoCardRepository: ProfileInfoCardRepository,
    val profileActivitiesCardRepository: ProfileActivitiesCardRepository,
    val notificationRepository: NotificationRepository,
    private val getCardsAndFeaturedProfilesUseCase: GetCardsAndFeaturedProfilesUseCase,
    private val getUserCountersByConfNamesUseCase: GetUserCountersByConfNamesUseCase,
    private val updateUserCounterUseCase: UpdateUserCounterUseCase,
    private val getLocalCountersUseCase: GetLocalCountersUseCase,
    private val appsFlyerBackendManager: AppsFlyerBackendManager,
    private val updateLanguageAndTagsUseCase: UpdateLanguageAndTagsUseCase,
    private val visitorIdUseCase: VisitorIdUseCase,
) : ViewModel(), NotificationsReceivedInterface,ChatWebSocket.HomeChatWebSocketReceivedInterface {


    companion object {
        const val TAG = "HomeViewModelTag"
        var ignoreUserUpdating: Boolean = false

        const val CACHE_EXPIRE_TIME_MINUTES = 5L
    }

    var profileActivitiesItemIdToRestore: Int? = null
    var badge2: Badge2Status = Badge2Status.NULL
    var fetchedRecommendedUsersIds = mutableListOf<String>()
    var swipedRecommendedUsersIds = mutableListOf<String>()
    var cachedUsers = mutableListOf<RecommendedUserModel>()
    var hasPremiumBeenRestored = false
    var hasUpgradedToPremium = false
    var communitiesLoaded: Boolean = false

    @Inject
    lateinit var duaAccount: DuaAccount

    var accountModel = userRepository.getAccount()
    var tags = userRepository.tagsLiveData()

    private var firstAdCardAppearanceSet: Boolean = false
    private var adCardCountdown: Int = 5
    private var firstQuestionCardAppearanceSet: Boolean = false
    private var questionCardCountdown: Int = 5
    private var firstFeatureCardAppearanceSet: Boolean = false
    private var featureCardCountdown: Int = 5


    private var brillionAdHasBeenShown = false
    private var incognitoModeCardHasBeenShown = false

    //this variable is reserved to remember the last card for Undo
    var lastCard: Pair<Direction, RecommendedUserModel>? = null
    var lastCardHandler: Handler? = null
    val _missedMatchCard: MutableSharedFlow<RecommendedUserModel?> = MutableSharedFlow()
    val missedMatchCard: SharedFlow<RecommendedUserModel?> get() = _missedMatchCard
    private var lastMissedMatchCard: RecommendedUserModel? = null
    private var lastMissedMatchCardIdForEvent: String? = null
    fun isMissedMatch():Boolean = lastCard != null && lastCard?.second?.cognitoUserId == lastMissedMatchCard?.cognitoUserId
    fun cleanMissedMatch() {
        lastMissedMatchCard = null
    }

    private var cardsSwipedCount: Int = 0
    fun increaseCardsSwipedCount() = cardsSwipedCount++
    fun getCardsSwipedCount() = cardsSwipedCount
    private var isUserRecommendedLoading: Boolean = false

    private val _recommendedUsers: MutableLiveData<List<RecommendedUserModel>> = MutableLiveData()
    val recommendedUsers: LiveData<List<RecommendedUserModel>>
        get() = _recommendedUsers

    val _rmodItem: SingleLiveData<RmodModel?> = SingleLiveData()
    val rmodItem: LiveData<RmodModel?>
        get() = _rmodItem

    private val _readWritePermission: SingleLiveData<Void> = SingleLiveData()
    val readWritePermission: LiveData<Void>
        get() = _readWritePermission

    private var _onRequestPermissionsResultForVideoCall: SingleLiveData<Boolean> =
        SingleLiveData()
    val onRequestPermissionsResultForVideoCall: LiveData<Boolean>
        get() = _onRequestPermissionsResultForVideoCall


    private val _isFromGuidelines: SingleLiveData<Boolean> = SingleLiveData()
    val isFromGuidelines: LiveData<Boolean>
        get() = _isFromGuidelines

    private val _showFilter: SingleLiveData<String> = SingleLiveData()
    val showFilter: LiveData<String>
        get() = _showFilter

    private val _recommendedUsersState: MutableLiveData<ListState> = MutableLiveData()
    val recommendedUsersState: LiveData<ListState>
        get() = _recommendedUsersState

    private val _featuredProfilesUsersState: MutableStateFlow<FeaturedProfilesState> = MutableStateFlow(FeaturedProfilesState())
    val featuredProfilesUsersState : LiveData<FeaturedProfilesState> get() = _featuredProfilesUsersState.asLiveData()

    private val _reloadRecommendedUsers: SingleLiveData<Void> = SingleLiveData()
    val reloadRecommendedUsers: LiveData<Void>
        get() = _reloadRecommendedUsers

    private val _limitReached: SingleLiveData<LimitReachedModel> = SingleLiveData()
    val limitReached: LiveData<LimitReachedModel>
        get() = _limitReached

    private val _onMatchFragmentShowed = SingleLiveData<Boolean>()
    private val _onBackFromConversation = SingleLiveData<Boolean>()

    //notificationNavigation variables
    private val _navigateToChat: SingleLiveData<ConversationModel> = SingleLiveData()
    val navigateToChat: LiveData<ConversationModel>
        get() = _navigateToChat

    private val _navigateToInstaChat: SingleLiveData<ConversationModel> = SingleLiveData()
    val navigateToInstaChat: LiveData<ConversationModel>
        get() = _navigateToInstaChat

    private val _navigateToInstaChatList: SingleLiveData<Boolean> = SingleLiveData()
    val navigateToInstaChatList: LiveData<Boolean>
        get() = _navigateToInstaChatList

    private val _deepLinkInstaChat: SingleLiveData<Boolean> = SingleLiveData()
    val deepLinkInstaChat: LiveData<Boolean>
        get() = _deepLinkInstaChat

   private val _deepLinkMatches: SingleLiveData<Boolean> = SingleLiveData()
    val deepLinkMatches: LiveData<Boolean>
        get() = _deepLinkMatches

    private val _openUserProfileToLikedYou: SingleLiveData<UserLikesModel> = SingleLiveData()

    val openUserProfileToLikedYou: LiveData<UserLikesModel>
        get() = _openUserProfileToLikedYou

    private val _newLikeYou: MutableLiveData<List<UserLikesModel>> = MutableLiveData()
    val newLikeYou: LiveData<List<UserLikesModel>>
        get() = _newLikeYou

    private val _newMatch: MutableLiveData<List<UserMatchesModel>> = MutableLiveData()
    val newMatch: LiveData<List<UserMatchesModel>>
        get() = _newMatch

    private val _newConversation: MutableLiveData<List<ConversationModel>> = MutableLiveData()
    val newConversation: LiveData<List<ConversationModel>>
        get() = _newConversation

    private val _unMatches: MutableLiveData<List<UserUnMatchedModel>> = MutableLiveData()
    val unMatches: LiveData<List<UserUnMatchedModel>>
        get() = _unMatches

    private val _newMatchInternal: SingleLiveData<String> = SingleLiveData()
    val newMatchInternal: LiveData<String> get() = _newMatchInternal

    private val _dislikeInstaChat: SingleLiveData<DislikeInstaChatModel> = SingleLiveData()
    val dislikeInstaChat: LiveData<DislikeInstaChatModel> get() = _dislikeInstaChat

    //dot Notification variables
    private val _showChatDotNotification: MutableLiveData<Boolean> = MutableLiveData()
    val showChatDotNotification: LiveData<Boolean>
        get() = _showChatDotNotification

    private val _showNotificationFeedDotNotification: MutableLiveData<Boolean> = MutableLiveData()
    val showNotificationFeedDotNotification: LiveData<Boolean>
        get() = _showNotificationFeedDotNotification

    private val _showLikesDotNotification: MutableLiveData<Boolean> = MutableLiveData()
    val showLikesDotNotification: LiveData<Boolean>
        get() = _showLikesDotNotification

    val _showOnlyMatchDotNotification: SingleLiveData<Boolean> = SingleLiveData()
    val showOnlyMatchDotNotification: LiveData<Boolean>
        get() = _showOnlyMatchDotNotification

    val _dismisCardWhenReporting: SingleLiveData<Void> = SingleLiveData()
    val dismisCardWhenReporting: LiveData<Void>
        get() = _dismisCardWhenReporting

    private val _deleteUserMatchByUserIdFromConversation1to1: SingleLiveData<String> =
        SingleLiveData()
    val deleteUserMatchByUserIdFromConversation1to1: LiveData<String>
        get() = _deleteUserMatchByUserIdFromConversation1to1

    private val _onNetworkConnectionChanged: SingleLiveData<Boolean> = SingleLiveData()
    val onNetworkConnectionChanged: LiveData<Boolean>
        get() = _onNetworkConnectionChanged

    private val _showMatchScreen: SingleLiveData<RecommendedUserModel> = SingleLiveData()
    val showMatchScreen: LiveData<RecommendedUserModel>
        get() = _showMatchScreen

    private val _showMatchScreenIfHasChat: SingleLiveData<HasChatModel> = SingleLiveData()
    val showMatchScreenIfHasChat: LiveData<HasChatModel>
        get() = _showMatchScreenIfHasChat

    private val _deleteUser: SingleLiveData<UserDeletedWebSocketModel> = SingleLiveData()
    val deleteUser: LiveData<UserDeletedWebSocketModel>
        get() = _deleteUser

    private val _showEnvelopeDialog: SingleLiveData<Boolean> = SingleLiveData()
    val showEnvelopeDialog: LiveData<Boolean>
        get() = _showEnvelopeDialog

    private val _conversationHasChat: SingleLiveData<ConversationModel> = SingleLiveData()
    val conversationHasChat: LiveData<ConversationModel>
        get() = _conversationHasChat

    private val _singleConversationHasChat: SingleLiveData<ConversationModel> = SingleLiveData()
    val singleConversationHasChat: LiveData<ConversationModel>
        get() = _singleConversationHasChat

    private val _conversationHasChatError: SingleLiveData<Boolean> = SingleLiveData()
    val conversationHasChatError: LiveData<Boolean>
        get() = _conversationHasChatError

    private val _consumePurchase: SingleLiveData<String> = SingleLiveData()
    val consumePurchase: LiveData<String>
        get() = _consumePurchase


    private val _showSpinningDialog: SingleLiveData<Boolean> = SingleLiveData()
    val showSpinningDialog: LiveData<Boolean>
        get() = _showSpinningDialog

    private val _premiumBoughtSuccessfully: MutableSharedFlow<Unit> = MutableSharedFlow()
    val premiumBoughtSuccessfully: SharedFlow<Unit>
        get() = _premiumBoughtSuccessfully.asSharedFlow()

    private val _cancelPremiumSub: SingleLiveData<Boolean> = SingleLiveData()
    val cancelPremiumSub: LiveData<Boolean>
        get() = _cancelPremiumSub

    private val _callEnded: SingleLiveData<Void> = SingleLiveData()
    val callEnded: LiveData<Void>
        get() = _callEnded

    private val _showShadowBannedDialog: SingleLiveData<Void> = SingleLiveData()
    val showShadowBannedDialog: LiveData<Void>
        get() = _showShadowBannedDialog

    //we use this to show the profile again if the user was out of instachats and was trying to send one
    //from new visited profile
    private val _instachatsBought: SingleLiveData<Void> = SingleLiveData()
    val instachatsBought: LiveData<Void>
        get() = _instachatsBought

    private val _profileActivitiesItems : MutableStateFlow<List<ProfileActivitiesModel>?> = MutableStateFlow(listOf())
    val profileActivitiesItems : LiveData<List<ProfileActivitiesModel>?> get() = _profileActivitiesItems.asLiveData()

    private val _filteredProfileActivitiesItems : MutableStateFlow<List<ProfileActivitiesModel>?> = MutableStateFlow(listOf())
    val filteredProfileActivitiesItems : LiveData<List<ProfileActivitiesModel>?> get() = _filteredProfileActivitiesItems.asLiveData()

    val communitiesList: LiveData<List<CommunityInfo>>
        get() = userRepository.localCommunities.asLiveData()

    fun showShadowBannedDialog() {
        _showShadowBannedDialog.call()
    }
    fun showEnvelopeDialog() {
        _showEnvelopeDialog.value = true
    }

    fun showCancelPremium(value: Boolean){
        _cancelPremiumSub.postValue(value)
    }

    private val _showTrophyDialog: SingleLiveData<Boolean> = SingleLiveData()
    val showTrophyDialog: LiveData<Boolean>
        get() = _showTrophyDialog

    private val _instaChatSent: SingleLiveData<Boolean> = SingleLiveData()
    val instaChatSent: LiveData<Boolean>
        get() = _instaChatSent

    private val _subscriptionValidationError: SingleLiveData<PaymentVerifyErrorBody> = SingleLiveData()
    val subscriptionValidationError: LiveData<PaymentVerifyErrorBody>
        get() = _subscriptionValidationError

    private val _userRemovedYou: SingleLiveData<String> = SingleLiveData()
    val userRemovedYou: LiveData<String>
        get() = _userRemovedYou


//    private val _accountCounterUpdate: SingleLiveData<InteractionType> = SingleLiveData()
//    val accountCounterUpdate: LiveData<InteractionType>
//        get() = _accountCounterUpdate

    private var interactionJob: Job? = null
    private var undoJob: Job? = null

    private val _showProgressBarUser: SingleLiveData<Boolean> = SingleLiveData()
    val showProgressBarUser: LiveData<Boolean>
        get() = _showProgressBarUser

    private val _interactionUndone: SingleLiveData<RecommendedUserModel> = SingleLiveData()
    val interactionUndone: LiveData<RecommendedUserModel>
        get() = _interactionUndone


    fun getSpecialOffer(): SpecialOfferDataModel? = DuaApplication.instance.getSpecialOfferData()

    fun getDontLetGoOffer() = duaSharedPrefs.getDontLetGoOfferData()

    private val _specialOfferReceived: SingleLiveData<Void> = SingleLiveData()
    val specialOfferReceived: LiveData<Void>
        get() = _specialOfferReceived

//    private val _accountCounterAnimation: SingleLiveData<Triple<Int, Int, InteractionType>> =
//        SingleLiveData()
//    val accountCounterAnimation: SingleLiveData<Triple<Int, Int, InteractionType>>
//        get() = _accountCounterAnimation


//    private val _isUserPremiumCounter: SingleLiveData<Boolean> = SingleLiveData()
//    val isUserPremiumCounter: LiveData<Boolean>
//        get() = _isUserPremiumCounter

    private val _openFilterGuide: SingleLiveData<Void> = SingleLiveData()
    val openFilterGuide: LiveData<Void>
        get() = _openFilterGuide

    private val _continueUserGuide: SingleLiveData<Void> = SingleLiveData()
    val continueUserGuide: LiveData<Void>
        get() = _continueUserGuide

    private var _openCreateProfile: SingleLiveData<Boolean> = SingleLiveData()
    val openCreateProfile: LiveData<Boolean>
        get() = _openCreateProfile


    private var _showInstagramReconnectDialog: SingleLiveData<Boolean> = SingleLiveData()
    val showInstagramReconnectDialog: LiveData<Boolean>
        get() = _showInstagramReconnectDialog

    private var _checkIfAdViewIsShowedOnCard: SingleLiveData<Boolean> = SingleLiveData()
    val checkIfAdViewIsShowedOnCard: LiveData<Boolean>
        get() = _checkIfAdViewIsShowedOnCard

    private var _boostStatusChanged: SingleLiveData<Void> = SingleLiveData()
    val boostStatusChanged: LiveData<Void>
        get() = _boostStatusChanged

    private var _userPhotosChanged: MutableLiveData<Void> = MutableLiveData()
    val userPhotosChanged: LiveData<Void>
        get() = _userPhotosChanged

    private var _purchaselySetUp: SingleLiveData<Void> = SingleLiveData()
    val purchaselySetUp: LiveData<Void>
        get() = _purchaselySetUp

   private var _isGracePeriod: MutableLiveData<Boolean> = MutableLiveData(false)
    val isGracePeriod: LiveData<Boolean>
        get() = _isGracePeriod



    private var _homeScreenType: MutableLiveData<HomeScreenType> = MutableLiveData(HomeScreenType.CARDS)
    val homeScreenType: LiveData<HomeScreenType>
        get() = _homeScreenType

    private var _userProfileNU: MutableLiveData<UserModel> = MutableLiveData()
    // This livedata contains the first value of UserModel that we fetch from database when initializing the ViewModel!
    // NU -> Not updated / Not updatable
    val userProfileNU: LiveData<UserModel>
        get() = _userProfileNU

    // This variable contains in-app purchases, which are shown to the user.
    val inAppPurchaseItems: MutableMap<String,InAppPurchaseItemInfoModel> = mutableMapOf()

    //This area is for reward ads
    var rewardAdTypeShowed: RewardAdType = RewardAdType.LIKE

    private var _refreshLikedYouHeader: SingleLiveData<Boolean> = SingleLiveData()
    val refreshLikedYouHeader: LiveData<Boolean>
        get() = _refreshLikedYouHeader

    var _lastTimeCurrentUserUpdated: Long = 0
        private set


    private val _consumableRewardUiState: MutableStateFlow<ConsumableRewardUiState> = MutableStateFlow(
        ConsumableRewardUiState()
    )
    val consumableRewardUiState : LiveData<ConsumableRewardUiState>
        get() = _consumableRewardUiState.asLiveData()

    fun refreshLikedYouHeader() {
        _refreshLikedYouHeader.value = true
    }

    var fetchedAccountModel: AccountModel? = null
    var fetchedProfileModel: UserModel? = null


    // end of reward ads area
    init {
        resetDotNotificationSeenTime()
        fetchCommunitiesIfNotCached()
        fetchUserAccountInfo()

        fetchUserProfile(fetchingForFirstTime = true)

        getUserFromDb()

        ChatWebSocket.addListener(this)
        appsFlyerBackendManager.checkAndSendDataForFaceBookFromDeepLink()
        // commenting out this method for now as Purchasely doesn't support offers
//        val offer = duaSharedPrefs.getPremiumOfferIdData()
//        offer?.let { getPremiumOfferFromId(offerId = offer.offerId, isFromDeepLink = offer.isFromDeepLink) }
    }


    suspend fun getVisitorId() : String = visitorIdUseCase.getVisitorId()

    fun continueUserGuide(){
        _continueUserGuide.call()
    }

    fun openFilterGuide(){
        _openFilterGuide.call()
    }

//    fun setIsUserPremiumCounter(value: Boolean) {
//        _isUserPremiumCounter.value = value
//    }

    fun afterPremiumVerified() {
        clearDontLetGoData()
    }

    fun showTrophyDialog() {
        _showTrophyDialog.value = true
    }

    fun notifyForNetworkConnectionChanged(boolean: Boolean) {
        if (onNetworkConnectionChanged.hasActiveObservers()) {
            _onNetworkConnectionChanged.value = boolean
        }
    }

    fun deleteUserMatchByUserId(userId: String) {
        _deleteUserMatchByUserIdFromConversation1to1.value = userId
    }

    val userProfile = userRepository.user
    fun isUserInFlyMode() = userProfile.value?.isInFlyMode() ?: false
    fun isUserInvisible() = userProfile.value?.isInvisible() ?: false
    fun isUserDisabled() = userProfile.value?.isDisabled ?: false

    var showUserProfile = SingleLiveData<RecommendedUserModel>()
    var showFeaturedUserProfile = SingleLiveData<FeaturedProfileModel>()

    fun setHasGrantedReadWritePermission(){
        _readWritePermission.call()
    }

    fun setOnRequestPermissionsResultForVideoCall(status: Boolean) {
        _onRequestPermissionsResultForVideoCall.value = status
    }

    fun setCheckIfAdViewIsShowedOnCard(){
        _checkIfAdViewIsShowedOnCard.value = true
    }

    fun setHomeScreenType(type: HomeScreenType) {
        _homeScreenType.value = type
    }

    val onMatchFragmentShowed: LiveData<Boolean>
        get() = _onMatchFragmentShowed
    val onBackFromConversation: LiveData<Boolean>
        get() = _onBackFromConversation

    var userCounters: List<CounterEntity>? = null
    private var undoCount: Int = 0
    private var instaChatCount: Int = 0
    private var likesCount: Int = 0
    private var dislikesCount: Int = 0
    private var flyingCount: Int = 0
    private var boostCount: Int = 0
    private var rewardVideoCount: Int = 0
    private var rewardAdUnblurCounter: Int = 0


    private var undoLimit = 0
    private var instaChatLimit = 1
    private var likesLimit = 50
    private var dislikesLimit = 50
    private var flyingLimit = 1
    private var boostLimit = 1
    private var rewardVideoLimit = 4
    private var rewardAdUnblurLimit = 1


    var ignoreTopDirectionRewind = false

    fun getUserCounters(user: UserModel, specificCounterName: String? = null, notifyCountersFetched: Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {
            val configurationNames =
                if (specificCounterName != null) listOf(specificCounterName) else
                    listOf(
                        user.counterConfigurationNames.likeCounterCN,
                        user.counterConfigurationNames.dislikeCounterCN,
                        user.counterConfigurationNames.instachatCounterCN,
                        user.counterConfigurationNames.boostCounterCN,
                        user.counterConfigurationNames.undoCounterCN,
                        user.counterConfigurationNames.unblurCounterCN,
                        user.counterConfigurationNames.flyCounterCN,
                        user.counterConfigurationNames.nameChangeCounterCN,
                        user.counterConfigurationNames.communityCN,
                        user.counterConfigurationNames.birthdayCounterCN,
                        user.counterConfigurationNames.genderCounterCN,
                        user.counterConfigurationNames.superlikeCounterCN,
                        user.counterConfigurationNames.rewardVideosCounterCN,
                        user.counterConfigurationNames.rewardedAdUnblurCounterCN,
                    )

            getUserCountersByConfNamesUseCase.invoke(configurationNames)
                .catch { e ->
                    Timber.e(e)
                }
                .collect { list ->
                    if (specificCounterName.isNullOrEmpty()) {
                        userCounters = list
                    } else {
                        userCounters?.firstOrNull { it.configurationName == specificCounterName }
                            ?.let {
                                userCounters = userCounters?.toMutableList()?.apply {
                                    remove(it)
                                    add(list.first())
                                }
                            }
                    }

                    list.let {
                        fillCounters(list, user)
                    }

                    if(notifyCountersFetched) {
                        EventBus.sendEvent(DuaEvent.CountersFetched)
                    }
                }
        }

    }

     fun syncLocalUserCounters(user: UserModel) {
        viewModelScope.launch(Dispatchers.IO) {
            val configurationNames = listOf(
                user.counterConfigurationNames.likeCounterCN,
                user.counterConfigurationNames.dislikeCounterCN,
                user.counterConfigurationNames.instachatCounterCN,
                user.counterConfigurationNames.boostCounterCN,
                user.counterConfigurationNames.undoCounterCN,
                user.counterConfigurationNames.unblurCounterCN,
                user.counterConfigurationNames.flyCounterCN,
                user.counterConfigurationNames.nameChangeCounterCN,
                user.counterConfigurationNames.communityCN,
                user.counterConfigurationNames.birthdayCounterCN,
                user.counterConfigurationNames.genderCounterCN,
                user.counterConfigurationNames.superlikeCounterCN,
                user.counterConfigurationNames.rewardVideosCounterCN,
                user.counterConfigurationNames.rewardedAdUnblurCounterCN,
            )

            getLocalCountersUseCase.invoke(configurationNames)
                .catch { e ->
                    Timber.e(e)
                }
                .collect { list ->
                    fillCounters(list, user)
                }

        }
    }

    private fun fillCounters(list: List<CounterEntity>?, user: UserModel) {
        list?.forEach { counterEntity ->
            when(counterEntity.configurationName){
                user.counterConfigurationNames.likeCounterCN ->  {
                    likesCount = counterEntity.total
                    likesLimit = counterEntity.configuration.limit
                }
                user.counterConfigurationNames.dislikeCounterCN -> {
                    dislikesCount = counterEntity.total
                    dislikesLimit = counterEntity.configuration.limit
                }
                user.counterConfigurationNames.instachatCounterCN ->  {
                    instaChatCount = counterEntity.total
                    instaChatLimit = counterEntity.configuration.limit
                }
                user.counterConfigurationNames.boostCounterCN ->  {
                    boostCount = counterEntity.total
                    boostLimit = counterEntity.configuration.limit
                }
                user.counterConfigurationNames.undoCounterCN ->  {
                    undoCount = counterEntity.total
                    undoLimit = counterEntity.configuration.limit
                }
                user.counterConfigurationNames.flyCounterCN ->  {
                    flyingCount = counterEntity.total
                    flyingLimit = counterEntity.configuration.limit
                }
                user.counterConfigurationNames.rewardVideosCounterCN ->  {
                    rewardVideoCount = counterEntity.total
                    rewardVideoLimit = counterEntity.configuration.limit
                }
                user.counterConfigurationNames.rewardedAdUnblurCounterCN ->  {
                    rewardAdUnblurCounter = counterEntity.total
                    rewardAdUnblurLimit = counterEntity.configuration.limit
                }
            }
        }
    }
    fun getInteractionLimit() = likesLimit
    fun getDislikesLimit() = dislikesLimit
    fun getInstachatLimit() = instaChatLimit
    fun getBoostLimit() = boostLimit

    fun setUserBadge2StatusOnDb(badge2Status: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val profile = userProfile.value
            profile?.let {
                it.badge2 = badge2Status
                userRepository.updateUser(it)
            }
        }
    }


    fun remainingLikes(): Boolean {
        return (likesCount < likesLimit) || (userProfile.value?.premiumType == PremiumEnum.PREMIUM2.type)
    }

    fun remainingDislikes(): Boolean {
        return (dislikesCount < dislikesLimit) || (userProfile.value?.premiumType == PremiumEnum.PREMIUM2.type)
    }

    fun getRemainingLikes(): Int {
        return likesLimit - likesCount
    }

    fun getRemainingDislikes(): Int {
        return dislikesLimit - dislikesCount
    }

    fun remainingInstaChatInteractions(): Boolean {
        return (instaChatCount < instaChatLimit) || (userProfile.value?.premiumType == PremiumEnum.PREMIUM2.type)
    }

    fun hasRemainingUndo(): Boolean {
        return (undoCount < undoLimit) || (userProfile.value?.premiumType != null)
    }

    fun hasRemainingBoosts(): Boolean {
        return (boostCount < boostLimit)
    }

    fun hasRemainingRewardVideos(): Boolean {
        return (rewardVideoCount < rewardVideoLimit)
    }

    fun hasRemainingRewardAdUnblur(): Boolean {
        return (rewardAdUnblurCounter < rewardAdUnblurLimit)
    }

    fun getRemainingUndo(): Int {
        return undoLimit - undoCount
    }

    fun getRemainingBoosts(): Int {
        return boostLimit - boostCount
    }

    fun hasActiveBoost(): Boolean {
        return if(userProfile.value?.profile != null) {
            val profile = userProfile.value!!.profile
            profile.isBoostActive && profile.boostedUntilTime != null && profile.boostedUntilTime!! > System.currentTimeMillis()
        } else {
            false
        }
    }

    fun isInCalculatingBoostStatus(): Boolean {
        return if(userProfile.value?.profile != null) {
            val profile = userProfile.value!!.profile
            profile.isBoostActive && profile.boostedUntilTime != null && profile.boostedUntilTime!! < System.currentTimeMillis()
        } else {
            false
        }
    }

    fun getRemainingFlights(): Int = flyingLimit - flyingCount

    fun getRemainingInstachats(): Int {
        return instaChatLimit - instaChatCount
    }

    fun hasRemainingFlights(): Boolean {
        return (flyingCount < flyingLimit) || (userProfile.value?.premiumType != null)
    }

    fun increaseLikesCounter() {
        likesCount++
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.likeCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = likesCount))
            }
        }
    }

    fun increaseDislikesCounter() {
        dislikesCount++
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.dislikeCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = dislikesCount))
            }
        }
    }

    fun increaseInstaChatInteractions() {
        instaChatCount++
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.instachatCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = instaChatCount))
            }
        }
    }

    fun increaseUndo() {
        undoCount++
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.undoCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = undoCount))
            }
        }
    }
    fun increaseBoost() {
        boostCount++
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.boostCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = boostCount))
            }
        }
    }

    fun increaseFlying() {
        flyingCount++
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.flyCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = flyingCount))
            }
        }
    }

    fun increaseRewardVideoCounter() {
        rewardVideoCount++
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.rewardVideosCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = rewardVideoCount))
            }
        }
    }

    fun increaseRewardAdUnblurCounter() {
        rewardAdUnblurCounter++
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.rewardedAdUnblurCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = rewardAdUnblurCounter))
            }
        }
    }

    fun increaseLikedYouRewardedAdsWatchCount() {
        viewModelScope.launch(Dispatchers.IO) {
            val newUser = userProfile.value?.copy( userAuxiliaryData = userProfile.value?.userAuxiliaryData?.copy(likedYouRewardedAdsWatchCount = userProfile.value?.userAuxiliaryData?.likedYouRewardedAdsWatchCount?.plus(1)))
            newUser?.let {
                userRepository.updateUser(it)
            }
            withContext(Dispatchers.Main) {
                _refreshLikedYouHeader.value = true
            }
        }
    }

    fun decreaseBoost() {
        boostCount--
        viewModelScope.launch(Dispatchers.IO) {
            userCounters?.firstOrNull{ it.configurationName == userProfile.value?.counterConfigurationNames?.boostCounterCN }?.let {
                updateUserCounterUseCase.invoke(it.copy(total = boostCount))
            }
        }
    }

    fun showFilter(eventSource: String = ClevertapFiltersEventSourceValues.CARD.value) {
        _showFilter.value = eventSource
    }

    fun getUndoLimit(): Int {
        return undoLimit
    }

    fun setLimitReached(model: LimitReachedModel) {
        //remove lastCard so that the user cant undo
        if (model.type == InteractionType.LIKE || model.type == InteractionType.DISLIKE)
            clearLastCard()

        _limitReached.value = model
    }

    fun reloadRecommendedUsers() {
        _reloadRecommendedUsers.call()
    }

    fun onMatchFragmentShowed(boolean: Boolean) {
        _onMatchFragmentShowed.value = boolean
    }

    fun setonBackFromConversation(boolean: Boolean) {
        _onBackFromConversation.value = boolean
    }

    fun setNavigateToChat(conversationModel: ConversationModel) {
        _navigateToChat.value = conversationModel
    }

    fun setNavigateToLikedYou(likesModel: UserLikesModel? = null) {
        if (likesModel?.type == InteractionType.SUPER_LIKE.value) {
            _openUserProfileToLikedYou.value = likesModel
        }
    }

    fun setNavigateToInstaChat(conversationModel: ConversationModel) {
        _navigateToInstaChat.value = conversationModel
    }

    fun setNavigateToInstaChatList(boolean: Boolean) {
        _navigateToInstaChatList.value = boolean
    }

    fun deepLinkToMatches() {
        _deepLinkMatches.value = true
    }

    fun deepLinkToInstachat() {
        _deepLinkInstaChat.value = true
    }

    //start
    /** This logic is to popBack() ConversationFragment if is open
     *  this logic of checking when notifications are clicked is temporary until we find a better choice  */
    private val _onNewPushNotificationsClick: SingleLiveData<Boolean> = SingleLiveData()
    val onNewPushNotificationsClick: LiveData<Boolean>
        get() = _onNewPushNotificationsClick

    /** This function is to popBack() ConversationFragment if is open*/
    fun setOnNewPushNotificationsClick() {
        if (onNewPushNotificationsClick.hasActiveObservers()) {
            _onNewPushNotificationsClick.value = true
        }
    }
    // end

    fun showMatchTabDotNotification(show: Boolean) {
        _showChatDotNotification.value = show
    }

    fun showMatchOnlyTabDotNotification(show: Boolean) {
        _showOnlyMatchDotNotification.value = show
    }

    fun showLikeYouTabDotNotification(show: Boolean) {
        _showLikesDotNotification.value = show
    }

    fun showNotificationFeedTabDotNotification(show:Boolean) {
        _showNotificationFeedDotNotification.value = show
    }

    fun checkForDotNotification(userMatchesModel: UserMatchesModel) {
        val isShowMatchTabDotNotification =
            userMatchesModel.time > duaSharedPrefs.getDotNotificationSharedPrefs()
                .getSeenTimeOnMatchesScreen()

        _showChatDotNotification.value = (isShowMatchTabDotNotification)
    }


    fun setOnUserImagesChanged() {
        _userPhotosChanged.value = null
    }

    fun checkForDotNotification(userLikesModel: UserLikesModel) {
        val isShowLikeYouDotNotification =
            userLikesModel.time > duaSharedPrefs.getDotNotificationSharedPrefs()
                .getSeenTimeOnLikedYouScreen()
//        This line is commented because maybe it will needed later
//        _showLikesDotNotification.value = (isShowLikeYouDotNotification)
    }

    fun checkForDotNotification(conversationModel: ConversationModel) {

        val isShowMatchTabDotNotification =
            conversationModel.lastMessageTime > duaSharedPrefs.getDotNotificationSharedPrefs()
                .getSeenTimeOnConversationScreen()

        _showChatDotNotification.value = (isShowMatchTabDotNotification)
    }

    fun resetDotNotificationSeenTime() {
        viewModelScope.launch(Dispatchers.IO) {
            if (duaSharedPrefs.getDotNotificationSharedPrefs().getSeenTimeOnMatchesScreen() == 0L) {
                duaSharedPrefs.getDotNotificationSharedPrefs().resetSeenTime()
            }
        }
    }

    fun getCardsAndFeaturedProfilesFromAPi(fromTouch: Boolean = false,
                                           applyCardsImmediately: Boolean,
                                           fetchCards: Boolean = true,
                                           fetchFeaturedProfiles: Boolean = true) {
        if (isUserDisabled()) return

        if (!isUserInvisible() && !isUserDisabled() && !isUserRecommendedLoading) {
            isUserRecommendedLoading = true

            if (!fromTouch)
                _recommendedUsersState.postValue(ListState.LOADING)

            getCardsAndFeaturedProfilesUseCase(fetchCards, fetchFeaturedProfiles)
                .onEach { result ->
                    when (result) {
                    is Resource.Success -> {

                        //region RMOD
                        result.data.rmodItem?.let {
                            _rmodItem.value = it
                        }
                        duaSharedPrefs.setRmodGenerated(false)
                        //endregion

                        //region CARDS
                        if(fetchCards) {
                            val cards = result.data.cards

                            // handle cards
                            Timber.tag("CARDSTAGSIZE").d("retrieved ${cards.size}")
                            sendClevertapCardResultEvent(duaSharedPrefs,cards.isNotEmpty(),userProfile.value)
                            sendFirebaseCardResultEvent(cards.isNotEmpty(),userProfile.value)
                            //remove users that were shown before
                            var filteredUsers = cards
                                .filterNot {
                                    fetchedRecommendedUsersIds.contains( it.cognitoUserId )
                                            || swipedRecommendedUsersIds.contains(it.cognitoUserId)
                                }

                            //Add User guide cards
                            //if (isSigningUpShowGuide && !duaSharedPrefs.getHasShownUserGuideInCards()){
                            //      filteredUsers.addAll(0,RecommendedUserModel.getUserGuideItems())
                            // }

                            if (filteredUsers.isEmpty())
                                _recommendedUsersState.postValue(ListState.EMPTY)

                            filteredUsers = addSpecialCardToList(filteredUsers)

                            Timber.tag("CARDSTAGSIZE").d("filtered ${filteredUsers.size}")


                            if(applyCardsImmediately) {
                                cachedUsers.clear()
                                setUsersRecommended(filteredUsers)
                            } else {
                                //add the users to cache list
                                cacheMoreUsers(filteredUsers.toMutableList())
                            }

                            //add the new users to all shown users list
                            fetchedRecommendedUsersIds.addAll(filteredUsers.map { it.cognitoUserId!! })

                            Timber.tag(CARDSTAG).d("filtered  ${filteredUsers.size}")
                        }
                        //endregion

                        //region Featured Profiles
                        if(fetchFeaturedProfiles) {
                            val featuredProfiles = result.data.featuredProfiles

                            // handle featured profiles
                            _featuredProfilesUsersState.update {
                                it.copy(
                                    list = featuredProfiles,
                                    isLoading = false,
                                    error = null
                                )
                            }

                        }
                        //endregion

                        isUserRecommendedLoading = false
                    }
                    is Resource.Error -> {
                    }
                    is Resource.Loading -> {
                        _featuredProfilesUsersState.update {
                            it.copy(
                                isLoading = true,
                                error = null
                            )
                        }
                    }
                }
                }
                .catch { exception ->
                    withContext(Dispatchers.Main){
                        when (exception) {
                            is LimitReachedException -> {
                                setLimitReached(LimitReachedModel(InteractionType.DISLIKE, LimitReachedScreenSource.FOR_YOU))
                            }
                            is NoConnectivityException -> {
                                _recommendedUsersState.value = ListState.NOCONNECTION
                            }
                            else -> {
                            }
                        }
                        isUserRecommendedLoading = false
                        Timber.tag("USERS_FOUND").d("ERROR")
                        exception.printStackTrace()
                    }

                    _featuredProfilesUsersState.update {
                        it.copy(
                            isLoading = false,
                            error = Exception(exception)
                        )
                    }
            }
                .launchIn(viewModelScope)

        }
    }


    fun addSpecialCardToList(list: List<RecommendedUserModel>): List<RecommendedUserModel> {
        Timber.tag("addSpecialCardToList").d("firstAdAppearance ${userProfile.value?.settings?.firstAdAppearance} | adFrequency ${userProfile.value?.settings?.adFrequency}")

        val firstCardAppearance = userProfile.value?.settings?.firstAdAppearance ?: 5
        val adFrequency = userProfile.value?.settings?.adFrequency ?: 15
        val firstQuestionAppearance = userProfile.value?.settings?.firstQuestionCardAppearance ?: 5
        val questionFrequency = userProfile.value?.settings?.questionCardFrequency ?: 15

        val dontLetGoCard = getDontLetGoCard()
        val firstFeatureAppearance = getFirstFeatureAppearance(dontLetGoCard)
        val featureFrequency = getFeatureFrequency(dontLetGoCard)
        val profileInfoModelItems = userProfile.value?.tags?.let { profileInfoCardRepository.getProfileInfoForCard(it) } ?: listOf()
        val profileActivitiesModelItems = userProfile.value?.tags?.let { profileActivitiesCardRepository.getProfileActivitiesForCard(it) } ?: listOf()

        var lookingFor: List<ProfileInfoModel>? = profileInfoModelItems.filter { it.type == ProfileInfoTypeCardSupport.LOOKING_FOR }
        var smoking: List<ProfileInfoModel>? = profileInfoModelItems.filter { it.type == ProfileInfoTypeCardSupport.SMOKE }
        var religion: List<ProfileInfoModel>? = profileInfoModelItems.filter { it.type == ProfileInfoTypeCardSupport.RELIGION }
        var haveChildren: List<ProfileInfoModel>? = profileInfoModelItems.filter { it.type == ProfileInfoTypeCardSupport.HAVE_CHILDREN }
        var wantChildren: List<ProfileInfoModel>? = profileInfoModelItems.filter { it.type == ProfileInfoTypeCardSupport.WANT_CHILDREN }

        var interests: List<ProfileActivitiesModel>? = profileActivitiesModelItems.filter { it.type == ProfileActivitiesTypeCardSupport.INTERESTS }
        var languages: List<ProfileActivitiesModel>? = profileActivitiesModelItems.filter { it.type == ProfileActivitiesTypeCardSupport.LANGUAGES }
        var showIncognitoModeCard = userProfile.value?.gender == GenderType.WOMAN.value && userProfile.value?.profile?.isGhost == false

        if (!firstAdCardAppearanceSet) {
            adCardCountdown = firstCardAppearance
            firstAdCardAppearanceSet = true
        }

        if (!firstQuestionCardAppearanceSet) {
            questionCardCountdown = firstQuestionAppearance
            firstQuestionCardAppearanceSet = true
        }

        if (!firstFeatureCardAppearanceSet) {
            featureCardCountdown = firstFeatureAppearance
            firstFeatureCardAppearanceSet = true
        }

        val arrayList = ArrayList(list)
        Timber.tag("addSpecialCardToList").d("newCountDown : $adCardCountdown | list.size : ${list.size} | homeViewModel.adCardCountdown :$adCardCountdown")

        if (adCardCountdown >= arrayList.size)
            adCardCountdown -= arrayList.size

        if (questionCardCountdown >= arrayList.size)
            questionCardCountdown -= arrayList.size

        if (featureCardCountdown >= arrayList.size)
            featureCardCountdown -= arrayList.size

        if (RemoteConfigUtils.getShowBrillionAd() && brillionAdHasBeenShown.not()) {
            if (list.size >= 2) {
                arrayList[1].isBrillionAd = true
                brillionAdHasBeenShown = true
            }
        }
        
        while (
            (questionCardCountdown < list.size && questionCardCountdown >= 0) ||
            (featureCardCountdown < list.size && featureCardCountdown >= 0) ||
            (adCardCountdown < list.size && adCardCountdown >= 0)
        ) {
            val nextCardIndex = when {
                questionCardCountdown < list.size -> questionCardCountdown
                featureCardCountdown < list.size -> featureCardCountdown
                adCardCountdown < list.size -> adCardCountdown
                else -> break
            }

            when (nextCardIndex) {
                questionCardCountdown -> {
                    Timber.tag("addSpecialCardToList").d("inserted question card at $questionCardCountdown")
                    var inserted = false
                    when {
                        lookingFor.isNullOrEmpty().not() -> {
                            arrayList[questionCardCountdown].isProfileInfo = true
                            arrayList[questionCardCountdown].profileInfoItems = lookingFor
                            lookingFor = null
                            inserted = true
                        }
                        smoking.isNullOrEmpty().not() -> {
                            arrayList[questionCardCountdown].isProfileInfo = true
                            arrayList[questionCardCountdown].profileInfoItems = smoking
                            smoking = null
                            inserted = true
                        }
                        religion.isNullOrEmpty().not() -> {
                            arrayList[questionCardCountdown].isProfileInfo = true
                            arrayList[questionCardCountdown].profileInfoItems = religion
                            religion = null
                            inserted = true
                        }
                        haveChildren.isNullOrEmpty().not() -> {
                            arrayList[questionCardCountdown].isProfileInfo = true
                            arrayList[questionCardCountdown].profileInfoItems = haveChildren
                            haveChildren = null
                            inserted = true
                        }
                        wantChildren.isNullOrEmpty().not() -> {
                            arrayList[questionCardCountdown].isProfileInfo = true
                            arrayList[questionCardCountdown].profileInfoItems = wantChildren
                            wantChildren = null
                            inserted = true
                        }
                        languages.isNullOrEmpty().not() -> {
                            arrayList[featureCardCountdown].isProfileActivities = true
                            arrayList[featureCardCountdown].profileActivitiesItems = languages
                            languages = null
                            inserted = true
                        }
                        interests.isNullOrEmpty().not() -> {
                            arrayList[featureCardCountdown].isProfileActivities = true
                            arrayList[featureCardCountdown].profileActivitiesItems = interests
                            interests = null
                            inserted = true
                        }
                    }
                    questionCardCountdown += questionFrequency
                    if (inserted) {
                        featureCardCountdown += featureFrequency
                        adCardCountdown += adFrequency
                    }
                }

                featureCardCountdown -> {
                    Timber.tag("addSpecialCardToList").d("inserted feature card at $featureCardCountdown")
                    var inserted = false

                    when {
                        dontLetGoCard.first -> {
                            arrayList[featureCardCountdown].apply {
                                isDontLetGoItem = true
                                dontLetGoDay =
                                    if (dontLetGoCard.second <= 1) 1 else dontLetGoCard.second
                            }
                            inserted = true
                        }

                    showIncognitoModeCard && incognitoModeCardHasBeenShown.not() ->{
                        arrayList[featureCardCountdown].isIncognitoMode = true
                        showIncognitoModeCard = false
                        incognitoModeCardHasBeenShown = true
                        inserted = true
                    }

                    }
                    featureCardCountdown += featureFrequency
                    if (inserted) {
                        adCardCountdown += adFrequency
                    }
                }

                adCardCountdown -> {
                    arrayList[adCardCountdown].isMobAd = true
                    adCardCountdown += adFrequency
                }

            }
        }

        return arrayList
    }

    private fun getFirstFeatureAppearance(dontLetGoCard: Pair<Boolean, Int>): Int {
        return if (dontLetGoCard.first) {
            when (dontLetGoCard.second) {
                1, 0 -> (userProfile.value?.settings?.dontLetGoAdFrequencyByDay?.day1?.firstAppearance) ?: 5
                2 -> (userProfile.value?.settings?.dontLetGoAdFrequencyByDay?.day2?.firstAppearance) ?: 5
                3 -> (userProfile.value?.settings?.dontLetGoAdFrequencyByDay?.day3?.firstAppearance) ?: 5
                else -> userProfile.value?.settings?.firstFeatureCardAppearance ?: 5
            }
        } else {
            userProfile.value?.settings?.firstFeatureCardAppearance ?: 5
        }
    }

    private fun getFeatureFrequency(dontLetGoCard: Pair<Boolean, Int>): Int {
        return if (dontLetGoCard.first) {
            when (dontLetGoCard.second) {
                1, 0 -> userProfile.value?.settings?.dontLetGoAdFrequencyByDay?.day1?.frequency ?: 15
                2 -> userProfile.value?.settings?.dontLetGoAdFrequencyByDay?.day2?.frequency ?: 15
                3 -> userProfile.value?.settings?.dontLetGoAdFrequencyByDay?.day3?.frequency ?: 15
                else -> userProfile.value?.settings?.featureCardFrequency ?: 15
            }
        } else {
            userProfile.value?.settings?.featureCardFrequency ?: 15
        }
    }


    fun onProfileInfoCardAppeared(typeId: Int) {
        profileInfoCardRepository.setTimeDismissedByTypeId(typeId)
    }

    fun onProfileActivitiesCardAppeared(typeId: Int) {
        profileActivitiesCardRepository.setProfileActivitiesTimeDismissedByTypeId(typeId)

    }

    fun swipeCard(
        recommendedUserModel: RecommendedUserModel,
        interactionType: InteractionType,
        message: String? = null,
        makeAPIRequest: Boolean = true,
        swipeSource: SwipeSourceValues? = null
    ) {
        Timber.tag(CARDSTAG).e("swipe card ${recommendedUserModel.firstName} : ${interactionType.name}")
        Timber.tag("UNDOCARD").d("swipe card ${recommendedUserModel.firstName} : ${interactionType.name} ${recommendedUserModel.isUndone}")
        if (undoJob != null && !undoJob!!.isCompleted) {
            undoJob?.invokeOnCompletion {
                swipeCard(recommendedUserModel, interactionType, message, false)
            }
        } else {
            if (makeAPIRequest) {
                interactionJob = viewModelScope.launch(Dispatchers.IO) {
                    val source = determineInteractionSource(interactionType, swipeSource, _homeScreenType.value)
                    Timber.tag("UNDOCARD").d("coroutine  ${recommendedUserModel.firstName} : ${interactionType.name} ${recommendedUserModel.isUndone}")
                    interactUser(recommendedUserModel, interactionType, message, source = source?.value,recommId = recommendedUserModel.recommId,recommSource=recommendedUserModel.recommSource)
                            .catch { ex ->
                                withContext(Dispatchers.Main) {
                                    ex.printStackTrace()
                                    when (ex) {
                                        is LimitReachedException -> {
                                            when (ex.type) {
                                                InteractionErrorType.INSTA_CHAT -> {
                                                    setLimitReached(LimitReachedModel(interactionType, if (_homeScreenType.value != HomeScreenType.FEATURED_PROFILE) LimitReachedScreenSource.FOR_YOU else LimitReachedScreenSource.POPULAR))
                                                    instaChatCount = instaChatLimit
                                                }
                                                InteractionErrorType.LIKE_DISLIKE -> {
                                                    setLimitReached(LimitReachedModel(interactionType, if (_homeScreenType.value != HomeScreenType.FEATURED_PROFILE) LimitReachedScreenSource.FOR_YOU else LimitReachedScreenSource.POPULAR))
                                                    if (interactionType == InteractionType.LIKE) {
                                                        likesCount = likesLimit
                                                    } else dislikesCount = dislikesLimit

                                                }
                                                InteractionErrorType.USER_SHADOW_BANNED_RECOMMENDATION_LIMIT_REACHED -> {
                                                    _showShadowBannedDialog.call()
                                                }
                                                else -> {}
                                            }
                                        }
                                        else -> {
//                                    ToastUtil.toast(R.string.smthg_went_wrong)
                                        }
                                    }
                                }
                            }
                            .collect { interactionResponse ->
                                withContext(Dispatchers.Main) {
                                    when (interactionResponse) {
                                        is Resource.Success -> {
                                            when (interactionType) {
                                                InteractionType.LIKE -> {
                                                    Timber.tag(CARDSTAG).e("Success")

                                                    if (interactionResponse.data.hasChat == true) {
                                                        getConversationByUserId(
                                                                recommendedUserModel.cognitoUserId!!,
                                                                HasChatModel(
                                                                        recommendedUserModel,
                                                                        interactionResponse.data.hasChat
                                                                )
                                                        )
                                                        val eventActivityType = recommendedUserModel.activityType

                                                        firebaseLogEvent(
                                                                FirebaseAnalyticsEventsName.REGULAR,
                                                                mapOf(
                                                                        FirebaseAnalyticsParameterName.INSTACHAT_MATCH_TYPE1_COUNT.value to 1L,
                                                                        FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                                                )
                                                        )

                                                        val eventPremiumType =getPremiumTypeEventProperty(userProfile.value)

                                                        val matchTypeValue = if(swipeSource == SwipeSourceValues.CROSS_PATH) ClevertapMatchTypeValues.CROSS_PATH.value else ClevertapMatchTypeValues.REGULAR.value
                                                        val recommSource = recommendedUserModel.recommSource ?: "dua"
                                                        val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                                                        val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                                                        sendClevertapEvent(
                                                            ClevertapEventEnum.MATCH, mapOf(
                                                                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                                                ClevertapEventPropertyEnum.MATCH_TYPE.propertyName to matchTypeValue,
                                                                ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                                                ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                                                ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                                                ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                                                ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge

                                                            ))

                                                    } else {
                                                        interactionResponse.data.matches?.find { it.userId == recommendedUserModel.id }
                                                                ?.let {
                                                                    recommendedUserModel.receivedSuperMatch =
                                                                            it.receivedSuperMatch
                                                                    _showMatchScreen.value =
                                                                            recommendedUserModel
                                                                    newMatchFromInternalInteraction(
                                                                            UserMatchesModel.convertToUserMatchesModel(
                                                                                    it,
                                                                                    recommendedUserModel,
                                                                                    interactionType
                                                                            ),
                                                                            interactionResponse.data.interactions!![0].existingInteractionId
                                                                                    ?: ""
                                                                    )
                                                                }
                                                    }

                                                }
                                                InteractionType.DISLIKE -> {
                                                    if(_homeScreenType.value == HomeScreenType.FEATURED_PROFILE) {
                                                        _featuredProfilesUsersState.update {
                                                            val list = it.list.toMutableList()
                                                            val user : FeaturedProfileModel? = list.find { it.id == recommendedUserModel.id }
                                                            list.remove(user)

                                                            it.copy(list=list)
                                                        }
                                                    }

                                                    if (_homeScreenType.value == HomeScreenType.CARDS &&
                                                        interactionResponse.data.hasMissedMatch == true ) {
                                                        lastMissedMatchCard = recommendedUserModel
                                                        lastMissedMatchCardIdForEvent = recommendedUserModel.cognitoUserId
                                                        _missedMatchCard.emit(recommendedUserModel)
                                                    }

                                                    dislikeInstaChat(
                                                            DislikeInstaChatModel(
                                                                    recommendedUserModel.cognitoUserId!!
                                                            )
                                                    )
                                                    _newMatchInternal.value = interactionResponse.data.interactions!![0].existingInteractionId
                                                            ?: ""
                                                }
                                                InteractionType.INSTA_CHAT -> {
                                                    if(_homeScreenType.value == HomeScreenType.FEATURED_PROFILE) {
                                                        _featuredProfilesUsersState.update {
                                                            val list = it.list.toMutableList()
                                                            val user : FeaturedProfileModel? = list.find { it.id == recommendedUserModel.id }
                                                            list.remove(user)

                                                            it.copy(list=list)
                                                        }
                                                    }


                                                    val eventSourceValue = when (swipeSource) {
                                                        SwipeSourceValues.PROFILE_VISIT_FEATURE -> { SwipeSourceValues.PROFILE_VISIT_FEATURE }
                                                        SwipeSourceValues.CROSS_PATH -> SwipeSourceValues.CROSS_PATH
                                                        else -> { if( _homeScreenType.value != HomeScreenType.FEATURED_PROFILE) SwipeSourceValues.FOR_YOU else SwipeSourceValues.LAST_ACTIVE }
                                                    }
                                                    val eventPremiumType = getPremiumTypeEventProperty(userProfile.value)
                                                    val eventActivityType = recommendedUserModel.activityType
                                                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                                                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                                                    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                                                    firebaseLogEvent(
                                                            FirebaseAnalyticsEventsName.INSTACHAT_SUCCESSFULLY_SENT,
                                                            mapOf(
                                                                    FirebaseAnalyticsParameterName.INSTACHAT_SUCCESSFULLY_SENT_COUNT.value to 1L,
                                                                    FirebaseAnalyticsParameterName.SOURCE.value to eventSourceValue.value,
                                                                    FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to eventActivityType
                                                            )
                                                    )


                                                    sendClevertapEvent(ClevertapEventEnum.INSTACHAT_SENT,
                                                        mapOf(
                                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                                            ClevertapEventPropertyEnum.SOURCE.propertyName to eventSourceValue.value,
                                                            ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge
                                                        )
                                                    )

//                                                _accountCounterUpdate.value =
//                                                    InteractionType.INSTA_CHAT

                                                if (interactionResponse.data.hasChat == true) {
                                                    getConversationByUserId(
                                                        recommendedUserModel.cognitoUserId!!,
                                                        HasChatModel(
                                                            recommendedUserModel,
                                                            interactionResponse.data.hasChat
                                                        )
                                                    )
                                                    val eventActivityType = recommendedUserModel.activityType

                                                    firebaseLogEvent(
                                                        FirebaseAnalyticsEventsName.INSTACHAT_MATCH_TYPE2,
                                                        mapOf(
                                                            FirebaseAnalyticsParameterName.INSTACHAT_MATCH_TYPE2_COUNT.value to 1L))

                                                    val matchTypeValue = if(swipeSource == SwipeSourceValues.CROSS_PATH) ClevertapMatchTypeValues.CROSS_PATH.value else ClevertapMatchTypeValues.INSTACHAT_TYPE2.value
                                                    val recommSource = recommendedUserModel.recommSource ?: "dua"
                                                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos
                                                    val premiumBadge = if(recommendedUserModel.profile.showPremiumBadge == true) PremiumBadgeValues.SHOWN.value else PremiumBadgeValues.HIDDEN.value

                                                    sendClevertapEvent(
                                                        ClevertapEventEnum.MATCH, mapOf(
                                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                                            ClevertapEventPropertyEnum.MATCH_TYPE.propertyName to matchTypeValue,
                                                            ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to eventActivityType,
                                                            ClevertapEventPropertyEnum.RECOMMENDATION_SOURCE.propertyName to recommSource,
                                                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden,
                                                            ClevertapEventPropertyEnum.RECEIVED_USER_PREMIUM_BADGE.propertyName to premiumBadge
                                                        ))


                                                    delay(1000)
                                                    val conversationResponse = chatRepository.getConversationByUserCognitoId( recommendedUserModel.cognitoUserId, null)
                                                    when(conversationResponse) {
                                                        is ResourceV2.Success -> {
                                                            val conversationModel = conversationResponse.data.conversation
                                                            interactionResponse.data.matches?.find { it.userId == recommendedUserModel.id }?.let {
                                                                recommendedUserModel.receivedSuperMatch = it.receivedSuperMatch
//                                                                _showMatchScreen.value = recommendedUserModel

                                                                val userExistingId = interactionResponse.data.interactions!![0].existingInteractionId ?: ""
                                                                newConversationFromInternalInteraction(
                                                                    conversationModel = conversationModel,
                                                                    existingUserId = userExistingId
                                                                )
                                                                showLikeYouTabDotNotification(false)
                                                            }
                                                        }
                                                        is ResourceV2.Error -> {}
                                                    }
                                                } else {
                                                    onInstaChatSent()
                                                    val conversationId = "${firstPartOfConversationId(userProfile.value!!.cognitoUserId)}-${secondPartOfConversationId(recommendedUserModel.cognitoUserId!!)} "
                                                    newFcmMessage(ConversationWebSocketModel("chat.newMessage",ConversationData(MessageModel.getEmptyMessage(),recommendedUserModel.asConversationModel(conversationId, message!!,userProfile.value!!.cognitoUserId)) ))
                                                }
                                            }
                                                else -> {}
                                            }

                                            when (interactionType) {
                                                InteractionType.LIKE -> {
                                                    increaseLikesCounter()
                                                    if (likesCount >= likesLimit)
                                                        syncLikesCounterResetTime()
                                                }
                                                
                                                InteractionType.INSTA_CHAT -> {
                                                    increaseInstaChatInteractions()
                                                    if (instaChatCount >= instaChatLimit)
                                                        syncInstaChatCounterResetTime()
                                                }
                                                InteractionType.DISLIKE -> {
                                                    increaseDislikesCounter()
                                                    if(dislikesCount >= dislikesLimit) {
                                                        syncDislikesCounterResetTime()
                                                    }
                                                }
                                                else -> {}
                                            }
                                        }
                                        is Resource.Loading -> {
                                        }
                                        else -> {}
                                    }
                                }
                            }
                }
            }
        }
        Timber.tag(CARDSTAG).d("InteractionJob : ${interactionJob?.isCompleted}")
    }
    private fun determineInteractionSource(
        interactionType: InteractionType,
        swipeSource: SwipeSourceValues?,
        homeScreenType: HomeScreenType?
    ): SwipeSourceValues? {

        if (interactionType in listOf(InteractionType.LIKE, InteractionType.INSTA_CHAT, InteractionType.DISLIKE)) {
           return swipeSource
        }
        return null
    }
    fun undoInteraction(recommendedUserModel: RecommendedUserModel) {
        Timber.tag("UNDOCARD")
            .d("undo card ${recommendedUserModel.firstName} ${recommendedUserModel.isUndone}")
        fun createJob() {
            undoJob = viewModelScope.launch(Dispatchers.IO) {
                userRepository.undoInteraction(recommendedUserModel.cognitoUserId!!)
                    .catch { ex ->
                        withContext(Dispatchers.Main) {
                            ex.printStackTrace()
                            interactionJob = null
                            when (ex) {
                                is LimitReachedException -> {
//                                        setLimitReached(InteractionType.UNDO)
                                    when (ex.type) {
                                        InteractionErrorType.UNDO -> {
                                            undoCount = undoLimit
                                        }

                                        else -> {}
                                    }
                                }

                                else -> {
//                                    ToastUtil.toast(R.string.smthg_went_wrong)
                                }
                            }
                        }
                    }
                    .collect {
                        withContext(Dispatchers.Main) {
                            when (it) {
                                is Resource.Success -> {
                                    Timber.tag(TAG).d(it.toString())
                                    interactionJob = null
                                    increaseUndo()
                                    if (instaChatCount >= instaChatLimit)
                                        syncUndoCounterResetTime()

                                    _interactionUndone.value = recommendedUserModel
                                    val wasAPotentialMatch =
                                        lastMissedMatchCardIdForEvent == recommendedUserModel.cognitoUserId
                                    val eventPremiumType =
                                        getPremiumTypeEventProperty(userRepository.user.value)
                                    val isPhotoHidden = recommendedUserModel.profile.hasBlurredPhotos

                                    firebaseLogEvent(
                                        FirebaseAnalyticsEventsName.UNDO, mapOf(
                                            FirebaseAnalyticsParameterName.UNDO_COUNT.value to 1L,
                                            FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                            FirebaseAnalyticsParameterName.ACTIVITY_TAG.value to recommendedUserModel.activityType,
                                            FirebaseAnalyticsParameterName.IS_RADIUS_EXTENDED.value to duaSharedPrefs.getIsRadiusExtended(),
                                            FirebaseAnalyticsParameterName.WAS_A_POTENTIAL_MATCH.value to wasAPotentialMatch
                                        )
                                    )
                                    sendClevertapEvent(
                                        ClevertapEventEnum.UNDO, mapOf(
                                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                            ClevertapEventPropertyEnum.ACTIVITY_TAG.propertyName to recommendedUserModel.activityType,
                                            ClevertapEventPropertyEnum.IS_RADIUS_EXTENDED.propertyName to duaSharedPrefs.getIsRadiusExtended(),
                                            ClevertapEventPropertyEnum.WAS_A_POTENTIAL_MATCH.propertyName to wasAPotentialMatch,
                                            ClevertapEventPropertyEnum.IS_PHOTO_HIDDEN.propertyName to isPhotoHidden
                                        )
                                    )
                                }

                                is Resource.Loading -> {
                                }

                                is Resource.Error -> {
                                }
                            }
                        }
                    }
            }
        }

        interactionJob?.let {
            it.invokeOnCompletion { createJob() }
        }
    }

    fun interactUser(
        recommendedUserModel: RecommendedUserModel,
        interactionType: InteractionType,
        message: String?,
        source: String?,
        recommId: String?,
        recommSource: String?,
    ): Flow<Resource<InteractionsResponse>> = flow {
        emit(Resource.Loading)
        removeUserWhenSwipe(recommendedUserModel)
        removeUserFromFeaturedProfiles(recommendedUserModel)
        try {
            val body = InteractionBody(
                interactionType.value,
                recommendedUserModel.cognitoUserId!!,
                message = message,
                source = source,
                recommId = recommId,
                recommSource = recommSource
            )
            val response = userService.interactUser(body)
            when {
                response.isSuccessful -> {
                    emit(Resource.Success(response.body()!!))
                }
                response.code() == 403 -> {

                    val error = response.errorBody()!!.string()
                    val errorObject = Gson().fromJson<InteractionErrorBody>(
                        error,
                        InteractionErrorBody::class.java
                    )
                    when (errorObject.type) {
                        InteractionErrorType.INSTA_CHAT.value -> {
                            throw LimitReachedException(InteractionErrorType.INSTA_CHAT)
                        }
                        InteractionErrorType.LIKE_DISLIKE.value -> {
                            throw LimitReachedException(InteractionErrorType.LIKE_DISLIKE)
                        }
                        InteractionErrorType.USER_SHADOW_BANNED_RECOMMENDATION_LIMIT_REACHED.value -> {
                            throw LimitReachedException(InteractionErrorType.USER_SHADOW_BANNED_RECOMMENDATION_LIMIT_REACHED)
                        }
                        else -> throw Exception("Error")
                    }
                }
                else -> {
                    throw Exception("Error")
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

     fun removeUserFromFeaturedProfiles(recommendedUserModel: RecommendedUserModel) {
        _featuredProfilesUsersState.update {
            val list = it.list.toMutableList()
            val user = list.find { it.id == recommendedUserModel.id }
            list.remove(user)

            it.copy(list=list)
        }
    }

    fun setUsersRecommended(users: List<RecommendedUserModel>) {
                Timber.tag(CARDSTAG).d("settings users ${users.size}")
                _recommendedUsers.value = users.toMutableList()
    }

    fun removeUserWhenSwipe(recommendedUserModel: RecommendedUserModel) {
        val items = ArrayList(_recommendedUsers.value!!)
        items.remove(recommendedUserModel)
        _recommendedUsers.postValue(items)
    }

    fun removeUserGuideCards() {
        val items = ArrayList(_recommendedUsers.value!!)
        _recommendedUsers.postValue(items.filter { it.cardUserGuideType == null })
    }

    fun removeUserWhenSwipeFromLikedYou(recommendedUserModel: RecommendedUserModel) {
        Timber.tag(CARDSTAG).d("removeUserWhenSwipeFromLikedYou")
        if (!_recommendedUsers.value.isNullOrEmpty()) {
            val items = ArrayList(_recommendedUsers.value!!)
            val item = items.find { it.cognitoUserId == recommendedUserModel.cognitoUserId }
            item?.let { itemFind ->
                items.removeAt(items.indexOf(itemFind))
                _recommendedUsers.value = items
            }
        }
    }

    fun unHideMyProfile(): LiveData<Result<UpdateUserResponse>> {
        val params = mapOf("profile" to mapOf("isInvisible" to false))
        return userRepository.updateUser(params)
    }

    fun updateFriendsShip(status: Boolean): LiveData<Result<UpdateUserResponse>> {
        val params = mapOf("profile" to mapOf("hasFriendshipEnabled" to status))
        return userRepository.updateUser(params)
    }


    fun updateLastDevice(lastDevice : String): LiveData<Result<UpdateUserResponse>>{
        val params = mapOf("lastDeviceId" to lastDevice)
        return userRepository.updateUser(params)
    }

    fun updateUserInvisible(userModel: UserModel) {
        viewModelScope.launch { userRepository.updateUserInDB(userModel) }
    }

    fun updateShowFriends(userModel: UserModel) {
        viewModelScope.launch { userRepository.updateUserInDB(userModel) }
    }

    fun updateLastDeviceInDB(userModel: UserModel) {
        viewModelScope.launch { userRepository.updateUserInDB(userModel) }
    }


    fun updateLocation(location: Location) {
        val params = mapOf("latitude" to location.latitude, "longitude" to location.longitude)
        val type = if (isUserInFlyMode()) LocationType.MOVE else LocationType.NORMAL

        viewModelScope.launch(Dispatchers.IO) {
            locationsRepository.updateLocationFlow(type.typeName, params)
                .catch { ex -> ex.printStackTrace() }
                .collect { response ->
                    when(response) {
                        is Resource.Success -> {
                            DuaAccount.user?.let {
                                it.profile.latitude = location.latitude
                                it.profile.longitude = location.longitude
                                it.profile.address = response.data.address
                                updateUser(it)
                            }
                        }
                        else -> {}
                    }
                }
        }
    }

    fun updatePreselectedLocation(userModel: UserModel) {
        viewModelScope.launch(Dispatchers.IO) {
            if (userModel.isInFlyMode()) {
                var locationExists = false
                val locations = locationsRepository.getLocations()
                val locationModel = LocationModel(
                    System.currentTimeMillis(),
                    userModel.profile.address?.substringBefore(",") ?: "",
                    userModel.profile.address?.substringAfter(", ") ?: "",
                    userModel.profile.latitude.toString(),
                    userModel.profile.longitude.toString()
                )

                for (location in locations) {
                    if (locationModel.address == location.address ||
                        (locationModel.latitude == location.latitude && locationModel.longitude == location.longitude)
                    )
                        locationExists = true
                }
                if (!locationExists) locationsRepository.insertLocation(locationModel)
            }
        }
    }

    fun updateUser(userModel: UserModel) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.updateUser(userModel)
        }
    }

    fun updateIsBillingEnabledOnUser(isBillingEnabled: Boolean) {
        viewModelScope.launch {
            val params = mapOf("isBillingAvailable" to isBillingEnabled)
            val isBillingAvailableKey = ClevertapUserPropertyEnum.IS_BILLING_AVAILABLE.value
            val clevertapUserProperties = mapOf(isBillingAvailableKey to isBillingEnabled)
            updateUserProfileInClevertap(clevertapUserProperties)
            userRepository.updateUserAsync(params)
        }
    }

    fun updateUserTags(model: UpdateTagsModel) {
        viewModelScope.launch(Dispatchers.IO) {
            val user = userProfile.value
            userRepository.updateUserTags(user,model)
                .catch {
                    Timber.tag(TAG + "updateUserTags").e(it)
                }.collect {
                    when(it){

                        is Resource.Success -> {
                            sendProfileInfoEvents(model)
                            Timber.tag(TAG + "updateUserTags").e("Success")
                        }
                        is Resource.Error -> Timber.tag(TAG + "updateUserTags").e("Error")
                        Resource.Loading -> {}
                    }
                }
        }
    }

    private fun sendProfileInfoEvents(model: UpdateTagsModel) {
        val infoType = when (model.typeId) {
            ProfileActivitiesTypeCardSupport.INTERESTS.typeId -> AddProfileInfoTypeValues.ACTIVITIES.value
            ProfileActivitiesTypeCardSupport.LANGUAGES.typeId -> AddProfileInfoTypeValues.LANGUAGES.value
            ProfileInfoTypeCardSupport.HAVE_CHILDREN.typeId -> AddProfileInfoTypeValues.HAVE_CHILDREN.value
            ProfileInfoTypeCardSupport.WANT_CHILDREN.typeId -> AddProfileInfoTypeValues.WANT_CHILDREN.value
            ProfileInfoTypeCardSupport.SMOKE.typeId -> AddProfileInfoTypeValues.SMOKING.value
            ProfileInfoTypeCardSupport.RELIGION.typeId -> AddProfileInfoTypeValues.RELIGION.value
            ProfileInfoTypeCardSupport.LOOKING_FOR.typeId -> AddProfileInfoTypeValues.LOOKING_FOR.value
            else -> null


        }
        val premiumType = getPremiumTypeEventProperty(userProfile.value)

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.ADD_PROFILE_INFORMATION, mapOf(
                AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.CARDS.value,
                AddProfileInfoEventProperties.INFO_TYPE.value to infoType,
                AddProfileInfoEventProperties.PREMIUM_TYPE.value to premiumType
            )
        )

        sendClevertapEvent(
            ClevertapEventEnum.ADD_PROFILE_INFORMATION, mapOf(
                AddProfileInfoEventProperties.INFO_SOURCE.value to AddProfileInfoSourceValues.CARDS.value,
                AddProfileInfoEventProperties.INFO_TYPE.value to infoType,
                AddProfileInfoEventProperties.PREMIUM_TYPE.value to premiumType
            )
        )
    }

    fun setUserBoostFinishes(){
        val user = userProfile.value

        user?.let {
            it.profile.isBoostActive = false
            it.profile.boostedUntil = null
            it.profile.boostedUntilTime = null
            updateUser(it)
        }
    }

    private fun getDontLetGoInfo() {
        viewModelScope.launch(Dispatchers.IO) {
            Timber.tag(TAG).d("coroutine launched")
            userRepository.getDontLetGoInfo()
                .catch { e ->
                    Timber.tag(TAG).d(e.message.toString())
                    e.printStackTrace()
                }
                .collect { response ->
                    when(response) {
                        is Resource.Success -> {
                            withContext(Dispatchers.Main) {
                                val model = response.data
                                duaSharedPrefs.setDontLetGoOfferData(model)

                                val subscriptionEndDate = model.currentSubscription?.let { Date(it.expireTime) }
                                PurchaselyManager.setDontLetGoOfferData(subscriptionEndDate, model.hasUserAlreadyBoughtDontLetGo)

                                setIsUserInGracePeriod(model.currentSubscription?.isGracePeriod ?: false)
                            }
                        }
                        is Resource.Error -> {}
                        is Resource.Loading -> {}
                    }
                }
        }
    }

    fun clearDontLetGoData() {
        duaSharedPrefs.setDontLetGoOfferData(null)
    }

    private fun fetchUserAccountInfo() {
        viewModelScope.launch(Dispatchers.IO) {
            Timber.tag(TAG).d("coroutine launched")
            userRepository.getUserAccountInfo()
                .catch { e ->
                    Timber.tag(TAG).d(e.message.toString())
                    e.printStackTrace()
                }
                .collect { user ->
                    fetchedAccountModel = user
                    callOnUserLoginCleverTap(fetchedProfileModel, fetchedAccountModel)
                }
        }
    }

    /*
     * This method is used to fetch user profile from backend
     * @param isAfterPremium is used to fetch user profile after premium purchase
     */
    fun fetchUserProfile(
        isAfterPremium: Boolean = false,
        fetchingForFirstTime: Boolean = false,
        notifyCountersFetched: Boolean = true
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getUserProfileFlow(isAfterPremium = isAfterPremium)
                .retryWhen { cause, attempt ->
                    val maxAttempt = 10
                    val baseMillis = 1000
                    if (cause is PremiumSyncException && attempt <= maxAttempt) {
                        val delay = baseMillis * (attempt + 1)
                        delay(delay)
                        return@retryWhen true
                    } else {
                        return@retryWhen false
                    }
                }
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        ex.printStackTrace()
                        when (ex) {
                            is UserNotExistException -> {
                                _openCreateProfile.value = true

                            }
                            is UserDeletedExistException -> {
                                duaAccount.deleteAllData()
                            }

                        }
                    }
                }
                .collect {
                    withContext(Dispatchers.Main) {
                        when (it) {
                            is Result.Success -> {
                                _lastTimeCurrentUserUpdated = System.currentTimeMillis()

                                val user = it.data
                                fetchedProfileModel = user
                                getUserCounters(user = user, notifyCountersFetched = notifyCountersFetched)
                                updatePreselectedLocation(user)

                                if(user.premiumType != null)
                                    getDontLetGoInfo()
                                else
                                    clearDontLetGoData()

                                getAllTags(user)
                                if (user.instagramStatus == INSTAGRAM_STATUS_TOKEN_REFRESH_ERROR) {
                                    _showInstagramReconnectDialog.value = true
                                }

                                callOnUserLoginCleverTap(fetchedProfileModel, fetchedAccountModel)

                                if (AppsFlyerProperties.getInstance().getString(AppsFlyerProperties.APP_USER_ID).isNullOrBlank()
                                ) {
                                   // AppsFlyerLib.getInstance().setCustomerUserId(user.cognitoUserId)
                                    linkCleverTapAppsFlyer()
                                    appsFlyerBackendManager.sendData()
                                }

                                AppLovinSdk.getInstance(DuaApplication.instance.applicationContext).userIdentifier = user.cognitoUserId
                                UXCam.setUserIdentity(user.cognitoUserId)

                                if(fetchingForFirstTime) {
                                    _purchaselySetUp.call()
                                }

                                PurchaselyManager.setPurchaselyUserAttributes(user)

                                if (user.isDisabled.not()) {
                                    updateLanguageAndTags(user.language)
                                }
                                duaSharedPrefs.setUserCognitoId(user.cognitoUserId)
                            }
                            is Result.Loading -> {
                            }
                            is Result.Error -> {
                            }
                        }
                    }
                }
        }
    }
    private fun fetchCommunitiesIfNotCached()  {
        viewModelScope.launch {
            val hasCachedCommunities = userRepository.hasCommunitiesCached()
            Timber.tag(TAG).d("hasCachedCommunities: $hasCachedCommunities")
            if (!hasCachedCommunities) {
                getCommunitiesList()
            }
        }
    }

    private fun getCommunitiesList() {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getCommunities()
        }
    }
    fun activateBoost(activeBoostActivationSource: ClevertapBoostActivationSource){
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.boostProfile()
                .catch { ex -> when(ex){
                    is BoostFailedException -> {
                        Timber.tag(TAG).d("activateBoost: ${ex.type}")
                        ToastUtil.toast("${ex.type}")
                        ex.printStackTrace()
                    }
                    else -> {
                        ex.printStackTrace()
                    }
                }
                }
                .collect { response ->
                    when(response){
                        is Resource.Success -> {
                            firebaseLogEvent(FirebaseAnalyticsEventsName.ACTIVATE_BOOST)

                            val premiumTypeValue = getPremiumTypeEventProperty(userProfile.value)

                            sendClevertapEvent(
                                ClevertapEventEnum.ACTIVATE_BOOST, mapOf(
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue,
                                    ClevertapEventPropertyEnum.BOOST_ACTIVATION_SOURCE.propertyName to activeBoostActivationSource.value
                                )
                            )
                            duaSharedPrefs.setLastActiveBoostPlaceForEvent(activeBoostActivationSource.value)
                            increaseBoost()
                            val model = userProfile.value
                            model?.let {
                                it.profile.isBoostActive = true
                                it.profile.boostedUntil = response.data.boostedUntil
                                it.profile.boostedUntilTime = response.data.boostedUntilTime
                                updateUser(it)

                                withContext(Dispatchers.Main) {
                                    setBoostStatusChanged()
                                }
                            }
                            Timber.tag(TAG).d("activateBoost: Success")
                        }
                        is Resource.Error -> {}
                        Resource.Loading -> {}
                    } }
        }
    }

    fun setBoostStatusChanged(){
        _boostStatusChanged.call()
    }

    private val _conversations = MutableLiveData<MutableList<ConversationModel>>()
    private val _instachatConversations = MutableLiveData<MutableList<ConversationModel>>()
    private val _removesInstaChat = MutableLiveData<MutableList<ConversationModel>>()
    private val _singleConversation: SingleLiveData<ConversationWebSocketModel> = SingleLiveData()

    val webSocketConversations: LiveData<MutableList<ConversationModel>>
        get() = _conversations
    val webSocketInstchatConversations: LiveData<MutableList<ConversationModel>>
        get() = _instachatConversations

    val removesInstaChat: LiveData<MutableList<ConversationModel>>
        get() = _removesInstaChat

    val singleConversation: SingleLiveData<ConversationWebSocketModel>
        get() = _singleConversation

    val  _isTypingStateChange = MutableSharedFlow<UpdateTypingStateChanged>(replay = 0)
    val isTypingStateChange: SharedFlow<UpdateTypingStateChanged>
        get() = _isTypingStateChange

    private val _likeMessageObserver: SingleLiveData<LikeMessageModel> = SingleLiveData()
    val likeMessageObserver: SingleLiveData<LikeMessageModel>
        get() = _likeMessageObserver

    fun setRmodDeleted() {
        _rmodItem.value?.isDeleted = true
    }
    fun setRmodSeen() {
        _rmodItem.value?.isSeen = true
    }

    fun setUserRemovedYou(userId: String) {
        _userRemovedYou.value = userId
    }

    fun clearConversations() {
        _conversations.value = null
    }

    fun clearInstachatConversations() {
        _instachatConversations.value = null
    }

    fun clearRemovesInstaChat() {
        _removesInstaChat.value = null
    }

    fun clearNewLikes() {
        _newLikeYou.value = null
    }

    fun clearNewMatches() {
        _newMatch.value = null
    }

    fun clearNewConversations() {
        _newConversation.value = null
    }

    fun clearNewUnMatches() {
        _unMatches.value = null
    }

    fun connectWithWebSocket() {
            ChatWebSocket.connectWebSocket()
    }

    fun updateConversationListFrom1to1Conversation(conversationWebSocketModel: ConversationWebSocketModel) {
        val item: ConversationModel = conversationWebSocketModel.data.conversationMember
        val items: ArrayList<ConversationModel>
        if ((item.type != ConversationType.INSTANT_CHAT.value && !webSocketConversations.value.isNullOrEmpty())
            || (item.type == ConversationType.INSTANT_CHAT.value && !webSocketInstchatConversations.value.isNullOrEmpty())
        ) {
            items =
                ArrayList(if (item.type != ConversationType.INSTANT_CHAT.value || item.isRMOD) webSocketConversations.value!! else webSocketInstchatConversations.value!!)
            val find = items.find { it.id == item.id }
            if (find != null) {
                items.remove(find)
                items.add(0, item)
            } else {
                items.add(0, item)
            }
        } else {
            items = ArrayList()
            items.add(0, item)
        }
        if (item.type != ConversationType.INSTANT_CHAT.value || item.isRMOD) {
            _conversations.value = items
        } else {
            _instachatConversations.value = items
        }
        insertConversation(item)
    }

    private fun insertConversation(conversationModel: ConversationModel) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.insertConversation(conversationModel)
        }
    }

    //HomeChatWebSocketReceivedInterface
    override fun newConversationToken(newConversationTokenModel: NewConversationTokenModel) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.insertConversationToken(
                ConversationToken(newConversationTokenModel.payload.conversationId,
                    newConversationTokenModel.payload.data.conversationToken)
            )
        }
    }

    override fun messageSent(webSocketMessageModel: WebSocketMessageModel) {
    }

    override fun newWebSocketMessage(webSocketMessageModel: WebSocketMessageModel) {
        viewModelScope.launch (Dispatchers.IO) {
            val conversationId = webSocketMessageModel.payload.conversationId
            val token = chatRepository.getConversationToken(conversationId).values.firstOrNull()?.token
            chatRepository.getConversationForWebSocket(conversationId, token)
                .catch { ex->
                    ex.printStackTrace()

            }.collect {
                when (it) {
                    is Resource.Success -> {
                        withContext(Dispatchers.Main) {
                            val item: ConversationModel = it.data
                            val message: MessageModel = webSocketMessageModel.mapToMessageModel()
                            item.apply {
                                lastMessageText = message.content
                                lastMessageSender = message.senderUserId
                                lastMessageTime = message.time
                                lastMessageType = message.type
                                seen = 0
                            }
                            if (message.type == MessageType.CALLS_ALLOWED.value)
                                item.areOutgoingCallsAllowed = true
                            else if (message.type == MessageType.CALLS_DISALLOWED.value)
                                item.areOutgoingCallsAllowed = false

                            val items: ArrayList<ConversationModel>
                            if ((item.type != ConversationType.INSTANT_CHAT.value && !webSocketConversations.value.isNullOrEmpty())
                                || (item.type == ConversationType.INSTANT_CHAT.value && !webSocketInstchatConversations.value.isNullOrEmpty())
                            ) {
                                items =
                                    ArrayList(if (item.type != ConversationType.INSTANT_CHAT.value) webSocketConversations.value!! else webSocketInstchatConversations.value!!)
                                val find = items.find { it.id == item.id }
                                if (find != null) {
                                    items.remove(find)
                                    items.add(0, item)
                                } else {
                                    items.add(0, item)
                                }
                            } else {
                                items = ArrayList()
                                items.add(0, item)
                            }
                            if (item.type != ConversationType.INSTANT_CHAT.value) {
                                _conversations.value = items
                                ifConversationExistToInstaChat(item)
                            } else {
                                _instachatConversations.value = items
                            }
                            checkForDotNotification(item)
                            insertConversation(item)
                        }
                    }
                    is Resource.Loading -> {

                    }
                    is Resource.Error -> {

                    }
                }
            }
        }
    }

    override fun likeMessageStateChange(likedMessageStateChanged: LikedMessageStateChanged) {
    }

    override fun updateTypingStateChanged(updateTypingStateChanged: UpdateTypingStateChanged) {
        viewModelScope.launch {
            _isTypingStateChange.emit(updateTypingStateChanged)
        }
    }

    override fun userNotPartOfConversationsError(errorChatWebSocketModel: ErrorChatWebSocketModel) {
        viewModelScope.launch(Dispatchers.IO) {
            errorChatWebSocketModel.payload.conversationId?.let {
                chatRepository.deleteConversationById(it)
            }
        }
    }
    override fun validateError(errorChatWebSocketModel: ErrorChatWebSocketModel) {}

    override fun defaultError(errorChatWebSocketModel: ErrorChatWebSocketModel) {}

    //end HomeChatWebSocketReceivedInterface

    override fun newFcmMessage(conversationWebSocketModel: ConversationWebSocketModel) {
        val item: ConversationModel = conversationWebSocketModel.data.conversationMember
        val message: MessageModel = conversationWebSocketModel.data.message

        //set message for conversation 1to1
        if (singleConversation.hasObservers()) {
            _singleConversation.value = conversationWebSocketModel
        }

        if (ChatWebSocket.isConnected() && item.type != ConversationType.INSTANT_CHAT.value) {
            viewModelScope.launch(Dispatchers.IO) {
                val conversationModel = chatRepository.getConversationById(item.id!!)
                if (conversationModel != null) {
                    if (conversationModel.lastMessageTime < item.lastMessageTime) {
                        withContext(Dispatchers.Main) {
                            handleNewFcmMessage(item, message)
                        }
                        if (!ChatWebSocket.isClosing())
                            ChatWebSocket.reConnectWebSocket()
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        handleNewFcmMessage(item, message)
                    }
                    if (!ChatWebSocket.isClosing())
                        ChatWebSocket.reConnectWebSocket()
                }
            }
        } else {
            handleNewFcmMessage(item, message)
        }
    }

    override fun likeMessage(likeMessageModel: LikeMessageModel?) {
        if (likeMessageObserver.hasObservers()) {
            _likeMessageObserver.value = likeMessageModel
        }
    }

    override fun newLike(likeYouWebSocketModel: LikeYouWebSocketModel) {
        val item: UserLikedYouNotificationResponse = likeYouWebSocketModel.data
        val userLikesModel = if (DuaApplication.instance.shouldShowPremium && userProfile.value?.premiumType == null ) {
            UserLikesModel.convertToUserLikesModelUnblur(item.data)
        } else {
            UserLikesModel.convertToUserLikesModel(item.data)
        }
        userLikesModel?.let {

            viewModelScope.launch(Dispatchers.IO) {
                likedYouRepository.insertLike(userLikesModel)
            }

            val items: ArrayList<UserLikesModel>
            if (!_newLikeYou.value.isNullOrEmpty()) {
                items = ArrayList(_newLikeYou.value!!)
                val find = items.find { it.user.id == userLikesModel.user.id }
                if (find != null) {
                    items.remove(find)
                    items.add(0, userLikesModel)
                } else {
                    items.add(0, userLikesModel)
                }
            } else {
                items = ArrayList()
                items.add(0, userLikesModel)
            }
            _newLikeYou.value = items
            checkForDotNotification(userLikesModel)
        }
    }

    override fun newMatch(matchesWebSocketModel: MatchesWebSocketModel) {
        val item = matchesWebSocketModel.data
        val userMatchesModel = UserMatchesModel.convertToUserMatchesModel(item.data)
        userMatchesModel?.let {
            viewModelScope.launch(Dispatchers.IO) {
                chatRepository.insertMatch(userMatchesModel)
            }

            val items: ArrayList<UserMatchesModel>
            if (!_newMatch.value.isNullOrEmpty()) {
                items = ArrayList(_newMatch.value!!)
                val find = items.find { it.id == userMatchesModel.id }
                if (find != null) {
                    items.remove(find)
                    items.add(0, userMatchesModel)
                } else {
                    items.add(0, userMatchesModel)
                }
            } else {
                items = ArrayList()
                items.add(0, userMatchesModel)
            }
            _newMatch.value = items
            checkForDotNotification(userMatchesModel)

        }
    }

    override fun dislikeInstaChat(dislikeInstaChatModel: DislikeInstaChatModel) {
        _dislikeInstaChat.value = dislikeInstaChatModel
    }

    private fun handleNewFcmMessage(item:ConversationModel,message:MessageModel){

        if (message.type == MessageType.CALLS_ALLOWED.value)
            item.areOutgoingCallsAllowed = true
        else if (message.type == MessageType.CALLS_DISALLOWED.value)
            item.areOutgoingCallsAllowed = false

        val items: ArrayList<ConversationModel>
        if ((item.type != ConversationType.INSTANT_CHAT.value && !webSocketConversations.value.isNullOrEmpty())
            || (item.type == ConversationType.INSTANT_CHAT.value && !webSocketInstchatConversations.value.isNullOrEmpty())
        ) {
            items =
                ArrayList(if (item.type != ConversationType.INSTANT_CHAT.value) webSocketConversations.value!! else webSocketInstchatConversations.value!!)
            val find = items.find { it.id == item.id }
            if (find != null) {
                items.remove(find)
                items.add(0, item)
            } else {
                items.add(0, item)
            }
        } else {
            items = ArrayList()
            items.add(0, item)
        }
        if (item.type != ConversationType.INSTANT_CHAT.value ||
            (item.type == ConversationType.INSTANT_CHAT.value && item.isRMOD)) {
            _conversations.value = items
            ifConversationExistToInstaChat(item)

            if(item.isRMOD) {
                _rmodItem.value?.copy(isHidden = true)?.let { _rmodItem.value = it }
            }
        } else {
            _instachatConversations.value = items
        }
        checkForDotNotification(item)
        insertConversation(item)
    }

    fun newMatchFromInternalInteraction(userMatchesModel: UserMatchesModel,existingUserId:String) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.insertMatch(userMatchesModel)
        }

        val items: ArrayList<UserMatchesModel>
        if (!_newMatch.value.isNullOrEmpty()) {
            items = ArrayList(_newMatch.value!!)
            val find = items.find { it.id == userMatchesModel.id }
            if (find != null) {
                items.remove(find)
                items.add(0, userMatchesModel)
            } else {
                items.add(0, userMatchesModel)
            }
        } else {
            items = ArrayList()
            items.add(0, userMatchesModel)
        }
        _newMatch.value = items

        _newMatchInternal.value = existingUserId

        checkForDotNotification(userMatchesModel)
    }

    fun newConversationFromInternalInteraction(conversationModel: ConversationModel, existingUserId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.insertConversation(conversationModel)
        }

        val items: ArrayList<ConversationModel>
        if (!_newConversation.value.isNullOrEmpty()) {
            items = ArrayList(_newConversation.value!!)
            val find = items.find { it.id == conversationModel.id }
            if (find != null) {
                items.remove(find)
                items.add(0, conversationModel)
            } else {
                items.add(0, conversationModel)
            }
        } else {
            items = ArrayList()
            items.add(0, conversationModel)
        }
        _newConversation.value = items

        _newMatchInternal.value = existingUserId

        checkForDotNotification(conversationModel)
    }

    override fun userUnmatched(userUnmatchedWebSocketModel: UserUnmatchedWebSocketModel) {
        val item = userUnmatchedWebSocketModel.data
        viewModelScope.launch(Dispatchers.IO) {
            chatRepository.deleteMatchByUserId(item.userId)
            chatRepository.deleteConversationByUserId(item.userId)
        }

        val items: ArrayList<UserUnMatchedModel>
        if (!_unMatches.value.isNullOrEmpty()) {
            items = ArrayList(_unMatches.value!!)
            val find = items.find { it.userId == item.userId }
            if (find != null) {
                items.remove(find)
                items.add(0, item)
            } else {
                items.add(0, item)
            }
        } else {
            items = ArrayList()
            items.add(0, item)
        }
        _unMatches.value = items
    }

    override fun userDeleted(userDeletedWebSocketModel: UserDeletedWebSocketModel) {
        _deleteUser.value = userDeletedWebSocketModel
    }

    fun closeWithWebSocket() {
        ChatWebSocket.close()
    }

    fun setFilteredUsers(data: List<RecommendedUserModel>) {
        //lambda to invoke whenever we need final product of cards
        val finalCards = {
            //variable to save users after each filtering case
            var newCards = mutableListOf<RecommendedUserModel>()
            val userFilter = userProfile.value?.filter
            //variable that holds current users that already match filter settings
            val currentCards = recommendedUsers.value?.filter { oldUser ->
                (oldUser.age >= userFilter?.minAge ?: 18 && oldUser.age <= userFilter?.maxAge ?: 40)
            }
            //cache data coming from the api after we call on filter changed
            val dataFromApi = data.toMutableList()
            //check for null and if not filter let only 10 distinct users otherwise just use users that came from API
            if (!currentCards.isNullOrEmpty()) {
                val firstCard = currentCards.first()
                if (dataFromApi.isNotEmpty()) {
                    dataFromApi.let { data ->
                        data.add(0, firstCard)
                        if (data.size > 10) data.remove(dataFromApi.last())
                        newCards = data
                        newCards = newCards.distinctBy { user -> user.id }.toMutableList()
                    }
                }
            } else {
                newCards = dataFromApi
            }
            newCards
        }
        //invoking lambda to set variable of recommended users }
        setUsersRecommended(finalCards.invoke())
    }

    /*fun addMoreUsers(newUsers: MutableList<RecommendedUserModel>) {
        val existingUsers = _users.value
        if (existingUsers != null) {
            val list = existingUsers.toMutableList()
            newUsers.removeAll { allRecommendedUsers.contains(it.cognitoUserId) }
            list.addAll(newUsers)
            allRecommendedUsers.addAll(newUsers.map { it.cognitoUserId })
            setUsersRecommended(list)
        }
    }*/

    /**
     * Called to set the cached users from pagination
     * This was done because the cardstackview library had a weird behavior when applying the new
     * cards while swiping that started to autoswipe cards
     *
     * @return if there were cachedUsers and if those users were applied to the _users variable
     */
    fun applyCachedUsers(): Boolean {
        return if (cachedUsers.isNotEmpty()) {
            setUsersRecommended(cachedUsers)
            cachedUsers.clear()
            true
        } else false
    }

    fun cacheMoreUsers(users: List<RecommendedUserModel>) {
        cachedUsers = users.toMutableList()
    }

    private val _userFilterUpdated = SingleLiveData<Boolean>()
    val userFilterUpdated: LiveData<Boolean> get() = _userFilterUpdated
    fun updateFilterUser(updated: Boolean) {
        _userFilterUpdated.postValue(updated)
    }

    fun removeReportedUser() {
        Timber.tag(CARDSTAG).d("removeReportedUser")
        val mutableUsers = _recommendedUsers.value?.toMutableList()
        mutableUsers?.let {
            if (it.isNotEmpty()) {
                it.removeAt(0)
                setUsersRecommended(it)
            } else {
                applyCachedUsers()
            }
        }
    }

    fun dissmissCardUserProfile() {
        _dismisCardWhenReporting.call()
    }

    fun setLikesCounterResetTime() {
        val user = userProfile.value
        user?.let { _user ->
            updateUserResetTimeByNameConfig(userProfile.value?.counterConfigurationNames?.likeCounterCN)
            getUserCounters(_user,_user.counterConfigurationNames.likeCounterCN)
        }
    }
    fun setDislikesCounterResetTime() {
        val user = userProfile.value
        user?.let { _user ->
            updateUserResetTimeByNameConfig(userProfile.value?.counterConfigurationNames?.dislikeCounterCN)
            getUserCounters(_user,_user.counterConfigurationNames.dislikeCounterCN)
        }
    }

    fun setUndoCounterResetTime() {
        val user = userProfile.value
        user?.let {
            updateUserResetTimeByNameConfig(it.counterConfigurationNames.undoCounterCN)
            getUserCounters(it,it.counterConfigurationNames.undoCounterCN)
        }
    }

    fun setInstaChatLimitReachedCounterRestTime() {
        val user = userProfile.value
        user?.let {
            updateUserResetTimeByNameConfig(it.counterConfigurationNames.instachatCounterCN)
            getUserCounters(it,it.counterConfigurationNames.instachatCounterCN)
        }
    }

    private fun updateUserResetTimeByNameConfig(nameConfiguration: String?) {
        userCounters?.firstOrNull{ it.configurationName == nameConfiguration }?.let { counterEntity ->
            if (counterEntity.counter.resetTime == null) {
                val newCounter = counterEntity.copy(
                    counter = counterEntity.counter.copy(
                        resetTime = CalculateIntervalTimeInHourUseCase.invoke(counterEntity)
                    )
                )
                userCounters = userCounters?.toMutableList()?.apply {
                    remove(counterEntity)
                    add(newCounter)
                }
                viewModelScope.launch(Dispatchers.IO) {
                    updateUserCounterUseCase.invoke(newCounter)
                }

            }
        }
    }

    fun navigatedToProfileFromRadar() {
        _isFromGuidelines.value = true
    }

    private val _referralIdGenerated: SingleLiveData<String> = SingleLiveData()
    val referralIdGenerated: LiveData<String>
        get() = _referralIdGenerated

    private val _showReferralFriendsShare: SingleLiveData<Uri> = SingleLiveData()
    val showReferralFriendsShare: LiveData<Uri>
        get() = _showReferralFriendsShare

    fun showReferralFriendsShare(link: Uri) {
        _showReferralFriendsShare.value = link
    }

    fun getReferral() {
        var isOnSharePref = false
        val currentReferralPref = duaSharedPrefs.getCurrentReferral()
        val currentReferralLastTimeSavedPref = duaSharedPrefs.getCurrentReferralLastTimeSaved()
        val sixDayMilliseconds: Long = 518400000

        if (currentReferralPref != "" && currentReferralLastTimeSavedPref != 0L && ((currentReferralLastTimeSavedPref + sixDayMilliseconds) > System.currentTimeMillis())) {
            isOnSharePref = true
            _referralIdGenerated.value = duaSharedPrefs.getCurrentReferral()
            duaSharedPrefs.setCurrentReferral("")
            duaSharedPrefs.setCurrentReferralLastTimeSaved(0L)
        }
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getReferral().catch {

            }.collect {
                viewModelScope.launch(Dispatchers.Main) {
                    if (!isOnSharePref) {
                        _referralIdGenerated.value = it.referralId
                        duaSharedPrefs.setCurrentReferral("")
                        duaSharedPrefs.setCurrentReferralLastTimeSaved(0L)
                        checkIfHasReferralId()
                    } else {
                        duaSharedPrefs.setCurrentReferral(it.referralId)
                        duaSharedPrefs.setCurrentReferralLastTimeSaved(System.currentTimeMillis())
                    }
                }

            }
        }
    }

    fun checkIfHasReferralId() {
        val currentReferralPref = duaSharedPrefs.getCurrentReferral()
        val currentReferralLastTimeSavedPref = duaSharedPrefs.getCurrentReferralLastTimeSaved()
        val sixDayMilliseconds: Long = 518400000
        if (currentReferralPref == "" || currentReferralLastTimeSavedPref == 0L || ((currentReferralLastTimeSavedPref + sixDayMilliseconds) < System.currentTimeMillis())) {
            viewModelScope.launch(Dispatchers.IO) {
                userRepository.getReferral().catch {
                }.collect {
                    duaSharedPrefs.setCurrentReferral(it.referralId)
                    duaSharedPrefs.setCurrentReferralLastTimeSaved(System.currentTimeMillis())
                }
            }
        }
    }

    fun getAllTags(userModel: UserModel) {
        viewModelScope.launch(Dispatchers.IO) {
            if ((userModel.settings.tagsVersion > duaSharedPrefs.getTagsVersion()) || !userRepository.hasTagsInDB() || duaSharedPrefs.isLanguageChange()) {
                userRepository.getAllTags(userModel.language)
                    .catch { e ->
                        e.printStackTrace()
                    }.collect {
                        userRepository.insertTags(it.tags)
                        duaSharedPrefs.setTagsVersion(it.version)
                        duaSharedPrefs.setLanguageChange(false)
                    }
            }
        }
    }

    fun forceGetAllTags(language: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val result = userRepository.getAndSaveAllTags(language)
            Timber.tag(TAG).d("force get tags: $result")
        }
    }


    fun shouldShowHometown():Boolean {
        val albanianCommunityId = "al"
        return userProfile.value?.communityInfo?.id == albanianCommunityId
    }

    private fun syncLikesCounterResetTime() {
        userProfile.value?.let {
            updateUserResetTimeByNameConfig(it.counterConfigurationNames.likeCounterCN)
            getUserCounters(it,it.counterConfigurationNames.likeCounterCN)
        }
    }

    private fun syncDislikesCounterResetTime() {
        userProfile.value?.let {
            updateUserResetTimeByNameConfig(it.counterConfigurationNames.dislikeCounterCN)
            getUserCounters(it,it.counterConfigurationNames.dislikeCounterCN)
        }
    }

    private fun syncInstaChatCounterResetTime() {
        userProfile.value?.let {
            updateUserResetTimeByNameConfig(it.counterConfigurationNames.instachatCounterCN)
            getUserCounters(it,it.counterConfigurationNames.instachatCounterCN)
        }
    }

    private fun syncUndoCounterResetTime() {
        userProfile.value?.let {
            updateUserResetTimeByNameConfig(it.counterConfigurationNames.undoCounterCN)
            getUserCounters(it, it.counterConfigurationNames.undoCounterCN)
        }
    }

    private val _showWatchAd: SingleLiveData<Int> = SingleLiveData()
    val showWatchAd: LiveData<Int>
        get() = _showWatchAd

    fun checkForWatchAd(): Boolean {
        val user = userProfile.value
        return if (user?.premiumType == null &&
            hasRemainingRewardVideos() &&
            user?.settings?.rewardedAdSwipeRewardAmount != null &&
            RemoteConfigUtils.isAdsEnabled()) {
            _showWatchAd.value = user.settings.rewardedAdSwipeRewardAmount ?: 1
            true
        } else false
    }

    private fun getConversationByUserId(cognitoUserId: String, hasChatModel: HasChatModel) {

        viewModelScope.launch(Dispatchers.IO) {
            delay(1000)
            chatRepository.getConversationByUserId(cognitoUserId, null)
                .catch { ex ->
                    ex.printStackTrace()
                    withContext(Dispatchers.Main) {
                        onConversationHasChatError()
                    }
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {
                            withContext(Dispatchers.Main) {
                                onConversationHasChatSuccess(it.data.conversation)
                                _showMatchScreenIfHasChat.value = hasChatModel

                            }

                        }
                        is Resource.Loading -> {

                        }
                        is Resource.Error -> {
                            withContext(Dispatchers.Main) {
                                onConversationHasChatError()
                            }
                        }
                    }
                }
        }
    }

    fun onConversationHasChatError() {
        _conversationHasChatError.value = true
    }

    private fun onConversationHasChatSuccess(data: ConversationModel) {
        _conversationHasChat.value = data
        _singleConversationHasChat.value = data
    }

    fun isUserVerified(): Boolean {
        return userProfile.value?.hasBadge1 == true
    }

    private fun onInstaChatSent() {
        _instaChatSent.value = true
    }

    //region Premium Offers
    fun anyOfferAvailable() :Boolean = specialOfferAvailable() || dontLetGoOfferAvailable()
    fun specialOfferAvailable(): Boolean {
        val specialOffer = getSpecialOffer()
        return specialOffer != null && System.currentTimeMillis() > specialOffer.startTime && System.currentTimeMillis() < specialOffer.endTime
    }
    fun dontLetGoOfferAvailable(model: DontLetGoOfferAvailableResponseModel? = null): Boolean {
        val dontLetGoOffer = model ?: getDontLetGoOffer()
        return dontLetGoOffer?.currentSubscription != null
                && (dontLetGoOffer.showDontLetGoOffer && !dontLetGoOffer.hasUserAlreadyBoughtDontLetGo)
                && System.currentTimeMillis() < dontLetGoOffer.currentSubscription.expireTime
    }

    fun dontLetGoDaysLeft(model: DontLetGoOfferAvailableResponseModel? = null): Int {
        val dontLetGoOffer = model ?: getDontLetGoOffer()
        return dontLetGoOffer?.currentSubscription?.expireTime?.let {
            val daysLeft = TimeUnit.MILLISECONDS.toDays(it - System.currentTimeMillis())
            if (daysLeft > 0) daysLeft.toInt() else 0
        } ?: 0
    }

    private fun getDontLetGoCard(): Pair<Boolean,Int> {
        val dontLetGoOffer = getDontLetGoOffer()
        val isOfferAvailable = dontLetGoOfferAvailable(dontLetGoOffer)
        val daysLeft = dontLetGoDaysLeft(dontLetGoOffer)
        val isUserPremium = userProfile.value?.premiumType != null
        val isBillingSupported = DuaApplication.instance.getBillingAvailable()
        return Pair(isOfferAvailable && isUserPremium && isBillingSupported && daysLeft <= 3,daysLeft)
    }

    fun getOfferType(): String? {
        return if (dontLetGoOfferAvailable()) {
            PremiumOfferTypes.DONT_LET_GO.offerType
        } else if(specialOfferAvailable()) {
            getSpecialOffer()!!.offerType
        } else null
    }


    fun setSpecialOfferReceived() {
        _specialOfferReceived.call()
    }

    //endregion

    private fun ifConversationExistToInstaChat(item: ConversationModel) {
        val items: ArrayList<ConversationModel>
        if (!_removesInstaChat.value.isNullOrEmpty()) {
            items =
                ArrayList(_removesInstaChat.value!!)
            val find = items.find { it.id == item.id }
            if (find != null) {
                items.remove(find)
                items.add(0, item)
            } else {
                items.add(0, item)
            }
        } else {
            items = ArrayList()
            items.add(0, item)
        }
        _removesInstaChat.value = items
    }

    suspend fun getInstagramMedia(cognitoId: String) = flow<Resource<InstagramMediaResponse>> {
        userRepository.getUserInstagramMedia(cognitoId = cognitoId)
            .catch { e ->
                e.printStackTrace()
            }
            .collect {
                emit(it)
            }
    }

    fun reconnectInstagram(code: String) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.reconnectInstagram(code)
                .catch { e ->
                    e.printStackTrace()
                }
                .collect {
                    Timber.tag(TAG).d(it.toString())
                }
        }
    }

    fun disconnectInstagram() {
        viewModelScope.launch(Dispatchers.IO) {
            val profile = userProfile.value?.copy(
                instagramToken = InstagramToken(
                    null,
                    null,
                    null
                ), instagramStatus = INSTAGRAM_STATUS_DISCONNECTED
            )
            userRepository.disconnectInstagram(profile)
                .catch { e ->
                    e.printStackTrace()
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {
                        }
                        else -> {
                        }
                    }
                }
        }
    }


    fun saveLastCard(direction: Direction, recommendedUserModel: RecommendedUserModel) {
        lastCardHandler?.removeCallbacksAndMessages(null)
        lastCard = Pair(direction, recommendedUserModel)
        lastCardHandler = Handler()
        lastCardHandler?.postDelayed({ clearLastCard() }, 60000)
    }

    // commenting out this method for now as Purchasely doesn't support offers
/*
    fun getPremiumOfferFromId(offerId: String?, appInboxMessageId: String? = null, isFromDeepLink: Boolean = false) {
        if(!offerId.isNullOrEmpty()) {
            viewModelScope.launch(Dispatchers.IO) {
                userRepository.getPremiumOfferById(offerId)
                    .catch { e -> e.printStackTrace() }
                    .collect { response ->
                        withContext(Dispatchers.Main) {
                            when(response) {
                                is Resource.Success -> {
                                    val model = response.data
                                    val now = System.currentTimeMillis()

                                    if(isFromDeepLink && model.isJourney) {
                                        duaSharedPrefs.setPremiumOfferIdData(null)
                                        return@withContext
                                    }

                                    if(now > model.eligibleFrom && now < model.eligibleUntil) {
                                        val endTime = now + TimeUnit.HOURS.toMillis(model.lengthInHours.toLong())
                                        val offer = SpecialOfferDataModel(endTime, model.offerType, model.productId, model.eligibleFrom)
                                        duaSharedPrefs.setPremiumSpecialOfferData(offer)
                                        duaSharedPrefs.setHasOfferBeenShownFromCards(false)
                                        setSpecialOfferReceived()
                                        duaSharedPrefs.setPremiumOfferIdData(null)
                                    }

                                    //Delete the appInboxMessage after we have handled it
                                    CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.deleteInboxMessage(appInboxMessageId)

                                    val sendBroadCastData = Intent(PremiumActivity.PREMIUM_OFFER_INTENT)
                                    sendBroadCastData.putExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, true)
                                    LocalBroadcastManager.getInstance(DuaApplication.instance).sendBroadcast(sendBroadCastData)

                                }
                            }
                        }
                    }
            }
        }
    }
*/


    fun clearLastCard() {
        lastCard = null
        viewModelScope.launch {
            _missedMatchCard.emit(null)
        }
        lastMissedMatchCard = null
    }

    fun userProfileProgressBarShow(show: Boolean) {
        _showProgressBarUser.value = show
    }

    fun verifyPayment(model: VerifyPaymentModel, purchase: Purchase, eventSource: String? = null, placementId: String? = null) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.verifyConsumable(model)
                .catch { ex ->
                    when (ex) {
                        is PaymentAlreadyConsumedException -> {
                            _consumePurchase.postValue(model.token)
                        }
                        else -> {
                            ex.printStackTrace()
                        }
                    }
                }
                .collect {
                    when (it) {
                        is Resource.Success -> {
                            //log event purchased

                            val propertyName = model.product.substringAfter(
                                "${
                                    BuildConfig.APPLICATION_ID.substringBeforeLast(".")
                                }."
                            ).replace(".", "_")

                            validateAndLogInAppPurchase(purchase,inAppPurchaseItems[model.product])

                            val eventPremiumType = getPremiumTypeEventProperty(userRepository.user.value)

                            sendClevertapEvent(ClevertapEventEnum.IN_APP_PURCHASED,
                                mapOf(
                                    ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                    ClevertapEventPropertyEnum.IN_APP_PRODUCT.propertyName to model.packageType,
                                    ClevertapEventPropertyEnum.PRODUCT_ID.propertyName to propertyName,
                                    ClevertapEventPropertyEnum.PLACEMENT_ID.propertyName to placementId,
                                    ClevertapEventPropertyEnum.PRICE_VALUE.propertyName to inAppPurchaseItems[model.product]?.price,
                                    ClevertapEventPropertyEnum.PRICE_CURRENCY.propertyName to inAppPurchaseItems[model.product]?.currency))

                            firebaseLogEvent(FirebaseAnalyticsEventsName.IN_APP_PURCHASED,
                                mapOf(
                                    FirebaseAnalyticsParameterName.EVENT_SOURCE.value to eventSource,
                                    FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                                    FirebaseAnalyticsParameterName.IN_APP_PRODUCT.value to model.packageType,
                                    FirebaseAnalyticsParameterName.PRODUCT_ID.value to propertyName,
                                    FirebaseAnalyticsParameterName.PLACEMENT_ID.value to placementId,
                                    FirebaseAnalyticsParameterName.PRICE_VALUE.value to inAppPurchaseItems[model.product]?.price,
                                    FirebaseAnalyticsParameterName.PRICE_CURRENCY.value to inAppPurchaseItems[model.product]?.currency))


                            sendAppsflyerEvent(AppsflyerEventsNameEnum.IS_IN_APP_BOUGHT, mapOf(AppsflyerEventsPropertyEnum.PRICE.eventsProperty to inAppPurchaseItems[model.product]?.price))

                            when (it.data.verified.type) {
                                PaymentType.IMPRESSIONS.type -> {
                                    val user = userProfile.value
                                    _consumePurchase.postValue(model.token)
                                    likesCount -= it.data.verified.count

                                    user?.let { _user ->
                                        userCounters?.firstOrNull { counter ->
                                            counter.configurationName == user.counterConfigurationNames.likeCounterCN
                                        }?.let { counter ->
                                            getUserCounters(_user,counter.configurationName)
                                        }
                                    }

                                    if (userProfile.value?.premiumType != null) {
                                        firebaseLogEvent(FirebaseAnalyticsEventsName.PURCHASED_IMPRESSIONS_ON_PREMIUM)
                                    }

                                    sendAppsflyerEvent(AppsflyerEventsNameEnum.IS_IN_APP_BOUGHT, mapOf(AppsflyerEventsPropertyEnum.PRICE.eventsProperty to inAppPurchaseItems[model.product]?.price))

                                    NotificationHelper.getNotificationHelper(DuaApplication.instance.applicationContext)
                                        .showInAppProductBought(
                                            DuaApplication.instance.applicationContext,
                                            DuaApplication.instance.getString(R.string.hurray) + " \uD83C\uDF89",
                                            DuaApplication.instance.getString(
                                                R.string.you_just_received_your_impressions,
                                                it.data.verified.count.toString()
                                            ),
                                            null,
                                            R.string.general_notification_,
                                            NotificationConstants.NOTIFICATION_ID_GENERAL,
                                            R.drawable.trophy_png
                                        )

                                    firebaseLogEvent(FirebaseAnalyticsEventsName.PURCHASED_IMPRESSIONS_NOTIFICATION)
                                }
                                PaymentType.FLYING.type -> {
                                    val user = userProfile.value
                                    _consumePurchase.postValue(model.token)
                                    flyingCount -= it.data.verified.count
                                    user?.let { _user ->
                                        userCounters?.firstOrNull { counter ->
                                            counter.configurationName == user.counterConfigurationNames.flyCounterCN
                                        }?.let { counter ->
                                            getUserCounters(_user,counter.configurationName)
                                        }
                                    }
                                    NotificationHelper.getNotificationHelper(DuaApplication.instance.applicationContext)
                                        .showInAppProductBought(
                                            DuaApplication.instance.applicationContext,
                                            DuaApplication.instance.getString(R.string.hurray) + " \uD83C\uDF89",
                                            DuaApplication.instance.getString(
                                                R.string.you_just_received_your_flights,
                                                it.data.verified.count.toString()
                                            ),
                                            null,
                                            R.string.general_notification_,
                                            NotificationConstants.NOTIFICATION_ID_GENERAL,
                                            R.drawable.airplane_png
                                        )

                                    firebaseLogEvent(FirebaseAnalyticsEventsName.PURCHASED_FLIGHTS_NOTIFICATION)
                                }
                                PaymentType.UNDO.type -> {
                                    val user = userProfile.value
                                    _consumePurchase.postValue(model.token)
                                    undoCount -= it.data.verified.count

                                    user?.let { _user ->
                                        userCounters?.firstOrNull { counter ->
                                            counter.configurationName == user.counterConfigurationNames.undoCounterCN
                                        }?.let { counter ->
                                            getUserCounters(_user,counter.configurationName)
                                        }
                                    }
                                    NotificationHelper.getNotificationHelper(DuaApplication.instance.applicationContext)
                                        .showInAppProductBought(
                                            DuaApplication.instance.applicationContext,
                                            DuaApplication.instance.getString(R.string.hurray) + " \uD83C\uDF89",
                                            DuaApplication.instance.getString(
                                                R.string.you_just_received_your_undos,
                                                it.data.verified.count.toString()
                                            ),
                                            null,
                                            R.string.general_notification_,
                                            NotificationConstants.NOTIFICATION_ID_GENERAL,
                                            R.drawable.undo_png
                                        )

                                    firebaseLogEvent(FirebaseAnalyticsEventsName.PURCHASED_UNDO_NOTIFICATION)

                                }
                                PaymentType.INSTACHATS.type -> {
                                    val user = userProfile.value
                                    _consumePurchase.postValue(model.token)
                                    instaChatCount -= it.data.verified.count


                                    user?.let { _user ->
                                        userCounters?.firstOrNull { counter ->
                                            counter.configurationName == user.counterConfigurationNames.instachatCounterCN
                                        }?.let { counter ->
                                            getUserCounters(_user,counter.configurationName)
                                        }
                                    }


                                    if (userProfile.value?.premiumType != null) {
                                        firebaseLogEvent(FirebaseAnalyticsEventsName.PURCHASED_INSTACHATS_ON_PREMIUM)

                                        sendClevertapEvent(
                                            ClevertapEventEnum.IN_APP_PURCHASED,
                                            mapOf(
                                                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                                ClevertapEventPropertyEnum.IN_APP_PRODUCT.propertyName to model.packageType,
                                                ClevertapEventPropertyEnum.PRODUCT_ID.propertyName to inAppPurchaseItems[model.product]?.productId,
                                                ClevertapEventPropertyEnum.PRICE_VALUE.propertyName to inAppPurchaseItems[model.product]?.price,
                                                ClevertapEventPropertyEnum.PRICE_CURRENCY.propertyName to inAppPurchaseItems[model.product]?.currency
                                            )
                                        )

                                        sendAppsflyerEvent(AppsflyerEventsNameEnum.IS_IN_APP_BOUGHT, mapOf(AppsflyerEventsPropertyEnum.PRICE.eventsProperty to inAppPurchaseItems[model.product]?.price))
                                    }


                                    NotificationHelper.getNotificationHelper(DuaApplication.instance.applicationContext)
                                        .showInAppProductBought(
                                            DuaApplication.instance.applicationContext,
                                            DuaApplication.instance.getString(R.string.hurray) + " \uD83C\uDF89",
                                            DuaApplication.instance.getString(
                                                R.string.you_just_received_your_InstaChat,
                                                it.data.verified.count.toString()
                                            ),
                                            null,
                                            R.string.general_notification_,
                                            NotificationConstants.NOTIFICATION_ID_GENERAL,
                                            R.drawable.instachat_png
                                        )

                                    firebaseLogEvent(FirebaseAnalyticsEventsName.PURCHASED_INSTACHATS_NOTIFICATION)

                                    withContext(Dispatchers.Main) {
                                        _instachatsBought.call()
                                    }
                                }
                                PaymentType.BOOST.type -> {
                                    val user = userProfile.value
                                    _consumePurchase.postValue(model.token)
                                    boostCount -= it.data.verified.count

                                    user?.let { _user ->
                                        userCounters?.firstOrNull { counter ->
                                            counter.configurationName == user.counterConfigurationNames.boostCounterCN
                                        }?.let { counter ->
                                            getUserCounters(_user,counter.configurationName)
                                        }
                                    }

                                    withContext(Dispatchers.Main) {
                                        setBoostStatusChanged()
                                    }


                                    if (userProfile.value?.premiumType != null) {
                                        val propertyName = model.product.substringAfter("${BuildConfig.APPLICATION_ID.substringBeforeLast(".")}.").replace(".", "_")
                                        firebaseLogEvent(FirebaseAnalyticsEventsName.PURCHASED_BOOST_ON_PREMIUM, mapOf(propertyName to 1L))
                                    }


                                    NotificationHelper.getNotificationHelper(DuaApplication.instance.applicationContext)
                                        .showInAppProductBought(
                                            DuaApplication.instance.applicationContext,
                                            DuaApplication.instance.getString(R.string.hurray) + " \uD83C\uDF89",
                                            DuaApplication.instance.getString(
                                                R.string.you_just_received_your_boost,
                                                it.data.verified.count.toString()
                                            ),
                                            null,
                                            R.string.general_notification_,
                                            NotificationConstants.NOTIFICATION_ID_GENERAL,
                                            R.drawable.boost_notification_icon
                                        )


                                }
                            }

                            Purchasely.synchronize()
                        }
                        is Resource.Loading -> {
                            _showSpinningDialog.postValue(true)
                        }
                        is Resource.Error -> {
                        }
                    }
                }
        }
    }

    @SuppressLint("StringFormatInvalid")
    fun verifySubscription(model: VerifyPaymentModel,
                           purchase: Purchase,
                           isRestoring: Boolean,
                           eventSource: String? = null,
                           placementId: String? = null) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.verifySubscription(model)
                .catch { ex ->
                    _showSpinningDialog.postValue(false)
                    showCancelPremium(false)

                    sendClevertapEvent(ClevertapEventEnum.PREMIUM_PURCHASE_FAILED, mapOf(
                        ClevertapEventPropertyEnum.COMMUNITY.propertyName to userProfile.value?.communityInfo?.id,
                        ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                    ))

                    when (ex) {
                        is PaymentAlreadyConsumedException -> {
                            _subscriptionValidationError.postValue(
                                PaymentVerifyErrorBody(null,null, PaymentVerifyErrorType.PAYMENT_ALREADY_CONSUMED.value)
                            )

                            _consumePurchase.postValue(model.token)
                        }
                        is SubscriptionConflictException -> {
                            Timber.tag("BILLING").d("Error model ${ex.error}")
                            _subscriptionValidationError.postValue(ex.error)

                            duaSharedPrefs.setPremiumOrderIdRetried(model.transactionId)
                            Timber.tag("BILLING").d(DuaApplication.instance.getString(R.string.current_subscription_is_linked_with_an_other_user))
                        }
                        else -> {
                            ex.printStackTrace()

                            _subscriptionValidationError.postValue(
                                PaymentVerifyErrorBody(null,null, PaymentVerifyErrorType.AN_ERROR_OCCURRED.value)
                            )
                        }
                    }
                }
                .collect { response ->
                    when (response) {
                        is Resource.Success -> {

                            validateAndLogInAppPurchase(purchase,inAppPurchaseItems[model.product])

                            NotificationHelper.getNotificationHelper(DuaApplication.instance.applicationContext)
                                .showInAppProductBought(
                                    DuaApplication.instance.applicationContext,
                                    DuaApplication.instance.getString(R.string.congratulations_celebrate),
                                        DuaApplication.instance.getString(R.string.enjoy_duapremium_to_the_fullest,DuaApplication.instance.getString(R.string.app_name)),
                                    null,
                                    R.string.general_notification_,
                                    NotificationConstants.NOTIFICATION_ID_GENERAL,
                                    R.drawable.premium_notification
                                )


                            val price = inAppPurchaseItems[model.product]?.price ?: "0"
                            val currency = inAppPurchaseItems[model.product]?.currency ?: ""

                            if(isRestoring) {
                                sendRestorePremiumEvent(
                                    productId = model.product,
                                    communityId = userProfile.value?.communityInfo?.id
                                )
                            } else {
                                firebaseLogEvent(FirebaseAnalyticsEventsName.PURCHASED_PREMIUM_NOTIFICATION)

                                val specialOfferEventName:String = when {
                                    PremiumOfferTypes.DONT_LET_GO.offerType == getOfferType() -> {
                                        AppsflyerSpecialOfferValueEnum.DONT_LET_GO.value
                                    }
                                    PremiumOfferTypes.PAY_1_GET_1.offerType == getOfferType() -> {
                                        AppsflyerSpecialOfferValueEnum.PAY_1_GET_1.value
                                    }
                                    (model.product == GooglePlayPackage.PREMIUM_PACKAGE_1_MONTH_OFFER.productId) -> {
                                        AppsflyerSpecialOfferValueEnum.SPECIAL_OFFER.value
                                    }
                                    else -> {
                                        ""
                                    }
                                }
                                sendPremiumPurchasedEvent(
                                    productId = model.product,
                                    priceValue = price.toFloat(),
                                    priceCurrency = currency,
                                    placementId = placementId,
                                    eventSource = eventSource
                                )

                                sendAppsflyerEvent(AppsflyerEventsNameEnum.IS_PREMIUM_BOUGHT,
                                    mapOf(AppsflyerEventsPropertyEnum.PACKAGE_NAME.eventsProperty to model.product,
                                        AppsflyerEventsPropertyEnum.SPECIAL_OFFER.eventsProperty to specialOfferEventName,
                                        AppsflyerEventsPropertyEnum.PRICE.eventsProperty to price))

                            }

                            val user = userProfile.value
                            user?.let {
                                it.premiumType = response.data.verified.premiumType
                                updateUser(user)
                            }

                            clearDontLetGoData()

                            hasPremiumBeenRestored = true
                            Purchasely.synchronize()

                            _showSpinningDialog.postValue(false)
                             showCancelPremium(true)

                            _premiumBoughtSuccessfully.emit(Unit)
                        }
                        is Resource.Loading -> {
                            _showSpinningDialog.postValue(true)
                            showCancelPremium(false)
                        }
                        is Resource.Error -> {
                        }
                    }
                }
        }
    }

    fun addInAppPurchaseItem(productId: String, model: InAppPurchaseItemInfoModel) {
        inAppPurchaseItems[productId] = model
    }

    fun isUserPremium(): Boolean {
        val user = userProfile.value
        return if (user != null)
            user.premiumType != null
        else false
    }

    fun getLikesExpirationInDays(): Long? {
        val seconds = userProfile.value?.settings?.limits?.interactionExpiration?.like
        return if(seconds != null)
            TimeUnit.SECONDS.toDays(seconds)
        else
            null
    }

    fun getMopubKeywords(): String {
        var userDataKeywords: String = ""
        userDataKeywords += "gender:${userProfile.value?.gender}"
        userDataKeywords += when (userProfile.value?.age) {
            in 18..24 -> ",age:group1"
            in 25..34 -> ",age:group2"
            in 35..44 -> ",age:group3"
            else -> ",age:group4"
        }

        if (userProfile.value?.tags?.any { it.tagTypeId == 1 } == true) {
            userDataKeywords += ",interests:sport"
        }
        if (userProfile.value?.tags?.any { it.tagTypeId == 3 } == true) {
            userDataKeywords += ",interests:pets"
        }

        userProfile.value?.tags?.find { it.tagTypeId == 4 }?.let {
            userDataKeywords += ",R:${it.tagItemId}"
        }

        if (isUserVerified()) {
            userDataKeywords += ",tick:Badge1"
        }

        if (badge2 == Badge2Status.APPROVED) {
            userDataKeywords += ",tick:Badge2"
        }


        return userDataKeywords
    }

    fun setCallEnded() {
        _callEnded.call()
    }


    private fun getUserFromDb() {
        viewModelScope.launch(Dispatchers.IO) {
            _userProfileNU.postValue(userRepository.getLoggedInUserModel())
        }
    }

    fun callOnUserLoginCleverTap(
        userModel: UserModel?,
        accountModel: AccountModel?
    ) {

        if(userModel == null || accountModel == null) {
            return
        }

        val areUserPropertiesSynced = areUserPropertiesSynced(userModel, accountModel)
        val haveUserPropertiesChanged = BuildConfig.CLEVERTAP_USER_PROPERTIES_VERSION > duaSharedPrefs.getClevertapUserPropertiesVersion()
        if(!areUserPropertiesSynced || haveUserPropertiesChanged || !duaSharedPrefs.getCalledOnLoginCleverTap() || !duaSharedPrefs.getNotificationCalledOnLoginClevertap()) {
            duaSharedPrefs.setCalledOnLoginCleverTap(true)
            duaSharedPrefs.setClevertapUserPropertiesVersion(BuildConfig.CLEVERTAP_USER_PROPERTIES_VERSION)
            val clevertapId = CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.getProperty(ClevertapUserPropertyEnum.IDENTITY.value)
            val cognitoId = userModel.cognitoUserId
            if(clevertapId != cognitoId)
                onLoginCleverTap(accountModel.email, accountModel.phone, accountModel.cognitoUserId)

            pushUserProfileClevertap(userModel, accountModel.email, accountModel.phone, accountModel.createdAt)

            CleverTapAPI.getDefaultInstance(DuaApplication.instance)?.pushFcmRegistrationId(duaSharedPrefs.getNotificationToken(), true)
            viewModelScope.launch {
                notificationRepository.getSettingsNotificationsFlow()
                    .catch { e->
                        e.printStackTrace()
                    }
                    .collect{
                        if(it is Resource.Success) {
                            duaSharedPrefs.setNotificationCalledOnLoginClevertap(true)
                            val notificationSettings = it.data.settings.convertToClevertapUserProperties()
                            pushUserProfileClevertap(userModel, accountModel.email, accountModel.phone, accountModel.createdAt,notificationSettings)
                        }
                    }
            }
        }
    }
    fun toggleGhostMode(isChecked: Boolean, eventSource: ClevertapGhostSourceValues){
        val params = mapOf("profile" to mapOf("isGhost" to isChecked))
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.ghostMode(isChecked,params)
                .catch {e->
                    e.printStackTrace()
                }
                .collect{
                    //we only send events here as per UI we observe user model and act upon it
                    sendGhostModeEvents(isChecked, eventSource,userProfile.value)
                }
        }
    }

    private fun sendGhostModeEvents(
        isChecked: Boolean,
        eventSource: ClevertapGhostSourceValues,
        userProfile:UserModel?
    ) {
        val ghostStatus =
            if (isChecked) ClevertapGhostStatusValues.ON.value else ClevertapGhostStatusValues.OFF.value
        val ghostSource =
            if (userProfile?.profile?.isInvisible == true) ClevertapGhostSourceValues.HIDE_PROFILE.value else eventSource.value
        sendClevertapEvent(
            event = ClevertapEventEnum.GHOST,
            properties = mapOf(
                ClevertapEventPropertyEnum.GHOST_STATUS.propertyName to ghostStatus,
                ClevertapEventPropertyEnum.GHOST_SOURCE.propertyName to ghostSource,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    userProfile
                ),
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to userProfile?.communityInfo?.communityName
            )
        )
        //Since the GA property values are the same with the clevertap property values, we use the same variables
        firebaseLogEvent(
            eventName = FirebaseAnalyticsEventsName.GHOST,
            params = mapOf(
                ClevertapEventPropertyEnum.GHOST_STATUS.propertyName to ghostStatus,
                ClevertapEventPropertyEnum.GHOST_SOURCE.propertyName to ghostSource,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    userProfile
                )
            )
        )
    }
    override fun onCleared() {
        super.onCleared()
        profileActivitiesItemIdToRestore = null
        ChatWebSocket.removeListener(this)
        closeWithWebSocket()
    }

    fun updateFeaturedProfilesState(userProfile: Profile?) {
        _featuredProfilesUsersState.update {
            it.copy(userProfile = userProfile)
        }
    }

    fun updateRewardAdType(rewardAdType: RewardAdType) {
        this.rewardAdTypeShowed = rewardAdType
    }

    fun currentRewardAdType(): RewardAdType {
        return rewardAdTypeShowed
    }

    fun onProfileActivitiesQuery(query: String?) {
        // Check if 'query' is empty or null, and apply filtering if it's not.
        // If 'query' is empty, we keep the original data as-is.
        val filterResults = query?.takeIf { it.isNotEmpty() }?.let { search ->
            // Filter the data based on 'query' (case-insensitive search)
            _profileActivitiesItems.value?.filter { it.name.contains(search, ignoreCase = true) }
        } ?: _profileActivitiesItems.value

        // Update the '_profileActivitiesItems' with the filtered or original data.
        _filteredProfileActivitiesItems.update {
            filterResults
        }
    }

    fun updateProfileActivities(profileActivitiesItems: List<ProfileActivitiesModel>?) {
        _profileActivitiesItems.update {
            profileActivitiesItems
        }
    }

    fun initFilteredProfileActivities() {
        _filteredProfileActivitiesItems.update {
             _profileActivitiesItems.value
        }

    }

    fun updateSelectedProfileActivityWithId(id: Int) {
        val updatedFilteredList = toggleProfileActivitySelection(_filteredProfileActivitiesItems.value, id)
        _filteredProfileActivitiesItems.update { updatedFilteredList }

        val updatedList = toggleProfileActivitySelection(_profileActivitiesItems.value, id)
        updateProfileActivities(updatedList)
    }

    private fun toggleProfileActivitySelection(list: List<ProfileActivitiesModel>?, id: Int): List<ProfileActivitiesModel>? {
        return list?.map { item ->
            if (item.id == id) {
                item.copy(isSelected = !item.isSelected)
            } else {
                item
            }
        }
    }

    fun removeRmod(
        interactionBody: InteractionBody,
        rmodItem: RmodModel?
    ) {
        viewModelScope.launch {
            try {
                val response = userRepository.removeRmod(interactionBody)

                Timber.tag(TAG).d(response.toString())

                val eventPremiumType = getPremiumTypeEventProperty(userProfile.value)
                sendRmodRemovedEvent(eventPremiumType, rmodItem?.totalPercentage)
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }


    fun updateConsumableRewardState(dialog:ConsumableRewardBaseDialog?, model: ConsumableRewardGivenModel?=null) {
        _consumableRewardUiState.update {
            it.copy(dialog = dialog, type = model?.rewardType)
        }
    }

    fun dismissConsumableRewardDialog() {
       _consumableRewardUiState.update {
           it.copy(dismissReward = true)
       }
    }

    fun onBoostHighlightShown() {
        _consumableRewardUiState.update {
            it.copy(showHighlightDialog = false)
        }
    }

    fun updateBoostHighlightData(screenCoordinates: IntArray, view: ConstraintLayout) {
        val xOnScreen = screenCoordinates[0]
        val yOnScreen = screenCoordinates[1]
        val width = view.width
        val height = view.height
        val community = userProfile.value?.communityInfo?.communityName

        val highlightData = BoostHighlightData(xOnScreen,yOnScreen,width,height,community)
        _consumableRewardUiState.update {
            it.copy(highLightData = highlightData)
        }
    }

    fun showBoostHighlightDialog() {
        _consumableRewardUiState.update {
            it.copy(showHighlightDialog = true)
        }
    }

    fun addCommunitiesToCards(updatedRecommendedUsers: List<RecommendedUserModel>) {
        communitiesLoaded = true
        setUsersRecommended(updatedRecommendedUsers)
    }

    fun redeemTicket(ticketId: String) {
        duaSharedPrefs.setSunnyHillAttendeeId(null)
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.redeemTicket(ticketId = ticketId)
                .catch { e->
                    withContext(Dispatchers.Main) {
                        e.printStackTrace()
                    }
                }
                .collect{ result->
                    withContext(Dispatchers.Main) {
                        when(result){
                            Resource.Loading -> {/*not used*/}
                            is Resource.Success -> {
                                Timber.tag(TAG).d("${result.data}")
                            }
                            else -> {/*not used*/}
                        }
                    }
                }
        }
    }

    fun setIsUserInGracePeriod(value: Boolean) {
        _isGracePeriod.value = value
    }

    private suspend fun updateLanguageAndTags(userLanguage:String) {
       withContext(Dispatchers.IO) {
           val language =  ModifiedLingver.getInstance().getLanguage()
           updateLanguageAndTagsUseCase(language,userLanguage)
       }
    }
}


data class ConsumableRewardUiState(
    val dialog: ConsumableRewardBaseDialog? = null,
    val type: String? = null,
    val dismissReward: Boolean? = null,
    val highLightData: BoostHighlightData?= null,
    val showHighlightDialog: Boolean = false
    )
