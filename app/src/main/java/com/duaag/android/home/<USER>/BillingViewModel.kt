package com.duaag.android.home.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.duaag.android.BuildConfig
import com.duaag.android.api.UserService
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.clevertap.*
import com.duaag.android.di.ActivityScope
import com.duaag.android.home.models.*
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.getPackageTypeForId
import com.duaag.android.utils.livedata.SingleLiveData
import javax.inject.Inject
import javax.inject.Named


@ActivityScope
class BillingViewModel @Inject constructor(
    @Named("private") val userService: UserService,
    val duaSharedPrefs: DuaSharedPrefs,
    val userRepository: UserRepository,
) : ViewModel(){

    companion object {
        const val TAG = "PlayBillingViewModelTag"
    }

    private val _selectedPackage: SingleLiveData<String> = SingleLiveData()
    val selectedPackage: LiveData<String>
        get() = _selectedPackage

    private val _buyPackage: SingleLiveData<String> = SingleLiveData()
    val buyPackage: LiveData<String>
        get() = _buyPackage

    private val _dismissDialog: SingleLiveData<Void> = SingleLiveData()
    val dismissDialog: LiveData<Void>
        get() = _dismissDialog


    fun buyPackage(item: String, eventSource: String? = null) {
        _buyPackage.value = item

        val eventName = "Get_${getPackageTypeForId(item)}_Initiate"
        val propertyName = item.substringAfter("${BuildConfig.APPLICATION_ID.substringBeforeLast(".")}.").replace(".", "_")
        firebaseLogEvent(eventName, mapOf(propertyName to 1L, FirebaseAnalyticsParameterName.PRODUCT_ID.value to propertyName))

        val eventPremiumType =getPremiumTypeEventProperty(userRepository.user.value)


        sendClevertapEvent(
            ClevertapEventEnum.IN_APP_PURCHASE_INITIATED,
            mapOf(
                ClevertapEventPropertyEnum.EVENT_SOURCE.propertyName to eventSource,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.IN_APP_PRODUCT.propertyName to getPackageTypeForId(item),
                ClevertapEventPropertyEnum.PRODUCT_ID.propertyName to propertyName))

        firebaseLogEvent(
            FirebaseAnalyticsEventsName.IN_APP_PURCHASE_INITIATED,
            mapOf(
                FirebaseAnalyticsParameterName.EVENT_SOURCE.value to eventSource,
                FirebaseAnalyticsParameterName.PREMIUM_TYPE.value to eventPremiumType,
                FirebaseAnalyticsParameterName.IN_APP_PRODUCT.value to getPackageTypeForId(item),
                FirebaseAnalyticsParameterName.PRODUCT_ID.value to propertyName))


        sendAppsflyerEvent(AppsflyerEventsNameEnum.HAS_USER_INITIATED_IN_APP)
    }

    fun setSelectedPackage(type: String) {
        _selectedPackage.value = type
    }

    fun dismissDialog() {
        _dismissDialog.postValue(null)
    }
}