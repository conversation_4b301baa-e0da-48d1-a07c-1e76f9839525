package com.duaag.android.last_login.domain.model

enum class AccountType {
    GOOGLE,
    FACEBOOK,
    EMAIL,
    PHONE,
    UNKNOWN;

    companion object {
        fun fromString(value: String?): AccountType {
            return when (value?.lowercase()) {
                "email" -> <PERSON><PERSON><PERSON>
                "phone" -> <PERSON><PERSON><PERSON><PERSON>
                "google" -> <PERSON><PERSON><PERSON><PERSON>
                "facebook" -> FACEBOOK
                else -> UNKNOWN
            }
        }
    }
}
