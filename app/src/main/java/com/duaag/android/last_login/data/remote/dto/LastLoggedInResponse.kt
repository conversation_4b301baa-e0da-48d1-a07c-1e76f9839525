package com.duaag.android.last_login.data.remote.dto

import androidx.annotation.Keep
import com.duaag.android.last_login.domain.model.AccountType
import com.duaag.android.last_login.domain.model.LastLoggedIn
import com.google.gson.annotations.SerializedName

@Keep
data class LastLoggedInResponse(
     @SerializedName("accountType")
     val accountType: String? = null,
     @SerializedName("isDeactivated")
     val isDeactivated: Boolean? = false
) {
    fun toDomain(): LastLoggedIn {
        return LastLoggedIn(
            accountType = AccountType.fromString(accountType),
            isDeactivated = isDeactivated ?: false
        )
    }
}