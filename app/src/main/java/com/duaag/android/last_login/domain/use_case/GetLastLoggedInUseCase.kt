package com.duaag.android.last_login.domain.use_case

import com.duaag.android.api.ResourceV2
import com.duaag.android.last_login.domain.model.LastLoggedIn
import com.duaag.android.user.UserRepository
import javax.inject.Inject


class GetLastLoggedInUseCase @Inject constructor(
    private val repository: UserRepository
) {
    suspend operator fun invoke(deviceId: String): ResourceV2<LastLoggedIn> {
        return repository.getLastLoggedIn(deviceId)
    }
}