package com.duaag.android.clevertap

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class CrossPathCreatedNotificationModel(
    @SerializedName("currentUser")
    var currentUser: CrossPathNotificationUserModel? = null,
    @SerializedName("counterPartUser")
    var counterPartUser: CrossPathNotificationUserModel? = null,
    @SerializedName("location")
    var location: LocationNotificationModel? = null,
    @SerializedName("timestamp")
    var timestamp: String? = null
) {
    @Keep
    data class CrossPathNotificationUserModel(
        @SerializedName("cognitoUserId")
        var cognitoUserId: String? = null,
        @SerializedName("pictureUrl")
        var pictureUrl: String? = null,
        @SerializedName("thumbnailUrl")
        var thumbnailUrl: String? = null,
        @SerializedName("bluredThumbnailUrl")
        var bluredThumbnailUrl: String? = null
    )

    @Keep
    data class LocationNotificationModel(
        @SerializedName("latitude")
        var latitude: Double? = null,
        @SerializedName("longitude")
        var longitude: Double? = null
    )
}

