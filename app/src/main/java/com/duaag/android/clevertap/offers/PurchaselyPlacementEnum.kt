package com.duaag.android.clevertap.offers

enum class PurchaselyPlacementEnum(val index: Int, val placementId: String) {
    PROFILE_GO_PREMIUM_BUTTON(1, "profile_go_premium_button"),
    SEE_WHO_LIKED_YOU(2, "see_who_liked_you"),
    OUT_OF_IMPRESSIONS(3, "out_of_impressions"),
    OUT_OF_INSTACHATS(4, "out_of_instachats"),
    OUT_OF_FLIGHTS(5, "out_of_flights"),
    OUT_OF_UNDOS(6, "out_of_undos"),
    SETTINGS_CALL_TO_ACTION_BUTTON(7, "settings_call_to_action_button"),
    ADS_REMOVAL(8, "ads_removal"),
    UPLOAD_PLUS_3_PHOTOS(9, "upload_plus_3_photos"),
    VOICE_CALL(10, "voice_call"),
    VIDEO_CALL(11, "video_call"),
    BLURRED_INSTA_CHAT(12, "blurred_insta_chat"),
    FINISHED_ONE_FREE_MIN_IN_CALL(13, "finished_one_free_min_in_call"),
    DONT_LET_GO(14, "dont_let_go"),
    ONBOARDING(15, "onboarding"),
    PREMIUM_SUBSCRIPTION_LOCAL_RADIUS(16, "premium_subscription_local_radius"),
    DYNAMIC_BOOST(17, "dynamic_boost"),
    DYNAMIC_INSTACHAT(18, "dynamic_instachat"),
    DYNAMIC_FLIGHT(19, "dynamic_flight"),
    DYNAMIC_UNDO(20, "dynamic_undo"),
    DYNAMIC_IMPRESSIONS(21, "dynamic_impressions"),
    SCROLLED_TO_BLURRED_FEATURED_PROFILE(22, "scrolled_to_blurred_featured_profile"),
    CLICK_TO_REVEAL_FEATURED_PROFILE(23, "click_to_reveal_featured_profile"),
    GHOST_MODE(24, "ghost_mode")
}

val indexToPlacementIdMap = PurchaselyPlacementEnum.values().associateBy({ it.index }, { it.placementId })

fun mapIndexesToPlacementIds(indexes: List<Int>): List<String> {
    return indexes.mapNotNull { indexToPlacementIdMap[it] }
}
