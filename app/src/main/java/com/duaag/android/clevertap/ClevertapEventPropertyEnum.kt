package com.duaag.android.clevertap

import androidx.annotation.Keep

@Keep
enum class ClevertapEventPropertyEnum(val propertyName: String) {
    PREMIUM_TYPE("premium_type"),
    PREVIOUS_PROFILE_PERCENTAGE("previous_profile_percentage"),
    ACTUAL_PROFILE_PERCENTAGE("actual_profile_percentage"),
    EVENT_SOURCE("event_source"),
    PRODUCT_TYPE("product_type"),
    ARE_DETAILS_LOCKED("are_details_locked"),
    DELETE_INSTACHAT_SOURCE("delete_instachat_source"),
    MATCH_TYPE("match_type"),
    UNMATCH_SOURCE("unmatch_source"),
    SIGN_UP_OR_SIGN_IN_MEDIUM("sign_up_or_sign_in_medium"),
    IS_SHADOW_BANNED("is_shadow_banned"),
    ARE_NOTIFICATIONS_ON("are_notifications_on"),
    GENDER_SWITCH("gender_switch"),
    REPORT_SOURCE("report_source"),
    REPORT_REASONS("report_reasons"),
    UNHIDE_SOURCE("unhide_source"),
    DELETE_ACCOUNT_REASONS("delete_account_reasons"),
    LOG_OUT_TYPE("log_out_type"),
    VERIFICATION_SOURCE("verification_source"),
    VERIFICATION_TYPE("verification_type"),
    PLAN_TYPE("plan_type"),
    PLAN_VALUE("plan_value"),
    PRODUCT_ID("product_id"),
    PRICE_VALUE("price_value"),
    PRICE_CURRENCY("price_currency"),
    IN_APP_PRODUCT("in_app_product"),
    MIN_AGE_FILTERED("min_age_filtered"),
    MAX_AGE_FILTERED("max_age_filtered"),
    LOCATION_FILTERED("location_filtered"),
    IS_RADIUS_EXTENDED("is_radius_extended"),
    IS_CARD_WITH_RESULTS("is_card_with_results"),
    COMMUNITY("community"),
    COMMUNITY_CHOOSEN_TYPE("community_choosen_type"),
    BILLING_AVAILABLE("billing_available"),
    INVITE_TYPE("invite_type"),
    VERIFICATION_STATUS("verification_status"),
    IS_IT_FORCED_VERIFICATION("is_it_forced_verification"),

    COMMUNITY_UPDATED_TO("community_updated_to"),

    BOOST_PERFOMANCE_STATUS("boost_perfomance_status"),
    BOOST_PERFOMANCE_VALUE("boost_perfomance_value"),
    CITY_CHOSEN("city_chosen"),
    CHANGE_LOCATION_TYPE("change_location_type"),

    SETUP_SOURCE("setup_source"),
    SIGN_IN_SOURCE("sign_in_source"),
    GENDER("gender"),
    DISTANCE("distance"),

    EDIT_PROFILE_SOURCE("edit_profile_source"),
    REWARD_TYPE("reward_type"),
    REWARD_SOURCE("reward_source"),

    ARE_PHOTOS_LOCKED("are_photos_locked"),
    UPLOAD_IMAGE_SOURCE("upload_image_source"),
    UPLOAD_IMAGE_STATUS("upload_image_status"),

    NETWORK_NAME("network_name"),
    AD_TYPE("ad_type"),
    SOURCE("source"),
    GHOST_STATUS("ghost_status"),
    GHOST_SOURCE("ghost_source"),
    PERMISSION_SOURCE_SCREEN("permission_source_screen"),
    ON("on"),
    OFF("off"),
    ZODIAC_STATUS("zodiac_status"),
    ZODIAC_SIGN("zodiac_sign"),
    UNBLUR_SOURCE("unblur_source"),
    INSTRUCTION_TYPE("instruction_type"),
    VIEW("view"),
    STATUS("status"),
    TYPE("type"),
    CONVERSATION_STARTED_TYPE("conversation_started_type"),
    PROMO_CODE_ERROR_TYPE("promo_code_error_type"),
    PROMO_CODE("promo_code"),
    LOCATION_ACCESS("location_access"),
    ACTIVITY_TAG("activity_tag"),
    CONVERSATION_ID("conversation_id"),
    PLACEMENT_ID("placement_id"),
    PROFILE_PROGRESS_BAR("profile_progress_bar"),
    PERMISSION_TO_ACCESS_CONTACTS("permission_to_access_contacts"),
    CONTACTS_COUNT("contacts_count"),
    IS_BADGE2_VERIFIED("is_badge2_verified"),
    IS_INCOGNITO_MODE("is_incognito_mode"),
    SCREEN_OUTPUT("screen_output"),
    IS_CROSS_PATH_WITH_RESULTS("is_cross_path_with_results"),
    SPOTTED_LOCATION("spotted_location"),
    HAS_INTEREACTED_BEFORE("has_intereacted_before"),
    INTERACTION_RECEIVED("interaction_received"),
    INTERACTED_BEFORE("interacted_before"),
    TIMES_SPOTTED("times_spotted"),
    RECOMMENDATION_SOURCE("recommendation_source"),
    ERROR_TYPE("error_type"),
    WAS_PREVIOUS_STEP_SKIPPABLE("was_previous_step_skippable"),
    IMAGE_DENIED_REASON("image_denied_reason"),
    COMMUNITY_SCREEN_OUTPUT("community_screen_output:"),
    EMAIL_INFO_ACCESS("email_info_access"),
    LOOKING_FOR_INFO("looking_for_info"),
    SMOKE_INFO("smoke_info"),
    RELIGION_INFO("religion_info"),
    LANGUAGES_INFO("languages_info"),
    CHILDREN_INFO("children_info"),
    DO_YOU_HAVE_CHILDREN_INFO("do_you_have_children_info"),
    DO_YOU_WANT_CHILDREN_INFO("do_you_want_children_info"),
    DO_YOU_SMOKE_INFO("do_you_smoke_info"),
    WHATS_YOUR_RELIGION_INFO("whats_your_religion_info"),
    VERIFICATION_FAILED_REASON("verification_failed_reason"),
    IS_PHOTO_HIDDEN("is_photo_hidden"),
    WAS_A_POTENTIAL_MATCH("was_a_potential_match"),
    ADVANCED_FILTERS_COUNT("advanced_filters_count"),
    INFO_TYPE("info_type"),
    RANGE_RADIUS_FILTERED("range_radius_filtered"),
    COMMUNITIES_FILTERED("communities_filtered"),
    COMMUNITIES_FILTERED_COUNT("communities_filtered_count"),
    COMMUNITIES_IF_RUN_OUT("communities_if_run_out"),
    MIN_HEIGHT_FILTERED("min_height_filtered"),
    MAX_HEIGHT_FILTERED("max_height_filtered"),
    HEIGHT_IF_RUN_OUT("height_if_run_out"),
    VERIFIED_USERS_FILTERED("verified_users_filtered"),
    LOOKING_FOR_FILTERED("looking_for_filtered"),
    LOOKING_FOR_IF_RUN_OUT("looking_for_if_run_out"),
    LANGUAGES_FILTERED("languages_filtered"),
    LANGUAGES_IF_RUN_OUT("languages_if_run_out"),
    RELIGION_FILTERED("religion_filtered"),
    RELIGION_IF_RUN_OUT("religion_if_run_out"),
    SOCIAL_MEDIA("social_media"),
    RECEIVED_USER_PREMIUM_BADGE("received_user_premium_badge"),
    BOOST_ACTIVATION_SOURCE("boost_activation_source"),
    COMPATIBILITY_SCORE("compatibility_score"),
    RESTORE_ERROR_TYPE("restore_error_type"),
    UNMATCH_REASONS("unmatch_reasons"),
    NUMBER_OF_LIKES("number_of_likes"),
    RETAIN_DATA("retain_data"),
    PERSIST_USER_PROGRESS_STEP("persist_user_progress_step"),
    DAYS_UNTIL_EXPIRE("days_until_expire"),
    ONBOARDING_TYPE("onboarding_type"),
}