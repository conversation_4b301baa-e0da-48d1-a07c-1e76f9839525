package com.duaag.android.clevertap

import androidx.annotation.Keep

@Keep
enum class ClevertapUserPropertyEnum(val value: String) {
    IDENTITY("Identity"),
    NAME("Name"),
    PREMIUM_TYPE("PremiumType"),
    EMAIL("Email"),
    PHONE("Phone"),
    <PERSON><PERSON><PERSON>("Gender"),
    USER_GENDER("UserGender"),
    COUNTRY("Country"),
    OPERATING_SYSTEM("OperatingSystem"),
    ISPREMIUM("IsPremium"),
    LOGGED_OUT("LoggedOut"),
    USER_SOURCE("UserSource"),
    DOB("DOB"),
    IS_PROFILE_HIDDEN("IsProfileHidden"),
    PROFILE_COMPLETION_PERCENTAGE("ProfileCompletionPercentage"),
    USER_ACCOUNT_CREATED_AT("UserAccountCreatedAt"),
    USER_PROFILE_CREATED_AT("UserProfileCreatedAt"),
    COMMUNITY("Community"),
    HAS_LOCATION_SERVICES_ENABLED("HasLocationServicesEnabled"),
    LOCATION_SERVICES_STATUS("LocationServicesStatus"),
    JOINED_SUNNY_HILL_GIVEAWAY("JoinedSunnyHillGiveaway"),
    CHECKED_IN_SUNNY_HILL("CheckedInSunnyHill"),

    MSG_PUSH("MSG-push"),
    MSG_EMAIL("MSG-email"),
    MSG_SMS("MSG-sms"),
    MSG_WHATSAPP("MSG-whatsapp"),
    APPLANGUAGE("AppLanguage"),
    RECEIVES_NEW_LIKE("ReceivesNewLike"),
    RECEIVES_NEW_MATCH("ReceivesNewMatch"),
    RECEIVES_NEW_MESSAGE("ReceivesNewMessage"),
    RECEIVES_NEW_INSTACHAT("ReceivesNewInstachat"),
    RECEIVES_NEW_OFFER("ReceivesNewOffer"),
    RECEIVES_RMOD("ReceivesRMOD"),
    RECEIVES_PROFILE_VISIT("ReceivesProfileVisit"),
    RECEIVES_BENEFIT_ALERTS("ReceivesBenefitAlerts"),
    RECEIVES_PROFILE_INFO("ReceivesProfileInfo"),
    RECEIVES_OTHER_NOTIFICATIONS("ReceivesOtherNotifications"),
    THEME("Theme"),
    INSTACHAT_COUNTER("InstachatCounter"),
    BOOST_COUNTER("BoostCounter"),

    IS_BILLING_AVAILABLE("IsBillingAvailable"),
    HAS_NOTIFICATIONS_ENABLED("HasNotificationsEnabled"),

    LIKES_COUNTER("LikesCounter"),
    DISLIKES_COUNTER("DislikesCounter"),
}
