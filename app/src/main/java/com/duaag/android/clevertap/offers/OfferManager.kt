package com.duaag.android.clevertap.offers

import com.duaag.android.sharedprefs.DuaSharedPrefs

fun getPlacementForOffer(
        offer: RealTimeClevertapOfferModel?,
        clickedPlacement: String
): String {

    if(offer == null || (offer.activeUntil != null && System.currentTimeMillis() > offer.activeUntil)) {
        return clickedPlacement
    }

    val placementIds = mapIndexesToPlacementIds(offer.showIn)

    return if (placementIds.contains(clickedPlacement)) {
        offer.placementId
    } else {
        clickedPlacement
    }
}

fun saveOfferInPreferences(
        offer: RealTimeClevertapOfferModel?,
        duaSharedPrefs: DuaSharedPrefs
) {
    when(offer?.type) {
        ClevertapOfferTypeEnum.PREMIUM.value -> {
            duaSharedPrefs.setRealTimeClevertapPremiumOffer(offer)
        }
        ClevertapOfferTypeEnum.BOOST.value -> {
            duaSharedPrefs.setRealTimeClevertapBoostOffer(offer)
        }
        ClevertapOfferTypeEnum.INSTACHATS.value -> {
            duaSharedPrefs.setRealTimeClevertapInstachatOffer(offer)
        }
        ClevertapOfferTypeEnum.IMPRESSIONS.value -> {
            duaSharedPrefs.setRealTimeClevertapImpressionsOffer(offer)
        }
        ClevertapOfferTypeEnum.FLIGHTS.value -> {
            duaSharedPrefs.setRealTimeClevertapFlightOffer(offer)
        }
        ClevertapOfferTypeEnum.UNDO.value -> {
            duaSharedPrefs.setRealTimeClevertapUndoOffer(offer)
        }
    }
}