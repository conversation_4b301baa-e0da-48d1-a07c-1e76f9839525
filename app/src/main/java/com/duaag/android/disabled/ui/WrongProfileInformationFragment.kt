package com.duaag.android.disabled.ui

import androidx.appcompat.app.AlertDialog
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.navigation.fragment.findNavController
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.WrongProfileInformationFragmentBinding
import com.duaag.android.disabled.DisabledActivity
import com.duaag.android.disabled.DisabledViewModel
import com.duaag.android.disabled.models.DisableUserReason
import com.duaag.android.disabled.models.DisableUserType
import com.duaag.android.disabled.viewmodels.WrongProfileInformationViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.setOnSingleClickListener
import javax.inject.Inject

class WrongProfileInformationFragment : Fragment() {

    companion object {
        fun newInstance() = WrongProfileInformationFragment()
    }

    private var _binding: WrongProfileInformationFragmentBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val disabledViewModel by viewModels<DisabledViewModel>({ activity as DisabledActivity }) { viewModelFactory }
    private val wrongProfileInformationViewModel by viewModels<WrongProfileInformationViewModel> { viewModelFactory }

    private var jobTitleListener: TextWatcher? = null
    private var educationListener: TextWatcher? = null
    private var descriptionListener: TextWatcher? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        (requireActivity() as DisabledActivity).disabledComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = WrongProfileInformationFragmentBinding.inflate(layoutInflater)

        disabledViewModel.userModel?.let {
            wrongProfileInformationViewModel.initializeViewModel(it)
            setTextFields(it)
        }

        binding.expandBtn.setOnClickListener {
            expandGuidelineMenu()
        }

        wrongProfileInformationViewModel.isDoneEnabled.observe(viewLifecycleOwner) { enable ->
            enableDoneButton(enable)
        }

        disabledViewModel.markedAsResolved.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
            this.hideKeyboard()
        }

        binding.doneBtn.setOnSingleClickListener {
            val wrongProfileInfoCategory = disabledViewModel.userModel?.disabledReasons
                    ?.filter { it.category == DisableUserType.WRONG_PROFILE_INFORMATION.type }

            val areMandatoryResolved = wrongProfileInfoCategory?.filter { it.isMandatory }?.all { it.isResolved } ?: false
            val notMandatoryNotResolved = wrongProfileInfoCategory?.filter { !it.isMandatory }?.all { !it.isResolved } ?: false

            val notMandatoryNotResolvedIds = wrongProfileInfoCategory?.filter { !it.isMandatory && !it.isResolved }?.map { it.id } ?: emptyList()
            val mandatoryNotResolvedIds = wrongProfileInfoCategory?.filter { it.isMandatory && !it.isResolved }?.map { it.id } ?: emptyList()

            val disabledReasonIds = mutableListOf<Int>()
            disabledReasonIds.addAll(mandatoryNotResolvedIds)
            disabledReasonIds.addAll(notMandatoryNotResolvedIds)

            if (areMandatoryResolved || disabledReasonIds.isEmpty()) {
                updateWrongInformation(notMandatoryNotResolved)
                firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_DISABLED_SAVE_CHANGES)
                this.hideKeyboard()
            } else {
                showConfirmDialog()
            }
        }

        jobTitleListener()
        educationListener()
        descriptionListener()
        sendScreenViewedAnalyticsEvent()

        return binding.root
    }

    private fun sendScreenViewedAnalyticsEvent() {
        val user = disabledViewModel.checkEnableUserObserver.value
        val eventPremiumType = getPremiumTypeEventProperty(user)

        sendClevertapEvent(
            ClevertapEventEnum.WRONG_PROFILE_INFO_REVIEW, mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
            ClevertapEventPropertyEnum.COMMUNITY.propertyName to user?.communityInfo?.id
        ))
    }

    private fun updateWrongInformation(shouldResolve: Boolean) {
        disabledViewModel.updateWrongProfileInformation(
                wrongProfileInformationViewModel.user!!, shouldResolve)
    }

    private fun enableDoneButton(status: Boolean) {
        binding.doneBtn.isEnabled = status
    }

    private fun jobTitleListener() {
        jobTitleListener = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                wrongProfileInformationViewModel.setJobs(s.toString())
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) { }
        }
        binding.jobTitleEditText.addTextChangedListener(jobTitleListener)
    }

    private fun educationListener() {
        educationListener = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                wrongProfileInformationViewModel.setEducations(s.toString())
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) { }
        }

        binding.educationEditText.addTextChangedListener(educationListener)
    }

    private fun descriptionListener() {
        descriptionListener = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                wrongProfileInformationViewModel.setDescription(s.toString())
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) { }
        }
        binding.descriptionEditText.addTextChangedListener(descriptionListener)
    }

    private fun expandGuidelineMenu() {
        val guidelineMenuVisible = binding.guidelineText.visibility == View.VISIBLE

        if (guidelineMenuVisible) {
            binding.guidelineText.visibility = View.GONE
            binding.expandBtn.animate().rotation(0f).setDuration(200).start()
        } else {
            binding.guidelineText.visibility = View.VISIBLE
            binding.expandBtn.animate().rotation(180f).setDuration(200).start()
            firebaseLogEvent(FirebaseAnalyticsEventsName.DISABLED_PROFILE_GUIDELINES)
        }
    }

    private fun showConfirmDialog() {
        val builder = AlertDialog.Builder(requireContext(),R.style.AlertDialogButtonTheme)

        builder.setTitle(getString(R.string.confirm_changes_title))
        builder.setMessage(R.string.confirm_changes_desc)
        builder.setPositiveButton(R.string.confirm_option) { _, _ ->
            updateWrongInformation(true)
            firebaseLogEvent(FirebaseAnalyticsEventsName.PROFILE_DISABLED_SAVE_CHANGES)

            val user = disabledViewModel.checkEnableUserObserver.value
            val eventPremiumType = getPremiumTypeEventProperty(user)

            sendClevertapEvent(ClevertapEventEnum.WRONG_PROFILE_INFO_CHANGED, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to user?.communityInfo?.id
            ))
        }
        builder.setNegativeButton(R.string.cancel, null)

        val dialog = builder.create()
        dialog.show()
    }

    private fun setTextFields(userModel: UserModel) {

        val wrongInfoType = userModel.disabledReasons?.filter { it.category == DisableUserType.WRONG_PROFILE_INFORMATION.type }
        val isReasonJob = wrongInfoType?.any { it.reason == DisableUserReason.JOB_TITLE.type } ?: false
        val isReasonEducation = wrongInfoType?.any { it.reason == DisableUserReason.EDUCATION.type } ?: false
        val isReasonDescription = wrongInfoType?.any { it.reason == DisableUserReason.DESCRIPTION.type } ?: false

        binding.jobTitleTextView.isVisible = isReasonJob
        binding.jobTitleEditText.isVisible = isReasonJob
        if (isReasonJob) {
            binding.jobTitleEditText.setText(wrongProfileInformationViewModel.getJob(userModel), TextView.BufferType.EDITABLE)
        }

        binding.educationTextView.isVisible = isReasonEducation
        binding.educationEditText.isVisible = isReasonEducation
        if (isReasonEducation) {
            binding.educationEditText.setText(wrongProfileInformationViewModel.getEducation(userModel), TextView.BufferType.EDITABLE)
        }

        binding.descriptionTextView.isVisible = isReasonDescription
        binding.descriptionEditText.isVisible = isReasonDescription
        if (isReasonDescription) {
            binding.descriptionEditText.setText(wrongProfileInformationViewModel.getDescription(userModel), TextView.BufferType.EDITABLE)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.jobTitleEditText.removeTextChangedListener(jobTitleListener)
        binding.educationEditText.removeTextChangedListener(educationListener)
        binding.descriptionEditText.removeTextChangedListener(descriptionListener)
        _binding = null
    }

}