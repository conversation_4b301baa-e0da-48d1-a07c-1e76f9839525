package com.duaag.android.disabled

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.findNavController
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.base.fragment.ImagePickerFragment
import com.duaag.android.databinding.DisabledActivityBinding
import com.duaag.android.disabled.di.DisabledComponent
import com.duaag.android.manage_pictures.ManagePicturesActivity
import com.duaag.android.manage_pictures.ManagePicturesActivity.Companion
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.signup.viewmodel.ChoosePictureViewModel
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.setOnSingleClickListener
import javax.inject.Inject
import com.uxcam.UXCam
import com.yalantis.ucrop.UCrop
import timber.log.Timber

class DisabledActivity : AppCompatActivity() {

    lateinit var disabledComponent: DisabledComponent

    private  var _binding: DisabledActivityBinding? = null
    private  val binding get() = _binding!!

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val disabledViewModel by viewModels<DisabledViewModel> { viewModelFactory }
    private val choosePicturesViewModel by viewModels<ChoosePictureViewModel> { viewModelFactory }


    override fun onCreate(savedInstanceState: Bundle?) {
        disabledComponent = (application as DuaApplication).appComponent.disabledComponent().create()
        disabledComponent.inject(this)
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
        super.onCreate(savedInstanceState)

        _binding = DisabledActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.backBtn.setOnSingleClickListener {
            findNavController(R.id.nav_disabled_profile).popBackStack()
            this.hideKeyboard()
        }

        findNavController(R.id.nav_disabled_profile).addOnDestinationChangedListener { controller, destination, arguments ->
            when (destination.id) {
                R.id.disabledProfileFragment -> {
                    binding.backBtn.visibility = View.GONE
                    binding.toolbarTitle.setText(R.string.disabled_profile)
                }
                R.id.inappropriatePicturesFragment -> {
                    binding.backBtn.visibility = View.VISIBLE
                    binding.toolbarTitle.setText(R.string.inappropriate_pictures_title)
                }
                R.id.wrongProfileInformationFragment -> {
                    binding.backBtn.visibility = View.VISIBLE
                    binding.toolbarTitle.setText(R.string.wrong_profile_information_title)
                }
                R.id.wrongDataDisabledFragment -> {
                    binding.backBtn.visibility = View.VISIBLE
                    binding.toolbarTitle.setText(R.string.wrong_data_title)
                }
            }
        }

        disabledViewModel.checkEnableUserObserver.observe(this){ userModel ->
            userModel?.let {
                if (!it.isDisabled) {
                    finish()
                }
            }
        }
    }

    fun setToolbarTitle(title: String) {
        binding.toolbarTitle.text = title
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        checkIfIsFromUICrop(requestCode, resultCode, data)
    }

    private fun checkIfIsFromUICrop(requestCode: Int, resultCode: Int, data: Intent?){
        if (resultCode == Activity.RESULT_OK) {
            when(requestCode){
                ImagePickerFragment.REQUEST_IMAGE_CAPTURE -> {
                    if (!NetworkChecker.isNetworkConnected(this)) {
                        ToastUtil.toast(getString(R.string.no_internet_string))
                        return
                    }
                    choosePicturesViewModel.imageChosen(data)
                }
                else -> {
                    if (data != null) {
                        val outputUri = UCrop.getOutput(data)

                        if (outputUri != null) {
                            Timber.tag(ManagePicturesActivity.TAG).d("outputUri: $outputUri")
                            if (!NetworkChecker.isNetworkConnected(this)) {
                                ToastUtil.toast(getString(R.string.no_internet_string))
                                return
                            }
                            choosePicturesViewModel.imageCropped(outputUri)
                        } else {
                            Timber.tag(ManagePicturesActivity.TAG).d("outputUri: null")
                        }
                    }
                }
            }
        } else if (resultCode == UCrop.RESULT_ERROR) {
            Timber.tag("TAG").e("handleCrop: cannot crop the image")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }
}