package com.duaag.android.appsflyer

enum class AppsflyerEventsNameEnum(val eventName:String) {
    IS_PROFILE_CREATED("is_profile_created"),
    IS_ACCOUNT_CREATION_INITIATED("is_account_creation_initiated"),
    IS_ACCOUNT_CREATED("is_account_created"),
    DID_USER_DELETE_PROFILE("did_user_delete_profile"),
    IS_PREMIUM_BOUGHT("is_premium_bought"),
    HAS_USER_INITIATED_PREMIUM("has_user_initiated_premium"),
    GENDER_("gender_"),
    HAS_USER_INITIATED_IN_APP("has_user_initiated_in_app"),
    IS_IN_APP_BOUGHT("is_in_app_bought"),
    LOG_IN("log_in"),
    FIRST_LOGIN_AFTER_INSTALL("first_log_in_after_install"),
    APP_OPEN("app_open"),
    FIRST_TIME_CONVERSATION_STARTED("first_time_conversation_started"),
    QUALITY_USER_APP_OPEN("quality_user_app_open"),
    GET_REWARD_POPUP("get_reward_popup"),
    REWARD_INCLUDED_VIEW("reward_included_view"),
    REWARDED_POPUP("rewarded_popup"),




}