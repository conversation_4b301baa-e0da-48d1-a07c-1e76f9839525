package com.duaag.android.appsflyer.domain

import com.duaag.android.api.Resource
import com.duaag.android.application.DuaApplication
import com.duaag.android.appsflyer.getAppsflyerId
import com.duaag.android.clevertap.linkCleverTapAppsFlyer
import com.duaag.android.di.ApplicationScope
import com.duaag.android.di.IoDispatcher
import com.duaag.android.events_tracker.models.AppsflyerBodyModel
import com.duaag.android.events_tracker.repositories.EventsTrackerRepository
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.utils.advertising_id.getAdvertisingId
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class AppsFlyerBackendManager @Inject constructor(
    private val eventsTrackerRepository: EventsTrackerRepository,
    private val duaSharedPrefs: DuaSharedPrefs,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    @ApplicationScope private val externalScope: CoroutineScope,
) {
    companion object {
        const val TAG = "SendDataToAppsFlyer"
    }

    fun sendData(shouldCheckLastValue: Boolean = false) {
        externalScope.launch(ioDispatcher) {
            getAdvertisingId(DuaApplication.instance.applicationContext)?.let { advertisingId ->

                if (shouldCheckLastValue && advertisingId == duaSharedPrefs.getLastSavedAdvertiseId()) return@launch

                val systemVersion = duaSharedPrefs.systemVersion
                val appVersion =
                    duaSharedPrefs.getAppVersion(DuaApplication.instance.applicationContext)
                val os = "android"
                val appsFlyerId: String = getAppsflyerId() ?: ""
                val appsflyerBodyModel =
                    AppsflyerBodyModel(os, appsFlyerId, systemVersion, appVersion, advertisingId)
                eventsTrackerRepository.sendDataToAppsFlyer(appsflyerBodyModel)
                    .catch { res ->
                        res.stackTrace
                    }.collect {
                        when (it) {
                            is Resource.Success -> {
                                duaSharedPrefs.setLastSavedAdvertiseId(advertisingId)
                                Timber.tag(TAG).d("sendDataToAppsFlyer: Success")
                            }
                            is Resource.Error -> Timber.tag(TAG).d("sendDataToAppsFlyer: Error")

                            Resource.Loading -> {}
                        }
                    }
            }
        }
    }

    fun deleteDataForTheAppsFlyer() {
        externalScope.launch(ioDispatcher) {
            duaSharedPrefs.setLastSavedAdvertiseId(null)
            val appsFlyerId:String = getAppsflyerId()?:""
            linkCleverTapAppsFlyer(deLink= true)
            eventsTrackerRepository.deleteDataForTheAppsFlyer(appsFlyerId).catch {res->
                res.stackTrace
            }.collect {
                when(it){
                    is Resource.Success -> Timber.tag(TAG).d("deleteDataForTheAppsFlyer: Success")
                    is Resource.Error -> Timber.tag(TAG).d("deleteDataForTheAppsFlyer: Error")
                    Resource.Loading -> {}
                }
            }
        }
    }

    fun checkAndSendDataForFaceBookFromDeepLink(){
        externalScope.launch(ioDispatcher) {
            val deepLink = duaSharedPrefs.getFaceBookDataFromDeepLink()
            if (deepLink != null) {
                val body = deepLink
                eventsTrackerRepository.sendDataForFaceBookFromDeepLink(body).catch { res ->
                    res.printStackTrace()
                }.collect {
                    when (it) {
                        is Resource.Success -> {
                            duaSharedPrefs.clearFaceBookDataFromDeepLink()
                            Timber.tag(TAG).d("sendDataForFaceBookFromDeepLink: Success")
                        }
                        is Resource.Error -> Timber.tag(TAG).d("sendDataForFaceBookFromDeepLink: Error")
                        Resource.Loading -> {}
                    }
                }
            }
        }
    }
}