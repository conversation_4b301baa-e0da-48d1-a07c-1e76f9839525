package com.duaag.android.appsflyer

import com.android.billingclient.api.Purchase
import com.appsflyer.AppsFlyerLib
import com.appsflyer.attribution.AppsFlyerRequestListener
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.premium_subscription.models.InAppPurchaseItemInfoModel
import timber.log.Timber
const val TAG = "ApssflyerExtensions"
//https://dev.appsflyer.com/hc/docs/uninstall-measurement-android#overriding-fcms-onmessagereceived
const val  AF_UINSTALL_TRACKING = "af-uinstall-tracking"
fun sendAppsflyerEvent(eventName: AppsflyerEventsNameEnum,properties: Map<String, Any?>? = mapOf()){
    AppsFlyerLib.getInstance().logEvent(DuaApplication.instance.applicationContext,eventName.eventName,properties,appsFlyerRequestListener)
}

fun sendAppsflyerEvent(eventName: String,properties: Map<String, Any?>? = mapOf()){
    AppsFlyerLib.getInstance().logEvent(DuaApplication.instance.applicationContext,eventName,properties,appsFlyerRequestListener)
}

fun validateAndLogInAppPurchase(
    purchase: Purchase,
    inAppPurchaseItemInfoModel: InAppPurchaseItemInfoModel?,
) {
    AppsFlyerLib.getInstance()
        .validateAndLogInAppPurchase(DuaApplication.instance.applicationContext,
            DuaApplication.instance.applicationContext.getString(R.string.ply_console_public_key),
            purchase.signature,
            purchase.originalJson,
            inAppPurchaseItemInfoModel?.price?: "",
            inAppPurchaseItemInfoModel?.currency?: "",
            mapOf());
}

fun getAppsflyerId(): String? {
    return AppsFlyerLib.getInstance().getAppsFlyerUID(DuaApplication.instance.applicationContext)
}

private val appsFlyerRequestListener: AppsFlyerRequestListener by lazy {
    object : AppsFlyerRequestListener {
        override fun onSuccess() {
            Timber.tag(TAG).d("Event sent successfully")
        }

        override fun onError(p0: Int, p1: String) {
            Timber.tag(TAG).d(" Event failed to be sent: Error code: $p0 Error description: $p1")
        }

    }
}