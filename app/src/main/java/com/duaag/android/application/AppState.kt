package com.duaag.android.application

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

object AppState {
    // TODO: Extend to contain more data for the app state in the future when we replace AWS
    private val _userSignedOut: MutableStateFlow<Boolean?> = MutableStateFlow(null)
    val userSignedOut: StateFlow<Boolean?> = _userSignedOut

    fun setUserSignedOut(signedOut: Boolean?) {
        _userSignedOut.value = signedOut
    }
}