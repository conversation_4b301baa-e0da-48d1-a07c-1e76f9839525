package com.duaag.android.sharedprefs

import com.duaag.android.R
import java.util.*

class DotNotificationSharedPrefs(private val duaSharedPrefs: DuaSharedPrefs) {
    fun setSeenTimeOnMatchesScreen(timestamp: Long) {
        duaSharedPrefs.setSpecialLongPreference(R.string.pref_dua_seen_time_on_match_screen, timestamp)
    }

    fun getSeenTimeOnMatchesScreen(): Long {
        return duaSharedPrefs.getSpecialLongPreference(R.string.pref_dua_seen_time_on_match_screen, 0)
    }

    fun setSeenTimeOnLikedYouScreen(timestamp: Long) {
        duaSharedPrefs.setSpecialLongPreference(R.string.pref_dua_seen_time_on_liked_you_screen, timestamp)
    }

    fun getSeenTimeOnLikedYouScreen(): Long {
        return duaSharedPrefs.getSpecialLongPreference(R.string.pref_dua_seen_time_on_liked_you_screen, 0)
    }

    fun setSeenTimeOnConversationScreen(timestamp: Long) {
        duaSharedPrefs.setSpecialLongPreference(R.string.pref_dua_seen_time_on_conversation_you_screen, timestamp)
    }

    fun setBothMatchAndConversationSeenTime(timestamp: Long){
        setSeenTimeOnMatchesScreen(timestamp)
        setSeenTimeOnConversationScreen(timestamp)
    }

    fun getSeenTimeOnConversationScreen(): Long {
        return duaSharedPrefs.getSpecialLongPreference(R.string.pref_dua_seen_time_on_conversation_you_screen, 0)
    }

    fun resetSeenTime(){
        setSeenTimeOnMatchesScreen(System.currentTimeMillis())
        setSeenTimeOnConversationScreen(System.currentTimeMillis())
        setSeenTimeOnLikedYouScreen(System.currentTimeMillis())
    }
}