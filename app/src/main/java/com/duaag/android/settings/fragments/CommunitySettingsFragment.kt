package com.duaag.android.settings.fragments


import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.base.models.CommunityInfo
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentCommunitySettingsBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.addDifferentTypeface
import com.duaag.android.utils.imageCircle
import com.duaag.android.utils.updateLocale
import javax.inject.Inject

class CommunitySettingsFragment : Fragment() {

    companion object {
        const val USER_COMMUNITY = "user_community"
    }

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel by viewModels<SettingsViewModel>({ activity as SettingsActivity } ) { viewModelFactory }

    private var _binding: FragmentCommunitySettingsBinding? = null
    private val binding get() = _binding!!

    private var initialCommunity : CommunityInfo? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initialCommunity = arguments?.getParcelable(USER_COMMUNITY)

        if(settingsViewModel.chosenCommunity.value == null && initialCommunity != null)
            settingsViewModel.setChosenCommunity(initialCommunity!!)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentCommunitySettingsBinding.inflate(inflater)


        binding.chosenCommunityName.setOnClickListener {
            showChooseCommunityBottomSheet()
        }


        settingsViewModel.chosenCommunity.observe(viewLifecycleOwner) { community ->
            toggleContinueButton(community != null && initialCommunity?.id != community.id)

            binding.chosenCommunityName.text = community?.communityName ?: ""

            if(community?.flag != null) {
                binding.chosenCommunityFlag.visibility = View.VISIBLE
                imageCircle(binding.chosenCommunityFlag, community.flag)
            } else
                binding.chosenCommunityFlag.visibility = View.GONE

        }

        settingsViewModel.showCommunityDialog.observe(viewLifecycleOwner) {
            showCommunityChangeDialog()
        }

        addDifferentTypeface(binding.attentionText, R.string.attention_community_change_an, R.color.title_primary)
        addDifferentTypeface(binding.communityDescription, R.string.change_community_note_an, R.color.title_primary)

        return binding.root
    }

    override fun onResume() {
        super.onResume()

        (requireActivity() as SettingsActivity).setToolbarTitle(getString(R.string.community))
    }

    private fun toggleContinueButton(enable: Boolean) {
        val visibility = if(enable) FloatingActionButtonVisibility.SHOWN else FloatingActionButtonVisibility.DISABLED
        settingsViewModel.setFabVisibility(visibility)
    }

    private fun showChooseCommunityBottomSheet() {
        val fragment = ChooseCommunitySettingsBottomSheet()
        if (childFragmentManager.findFragmentByTag("ChooseCommunitySettingsBottomSheet") == null) {
            fragment.show(childFragmentManager, "ChooseCommunitySettingsBottomSheet")
        }
    }

    private fun showCommunityChangeDialog() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(requireContext(), R.style.AlertDialogButtonTheme)
        sendChangeCommunityInitiateEvent()
        builder.apply {
            setTitle(R.string.warning)
                .setMessage(R.string.change_community_pop_up_desc)
                .setPositiveButton(R.string.continue_text) { dialog, _ ->
                    (requireActivity() as SettingsActivity).hasNameChanged = true
                    settingsViewModel.updateCommunity(settingsViewModel.chosenCommunity.value!!, settingsViewModel.userProfile.value!!)
                    dialog.cancel()
                    findNavController().popBackStack()
                }
                .setNegativeButton(R.string.cancel) { _, _ -> }
        }
        return builder.create().run {
            show()
        }
    }

    private fun sendChangeCommunityInitiateEvent() {
        val communityUpdateTo =
            "${settingsViewModel.userProfile.value?.communityInfo?.communityName} to ${settingsViewModel.chosenCommunity.value?.communityName}}"
        firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_COMMUNITY_INITIATED, mapOf(
            FirebaseAnalyticsParameterName.COMMUNITY_UPDATED_TO.value to communityUpdateTo))

        duaSharedPrefs.setUserCommunityNameBeforeChange(settingsViewModel.userProfile.value?.communityInfo?.communityName)

        sendClevertapEvent(ClevertapEventEnum.CHANGE_COMMUNITY_INITIATED, mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                settingsViewModel.userProfile.value),
            ClevertapEventPropertyEnum.COMMUNITY_UPDATED_TO.propertyName to communityUpdateTo))

    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}
