package com.duaag.android.settings.fragments


import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.databinding.FragmentInputCurrentPasswordBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.signup.models.ChangePasswordAuthResult
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.onKeyboardNext
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.google.android.material.floatingactionbutton.FloatingActionButton
import javax.inject.Inject

class InputCurrentPasswordFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) {viewModelFactory}

    private var _binding: FragmentInputCurrentPasswordBinding? = null
    private val binding get() = _binding!!

    private lateinit var textChangeListener: CurrentPasswordWatcher
    private var mIsChangeLogin : Boolean = false
    private var mIsPhone : Boolean = false
    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            InputCurrentPasswordFragmentArgs.fromBundle(it).apply {
                mIsChangeLogin = isChangeLogin
                mIsPhone = isPhone
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        _binding = FragmentInputCurrentPasswordBinding.inflate(inflater)
        binding.let { it ->
            it.lifecycleOwner = viewLifecycleOwner
            it.viewModel = settingViewModel
            it.isChangeLogin = mIsChangeLogin
//            it?.toolbar?.setNavigationOnClickListener {
//                hideKeyboard()
//                findNavController().popBackStack()
//            }
            textChangeListener=CurrentPasswordWatcher(binding)
            it.editTextPasswordCurrent.addTextChangedListener(textChangeListener)
        }


        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        whenPasswordChange()
        registerCallbacks()
    }

    private fun registerCallbacks() {
        settingViewModel.currentPasswordValidity.observe(viewLifecycleOwner, Observer {
            when (it) {
                is ChangePasswordAuthResult.Success -> {
                    binding.progressBar.visibility = View.GONE
                    if (mIsChangeLogin) {
                        val direction = if (mIsPhone) {
                      InputCurrentPasswordFragmentDirections.actionChangeLoginInputCurrentPasswordFragmentToChangeLoginVerifyPhoneFragment().apply {
                          isChangeLogin = true
                      }

                        } else {
                            InputCurrentPasswordFragmentDirections.actionChangeLoginInputCurrentPasswordFragmentToChangeLoginVerifyEmailFragment().apply {
                                isChangeLogin = true
                            }
                        }
                        findNavController().navigate(direction)

                    } else {
                        findNavController()
                            .navigate(R.id.action_changePasswordFragment_to_inputNewPasswordFragment)
                    }

                }
                is ChangePasswordAuthResult.Loading -> {
                    binding.progressBar.visibility = View.VISIBLE
                }
                is ChangePasswordAuthResult.Error -> {
                    binding.progressBar.visibility = View.GONE
                    when (it.e) {
                        is com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException -> showCurrPassErrDialog(
                            getString(R.string.too_many_times)
                        )
                        else -> showCurrPassErrDialog(getString(R.string.wrong_pass))
                    }
                }
            }
        })
    }

    private fun showCurrPassErrDialog(message: String) {
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)
        builder.apply {

            setMessage(message)
                    .setPositiveButton(resources.getString(R.string.ok_dialog)) { dialog, _ ->
                        dialog.dismiss()
                    }

        }
        return builder.create().run { show() }
    }


    /** only for testing*/
    private fun whenPasswordChange() {
        binding.let {
            it.editTextPasswordCurrent.onKeyboardNext {
                if (activity?.findViewById<FloatingActionButton>(R.id.fab_next)?.isEnabled!!) {
                    activity?.findViewById<FloatingActionButton>(R.id.fab_next)?.performClick()
                }
            }
        }
    }

    inner class CurrentPasswordWatcher(private val it: FragmentInputCurrentPasswordBinding) : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            it.editTextPasswordCurrent.text?.let { text ->
                if (text.length >= 6)
                    settingViewModel.setFabVisibility(FloatingActionButtonVisibility.SHOWN)
                else
                    settingViewModel.setFabVisibility(FloatingActionButtonVisibility.DISABLED)
            }
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }
    }


    override fun onDestroyView() {
        binding.editTextPasswordCurrent.removeTextChangedListener(textChangeListener)
        _binding = null
        super.onDestroyView()
    }

    override fun onResume() {
        super.onResume()
        binding.editTextPasswordCurrent.requestFocus()
        binding.editTextPasswordCurrent.showKeyboard()
        val toolbarText = if (mIsChangeLogin) getString(R.string.account_settings) else getString(R.string.change_password)
        (requireActivity() as SettingsActivity).setToolbarTitle(toolbarText)
    }

}
