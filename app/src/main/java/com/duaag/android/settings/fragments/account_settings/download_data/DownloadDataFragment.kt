package com.duaag.android.settings.fragments.account_settings.download_data

import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.DownloadDataFragmentBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.fragments.account_settings.download_data.model.DownloadDataStatus
import com.duaag.android.utils.formatDownloadDataTime
import com.duaag.android.utils.formatTime
import com.duaag.android.utils.setTextColorRes
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.SpinningCircleDialog
import javax.inject.Inject

const val THREE_DAYS = 300000

class DownloadDataFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) {viewModelFactory}



    private var _binding: DownloadDataFragmentBinding? = null
    private val binding get() = _binding
    private var spinningCircleDialog: SpinningCircleDialog? = null

    var myDownloadId: Long = 0


    override fun onAttach(context: Context) {
        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
        super.onAttach(context)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = DownloadDataFragmentBinding.inflate(inflater)
        binding?.lifecycleOwner = viewLifecycleOwner

        settingViewModel.downloadData()
        settingViewModel.setShowHideMainLoader(true)

        spinningCircleDialog = SpinningCircleDialog(requireContext())

        settingViewModel.downloadDataStatus.observe(viewLifecycleOwner,{ downloadDataStatus ->

            downloadDataStatus?.let {
                val title:Int
                val body:Int
                val buttonText:Int
                val createdExpiresInfoVisibility:Int

                when(it){
                    DownloadDataStatus.REQUESTING_DOWNLOAD_DATA -> {
                        binding?.progressBar?.visibility = View.GONE
                        title = R.string.request_data
                        body = R.string.tap_to_make_a_request_for_data
                        buttonText = R.string.request_data
                        createdExpiresInfoVisibility = View.GONE
                    }
                    DownloadDataStatus.PROCESSING_DOWNLOAD_DATA -> {
                        binding?.progressBar?.visibility = View.GONE
                        binding?.downloadDataButton?.setTextColorRes(R.color.others)
                        title = R.string.request_data
                        body = R.string.be_patient_data_is_processing
                        buttonText = R.string.processing
                        createdExpiresInfoVisibility = View.GONE

                    }
                    DownloadDataStatus.DOWNLOAD_DATA -> {
                        binding?.progressBar?.visibility = View.GONE
                        title = R.string.download_your_data
                        body = R.string.you_have_3_days_to_download_your_data
                        buttonText = R.string.download_data
                        createdExpiresInfoVisibility = View.VISIBLE

                        val createdTime: String? = ((settingViewModel.createdDate).toLong()).formatTime()
                        val expiredTime: String? = ((settingViewModel.expiredDate).toLong()).formatTime()
                        val createdDate: String = ((settingViewModel.createdDate).toLong()).formatDownloadDataTime()
                        val expiredDate: String = ((settingViewModel.expiredDate).toLong()).formatDownloadDataTime()
                        binding?.created?.text = getString(R.string.created,createdTime,createdDate)
                        binding?.expires?.text = getString(R.string.expires,expiredTime,expiredDate)
                    }
                }

                binding?.createdExpiriesInfo?.visibility = createdExpiresInfoVisibility
                binding?.downloadDataTitle?.setText(title)
                binding?.downloadDataDesc?.setText(body)
                binding?.downloadDataButton?.setText(buttonText)
            }
        })

        settingViewModel.downloadDirectData.observe(viewLifecycleOwner, {
            requestToDownloadData()
        })


        binding?.downloadDataButton?.setOnClickListener{

            when(settingViewModel.downloadDataStatus.value){
                DownloadDataStatus.REQUESTING_DOWNLOAD_DATA -> {
                    settingViewModel.postUserData()
                    settingViewModel.setDownloadDataStatus(DownloadDataStatus.PROCESSING_DOWNLOAD_DATA)
                    sendRequestDownloadDataEvent()
                }
                DownloadDataStatus.PROCESSING_DOWNLOAD_DATA -> {}
                DownloadDataStatus.DOWNLOAD_DATA -> {
                    if (System.currentTimeMillis() <= settingViewModel.timeStamp + THREE_DAYS) {
                        requestToDownloadData()
                    }else{
                        settingViewModel.downloadData(true)
                    }
                }
                null -> {
                }
            }
        }

        settingViewModel.showHideMainLoader.observe(viewLifecycleOwner, { show ->
            if (show)
            binding?.progressBar?.visibility = View.VISIBLE
            else
                binding?.progressBar?.visibility = View.GONE
        })


        settingViewModel.showHideDownloadLoader.observe(viewLifecycleOwner,{
            spinningCircleDialog?.show()
        })

        return binding?.root
    }

    private val downloadManagerBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            var id = intent?.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
            if (id == myDownloadId) {
                settingViewModel.setShowHideDownloadLoader(false)
                sendDownloadDataEvent()
                val currentFragment = findNavController().currentDestination?.id
                if(currentFragment == R.id.downloadDataFragment){
                    val intent1 = Intent(requireContext(), SettingsActivity::class.java)
                    startActivity(intent1)
                    (activity as SettingsActivity).finish()
                }
            }
        }
    }
    private fun requestToDownloadData(){
        settingViewModel.setShowHideDownloadLoader(true)
        firebaseLogEvent(FirebaseAnalyticsEventsName.DOWNLOAD_DATA)
        val request = DownloadManager.Request(
            Uri.parse(settingViewModel.getUrl))
            .setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or DownloadManager.Request.NETWORK_MOBILE)
            .setTitle(R.string.download_your_data.toString())
            .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            .setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS,"My_Dua_data.zip")
            .setAllowedOverMetered(true)

        val downloadManager = requireContext().getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        myDownloadId = downloadManager.enqueue(request)
    }

    private fun sendRequestDownloadDataEvent() {
        val premiumTypeValue = getPremiumTypeEventProperty(settingViewModel.userProfile.value)
        sendClevertapEvent(
            ClevertapEventEnum.DOWNLOAD_DATA_REQUESTED,
            mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue)
        )

    }

    private fun sendDownloadDataEvent() {
        val premiumTypeValue = getPremiumTypeEventProperty(settingViewModel.userProfile.value)
        sendClevertapEvent(
            ClevertapEventEnum.DOWNLOAD_DATA,
            mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue)
        )
    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(R.string.download_data))
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            requireContext().registerReceiver(downloadManagerBroadcastReceiver, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE), Context.RECEIVER_EXPORTED)
        } else {
            requireContext().registerReceiver(downloadManagerBroadcastReceiver, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE))
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        requireContext().unregisterReceiver(downloadManagerBroadcastReceiver)
        _binding = null
        spinningCircleDialog = null
    }


}
