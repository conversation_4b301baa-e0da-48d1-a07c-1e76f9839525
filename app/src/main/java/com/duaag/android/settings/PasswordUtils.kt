package com.duaag.android.settings

import com.duaag.android.settings.models.PasswordType
import com.duaag.android.utils.isAlphanumeric
import com.duaag.android.utils.isContentLowerCase
import com.duaag.android.utils.isContentNumber
import com.duaag.android.utils.isContentUpperCase

fun checkForStrengthPassword(string: String): PasswordType {
    return if (string.isEmpty() || string.length < 6 || !string.isContentNumber) {
        PasswordType.NOT_ENOUGH
    } else if (!string.isAlphanumeric && (string.isContentUpperCase && string.isContentLowerCase)) {
        PasswordType.STRONG
    } else if (!string.isAlphanumeric && !(string.isContentUpperCase && string.isContentLowerCase)) {
        PasswordType.SYMBOLS
    } else if (!string.isAlphanumeric || (string.isContentUpperCase && string.isContentLowerCase)) {
        PasswordType.UPPER_CASE_AND_LOWER_CASE
    } else {
        PasswordType.POOR
    }
}