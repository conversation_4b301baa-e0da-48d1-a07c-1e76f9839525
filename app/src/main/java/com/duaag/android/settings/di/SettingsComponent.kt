package com.duaag.android.settings.di

import com.duaag.android.di.ActivityScope
import com.duaag.android.login.fragments.LoginInfoBottomSheetFragment
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.fragments.ChooseCommunitySettingsBottomSheet
import com.duaag.android.settings.fragments.CommunitySettingsFragment
import com.duaag.android.settings.fragments.InputCurrentPasswordFragment
import com.duaag.android.settings.fragments.InputNewPasswordFragment
import com.duaag.android.settings.fragments.SettingsFragment
import com.duaag.android.settings.fragments.SocialMediaFragment
import com.duaag.android.settings.fragments.VerifyNewPasswordFragment
import com.duaag.android.settings.fragments.account_settings.AccountSettingsFragment
import com.duaag.android.settings.fragments.account_settings.adjust_profile_info.ChangeBirthdayFragment
import com.duaag.android.settings.fragments.account_settings.adjust_profile_info.ChangeGenderFragment
import com.duaag.android.settings.fragments.account_settings.adjust_profile_info.ChangeNameFragment
import com.duaag.android.settings.fragments.account_settings.delete_account.ConfirmDeletionFragment
import com.duaag.android.settings.fragments.account_settings.delete_account.DeleteAccountPermanentlyFragment
import com.duaag.android.settings.fragments.account_settings.delete_account.DeleteAccountReasonsFragment
import com.duaag.android.settings.fragments.account_settings.delete_account.ShowFriendsFragment
import com.duaag.android.settings.fragments.account_settings.delete_account.TypeDeleteFragment
import com.duaag.android.settings.fragments.account_settings.delete_account.love_story.LoveStoryBottomSheetFragment
import com.duaag.android.settings.fragments.account_settings.download_data.DownloadDataFragment
import com.duaag.android.settings.fragments.appearance.AppearanceSettingsFragment
import com.duaag.android.settings.fragments.block_contacts.presentation.AddContactBottomSheet
import com.duaag.android.settings.fragments.block_contacts.presentation.BlockAllContactsBottomSheet
import com.duaag.android.settings.fragments.block_contacts.presentation.BlockContactsFragment
import com.duaag.android.settings.fragments.help_center.HelpCenterFragment
import com.duaag.android.settings.fragments.language.LanguageFragment
import com.duaag.android.settings.fragments.notifications.NotificationSettingsFragment
import com.duaag.android.settings.fragments.notifications.pushnotification.PushNotificationFragment
import com.duaag.android.settings.fragments.premium.PremiumSettingsFragment
import com.duaag.android.settings.fragments.premium.RedeemCodeBottomSheet
import com.duaag.android.settings.fragments.verify_account.AccountVerificationFragment
import com.duaag.android.settings.fragments.verify_account.VerifyAccountCodeFragment
import com.duaag.android.settings.fragments.verify_account.VerifyEmailFragment
import com.duaag.android.settings.fragments.verify_account.VerifyPhoneFragment
import dagger.Subcomponent


// Definition of a Dagger subcomponent
@ActivityScope
@Subcomponent(modules = [SettingsViewModelModule::class])
interface SettingsComponent {

    // Factory to create instances of RegistrationComponent
    @Subcomponent.Factory
    interface Factory {
        fun create(): SettingsComponent
    }

    // Classes that can be injected by this Component
    fun inject(activity: SettingsActivity)

    fun inject(fragment: InputNewPasswordFragment)
    fun inject(fragment: VerifyNewPasswordFragment)
    fun inject(fragment: SettingsFragment)
    fun inject(fragment: InputCurrentPasswordFragment)
    fun inject(fragment: LanguageFragment)
    fun inject(fragment: NotificationSettingsFragment)
    fun inject(fragment: AccountSettingsFragment)
    fun inject(fragment: PushNotificationFragment)
    fun inject(fragment: AppearanceSettingsFragment)
    fun inject(fragment: DeleteAccountReasonsFragment)
    fun inject(fragment: AccountVerificationFragment)
    fun inject(fragment: VerifyPhoneFragment)
    fun inject(fragment: VerifyEmailFragment)
    fun inject(fragment: VerifyAccountCodeFragment)
    fun inject(deleteAccountPermanently: DeleteAccountPermanentlyFragment)
    fun inject(typeDeleteFragment: TypeDeleteFragment)
    fun inject(fragment: SocialMediaFragment)
    fun inject(bottomSheetFragment: LoginInfoBottomSheetFragment)
    fun inject(showFriendsFragment: ShowFriendsFragment)
    fun inject(premiumSettingsFragment: PremiumSettingsFragment)
    fun inject(downloadDataFragment: DownloadDataFragment)
    fun inject(changeNameFragment: ChangeNameFragment)
    fun inject(changeBirthdayFragment: ChangeBirthdayFragment)
    fun inject(changeGenderFragment: ChangeGenderFragment)
    fun inject(communityFragment: CommunitySettingsFragment)
    fun inject(chooseCommunityBottomSheet: ChooseCommunitySettingsBottomSheet)
    fun inject(helpCenterFragment: HelpCenterFragment)
    fun inject(loveStoryBottomSheetFragment: LoveStoryBottomSheetFragment)
    fun inject(redeemCodeBottomSheet: RedeemCodeBottomSheet)
    fun inject(blockContactsFragment: BlockContactsFragment)
    fun inject(addContactBottomSheet: AddContactBottomSheet)
    fun inject(confirmDeletionFragment: ConfirmDeletionFragment)
}