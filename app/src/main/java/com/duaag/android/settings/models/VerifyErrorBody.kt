package com.duaag.android.settings.models

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class VerifyErrorBody(
        @field:SerializedName("type")
        val type: String? = null,

        @field:SerializedName("message")
        val message: String? = null
)

enum class VerifyErrorType(val value: String) {
    USER_NOT_FOUND("user_not_found"),
    LIMIT_REACHED("phone_verification_limit_reached"),
    SMS_LIMIT_REACHED("sms_verification_limit_reached"),
    EMAIL_LIMIT_REACHED("email_verification_limit_reached"),
    PHONE_ALREADY_IN_USE("phone_is_already_in_use"),
    EMAIL_ALREADY_IN_USE("email_is_already_in_use"),
    SMS_CODE_MISMATCH("sms_verification_code_not_matching"),
    EMAIL_CODE_MISMATCH("email_verification_code_not_matching"),
    PASSWORD_REQUIRED("password_required"),
    USER_IS_CREDENTIAL_USER("user_is_credential_user")
}
