package com.duaag.android.settings.fragments.language

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.RecyclerviewLanguageItemBinding
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.fragments.language.LanguageConstants.LanguageItem
import com.duaag.android.settings.fragments.language.LanguageFragment.OnListFragmentInteractionListener

class LanguageRecyclerViewAdapter(
        private val mListener: OnListFragmentInteractionListener?
) : RecyclerView.Adapter<LanguageRecyclerViewAdapter.ViewHolder>() {

    private val mOnClickListener: View.OnClickListener
    private var lastCheckedRB: RadioButton? = null

    var mValues: List<LanguageItem> = emptyList()
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    init {

        mOnClickListener = View.OnClickListener { v ->
            val item = v.tag as LanguageItem
            // Notify the active callbacks interface (the activity, if the fragment is attached to
            // one) that an item has been selected
            item.isChecked = if (item.isChecked?.equals(true)!!) return@OnClickListener else true

            mListener?.onListFragmentInteraction(item)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
        val binding = RecyclerviewLanguageItemBinding.inflate(view, parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = mValues[position]
        holder.bind(item)

        //Implementing single-check
        holder.binding.radioButton.setOnCheckedChangeListener { _, _ ->
            val checkedRb = holder.itemView.findViewById(R.id.radioButton) as RadioButton
            lastCheckedRB?.isChecked = false
            //store the clicked radiobutton
            lastCheckedRB = checkedRb
        }
    }

    override fun getItemCount(): Int = mValues.size

    inner class ViewHolder(var binding: RecyclerviewLanguageItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: LanguageItem) {
            binding.item = item
            with(binding.radioButton) {
                tag = item
                setOnClickListener(mOnClickListener)
            }
        }
    }
}
