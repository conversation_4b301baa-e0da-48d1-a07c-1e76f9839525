package com.duaag.android.settings.fragments.account_settings.delete_account

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.clevertap.*
import com.duaag.android.databinding.DeleteReasonsFragmentBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.fragments.account_settings.delete_account.ConfirmDeletionFragment.Companion.DELETION_REASON_MODEL
import com.duaag.android.settings.fragments.account_settings.delete_account.adapters.DeleteReasonsAdapter
import com.duaag.android.settings.fragments.account_settings.delete_account.adapters.DeleteReasonsAdapter.ReasonsClickListener
import com.duaag.android.settings.fragments.account_settings.delete_account.love_story.LoveStoryBottomSheetFragment
import com.duaag.android.settings.fragments.account_settings.delete_account.model.DeleteReasonsData
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.SpinningCircleDialog
import javax.inject.Inject


class DeleteAccountReasonsFragment : Fragment(),LoveStoryBottomSheetFragment.LoveStorySheetListener {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<DeleteAccountReasonsViewModel>({ activity as SettingsActivity }) { viewModelFactory }

    @Inject
    lateinit var duaAccount: DuaAccount

    private var _binding: DeleteReasonsFragmentBinding? = null
    private val binding get() = _binding!!

    private var adapter: DeleteReasonsAdapter? = null

    private var spinningCircleDialog: SpinningCircleDialog? = null
    private var model:DeleteReasonsData.DeleteReasonsModel?=null

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        _binding = DeleteReasonsFragmentBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner

        spinningCircleDialog = context?.let { it1 -> SpinningCircleDialog(it1) }

        adapter = DeleteReasonsAdapter(ReasonsClickListener {
            viewModel.checkSelected(it)
            model=it

            if(model?.clevertapDeleteReasonsValues == ClevertapDeleteReasonsValues.FOUND_SOMEONE_ON_DUA){
                LoveStoryBottomSheetFragment.newInstance().show(childFragmentManager,"LoveStory")
            } else {
                findNavController().navigate(
                    R.id.action_deleteAccountReasons_to_confirmDeletionFragment,
                    bundleOf(DELETION_REASON_MODEL to model)
                )
            }
            firebaseLogEvent(FirebaseAnalyticsEventsName.DELETING_REASON, mapOf(
                FirebaseAnalyticsParameterName.CONTINUE_ACC_DELETION.value to 1L))

        })

        binding.textTitle.text = (getString(R.string.reasons_delete_an, getString(R.string.app_name)))

        val recyclerView = binding.recyclerDeleteReasons
        recyclerView.setHasFixedSize(true)
        recyclerView.adapter = adapter


        viewModel.deleteReasons.observe(viewLifecycleOwner, Observer{
            adapter?.submitList(it)
        })

        viewModel.isReasonSelected.observe(viewLifecycleOwner, Observer{
             binding.isSelected=it
        })
        sendScreenEvent()
        return binding.root
    }

    private fun sendScreenEvent() {
        val eventPremiumType =
            getPremiumTypeEventProperty(viewModel.userRepository.user.value)

        sendClevertapEvent(
            ClevertapEventEnum.DELETING_REASON_SCREENVIEW, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType
            )
        )
    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(R.string.deleting_reasion))

    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        adapter = null
        spinningCircleDialog = null
    }

    override fun onContinueClicked() {
        findNavController().navigate(
            R.id.action_deleteAccountReasons_to_confirmDeletionFragment,
            bundleOf(DELETION_REASON_MODEL to model)
        )
    }
}




