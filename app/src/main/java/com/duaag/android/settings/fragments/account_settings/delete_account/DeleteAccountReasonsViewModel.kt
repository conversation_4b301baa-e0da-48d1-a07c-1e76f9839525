package com.duaag.android.settings.fragments.account_settings.delete_account

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.duaag.android.api.Result
import com.duaag.android.base.models.UserModel
import com.duaag.android.settings.fragments.account_settings.delete_account.model.DeleteReasonsData.DeleteReasonsModel
import com.duaag.android.settings.fragments.account_settings.delete_account.model.DeleteReasonsData.getDeleteReasons
import com.duaag.android.user.UserRepository
import okhttp3.ResponseBody
import java.util.*
import javax.inject.Inject

class DeleteAccountReasonsViewModel @Inject constructor(val context: Context, val userRepository: UserRepository) : ViewModel() {
    private var _deleteReasons: MutableLiveData<List<DeleteReasonsModel>> = MutableLiveData()
    val deleteReasons: LiveData<List<DeleteReasonsModel>>
        get() = _deleteReasons

    private var _isReasonSelected: MutableLiveData<Boolean> = MutableLiveData()
    val isReasonSelected: LiveData<Boolean>
        get() = _isReasonSelected

    private val _user = userRepository.user
    fun userObserver(): LiveData<UserModel> = _user

    fun deactivatedUser(): LiveData<Result<ResponseBody>> {
        return userRepository.deactivatedUser()
    }

    private fun setDeleteReasons(deleteReasons: List<DeleteReasonsModel>) {
        _deleteReasons.value = deleteReasons
    }

    private fun setItems(items: ArrayList<DeleteReasonsModel>) {
        _deleteReasons.value = items
    }

    private fun onCheckSelected(state: Boolean = false) {
        _isReasonSelected.value = state

    }

    internal fun checkSelected(it: DeleteReasonsModel) {
        val items = arrayListOf<DeleteReasonsModel>()
        _deleteReasons.value?.let { it1 -> items.addAll(it1) }
        if (!items.isNullOrEmpty()) {
            for ((index, value) in items.withIndex())
                items[index] = value.copy(isSelected = value.id == it.id)
            setItems(items)
            onCheckSelected(true)
        }
    }

    init {
        setDeleteReasons(getDeleteReasons(context))
        onCheckSelected()
    }

}