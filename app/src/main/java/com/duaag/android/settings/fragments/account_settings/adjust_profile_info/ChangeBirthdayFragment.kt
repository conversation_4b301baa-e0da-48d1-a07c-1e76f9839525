package com.duaag.android.settings.fragments.account_settings.adjust_profile_info

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.DatePicker
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.databinding.FragmentChangeBirthdayBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.addDifferentTypeface
import com.duaag.android.utils.formatCalendarToAPIString
import com.duaag.android.utils.formatToUTC
import com.duaag.android.utils.getStringPlaceHolder
import com.duaag.android.utils.getUserAge
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.uxcam.UXCam
import java.util.Calendar
import javax.inject.Inject


class ChangeBirthdayFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) { viewModelFactory }

    private var _binding: FragmentChangeBirthdayBinding? = null
    private val binding get() = _binding!!
    var isDateChanged = false


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }



    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentChangeBirthdayBinding.inflate(inflater)

        onBirthdaySelected()
        addDifferentTypeface(binding.attentionTxt,R.string.change_age_restriction,R.color.title_primary)

        initDatePicker(binding.changeBirthdateDatePicker)
        UXCam.occludeSensitiveView(binding.birthdayText)
        binding.btnSave.setOnSingleClickListener{
            if(NetworkChecker.isNetworkConnected(requireContext()))
                settingsViewModel.showBirthdayDialog()
            else
                ToastUtil.toast(R.string.no_internet_connection)
        }
        settingsViewModel.showBirthdayDialog.observe(viewLifecycleOwner) {
            showBirthdayChangeDialog()
        }

        return binding.root
    }

    companion object {
        @JvmStatic
        fun newInstance() = ChangeBirthdayFragment()
    }

    private fun onBirthdaySelected() {
        settingsViewModel.changeBirthdayCalendar.observe(viewLifecycleOwner) {
            val currentBirthdate = settingsViewModel.userProfile.value?.birthDate
            val calendarFormatter = formatCalendarToAPIString(it)

            binding.btnSave.isEnabled = currentBirthdate != calendarFormatter

            binding.birthdayText.text = getStringPlaceHolder(
                "${formatToUTC(it)} - ",
                getString(R.string.x_years_old, getUserAge(it).toString()),
                "",
                R.color.title_primary,
                requireContext(),
                R.font.tt_norms_pro_normal
            )
        }
    }


    private fun initDatePicker(datePicker: DatePicker) {
        val dates = settingsViewModel.userProfile.value!!.birthDate.split("-")
        val year = dates[2].toInt()
        val month = dates[1].toInt()
        val day = dates[0].toInt()

        val cal = Calendar.getInstance()
        cal.set(Calendar.YEAR, year)
        cal.set(Calendar.MONTH, month - 1)
        cal.set(Calendar.DAY_OF_MONTH,day)

        val maxCalendar = Calendar.getInstance()
        val minCalendar = Calendar.getInstance()


        maxCalendar.add(Calendar.YEAR, -18)
        minCalendar.add(Calendar.YEAR, -100)
        minCalendar.add(Calendar.MONTH, +1)
        datePicker.maxDate = maxCalendar.timeInMillis
        datePicker.minDate = minCalendar.timeInMillis
        settingsViewModel.updateChangedBirthdayCalendar(cal)


        datePicker.init(year, month - 1, day) { _, mYear, monthOfYear, dayOfMonth ->
            val calendar = Calendar.getInstance()
            calendar.set(mYear, monthOfYear, dayOfMonth)
            settingsViewModel.updateChangedBirthdayCalendar(calendar)

        }
    }

    private fun showBirthdayChangeDialog() {
        val builder = AlertDialog.Builder(requireContext(), R.style.AlertDialogButtonTheme)
        builder.apply {
            setTitle(R.string.warning)
                .setMessage(R.string.change_age_warning)
                .setPositiveButton(R.string.continue_text) { dialog, _ ->
                    (requireActivity() as SettingsActivity).hasAgeChanged = true
                    settingsViewModel.updateBirthday(
                        formatCalendarToAPIString(settingsViewModel.changeBirthdayCalendar.value!!),
                        getUserAge(settingsViewModel.changeBirthdayCalendar.value!!),
                        settingsViewModel.userProfile.value!!
                    )
                    dialog.cancel()
                    findNavController().popBackStack()
                }
                .setNegativeButton(R.string.cancel) { dialog, _ ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            show()
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}



