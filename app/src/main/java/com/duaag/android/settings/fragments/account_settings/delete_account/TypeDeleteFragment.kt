package com.duaag.android.settings.fragments.account_settings.delete_account

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.databinding.TypeDeleteFragmentBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import javax.inject.Inject

class TypeDeleteFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) { viewModelFactory }

    private var _binding: TypeDeleteFragmentBinding? = null
    private val binding get() = _binding!!


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }


    companion object {
        fun newInstance() = TypeDeleteFragment()
    }

    override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?,
    ): View? {

        _binding = TypeDeleteFragmentBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = settingsViewModel

        binding.editTxtTypeDelete.addTextChangedListener(object : TextWatcher {

            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}

            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}

            override fun afterTextChanged(editable: Editable) {
                if (binding.editTxtTypeDelete.text?.isNotEmpty() == true) {
                    settingsViewModel.deleteInput = binding.editTxtTypeDelete.text.toString()
                    settingsViewModel.setFabVisibility(FloatingActionButtonVisibility.SHOWN)
                }
            }
        })
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        binding.editTxtTypeDelete.requestFocus()
        binding.editTxtTypeDelete.showKeyboard()

    }

    override fun onDestroy() {
        super.onDestroy()
        settingsViewModel.deleteInput = null
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}