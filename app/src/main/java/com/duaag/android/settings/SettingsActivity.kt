package com.duaag.android.settings

import android.app.Activity
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavController
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.NavigationUI
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.application.AppState
import com.duaag.android.application.DuaApplication
import com.duaag.android.auth_interfaces.HasFloatingActionButton
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapVerificationTypeValues
import com.duaag.android.clevertap.sendEmailOrPhoneVerificationButtonClickEvent
import com.duaag.android.clevertap.setAppThemeInClevertap
import com.duaag.android.databinding.ActivitySettingsNewBinding
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.firebase.NotificationType
import com.duaag.android.firebase.model.NotificationModel
import com.duaag.android.home.HomeActivity
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.login.StartActivity
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.PurchaselyManager
import com.duaag.android.settings.di.SettingsComponent
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.settings.fragments.language.LanguageConstants
import com.duaag.android.settings.fragments.language.LanguageFragment
import com.duaag.android.settings.fragments.language.locale.ModifiedLingver
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.disableButton
import com.duaag.android.utils.enableButton
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.openHomeActivity
import com.duaag.android.views.EnvelopeDialog
import com.duaag.android.views.SpinningCircleDialog
import com.google.android.material.appbar.MaterialToolbar
import com.google.firebase.Firebase
import com.google.firebase.dynamiclinks.androidParameters
import com.google.firebase.dynamiclinks.dynamicLinks
import com.google.firebase.dynamiclinks.iosParameters
import com.google.firebase.dynamiclinks.shortLinkAsync
import com.google.firebase.dynamiclinks.socialMetaTagParameters
import kotlinx.coroutines.launch
import javax.inject.Inject


class SettingsActivity : AppCompatActivity(), LanguageFragment.OnListFragmentInteractionListener {

    companion object {
        const val SETTINGS_ACTIVITY_START_DESTINATION_INTENT = "SETTINGS_ACTIVITY_START_DESTINATION_INTENT"

        const val HAS_NAME_CHANGED = "hasNameChanged"
        const val HAS_AGE_CHANGED = "hasAgeChanged"
        const val HAS_GENDER_CHANGED = "hasGenderChanged"
        const val HAS_RESTORED_PREMIUM = "hasRestoredPremium"
        const val HAS_UPGRADED_TO_PREMIUM = "hasUpgradedToPremium"
    }


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel: SettingsViewModel by viewModels { viewModelFactory }
    private val homeViewModel by viewModels<HomeViewModel> { viewModelFactory }

    lateinit var settingsComponent: SettingsComponent

    private var _binding: ActivitySettingsNewBinding? = null
    private val binding get() = _binding!!

    private var spinningCircleDialog: SpinningCircleDialog? = null

    @Inject
    lateinit var appState: AppState

    private lateinit var navController: NavController
    private lateinit var appBarConfiguration: AppBarConfiguration
    var hasNameChanged = false
    var hasAgeChanged = false
    var hasGenderChanged = false

    @Inject
    lateinit var duaAccount: DuaAccount

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    override fun onCreate(savedInstanceState: Bundle?) {
        settingsComponent = (application as DuaApplication).appComponent.settingsComponent().create()
        settingsComponent.inject(this)

        super.onCreate(savedInstanceState)
        _binding = ActivitySettingsNewBinding.inflate(layoutInflater)
        setContentView(binding.root)

        intent?.getStringExtra(SETTINGS_ACTIVITY_START_DESTINATION_INTENT)?.let {
            settingsViewModel.startDestination = when (it) {
                "Phone_Verification" -> DefaultDestination.PHONE_VERIFICATION
                "Email_Verification" -> DefaultDestination.EMAIL_VERIFICATION
                "Download_Data_Notification" -> DefaultDestination.DOWNLOAD_DATA_NOTIFICATION
                "Notifications" -> DefaultDestination.NOTIFICATIONS
                "Community"-> DefaultDestination.COMMUNITY_CHANGE
                "Appearance" -> DefaultDestination.APPEARANCE
                "Name_Change" -> DefaultDestination.NAME_CHANGE
                "Gender_Change"-> DefaultDestination.GENDER_CHANGE
                "Birthday_Change" -> DefaultDestination.BIRTHDAY_CHANGE
                "Account_Verification" -> DefaultDestination.ACCOUNT_VERIFICATION
                "Account_Settings" -> DefaultDestination.ACCOUNT_SETTINGS



                else -> DefaultDestination.SETTINGS_FRAGMENT
            }
        }

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED){
                appState.userSignedOut.collect{
                    if(it == true){
                        val intent = Intent(this@SettingsActivity, StartActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                        <EMAIL>(intent)
                        appState.setUserSignedOut(null)
                    }
                }
            }
        }


        if (intent.hasExtra("is_from_verify_instachat")) {
            val isFromVerifyInstachat = intent.getBooleanExtra("is_from_verify_instachat", false)
            if (isFromVerifyInstachat) settingsViewModel.navigatedFromVerifyInstachat()

        }

        setSupportActionBar(binding.toolbar)

        setUpNav(savedInstanceState)
        binding.let {
            it.fabNext.hide()
            it.toolbar.setNavigationOnClickListener {
                if (navController.currentDestination?.id == R.id.settingsFragment) {
                    hideKeyboard()
                    finish()
                } else {
                    hideKeyboard()
                    onBackPressed()
                }
            }
        }

        settingsViewModel.userProfile.observe(this, {
            if (it != null) {
                settingsViewModel. getUserCounters(it)
                settingsViewModel.hasBadge1 = it.hasBadge1
                settingsViewModel.setBadge2Status(when (it.badge2) {
                    Badge2Status.APPROVED.status -> Badge2Status.APPROVED
                    Badge2Status.PROCESSING.status -> Badge2Status.PROCESSING
                    Badge2Status.NOT_APPROVED.status -> Badge2Status.NOT_APPROVED
                    Badge2Status.NULL.status -> Badge2Status.NULL
                    else -> Badge2Status.NULL
                })
            }
        })

        settingsViewModel.accountModel.observe(this, { accountModel ->
            if (accountModel != null) {
                if (accountModel.phone == null && accountModel.email == null) {
                    settingsViewModel.authMethod = AuthMethod.PHONE
                } else if (accountModel.phone == null && accountModel.email != null) {
                    settingsViewModel.authMethod = AuthMethod.PHONE
                } else if (accountModel.email == null && accountModel.phone != null) {
                    settingsViewModel.authMethod = AuthMethod.EMAIL
                }
            }
        })

        settingsViewModel.fabVisibility.observe(this, Observer { floatingActionbottonVisibility ->
            floatingActionbottonVisibility?.let {
                when (it) {
                    FloatingActionButtonVisibility.SHOWN -> {
                        binding.fabNext.enableButton()
                    }
                    FloatingActionButtonVisibility.HIDDEN -> {
                        binding.fabNext.hide()
                    }
                    FloatingActionButtonVisibility.DISABLED -> {
                        if (findNavController(R.id.nav_host_settings).currentDestination?.id != R.id.settingsFragment)
                            binding.fabNext.disableButton()
                    }
                    else -> {
                    }
                }
            }
        })

        settingsViewModel.tagsFetched.observe(this) { fetchedSuccessfully ->
            openHomeActivity(this)
        }

        settingsViewModel.showSpinningDialog.observe(this) { showSpinningDialog ->
            if(showSpinningDialog)
                spinningCircleDialog?.show()
            else
                spinningCircleDialog?.dismiss()
        }

        binding.fabNext.setOnClickListener {
            hideKeyboard()
            when (findNavController(R.id.nav_host_settings).currentDestination?.id) {
                R.id.changePasswordFragment -> {
//                    findNavController(R.id.nav_host_settings)
//                            .navigate(R.id.action_changePasswordFragment_to_inputNewPasswordFragment)
                    settingsViewModel.validateCurrentPassword()
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.DISABLED)
                }

                R.id.inputNewPasswordFragment -> {
                    findNavController(R.id.nav_host_settings)
                            .navigate(R.id.action_inputNewPasswordFragment_to_verifyNewPasswordFragment)
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.DISABLED)
                }
                R.id.verifyNewPasswordFragment -> {
                    settingsViewModel.changePassword()
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }
                R.id.verifyPhoneFragment -> {
                    settingsViewModel.addPhoneNumber()
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }
                R.id.verifyEmailFragment -> {
                    settingsViewModel.addEmailAddress()
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }

                R.id.change_Login_InputCurrentPasswordFragment -> {
                    settingsViewModel.validateCurrentPassword()
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.DISABLED)
                }

                R.id.change_Login_VerifyPhoneFragment -> {
                    settingsViewModel.addPhoneNumber()
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.DISABLED)
                }

                R.id.change_Login_verifyEmailFragment -> {
                    settingsViewModel.addEmailAddress()
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.DISABLED)
                }

                R.id.change_Login_verifyCodeFragment -> {
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.DISABLED)
                }

                R.id.communityFragmentSettings -> {
                    if(NetworkChecker.isNetworkConnected(this))
                        settingsViewModel.showCommunityDialog()
                    else
                        ToastUtil.toast(R.string.no_internet_connection)
                }


            }
        }

        settingsViewModel.deleteUser.observe(this, Observer {
            duaAccount.deleteAllData()
        })

        homeViewModel.showEnvelopeDialog.observe(this) {
            if (it) {
                lifecycleScope.launchWhenResumed {
                    if (supportFragmentManager.findFragmentByTag("EnvelopeDialog") == null)
                        EnvelopeDialog.newInstance(homeViewModel.getInteractionLimit().toString(),ClevertapEventSourceValues.SETTINGS.value)
                            .show(supportFragmentManager, "EnvelopeDialog")
                }
            }
        }

        spinningCircleDialog = SpinningCircleDialog(this)

        setAppThemeInClevertap(this)
    }

    private fun setUpNav(savedInstanceState: Bundle?) {
        val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_settings) as NavHostFragment
        navController = navHostFragment.navController

        if(savedInstanceState == null){
            val navGraph = navController.navInflater.inflate(R.navigation.nav_graph_settings)
            navGraph.setStartDestination(when (settingsViewModel.startDestination) {
                DefaultDestination.SETTINGS_FRAGMENT -> R.id.settingsFragment
                DefaultDestination.PHONE_VERIFICATION -> {
                    sendEmailOrPhoneVerificationButtonClickEvent(homeViewModel.userProfile.value, ClevertapVerificationTypeValues.PHONE.value)
                    R.id.verifyPhoneFragment
                }
                DefaultDestination.EMAIL_VERIFICATION -> {
                    sendEmailOrPhoneVerificationButtonClickEvent(homeViewModel.userProfile.value, ClevertapVerificationTypeValues.EMAIL.value)
                    R.id.verifyEmailFragment
                }
                DefaultDestination.DOWNLOAD_DATA_NOTIFICATION -> R.id.downloadDataFragment
                DefaultDestination.NOTIFICATIONS -> R.id.notificationSettingsFragment
                DefaultDestination.APPEARANCE -> R.id.appearanceSettingsFragment
                DefaultDestination.COMMUNITY_CHANGE -> R.id.communityFragmentSettings
                DefaultDestination.NAME_CHANGE -> R.id.changeNameFragment
                DefaultDestination.GENDER_CHANGE -> R.id.changeGenderFragment
                DefaultDestination.BIRTHDAY_CHANGE -> R.id.changeBirthdayFragment
                DefaultDestination.ACCOUNT_VERIFICATION -> R.id.accountVerificationFragment
                DefaultDestination.ACCOUNT_SETTINGS ->R.id.accountSettingsFragment
            })

        //    settingsViewModel.authMethod = if (settingsViewModel.startDestination == DefaultDestination.PHONE_VERIFICATION) AuthMethod.PHONE else if(settin) AuthMethod.EMAIL
            navController.graph = navGraph
        }

        appBarConfiguration = AppBarConfiguration.Builder()
                .setFallbackOnNavigateUpListener {
                    // Trigger the Activity's navigate up functionality
                    super.onSupportNavigateUp()
                }.build()
        NavigationUI.setupActionBarWithNavController(this, navController, appBarConfiguration)
        navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                R.id.settingsFragment -> {
                    settingsViewModel.clearViewModelData()
                    binding.toolbar.visibility = View.VISIBLE
                    binding.toolbar.setNavigationIcon(R.drawable.ic_close_icon)
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }

                R.id.changePasswordFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.inputNewPasswordFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.verifyNewPasswordFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.accountSettingsFragment -> {
                    binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }

                R.id.notificationSettingsFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.pushNotificationFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.change_Login_InputCurrentPasswordFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.change_Login_VerifyPhoneFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.change_Login_verifyEmailFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)


                R.id.deleteAccountReasons ->{
                    binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)
                    settingsViewModel.setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }

                R.id.deleteAccountPermanently -> {
                    binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)
                    settingsViewModel.setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }

                R.id.welcomeScreen2 -> binding.toolbar.visibility = View.GONE

                R.id.changeNameFragment -> {
                    settingsViewModel.setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                    binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)
                    setToolbarTitle(getString(R.string.adjust_profile_info))
                }

                R.id.changeGenderFragment -> {
                    binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)
                    setToolbarTitle(getString(R.string.adjust_profile_info))
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }

                R.id.changeBirthdayFragment -> {
                    binding.toolbar.setNavigationIcon( R.drawable.ic_chevron_left)
                    setToolbarTitle(getString(R.string.adjust_profile_info))
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }

                R.id.socialMediaFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.accountVerificationFragment -> {
                    binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)
                    settingsViewModel.setFabVisibility(FloatingActionButtonVisibility.HIDDEN)
                }

                R.id.premiumSettingsFragment -> {
                    binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)
                    setToolbarTitle(getString(R.string.premium_settings))
                }

                R.id.verifyPhoneFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.downloadDataFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.verifyCodeFragment2 -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.change_Login_verifyCodeFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.appearanceSettingsFragment -> binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)

                R.id.blockContactsFragment -> {
                    binding.toolbar.setNavigationIcon(R.drawable.ic_chevron_left)
                    setToolbarTitle(getString(R.string.block_contacts))
                }
            }
        }
    }

    fun getToolbar(): MaterialToolbar? {
        return binding.toolbar
    }

    fun setToolbarTitle(title: String) {
        binding.toolbarTitle.text = title
    }

    fun toggleMoreButton(enable:Boolean) {
        binding.moreBtn.isVisible = enable
    }

    fun toggleAddButton(enable:Boolean) {
        binding.addBtn.isVisible = enable
    }

    fun getMoreMenuButton(): View {
        return binding.moreBtn
    }

    fun getAddMenuButton(): View {
        return binding.addBtn
    }

    override fun onSupportNavigateUp(): Boolean {
        return NavigationUI.navigateUp(navController, appBarConfiguration) || super.onSupportNavigateUp()
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
        LocalBroadcastManager.getInstance(this).unregisterReceiver(onUpdateDataReceiver)
        spinningCircleDialog = null
    }


    override fun onListFragmentInteraction(item: LanguageConstants.LanguageItem?) {
//        when (item?.position) {
//            0 -> setNewLocale(LANGUAGE_ENGLISH)
//
//            1 -> setNewLocale(LANGUAGE_ALBANIAN)
//
//            2 -> setNewLocale(LANGUAGE_GERMAN)
//        }
        item?.language?.let {
            setNewLocale(item.language)
            spinningCircleDialog?.show()
        }
    }

    private fun setNewLocale(language: String, country: String = "") {
        val lang = language
        lang.let { it ->
            settingsViewModel.updateLanguage(it).observe(this, Observer {
                when (it) {
                    is Result.Success -> {
                        homeViewModel.forceGetAllTags(language)
                        duaSharedPrefs.setLanguageChange(true)
                        ModifiedLingver.getInstance().setLocale(applicationContext, language, country)
                        PurchaselyManager.changeLanguage(lang)

                        settingsViewModel.getTags(language)
//                        ToastUtil.toast(getString(R.string.changed_language))
                    }
                    is Result.Loading -> {
                        settingsViewModel.setShowSpinningDialog(true)
                    }
                    is Result.Error -> {
                        settingsViewModel.setShowSpinningDialog(false)
                        ToastUtil.toast(resources.getString(R.string.an_error_occurred))
                    }
//                else -> settingsViewModel.onLangStart();
                }
            })
        }
    }

    private fun restartHomeActivity() {
        val i = Intent(this, HomeActivity::class.java)
        startActivity(i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK))
    }

    private val onUpdateDataReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val extras = intent.extras
            if (extras != null) {
                if (extras.containsKey("action") && extras.containsKey("jsonData"))
                    onDataUpdateFromNotification(NotificationModel(extras))
            } else if (intent.getStringExtra("action") != null && intent.getStringExtra("jsonData") != null) {
                Bundle().apply {
                    putString("action", intent.getStringExtra("action"))
                    putString("jsonData", intent.getStringExtra("jsonData"))
                    onDataUpdateFromNotification(NotificationModel(this))
                }
            }
        }
    }

    private fun onDataUpdateFromNotification(notificationModel: NotificationModel) {
        if (notificationModel.type == NotificationType.USER_DELETED) {
            settingsViewModel.userDelete()
        }
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this).registerReceiver(onUpdateDataReceiver,
                IntentFilter(NotificationHelper.UPDATE_DATA)
        )
    }

    enum class DefaultDestination {
        SETTINGS_FRAGMENT, PHONE_VERIFICATION, EMAIL_VERIFICATION , DOWNLOAD_DATA_NOTIFICATION,
        NOTIFICATIONS,APPEARANCE,COMMUNITY_CHANGE,NAME_CHANGE,GENDER_CHANGE,BIRTHDAY_CHANGE, ACCOUNT_VERIFICATION, ACCOUNT_SETTINGS
    }

    private fun showDeletionFailedDialog() {

        val builder = AlertDialog.Builder(this, R.style.AlertDialogButtonTheme)
        builder.apply {
            setTitle(R.string.deletion_failed)
                    .setMessage(R.string.delete_is_spelled_wrong)
                    .setPositiveButton(R.string.try_again) { dialog, _ ->
                        dialog.cancel()
                    }
                    .setNegativeButton(R.string.cancel) { _, _ ->
                        finish()
                    }
        }
        return builder.create().run {
            show()
        }
    }

    private val chosenComponentReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            unregisterReceiver(this)

            val clickedComponent: ComponentName? = intent?.getParcelableExtra(
                    Intent.EXTRA_CHOSEN_COMPONENT
            );

            clickedComponent?.let {
                homeViewModel.showEnvelopeDialog()
            }

        }

    }

    private fun shareDua(referralId: Uri) {
        // Use custom action only for your app to receive the broadcast
        val shareAction = "$packageName.share.SHARE_ACTION"
        val receiver = Intent(shareAction)
        val flag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else  PendingIntent.FLAG_UPDATE_CURRENT
        val pi = PendingIntent.getBroadcast(
                this, 0, receiver, flag
        )
        val shareIntent = Intent.createChooser(getShareIntent(referralId), null, pi.intentSender)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            registerReceiver(chosenComponentReceiver, IntentFilter(shareAction), Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(chosenComponentReceiver, IntentFilter(shareAction))
        }
        startActivity(shareIntent)
    }

    private fun getShareIntent(referralId: Uri): Intent {
        val text = "${resources.getString(R.string.invite_text)}\n$referralId"
        return Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, text)
            type = "text/plain"
        }
    }

    private fun generateInvitationLink(referralId: String) {

        val invitationLink = "${BuildConfig.INVITATION_LINK}$referralId"
        Firebase.dynamicLinks.shortLinkAsync {
            link = Uri.parse(invitationLink)
            domainUriPrefix = BuildConfig.DOMAIN_URI_PREFIX
            androidParameters(packageName) {
                minimumVersion = BuildConfig.DYNAMIC_LINKS_ANDROID_MINIMUM_VERSION
            }
            iosParameters(BuildConfig.IOS_ID) {
                appStoreId = BuildConfig.DYNAMIC_LINKS_APP_STORE_ID
                minimumVersion = BuildConfig.DYNAMIC_LINKS_IOS_MINIMUM_VERSION
            }
            socialMetaTagParameters {
                title = getString(R.string.refer_your_friend_to_join)
                description = getString(R.string.install_dua_to_join_our_wonderful)
                imageUrl =
                        Uri.parse(BuildConfig.DYNAMIC_LINKS_IMAGE_URL)
            }
        }.addOnSuccessListener { shortDynamicLink ->
            shortDynamicLink.shortLink?.let {
                homeViewModel.showReferralFriendsShare(it)
            }

        }
    }

    fun checkShareCompat() {
        homeViewModel.getReferral()
        homeViewModel.referralIdGenerated.observe(this, {
            if (NetworkChecker.isNetworkConnected(this)) {
                generateInvitationLink(it)
            } else ToastUtil.toast(getString(R.string.no_internet_connection))
        })

        homeViewModel.showReferralFriendsShare.observe(this, { uri ->
            // check if the activity resolves
            if (null == getShareIntent(uri).resolveActivity(packageManager)) {
                ToastUtil.toast("Your device doesn't support this action")
            } else shareDua(uri)
        })

    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            PremiumActivity.PREMIUM_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    val sendBroadCastData = Intent(PremiumActivity.PREMIUM_INTENT)
                    sendBroadCastData.putExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, true)
                    LocalBroadcastManager.getInstance(this).sendBroadcast(sendBroadCastData)
                    homeViewModel.fetchUserProfile(isAfterPremium = true)
                    homeViewModel.afterPremiumVerified()
                    homeViewModel.hasUpgradedToPremium = true
                }
            }
        }
    }
    override fun finish() {
        val data = Intent()

        if(hasNameChanged)
            data.putExtra(HAS_NAME_CHANGED, true)

        if(hasAgeChanged)
            data.putExtra(HAS_AGE_CHANGED, true)

        if(hasGenderChanged)
            data.putExtra(HAS_GENDER_CHANGED, true)

        if (homeViewModel.hasPremiumBeenRestored)
            data.putExtra(HAS_RESTORED_PREMIUM, true)

        if (homeViewModel.hasUpgradedToPremium)
            data.putExtra(HAS_UPGRADED_TO_PREMIUM, true)

        setResult(RESULT_OK, data)

        super.finish()
    }
}

