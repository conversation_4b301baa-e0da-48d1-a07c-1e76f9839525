package com.duaag.android.settings.fragments

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.databinding.FragmentInputNewPasswordBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.checkForStrengthPassword
import com.duaag.android.settings.models.PasswordType
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.*
import com.google.android.material.floatingactionbutton.FloatingActionButton
import javax.inject.Inject

class InputNewPasswordFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) {viewModelFactory}

    private var _binding: FragmentInputNewPasswordBinding?=null
    private val binding get() = _binding!!


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        _binding = FragmentInputNewPasswordBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel=settingViewModel
        binding.editTextPasswordNew.addTextChangedListener(TextWatcher1(binding))

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        whenPasswordChange()
    }

    /** only for testing*/
    private fun whenPasswordChange() {
        binding.let {
//            it.editTextPasswordNew.addTextChangedListener(TextWatcher1(it))
            it.editTextPasswordNew.onKeyboardNext {
                if(activity?.findViewById<FloatingActionButton>(R.id.fab_next)?.isEnabled!!){
                    activity?.findViewById<FloatingActionButton>(R.id.fab_next)?.performClick()
                }
            }
        }

    }

    inner class TextWatcher1(private val it: FragmentInputNewPasswordBinding) : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            it.editTextPasswordNew.text.toString()
            changeLabels(checkForStrengthPassword(it.editTextPasswordNew.text.toString()))
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

        }
    }

    fun changeLabels(string: PasswordType) {
        when (string) {
            PasswordType.NOT_ENOUGH -> {
                view?.findViewById<ProgressBar>(R.id.progressBar)?.progress = 0
                view?.findViewById<TextView>(R.id.txt_at_last_characters)?.setTextColorRes(R.color.description_primary)
                view?.findViewById<TextView>(R.id.txt_uper_and_lower_case)?.setTextColorRes(R.color.description_primary)
                view?.findViewById<TextView>(R.id.txt_contains_symbol)?.setTextColorRes(R.color.description_primary)

                settingViewModel.setFabVisibility(FloatingActionButtonVisibility.DISABLED)
            }

            PasswordType.POOR -> {
                view?.findViewById<ProgressBar>(R.id.progressBar)?.progress = 30
                view?.findViewById<TextView>(R.id.txt_at_last_characters)?.setTextColorRes(R.color.title_primary)
                view?.findViewById<TextView>(R.id.txt_uper_and_lower_case)?.setTextColorRes(R.color.description_primary)
                view?.findViewById<TextView>(R.id.txt_contains_symbol)?.setTextColorRes(R.color.description_primary)

                settingViewModel.setFabVisibility(FloatingActionButtonVisibility.SHOWN)
            }

            PasswordType.UPPER_CASE_AND_LOWER_CASE -> {
                view?.findViewById<ProgressBar>(R.id.progressBar)?.progress = 50
                view?.findViewById<TextView>(R.id.txt_at_last_characters)?.setTextColorRes(R.color.title_primary)
                view?.findViewById<TextView>(R.id.txt_uper_and_lower_case)?.setTextColorRes(R.color.title_primary)
                view?.findViewById<TextView>(R.id.txt_contains_symbol)?.setTextColorRes(R.color.description_primary)

                settingViewModel.setFabVisibility(FloatingActionButtonVisibility.SHOWN)
            }

            PasswordType.SYMBOLS -> {
                view?.findViewById<ProgressBar>(R.id.progressBar)?.progress = 50
                view?.findViewById<TextView>(R.id.txt_at_last_characters)?.setTextColorRes(R.color.title_primary)
                view?.findViewById<TextView>(R.id.txt_uper_and_lower_case)?.setTextColorRes(R.color.description_primary)
                view?.findViewById<TextView>(R.id.txt_contains_symbol)?.setTextColorRes(R.color.title_primary)

                settingViewModel.setFabVisibility(FloatingActionButtonVisibility.SHOWN)
            }

            PasswordType.STRONG -> {
                view?.findViewById<ProgressBar>(R.id.progressBar)?.progress = 100
                view?.findViewById<TextView>(R.id.txt_at_last_characters)?.setTextColorRes(R.color.title_primary)
                view?.findViewById<TextView>(R.id.txt_uper_and_lower_case)?.setTextColorRes(R.color.title_primary)
                view?.findViewById<TextView>(R.id.txt_contains_symbol)?.setTextColorRes(R.color.description_primary)

                settingViewModel.setFabVisibility(FloatingActionButtonVisibility.SHOWN)
            }
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    override fun onResume() {
        super.onResume()
        binding.editTextPasswordNew.requestFocus()
        binding.editTextPasswordNew.showKeyboard()
    }
}
