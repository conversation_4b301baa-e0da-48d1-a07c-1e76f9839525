package com.duaag.android.settings.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapRestoreErrorTypeValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentSubscriptionAlreadyExistsBottomSheetBinding
import com.duaag.android.home.models.PaymentVerifyErrorBody
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.settings.models.AccountCreatedMethod
import com.duaag.android.utils.imageCircle
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class SubscriptionAlreadyExistsBottomSheet : BottomSheetDialogFragment() {

    private var _binding: FragmentSubscriptionAlreadyExistsBottomSheetBinding? = null
    private val binding get() = _binding!!

    var subscriptionFailedModel: PaymentVerifyErrorBody? = null
    var isRestoring: Boolean = false


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        subscriptionFailedModel = arguments?.getParcelable(SUBSCRIPTION_CONFLICT_DATA)
        isRestoring = arguments?.getBoolean(IS_RESTORING_SUBSCRIPTION, false) ?: false
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSubscriptionAlreadyExistsBottomSheetBinding.inflate(inflater, container, false)

        binding.continueBtn.setOnSingleClickListener {
            if(activity is PremiumActivity)
                dismissAllowingStateLoss()
            else
                findNavController().popBackStack()
        }

        if(isRestoring) {
            binding.title.setText(R.string.premium_on_another_account_title)
            binding.description.setText(R.string.premium_on_another_account_desc)

            sendScreenViewEvent()
        } else {
            binding.title.setText(R.string.premium_on_another_account_sub_conflict_title)
            binding.description.setText(R.string.premium_on_another_account_sub_conflict_desc)
        }


        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        subscriptionFailedModel?.let {
            val nameAndAge = "${it.conflictUserData?.firstName}, ${it.conflictUserData?.age}"
            val createdWithString = when(it.conflictUserData?.accountMedium?.medium) {
                AccountCreatedMethod.FACEBOOK.method  -> {
                    getString(R.string.account_created_with_facebook)
                }
                AccountCreatedMethod.GOOGLE.method -> {
                    getString(R.string.account_created_with_google)
                }
                AccountCreatedMethod.APPLE.method -> {
                    getString(R.string.account_created_with_apple)
                }
                AccountCreatedMethod.SPOTTED.method -> {
                    getString(R.string.created_with_spotted)
                }
                AccountCreatedMethod.EMAIL.method -> {
                    getString(R.string.account_created_with_email)
                }
                AccountCreatedMethod.PHONE.method -> {
                    getString(R.string.account_created_with_phone)
                }
                else -> { "" }
            }
            val accountInfo = "$createdWithString\n${it.conflictUserData?.accountMedium?.accountData}"

            binding.userName.text = nameAndAge
            binding.accountInfo.text = accountInfo
            imageCircle(binding.userImg, it.conflictUserData?.pictureUrl)
        }
    }

    private fun sendScreenViewEvent() {
        sendClevertapEvent(
            ClevertapEventEnum.RESTORE_ERROR_POPUP, mapOf(
            ClevertapEventPropertyEnum.RESTORE_ERROR_TYPE.propertyName to ClevertapRestoreErrorTypeValues.OTHER_ACCOUNT.value,
        ))
    }

    companion object {
        const val SUBSCRIPTION_CONFLICT_DATA = "subscription_conflict_data"
        const val IS_RESTORING_SUBSCRIPTION = "is_restoring_subscription"

        fun newInstance(
            model: PaymentVerifyErrorBody,
            isRestoring: Boolean
        ): SubscriptionAlreadyExistsBottomSheet =
            SubscriptionAlreadyExistsBottomSheet().apply {
                arguments = bundleOf(
                    SUBSCRIPTION_CONFLICT_DATA to model,
                    IS_RESTORING_SUBSCRIPTION to isRestoring
                )
            }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}