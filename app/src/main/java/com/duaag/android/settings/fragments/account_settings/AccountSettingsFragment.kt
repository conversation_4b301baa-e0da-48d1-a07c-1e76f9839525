package com.duaag.android.settings.fragments.account_settings

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.auth_interfaces.HasFloatingActionButton
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapGhostSourceValues
import com.duaag.android.clevertap.ClevertapPremiumTypeValues
import com.duaag.android.clevertap.ClevertapUnhideSourceValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentAccountSettingsBinding
import com.duaag.android.exceptions.PremiumException
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.premium_subscription.showBillingNotAvailable
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.fragments.account_settings.ghost_mode.IncognitoModeDialogFragment
import com.duaag.android.settings.fragments.account_settings.ghost_mode.IncognitoModeType
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.addDifferentTypeface
import com.duaag.android.utils.getRemainingMonths
import com.duaag.android.utils.makeLinks
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.utils.updateLocale
import com.google.android.material.switchmaterial.SwitchMaterial
import com.uxcam.UXCam
import javax.inject.Inject


class AccountSettingsFragment : Fragment(), IncognitoModeDialogFragment.IncognitoModeDialogListener {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) {viewModelFactory}
    private val homeViewModel: HomeViewModel by viewModels({ activity as SettingsActivity }) {viewModelFactory}

    private var _binding: FragmentAccountSettingsBinding? = null
    private val binding get() = _binding!!

    private val premiumBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val isPremium = intent?.getBooleanExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, false)
                ?: false

            settingViewModel.updateGhostModeState(upgradedToPremium = isPremium)

        }
    }
    companion object{
        const val ONE_DAY_IN_MILLIS = 24L * 3600 * 1000
    }

    var ghostModeInfoFromPremiumFlow = false
    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        _binding = FragmentAccountSettingsBinding.inflate(inflater)

        //register receiver for premium subscription events
        val premiumSubscriptionFilter = IntentFilter(PremiumActivity.PREMIUM_INTENT)
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(premiumBroadcastReceiver, premiumSubscriptionFilter)

        addDifferentTypeface(binding.adjustProfileNote, R.string.note_you_can_only_change_your_profile_information_once_in_every_six_months_an)
        binding.noteId.let { addDifferentTypeface(it, R.string.cancel_your_subscription_an, R.color.title_primary) }

        binding.let {
            it.lifecycleOwner = viewLifecycleOwner
            it.cardDeleteMyAccount.setOnSingleClickListener {
                val action = AccountSettingsFragmentDirections.actionAccountSettingsFragmentToDeleteAccountPermanently()
                findNavController().navigate(action)
            }
            //UXCam
            UXCam.occludeSensitiveView(it.phoneHeader)
            UXCam.occludeSensitiveView(it.emailHeader)
            UXCam.occludeSensitiveView(it.birthdateHeader)



        }

        settingViewModel.getUser().observe(viewLifecycleOwner, Observer { userModel ->
            userModel?.let {
                settingViewModel.updateGhostModeState(it.profile.isGhost,it.settings.isGhostModeEnabled,it.isInvisible())
                if (it.isDisabled) binding.hideProfileSwitch.isEnabled = false
                binding.hideProfileSwitch.isChecked = it.isInvisible()

                binding.name.text = it.firstName
                binding.genderHeader.setText(if(it.gender == GenderType.MAN.value) R.string.male else R.string.female)
                binding.birthdateHeader.text = it.birthDate

                val configurationNames = it.counterConfigurationNames
                val nameChangeResetTime = settingViewModel.userCounters?.firstOrNull { it.configurationName == configurationNames.nameChangeCounterCN }
                if(nameChangeResetTime?.counter?.resetTime != null) {
                    val remainingMonths = getRemainingMonths(System.currentTimeMillis(), nameChangeResetTime.counter.resetTime - ONE_DAY_IN_MILLIS)

                    binding.nameTop.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.name.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.nameArrow.setImageResource(R.drawable.ic_disabled_chevron_right)
                    binding.changeNameBtn.text = getString(R.string.available_for_change_in_x_months, remainingMonths)
                    binding.changeNameBtn.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.changeNameBtn.isClickable = false
                    binding.cardChangeName.isClickable = false
                }
                val genderChangeResetTime = settingViewModel.userCounters?.firstOrNull { it.configurationName == configurationNames.genderCounterCN }

                if(genderChangeResetTime?.counter?.resetTime != null) {
                    val remainingMonths = getRemainingMonths(System.currentTimeMillis(), genderChangeResetTime.counter.resetTime - ONE_DAY_IN_MILLIS)

                    binding.genderTop.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.genderHeader.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.genderArrow.setImageResource(R.drawable.ic_disabled_chevron_right)
                    binding.changeGenderBtn.text = getString(R.string.available_for_change_in_x_months, remainingMonths)
                    binding.changeGenderBtn.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.changeGenderBtn.isClickable = false
                    binding.cardChangeGender.isClickable = false

                }
                val birthdayChangeResetTime = settingViewModel.userCounters?.firstOrNull { it.configurationName == configurationNames.birthdayCounterCN }

                if(birthdayChangeResetTime?.counter?.resetTime != null) {
                    val remainingMonths = getRemainingMonths(System.currentTimeMillis(), birthdayChangeResetTime.counter.resetTime - ONE_DAY_IN_MILLIS)

                    binding.birthdateTop.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.birthdateHeader.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.birthdateArrow.setImageResource(R.drawable.ic_disabled_chevron_right)
                    binding.changeBirthdateBtn.text = getString(R.string.available_for_change_in_x_months, remainingMonths)
                    binding.changeBirthdateBtn.setTextColor(resources.getColor(R.color.disable_primary, requireActivity().theme))
                    binding.changeBirthdateBtn.isClickable = false
                    binding.cardChangeBirthdate.isClickable = false
                }
            }
        })

        binding.ghostModeSwitch.setOnSingleClickListener {
            if (!NetworkChecker.isNetworkConnected(requireContext())) {
                ToastUtil.toast(getString(R.string.no_internet_connection))
                binding.ghostModeSwitch.isChecked = !binding.ghostModeSwitch.isChecked
                return@setOnSingleClickListener
            }

            if( settingViewModel.userProfile.value?.gender != GenderType.WOMAN.value && (!DuaApplication.instance.getBillingAvailable() && settingViewModel.userProfile.value?.premiumType == null)){
                requireActivity().showBillingNotAvailable(
                    title = getString(R.string.ghost_mode_no_billing)
                )
                (it as? SwitchMaterial)?.apply {
                    isChecked = false
                }

                return@setOnSingleClickListener
            }
            settingViewModel.toggleGhostMode(
                isChecked = binding.ghostModeSwitch.isChecked,
                eventSource = ClevertapGhostSourceValues.ACC_SETTINGS
            )
        }

        settingViewModel.dataUi
            .observe(viewLifecycleOwner){
            setVisibility(binding.ghostModeCl,it.isGhostModeEnabled)
            if(it.error is PremiumException) {
                //clear error
                settingViewModel.updateGhostModeState(error = null)
                requireActivity().openPremiumPaywall(
                    eventSourceClevertap = ClevertapEventSourceValues.GHOST_MODE,
                    placementId = PurchaselyPlacement.GHOST_MODE.id,
                    userModel = homeViewModel.userProfile.value
                )
            }
                setVisibility(binding.ghostModeTag,settingViewModel.userProfile.value?.gender == GenderType.MAN.value)
            binding.ghostModeSwitch.isChecked = it.ghostMode

            if(it.showGhostInfoDialog) {
                IncognitoModeDialogFragment.showInfo(requireContext(), childFragmentManager)
            }
            if(it.upgradedToPremium){
                IncognitoModeDialogFragment.showActivateGhost(requireContext(), childFragmentManager)
            }

            if(it.showGhostActivateDialog && !it.ghostActiveDialogShownOnce) {
                settingViewModel.onGhostActiveDialogShown()
                IncognitoModeDialogFragment.showActivate(requireContext(), childFragmentManager)
            }

        }

        binding.hideProfileSwitch.setOnClickListener {
            if (!NetworkChecker.isNetworkConnected(requireContext())) {
                ToastUtil.toast( getString(R.string.no_internet_connection))
                binding.hideProfileSwitch.isChecked = !binding.hideProfileSwitch.isChecked
                return@setOnClickListener
            }
            toggleVisibility(binding.hideProfileSwitch.isChecked)
        }

        if (homeViewModel.hasActiveBoost()) {
            binding.noteHideProfileBoost.isVisible = homeViewModel.hasActiveBoost()
            addDifferentTypeface(binding.noteHideProfileBoost, R.string.do_not_hide_profile_boost, R.color.title_primary)
        }

        settingViewModel.accountModel.observe(viewLifecycleOwner, { accountModel ->
            if ((accountModel.facebookId != null) || (accountModel.phone == null && accountModel.email == null)) {
                binding.changLoginMethod.visibility = View.GONE
                binding.headerDelete.setText(R.string.we_do_not_recommend_to_delete_your_account)
            } else if (accountModel.phone == null && accountModel.email != null) {
                binding.cardChangePhone.visibility = View.GONE
                binding.imageView28.visibility = View.GONE
            } else if (accountModel.email == null && accountModel.phone != null) {
                binding.cardChangeEmail.visibility = View.GONE
                binding.imageView29.visibility = View.GONE
            }
            binding.phoneHeader.text = accountModel.phone
            binding.emailHeader.text = accountModel.email
        })

        binding.cardChangePhone.setOnClickListener {
            firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_PHONE_INFO)

            val directions = AccountSettingsFragmentDirections.actionAccountSettingsFragmentToChangeLoginInputCurrentPasswordFragment().apply {
                isChangeLogin = true
                isPhone = true
            }
            settingViewModel.authMethod = AuthMethod.PHONE
            findNavController().navigate(directions)
            (settingViewModel as HasFloatingActionButton).setFabVisibility(
                FloatingActionButtonVisibility.DISABLED)
        }

        binding.cardChangeEmail.setOnClickListener {
            firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_EMAIL_INFO)

            val directions = AccountSettingsFragmentDirections.actionAccountSettingsFragmentToChangeLoginInputCurrentPasswordFragment().apply {
                isChangeLogin = true
                isPhone = false
            }
            settingViewModel.authMethod = AuthMethod.EMAIL
            findNavController().navigate(directions)
            (settingViewModel as HasFloatingActionButton).setFabVisibility(
                FloatingActionButtonVisibility.DISABLED)
        }

        if(BuildConfig.IS_FRIENDSHIP_ENABLED){
            binding.lookingForFriends.visibility = View.VISIBLE
        }else{
            binding.lookingForFriends.visibility = View.GONE
        }

        binding.showMeId.setOnClickListener{
            val directions = AccountSettingsFragmentDirections.actionAccountSettingsFragmentToLookingForFriends()
            findNavController().navigate(directions)
        }

        binding.downloadData.setOnClickListener{
            val directions = AccountSettingsFragmentDirections.actionAccountSettingsFragmentToDownloadDataFragment()
            findNavController().navigate(directions)
        }

        binding.cardChangeName.setOnClickListener {
            if (NetworkChecker.isNetworkConnected(requireContext())) {
                val user = settingViewModel.userProfile.value
                val resetTime = homeViewModel.userCounters?.firstOrNull { it.configurationName == user?.counterConfigurationNames?.nameChangeCounterCN }?.counter?.resetTime
                if(resetTime == null)
                    findNavController().navigate(AccountSettingsFragmentDirections.actionAccountSettingsFragmentToChangeNameFragment())
            } else {
                ToastUtil.toast(R.string.no_internet_connection)
            }
        }

        binding.cardChangeGender.setOnClickListener {
            if (NetworkChecker.isNetworkConnected(requireContext())) {
                val user = settingViewModel.userProfile.value
                val resetTime = homeViewModel.userCounters?.firstOrNull { it.configurationName == user?.counterConfigurationNames?.genderCounterCN }?.counter?.resetTime
                if(resetTime == null)
                    findNavController().navigate(AccountSettingsFragmentDirections.actionAccountSettingsFragmentToChangeGenderFragment())
            } else {
                ToastUtil.toast(R.string.no_internet_connection)
            }
        }

        binding.cardChangeBirthdate.setOnClickListener {
            if (NetworkChecker.isNetworkConnected(requireContext())) {
                val user = settingViewModel.userProfile.value
                val resetTime = homeViewModel.userCounters?.firstOrNull { it.configurationName == user?.counterConfigurationNames?.birthdayCounterCN }?.counter?.resetTime
                if(resetTime == null)
                    findNavController().navigate(AccountSettingsFragmentDirections.actionAccountSettingsFragmentToChangeBirthdayFragment())
            } else {
                ToastUtil.toast(R.string.no_internet_connection)
            }
        }

        setOnClickListeners()

        return binding.root
    }

    private fun toggleVisibility(isChecked: Boolean?) {
        settingViewModel.toggleInvisibleMode(isChecked ?: false)?.let {
            it.observe(viewLifecycleOwner, Observer {
            when (it) {
                is Result.Success -> {
                    if (isChecked == true) {
                        val eventPremiumType =
                            if (settingViewModel.userProfile.value?.premiumType != null) ClevertapPremiumTypeValues.PREMIUM.value
                            else ClevertapPremiumTypeValues.FREEMIUM.value

                        sendClevertapEvent(
                            ClevertapEventEnum.HIDE_PROFILE,
                            mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                        )
                    } else {
                        val eventPremiumType =
                            if (settingViewModel.userProfile.value?.premiumType != null) ClevertapPremiumTypeValues.PREMIUM.value
                            else ClevertapPremiumTypeValues.FREEMIUM.value

                        val unhideSourceValue = ClevertapUnhideSourceValues.SETTINGS.values

                        sendClevertapEvent(
                            ClevertapEventEnum.UNHIDE_PROFILE, mapOf(
                                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                ClevertapEventPropertyEnum.UNHIDE_SOURCE.propertyName to unhideSourceValue
                            )
                        )
                    }
                }

                is Result.Error -> {
                    binding.hideProfileSwitch.isChecked = !binding.hideProfileSwitch.isChecked
                }

                else -> {}
            }
        })
        } ?: kotlin.run { binding.hideProfileSwitch.isChecked = false }

    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(R.string.account_settings))

        if(settingViewModel.userProfile.value?.premiumType != null) {
            binding.noteId.visibility = View.VISIBLE
        }else{
            binding.noteId.visibility = View.GONE
        }

        if (settingViewModel.userProfile.value?.profile?.hasFriendshipEnabled == false) {
            binding.friendsDsc.setText(R.string.inactive)
        } else {
            binding.friendsDsc.setText(R.string.active)

        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(premiumBroadcastReceiver)
        _binding = null
    }


    private fun setOnClickListeners() {
        binding.noteId.apply {
            val firstPairTitle = getString(R.string.google_play_store)
            val googlePlayStoreLink1 = "https://play.google.com/store/account/subscriptions"

            makeLinks(null,
                    Pair(
                            first = firstPairTitle,
                            second = View.OnClickListener {
                                isPackageInstalled(getString(R.string.google_play_store), googlePlayStoreLink1, googlePlayStoreLink1, requireActivity())
                            }
                    )
            )
        }
    }

    fun isPackageInstalled(packageName: String, uriString: String, url: String, activity: Activity) {
        var uri = Uri.parse(uriString)

        try {
            val pm = requireContext().packageManager
            val applicationInfo: ApplicationInfo = pm.getApplicationInfo(packageName, 0)
            if (applicationInfo.enabled) {
                uri = Uri.parse("$uri")
            }
            val intent = Intent(Intent.ACTION_VIEW, uri)
            startActivity(intent)
        } catch (ignored: PackageManager.NameNotFoundException) {
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("$uri")))
        }
    }

    override fun onButtonClick(dialog: DialogFragment, type: IncognitoModeType) {
        if (!NetworkChecker.isNetworkConnected(requireContext())) {
            ToastUtil.toast(getString(R.string.no_internet_connection))
            return
        }
        when(type) {
            IncognitoModeType.INFO -> {
                dialog.dismissAllowingStateLoss()
            }
            IncognitoModeType.ACTIVE -> {
                toggleVisibility(true)
                dialog.dismissAllowingStateLoss()
            }
            IncognitoModeType.ACTIVE_PREMIUM -> {
                settingViewModel.toggleGhostMode(
                    isChecked = true,
                    eventSource = ClevertapGhostSourceValues.ACC_SETTINGS)
                ghostModeInfoFromPremiumFlow = true
                dialog.dismissAllowingStateLoss()
            }

            else -> {}
        }
    }

    override fun onDismiss(dialog: DialogFragment, type: IncognitoModeType) {
        when(type) {
            IncognitoModeType.INFO -> {
                if(ghostModeInfoFromPremiumFlow) {
                    ghostModeInfoFromPremiumFlow = false
                    settingViewModel.onGhostActiveDialogShown()
                    requireActivity().finish()
                }
            }

            IncognitoModeType.ACTIVE_PREMIUM -> {
                settingViewModel.onGhostActiveDialogShown()
            }
            else -> {}
        }    }

}
