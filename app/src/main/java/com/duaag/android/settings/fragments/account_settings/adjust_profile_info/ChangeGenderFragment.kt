package com.duaag.android.settings.fragments.account_settings.adjust_profile_info

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.auth_interfaces.HasFloatingActionButton
import com.duaag.android.databinding.FragmentChangeGenderBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.GenderType
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.addDifferentTypeface
import com.duaag.android.utils.addDifferentTypefacePlaceholder
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.google.android.material.card.MaterialCardView
import timber.log.Timber
import javax.inject.Inject

class ChangeGenderFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) { viewModelFactory }

    private var _binding: FragmentChangeGenderBinding? = null
    private val binding get() = _binding!!

    private var maleCardChangeListener: MaterialCardView.OnCheckedChangeListener? =
        MaterialCardView.OnCheckedChangeListener { card, isChecked ->
            card.checkedIcon = ContextCompat.getDrawable(requireContext(),R.drawable.transparent_drawable)
            if (isChecked) {
                card.isSelected = true
                card.children.forEach {
                    it.isSelected = true
                }
                settingsViewModel.setGender(GenderType.MAN)

            } else {
                card.isSelected = false
                card.children.forEach {
                    it.isSelected = false
                }

                if(!binding.femaleCardView.isChecked)
                    settingsViewModel.setGender(null)
            }

        }

    private var femaleCardChangeListener: MaterialCardView.OnCheckedChangeListener? =
        MaterialCardView.OnCheckedChangeListener { card, isChecked ->
            card.checkedIcon = ContextCompat.getDrawable(requireContext(),R.drawable.transparent_drawable)
            if (isChecked) {
                card.isSelected = true
                card.children.forEach {
                    it.isSelected = true
                }

                settingsViewModel.setGender(GenderType.WOMAN)

            } else {
                card.isSelected = false
                card.children.forEach {
                    it.isSelected = false
                }

                if(!binding.maleCardView.isChecked)
                    settingsViewModel.setGender(null)
            }
        }



    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }



    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentChangeGenderBinding.inflate(inflater)

        binding.changeGenderDesc.text = getString(R.string.your_gender_will_appear_in_your_profile,getString(R.string.app_name))
        addDifferentTypeface(binding.attentionTxt,R.string.note_you_can_only_change_your_gender_once_in_every_six_months_an,R.color.title_primary)


        settingsViewModel.showGenderDialog.observe(viewLifecycleOwner) {
            showGenderChangeDialog()
        }

        settingsViewModel.userGender.observe(viewLifecycleOwner) {
            binding.btnSave.isEnabled = it != null && it.value != settingsViewModel.userProfile.value?.gender
        }

        binding.maleCardView.let { card ->
            card.setOnCheckedChangeListener(maleCardChangeListener)
            card.setOnClickListener {
                modifyGenderMaleCard(it as MaterialCardView)
            }
        }

        binding.femaleCardView.let { card ->
            card.setOnCheckedChangeListener(femaleCardChangeListener)
            card.setOnClickListener {
                modifyGenderFemaleCard(it as MaterialCardView)
            }
        }

        binding.btnSave.setOnSingleClickListener{
            if(NetworkChecker.isNetworkConnected(requireContext()))
                settingsViewModel.showGenderDialog()
            else
                ToastUtil.toast(R.string.no_internet_connection)
        }

        val userGender = settingsViewModel.userProfile.value?.gender

        if (userGender == GenderType.MAN.value) {
            binding.maleCardView.isChecked = true
        } else {
            binding.femaleCardView.isChecked = true
        }

        return binding.root
    }



    private fun modifyGenderFemaleCard(femaleCardView: MaterialCardView) {
        femaleCardView.isChecked = !femaleCardView.isChecked
        binding.maleCardView.isChecked = false
    }

    private fun modifyGenderMaleCard(maleCardView: MaterialCardView) {
        maleCardView.isChecked = !maleCardView.isChecked
        binding.femaleCardView.isChecked = false
    }

    private fun showGenderChangeDialog() {
        val builder = AlertDialog.Builder(
            requireContext(),
            R.style.AlertDialogButtonTheme
        )
        val messageId = if(settingsViewModel.userProfile.value?.badge2 == Badge2Status.APPROVED.status) R.string.gender_chnage_warning else R.string.gender_chnage_warning_freemium
        builder.apply {
            setTitle(R.string.warning)
                .setMessage(messageId)
                .setPositiveButton(R.string.continue_text) { dialog, _ ->
                    (requireActivity() as SettingsActivity).hasGenderChanged = true
                    settingsViewModel.updateGender(settingsViewModel.userGender.value!!.value, settingsViewModel.userProfile.value!!)
                    dialog.cancel()
                    findNavController().popBackStack()
                }
                .setNegativeButton(R.string.cancel) { dialog, _ ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        maleCardChangeListener = null
        femaleCardChangeListener = null
        _binding = null
    }

}