package com.duaag.android.settings.fragments

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.duaag.android.R
import com.duaag.android.databinding.SocialMediaItemBinding
import com.duaag.android.settings.models.SocialMediaDataModel
import com.giphy.sdk.analytics.GiphyPingbacks.context

class SocialMediaAdapter(private val socialalMediaDataModels: List<SocialMediaDataModel>, private val clickListener : SocialMediaClickListener) : RecyclerView.Adapter<SocialMediaAdapter.SocialMediaViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SocialMediaViewHolder {
        val binding: SocialMediaItemBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), R.layout.social_media_item, parent, false)
        return SocialMediaViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SocialMediaViewHolder, position: Int) {
        socialalMediaDataModels[holder.adapterPosition].let { holder.bind(it,clickListener) }
    }

    override fun getItemCount(): Int = socialalMediaDataModels.size


    class SocialMediaViewHolder(val binding: SocialMediaItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(socialMediaDataModel: SocialMediaDataModel, clickListener: SocialMediaClickListener) {
                binding.socialMediaTitle.text = socialMediaDataModel.socialMediaTitle

            Glide.with(context)
            .load(socialMediaDataModel.socialMediaImg)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    .into(binding.socialMediaIcon)

                binding.root.setOnClickListener {
                    clickListener.onClick(socialMediaDataModel)
                }

                binding.executePendingBindings()

            }
        }
}

class SocialMediaClickListener (val clickListener: (items:SocialMediaDataModel) -> Unit){
    fun onClick(item:SocialMediaDataModel) = clickListener(item)
}
