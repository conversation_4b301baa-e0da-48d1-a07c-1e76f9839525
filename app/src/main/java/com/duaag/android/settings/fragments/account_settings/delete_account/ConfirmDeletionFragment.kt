package com.duaag.android.settings.fragments.account_settings.delete_account

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.appsflyer.AppsflyerEventsNameEnum
import com.duaag.android.appsflyer.sendAppsflyerEvent
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.ConfirmDeletionFragmentBinding
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.fragments.account_settings.delete_account.love_story.LoveStoryBottomSheetFragment
import com.duaag.android.settings.fragments.account_settings.delete_account.love_story.LoveStoryUiData
import com.duaag.android.settings.fragments.account_settings.delete_account.love_story.LoveStoryViewModel
import com.duaag.android.settings.fragments.account_settings.delete_account.model.DeleteReasonsData
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.SpinningCircleDialog
import javax.inject.Inject

class ConfirmDeletionFragment : Fragment(), LoveStoryBottomSheetFragment.LoveStorySheetListener {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<DeleteAccountReasonsViewModel>({ activity as SettingsActivity }) { viewModelFactory }
    private val loveStoryViewModel by viewModels<LoveStoryViewModel>({ activity as SettingsActivity }) { viewModelFactory }

    private var _binding: ConfirmDeletionFragmentBinding? = null
    private val binding get() = _binding!!

    private var spinningCircleDialog: SpinningCircleDialog? = null
    private var model: DeleteReasonsData.DeleteReasonsModel? = null

    @Inject
    lateinit var duaAccount: DuaAccount


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        model = arguments?.getParcelable(DELETION_REASON_MODEL)
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        _binding = ConfirmDeletionFragmentBinding.inflate(inflater)
        sendScreenEvent()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        loveStoryViewModel.dataUi.observe(viewLifecycleOwner) { uiData ->
            handleLoveStorySubmission(loveStoryViewModel, uiData)
            handleApiError(uiData)
        }

        binding.deleteBtn.setOnSingleClickListener {
            createDialog(model)
        }

        binding.keepBtn.setOnSingleClickListener {
            findNavController().navigate(R.id.action_confirmDeletionFragment_to_accountSettingsFragment)
        }
    }

    private fun handleLoveStorySubmission(
        viewModel: LoveStoryViewModel,
        uiData: LoveStoryUiData
    ) {
        if (uiData.loveStorySubmitted == true) {
            viewModel.onLoveStorySubmit()
            registerDeleteAccountCallback(model)
        }
    }

    private fun handleApiError(uiData: LoveStoryUiData) {
        if (uiData.loveStoryApiError != null) {
            spinningCircleDialog?.dismiss()

            ToastUtil.toast(getString(R.string.something_is_not_right))
            loveStoryViewModel.resetLoveStoryError()
        }
    }

    @SuppressLint("InflateParams")
    private fun createDialog(model: DeleteReasonsData.DeleteReasonsModel?) {
        val builder = AlertDialog.Builder(requireContext(), R.style.AlertDialogButtonTheme)

        builder.apply {

            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setTitle(resources.getString(R.string.warning_exclamation))
                .setMessage(resources.getString(R.string.delete_account_message))
                .setNegativeButton(resources.getString(R.string.cancel)) { dialog, which ->
                    dialog.cancel()
                }
                .setPositiveButton(resources.getString(R.string.delete)) { dialog, which ->
                    spinningCircleDialog?.show()

                    if(model?.title == context.getString(R.string.delete_reason_1,context.getString(R.string.app_name))
                        && !loveStoryViewModel.dataUi.value?.story.isNullOrEmpty()) {
                        loveStoryViewModel.submitLoveStory()
                    } else {
                        registerDeleteAccountCallback(model)
                    }
                }
        }
        sendConfirmDeletePopupScreenEvent()
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(context, R.color.red_500))
            }
            show()
        }
    }

    private fun registerDeleteAccountCallback(model: DeleteReasonsData.DeleteReasonsModel?) {
        viewModel.deactivatedUser().observe(viewLifecycleOwner) {
            when (it) {
                is Result.Success -> {

                    registerFirebaseEvents(model)
                    duaAccount.deleteAllData()
                    sendAppsflyerEvent(AppsflyerEventsNameEnum.DID_USER_DELETE_PROFILE)
                    spinningCircleDialog?.dismiss()

                    val eventPremiumType =
                        getPremiumTypeEventProperty(viewModel.userRepository.user.value)

                    sendClevertapEvent(
                        ClevertapEventEnum.DELETE_ACCOUNT, mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.DELETE_ACCOUNT_REASONS.propertyName to model?.clevertapDeleteReasonsValues?.values
                        )
                    )
                }

                is Result.Loading -> {
                }

                is Result.Error -> {
                    spinningCircleDialog?.dismiss()
                    ToastUtil.toast(resources.getString(R.string.smthg_went_wrong))
                    logError(ErrorStatus.DEACTIVATED_USER)
                }

            }
        }
    }


    private fun registerFirebaseEvents(model: DeleteReasonsData.DeleteReasonsModel?) {
        firebaseLogEvent(
            FirebaseAnalyticsEventsName.DELETE_ACCOUNT_REASON,
            mapOf(
                FirebaseAnalyticsParameterName.DELETE_REASONS.value to model?.firebaseDeleteReason?.value,
                FirebaseAnalyticsParameterName.DELETE_ACC_POPUP_COUNT.value to 1L,
                model?.firebaseDeleteReason?.value to 1L))

    }

    private fun sendScreenEvent() {
        val eventPremiumType =
            getPremiumTypeEventProperty(viewModel.userRepository.user.value)

        sendClevertapEvent(
            ClevertapEventEnum.CONFIRM_DELETION_SCREENVIEW, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.DELETE_ACCOUNT_REASONS.propertyName to model?.clevertapDeleteReasonsValues?.values
            )
        )
    }

    private fun sendConfirmDeletePopupScreenEvent() {
        val eventPremiumType =
            getPremiumTypeEventProperty(viewModel.userRepository.user.value)

        sendClevertapEvent(
            ClevertapEventEnum.CONFIRM_DELETION_POPUP, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.DELETE_ACCOUNT_REASONS.propertyName to model?.clevertapDeleteReasonsValues?.values
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        spinningCircleDialog = null
    }

    override fun onContinueClicked() {
        spinningCircleDialog?.show()
        registerDeleteAccountCallback(model)
    }

    companion object {
        const val DELETION_REASON_MODEL = "deletion_reason_model"
    }
}