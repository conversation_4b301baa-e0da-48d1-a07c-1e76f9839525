package com.duaag.android.settings.fragments.block_contacts.data.dto
import com.google.errorprone.annotations.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class AddPhoneNumberBodyDto(
    @SerializedName("defaultCountryCode")
    val defaultCountryCode: String?,
    @SerializedName("numbers")
    val numbers: List<Number>
)

@Keep
data class Number(
    @SerializedName("name")
    val name: String,
    @SerializedName("phoneNumber")
    val phoneNumber: String
)