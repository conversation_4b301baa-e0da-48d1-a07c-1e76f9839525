package com.duaag.android.settings.fragments.verify_account


import android.content.Context
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast.LENGTH_LONG
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.findNavController
import com.amazonaws.services.cognitoidentityprovider.model.CodeMismatchException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.amazonaws.services.cognitoidentityprovider.model.UserNotFoundException
import com.amazonaws.services.cognitoidentityprovider.model.UsernameExistsException
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.auth_interfaces.HasFloatingActionButton
import com.duaag.android.clevertap.*
import com.duaag.android.databinding.FragmentVerifyAccountCodeBinding
import com.duaag.android.firebase.NotificationConstants
import com.duaag.android.firebase.NotificationHelper
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.PinField
import okhttp3.ResponseBody
import timber.log.Timber
import javax.inject.Inject

class VerifyAccountCodeFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel by viewModels<SettingsViewModel>({ activity as SettingsActivity }) { viewModelFactory }
    private var _binding: FragmentVerifyAccountCodeBinding? = null
    private val binding get() = _binding!!

    private var mIsChangeLogin : Boolean = false


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
        Timber.tag("VIEWMODEL").d(settingsViewModel.toString())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            VerifyAccountCodeFragmentArgs.fromBundle(it).apply {
                mIsChangeLogin = isChangeLogin
            }
        }
    }
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = FragmentVerifyAccountCodeBinding.inflate(inflater)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = settingsViewModel
        val view = binding.root


        settingsViewModel.verifyPhoneNumber.observe(viewLifecycleOwner, Observer {
            handleResponsePhone(it)
        })

        settingsViewModel.verifyEmailAddress.observe(viewLifecycleOwner, Observer {
            handleResponseEmail(it)
        })
        settingsViewModel.elapsedTime.observe(viewLifecycleOwner, {
            val string = getString(R.string.you_can_resend_the_code_an, it)
            binding.timerTextView.text = string
        })
        settingsViewModel.showTimer.observe(viewLifecycleOwner, {
            binding.showTimer = it
        })

        if(settingsViewModel.phoneLiveData.value != null) {
            sendEmailOrPhoneVerificationCodeEvent(settingsViewModel.userProfile.value, ClevertapVerificationTypeValues.PHONE.value)
        }else {
            sendEmailOrPhoneVerificationCodeEvent(settingsViewModel.userProfile.value, ClevertapVerificationTypeValues.EMAIL.value)
        }

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val contactInfo = settingsViewModel.phoneLiveData.value
                ?: settingsViewModel.emailLiveData.value?.lowercase()
        val start = getString(R.string.we_ve_sent_a_6_digit_code)
        val email = " $contactInfo"
        val end = getString(R.string.please_enter_your)
        val wordtoSpan: Spannable = SpannableString(start + email + end)
        wordtoSpan.setSpan(ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.pink_500)), start.length, start.length + email.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.emailDescription.text = wordtoSpan
        //Assign custom listener to our custom pin view
//        binding.modifiedPinView.onTextListener = ModifiedPinViewListener()
        binding.modifiedPinView.onTextCompleteListener = PinFieldListener()
    }

    private fun handleResponsePhone(it: Result<ResponseBody>) {
        when (it) {
            is Result.Success -> {
                settingsViewModel.verifyDigits.postValue("")
                binding.progressBar.visibility = View.GONE
                if (mIsChangeLogin) {
                    val eventPremiumType = getPremiumTypeEventProperty(settingsViewModel.userProfile.value)
                    sendClevertapEvent(ClevertapEventEnum.LOG_IN_INFORMATION_CHANGED,
                        mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                    )

                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(
                        FloatingActionButtonVisibility.HIDDEN
                    )

                    val phoneKey = ClevertapUserPropertyEnum.PHONE.value
                    val clevertapUserProperties = mapOf(phoneKey to settingsViewModel.phoneLiveData.value)
                    updateUserProfileInClevertap(clevertapUserProperties)

                    settingsViewModel.updateAccountAfterVerification(isChangeLogin = settingsViewModel.phoneLiveData.value)
                    firebaseLogEvent(FirebaseAnalyticsEventsName.LOGIN_INFO_CHANGED)
                    ToastUtil.toast(getString(R.string.successfully_changed_sign_in_info),LENGTH_LONG)
                    val direction = VerifyAccountCodeFragmentDirections.actionChangeLoginVerifyCodeFragmentToAccountSettingsFragment()
                    view?.findNavController()?.navigate(direction)
                } else {
                    settingsViewModel.updateAccountAfterVerification()

                    sendVerifiedEmailOrPhoneEvent(settingsViewModel.userProfile.value, ClevertapVerificationTypeValues.PHONE.value)

                    if (settingsViewModel.startDestination == SettingsActivity.DefaultDestination.SETTINGS_FRAGMENT) {
                        view?.findNavController()
                            ?.navigate(R.id.action_verifyCodeFragment2_to_accountVerificationFragment)
                    } else {
                        if (settingsViewModel.navigatedFromVerifyInstachat.value == true) {
                            NotificationHelper.getNotificationHelper(DuaApplication.instance.applicationContext)
                                .showInstaChatVerifiedNotification(
                                    DuaApplication.instance.applicationContext,
                                    "Your profile is now verified!",
                                    "Check out your earned badge \uD83D\uDD25",
                                    null,
                                    R.string.general_notification_,
                                    NotificationConstants.NOTIFICATION_ID_GENERAL
                                )
                            requireActivity().finish()

                        } else {
                            requireActivity().finish()

                        }
                    }
                }
            }
            is Result.Loading -> {
                binding.modifiedPinView.apply {
                    isEnabled = false
                }
                binding.progressBar.visibility = View.VISIBLE
                if (mIsChangeLogin){
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(
                        FloatingActionButtonVisibility.DISABLED
                    )
                }
            }

            is Result.Error -> {
                binding.modifiedPinView.apply {
                    isEnabled = true
                    showKeyboard()
                }
                if (mIsChangeLogin){
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(
                        FloatingActionButtonVisibility.DISABLED
                    )
                }
                binding.progressBar.visibility = View.GONE
                settingsViewModel.verifyDigits.postValue("")
                when (it.exception) {
                    is CodeMismatchException -> {
                        handleVerifyError(R.string.code_mismatch_phone, View.VISIBLE)
                    }
                    is UserNotFoundException -> {
                        handleVerifyError(R.string.user_can_not_be_found, View.VISIBLE)
                    }
                    is UsernameExistsException -> {
                        handleVerifyError(R.string.phone_already_in_use, View.VISIBLE)
                    }
                    is LimitExceededException -> {
                        handleVerifyError(getString(R.string.attempt_limit_reached_try_again_later), View.VISIBLE)
                    }
                    else -> handleVerifyError(it.exception.message ?: "", View.VISIBLE)
                }
            }

        }

    }

    private fun handleResponseEmail(it: Result<ResponseBody>) {
        when (it) {
            is Result.Success -> {
                settingsViewModel.verifyDigits.postValue("")
                binding.progressBar.visibility = View.GONE
                if (mIsChangeLogin) {
                    val eventPremiumType = getPremiumTypeEventProperty(settingsViewModel.userProfile.value)
                    sendClevertapEvent(ClevertapEventEnum.LOG_IN_INFORMATION_CHANGED,
                        mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                    )

                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(
                        FloatingActionButtonVisibility.HIDDEN
                    )

                    val emailKey = ClevertapUserPropertyEnum.EMAIL.value
                    val clevertapUserProperties = mapOf(emailKey to settingsViewModel.emailLiveData.value?.lowercase())
                    updateUserProfileInClevertap(clevertapUserProperties)

                    settingsViewModel.updateAccountAfterVerification(isChangeLogin = settingsViewModel.emailLiveData.value?.lowercase())
                    firebaseLogEvent(FirebaseAnalyticsEventsName.LOGIN_INFO_CHANGED)
                    ToastUtil.toast(getString(R.string.successfully_changed_sign_in_info),LENGTH_LONG)
                    val direction = VerifyAccountCodeFragmentDirections.actionChangeLoginVerifyCodeFragmentToAccountSettingsFragment()
                    view?.findNavController()?.navigate(direction)
                } else {
                    settingsViewModel.updateAccountAfterVerification()

                    sendVerifiedEmailOrPhoneEvent(settingsViewModel.userProfile.value, ClevertapVerificationTypeValues.EMAIL.value)

                    if (settingsViewModel.startDestination == SettingsActivity.DefaultDestination.SETTINGS_FRAGMENT) {
                        view?.findNavController()
                            ?.navigate(R.id.action_verifyCodeFragment2_to_accountVerificationFragment)
                    } else {
                        if (settingsViewModel.navigatedFromVerifyInstachat.value == true) {
                            NotificationHelper.getNotificationHelper(DuaApplication.instance.applicationContext)
                                .showInstaChatVerifiedNotification(
                                    DuaApplication.instance.applicationContext,
                                    getString(R.string.your_profile_now_verified),
                                    getString(R.string.check_your_badge),
                                    null,
                                    R.string.general_notification_,
                                    NotificationConstants.NOTIFICATION_ID_GENERAL
                                )
                            requireActivity().finish()

                        } else {
                            requireActivity().finish()
                        }
                    }
                }
            }
            is Result.Loading -> {
                binding.progressBar.visibility = View.VISIBLE
                if (mIsChangeLogin){
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(
                        FloatingActionButtonVisibility.DISABLED
                    )
                }
            }

            is Result.Error -> {
                if (mIsChangeLogin){
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(
                        FloatingActionButtonVisibility.DISABLED
                    )
                }
                binding.progressBar.visibility = View.GONE
                settingsViewModel.verifyDigits.postValue("")
                when (it.exception) {
                    is CodeMismatchException -> {
                        handleVerifyError(R.string.code_mismatch_email, View.VISIBLE)
                    }
                    is UserNotFoundException -> {
                        handleVerifyError(R.string.user_can_not_be_found, View.VISIBLE)
                    }
                    is UsernameExistsException -> {
                        handleVerifyError(R.string.email_already_in_use, View.VISIBLE)
                    }
                    is LimitExceededException -> {
                        handleVerifyError(getString(R.string.attempt_limit_reached_try_again_later), View.VISIBLE)
                    }
                    else -> handleVerifyError(it.exception.message ?: "", View.VISIBLE)
                }
            }

        }

    }

    private fun handleVerifyError(message: Int, visibility: Int) {
        binding.errorCode.setText(message)
        binding.errorCode.visibility = visibility
    }

    private fun handleVerifyError(message: String, visibility: Int) {
        binding.errorCode.text = message
        binding.errorCode.visibility = visibility
    }

    //Custom listener to use for our case, of our custom type
    inner class PinFieldListener : PinField.OnTextCompleteListener {
        override fun onTextComplete(enteredText: String): Boolean {
            if (settingsViewModel.phoneLiveData.value != null) {
                if (mIsChangeLogin) {
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(
                        FloatingActionButtonVisibility.SHOWN
                    )
                }

                settingsViewModel.verifyPhoneCode(enteredText)
            } else if (settingsViewModel.emailLiveData.value != null) {

                if (mIsChangeLogin) {
                    (settingsViewModel as HasFloatingActionButton).setFabVisibility(
                        FloatingActionButtonVisibility.SHOWN
                    )
                }

                settingsViewModel.verifyEmailAddress(enteredText)
            }
            return false
        }

        override fun onTextChange(enteredText: String) {
            if (enteredText.isNotEmpty()) binding.errorCode.visibility = View.INVISIBLE
        }

    }

    override fun onResume() {
        super.onResume()
        val title = when (settingsViewModel.authMethod) {
            AuthMethod.PHONE -> R.string.phone_verification
            else -> R.string.email_verification
        }
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(title))
        binding.modifiedPinView.postDelayed({
            binding.modifiedPinView.requestFocus()
            binding.modifiedPinView.showKeyboard()
        }, 200)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}
