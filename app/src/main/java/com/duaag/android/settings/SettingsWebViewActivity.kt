package com.duaag.android.settings

import android.os.Bundle
import android.view.KeyEvent
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.ActivitySettingsWebViewBinding
import com.duaag.android.utils.getStringResourceByName


class SettingsWebViewActivity : AppCompatActivity() {
    private var webView: WebView? = null
    private var _binding: ActivitySettingsWebViewBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = DataBindingUtil.setContentView(this, R.layout.activity_settings_web_view)
        binding.lifecycleOwner = this
        webView = binding.duaWebView
        setSupportActionBar(binding.toolbar)
        binding.toolbar.setNavigationOnClickListener {
            finish()
            overridePendingTransition(R.anim.enter_from_left, R.anim.exit_to_right)

        }
        createWebView(WebViewData().getUrl() ?: "")
        sendWebViewEvent()
    }

    inner class WebViewData {
        private val b: Bundle? = intent.extras
        val data=b?.getStringArrayList(getString(R.string.key_webView))
        fun getUrl(): String? = data?.get(0)
        fun getTitle(): String? =data?.get(1)
    }

    private fun createWebView(url: String) {
            webView?.apply {
            webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                  return false
                }
            }

            settings.javaScriptEnabled = true
            loadUrl(url)
        }
    }

    private fun sendWebViewEvent() {
        val premiumTypeValue =
            getPremiumTypeEventProperty(DuaApplication.instance.isLoggedInUserPremium())
        when(WebViewData().getUrl()) {
            getStringResourceByName(BuildConfig.PRIVACY_POLICY_LINK_KEY) -> {
                sendClevertapEvent(
                    ClevertapEventEnum.PRIVACY_POLICY_SCREENVIEW,
                    mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue)
                )
            }

            getStringResourceByName(BuildConfig.TERMS_AND_CONDITIONS_LINK_KEY) -> {
                sendClevertapEvent(
                    ClevertapEventEnum.TERMS_AND_CONDITIONS_SCREENVIEW,
                    mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue)
                )
            }

            getStringResourceByName(BuildConfig.SUBMIT_REQUEST_KEY) -> {
                sendClevertapEvent(
                    ClevertapEventEnum.SUBMIT_A_REQUEST_CLICKED,
                    mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue)
                )
            }

            getStringResourceByName(BuildConfig.SAFETY_TIPS_KEY) -> {
                sendClevertapEvent(
                    ClevertapEventEnum.SAFETY_TIPS_CLICKED,
                    mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to premiumTypeValue)
                )
            }



            else ->{}
        }
    }



//    private fun createWebView(url: String?) {
//        webView.apply {
//            webViewClient = object : WebViewClient() {
//                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
//                    if (url != null) {
//                        if (Uri.parse(url).host == getString(R.string.dua) && url.startsWith(getString(R.string.dua_https))) {
//                            // This is my web site, so do not override; let my WebView load the page
//                            return false
//                        }
//                    }
//                    // Otherwise, the link is not for a page on my site, so launch another Activity that handles URLs
//                    Intent(Intent.ACTION_VIEW, Uri.parse(url)).apply {
//                        startActivity(this)
//                    }
//                    return true
//                }
//            }
//
//            settings.javaScriptEnabled = true
//            loadUrl(url)
//        }
//    }

    override fun onResume() {
        super.onResume()
        binding.toolbarTitle.text=WebViewData().getTitle()
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding?.unbind()
        _binding = null
        webView = null
    }

    override fun onBackPressed() {
        super.onBackPressed()
        overridePendingTransition(R.anim.enter_from_left, R.anim.exit_to_right)

    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
// Check if the key event was the Back button and if there's history
        if (keyCode == KeyEvent.KEYCODE_BACK && webView?.canGoBack() == true) {
            webView?.goBack()
            return true
        }
        // If it wasn't the Back key or there's no web page history, bubble up to the default
        // system behavior (probably exit the activity)
        return super.onKeyDown(keyCode, event)
    }

}
