package com.duaag.android.settings.fragments

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.appcompat.widget.SearchView
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.databinding.ChooseCommunityBottomSheetBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.signup.adapters.CommunityAdapter
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import javax.inject.Inject


class ChooseCommunitySettingsBottomSheet : BottomSheetDialogFragment() {

    private var _binding: ChooseCommunityBottomSheetBinding? = null
    private val binding get() = _binding!!


    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel by viewModels<SettingsViewModel>({ activity as SettingsActivity} ) { viewModelFactory }


    override fun onAttach(context: Context) {
        super.onAttach(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.setOnShowListener { dialogInterface ->
            val d = dialogInterface as BottomSheetDialog
            val bottomSheet =
                d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.let {
                BottomSheetBehavior.from<FrameLayout?>(it).state =
                    BottomSheetBehavior.STATE_EXPANDED
            }

        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        _binding = ChooseCommunityBottomSheetBinding.inflate(inflater, container, false)


        binding.closeBtn.setOnClickListener {
            dismiss()
        }

        binding.somethingWentWrongContainer.tryAgainBtn.setOnClickListener {
            binding.somethingWentWrongContainer.root.visibility = View.GONE
            settingsViewModel.fetchCommunitiesIfNotCached()
        }

        binding.search.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                queryCommunities(query.toString())
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                queryCommunities(newText.toString())
                return false
            }
        })

        initRecyclerViews()

        return binding.root
    }

    private fun initRecyclerViews() {

        val communityAdapter = CommunityAdapter(false,
            settingsViewModel.chosenCommunity.value?.id,
            CommunityAdapter.CommunityClickListener { community ->
                settingsViewModel.setChosenCommunity(community)
                dismiss()
            })

        binding.communities.layoutManager = LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)

        binding.communities.adapter = communityAdapter

        settingsViewModel.communitiesList.observe(viewLifecycleOwner) { recentItems ->
            if(recentItems.isEmpty()) {
                binding.somethingWentWrongContainer.root.visibility = View.VISIBLE
            } else {
                binding.somethingWentWrongContainer.root.visibility = View.GONE
            }
            (binding.communities.adapter as CommunityAdapter).setData(recentItems)
        }

    }

    private fun queryCommunities(query: String) {
        val filteredData = settingsViewModel.communitiesList.value?.filter { it.communityName.contains(query, true) }.orEmpty()
        val adapter = binding.communities.adapter as CommunityAdapter
        adapter.setData(filteredData)

        if(query.isEmpty())
            binding.communities.scrollToPosition(0)
    }

    override fun onDestroyView() {
        super.onDestroyView()

        _binding = null
    }

}
