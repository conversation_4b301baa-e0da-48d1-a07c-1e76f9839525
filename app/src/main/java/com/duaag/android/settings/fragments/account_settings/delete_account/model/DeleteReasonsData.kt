package com.duaag.android.settings.fragments.account_settings.delete_account.model

import android.content.Context
import android.os.Parcelable
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapDeleteReasonsValues
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.FirebaseDeleteReasonsValues
import kotlinx.parcelize.Parcelize
import java.util.*

object DeleteReasonsData {

    @Parcelize
    data class DeleteReasonsModel(
        val id: String = UUID.randomUUID().toString(),
        val title: String,
        val isSelected: Boolean = false,
        val firebaseDeleteReason: FirebaseDeleteReasonsValues,
        val clevertapDeleteReasonsValues: ClevertapDeleteReasonsValues
    ) : Parcelable

    fun getDeleteReasons(context: Context): List<DeleteReasonsModel> {
        return listOf(
                DeleteReasonsModel(
                    title = context.getString(R.string.delete_reason_1,context.getString(R.string.app_name)),
                    firebaseDeleteReason = FirebaseDeleteReasonsValues.FOUND_SOMEONE_ON_DUA,
                    clevertapDeleteReasonsValues = ClevertapDeleteReasonsValues.FOUND_SOMEONE_ON_DUA),
                DeleteReasonsModel(
                    title = context.getString(R.string.delete_reason_2),
                    firebaseDeleteReason = FirebaseDeleteReasonsValues.NOT_ENOUGH_PROFILES,
                    clevertapDeleteReasonsValues = ClevertapDeleteReasonsValues.NOT_ENOUGH_PROFILES),
                DeleteReasonsModel(
                    title = context.getString(R.string.delete_reason_3),
                    firebaseDeleteReason = FirebaseDeleteReasonsValues.NOT_ENOUGH_MATCHES,
                    clevertapDeleteReasonsValues = ClevertapDeleteReasonsValues.NOT_ENOUGH_MATCHES),
                DeleteReasonsModel(
                    title = context.getString(R.string.delete_reason_4),
                    firebaseDeleteReason = FirebaseDeleteReasonsValues.TOO_MANY_NOTIFICATIONS,
                    clevertapDeleteReasonsValues = ClevertapDeleteReasonsValues.TOO_MANY_NOTIFICATIONS),
                DeleteReasonsModel(
                    title = context.getString(R.string.delete_reasons_5),
                    firebaseDeleteReason = FirebaseDeleteReasonsValues.IN_A_RELATIONSHIP,
                    clevertapDeleteReasonsValues = ClevertapDeleteReasonsValues.IN_A_RELATIONSHIP),
                DeleteReasonsModel(
                    title = context.getString(R.string.delete_reasons_6),
                    firebaseDeleteReason = FirebaseDeleteReasonsValues.DATING_APPS_ARENT_FOR_ME,
                    clevertapDeleteReasonsValues = ClevertapDeleteReasonsValues.DATING_APPS_ARENT_FOR_ME),
        )

    }

}