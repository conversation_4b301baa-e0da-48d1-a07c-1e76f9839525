package com.duaag.android.settings.fragments.account_settings.delete_account.love_story

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.duaag.android.BuildConfig
import com.duaag.android.api.Resource
import com.duaag.android.base.models.AccountModel
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.di.IoDispatcher
import com.duaag.android.di.MainDispatcher
import com.duaag.android.settings.fragments.account_settings.delete_account.model.LoveStoryRequest
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.YouTubeUtil
import com.duaag.android.utils.getStringResourceByName
import com.duaag.android.utils.isEmailValid
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class LoveStoryViewModel @Inject constructor(
    private val userRepository: UserRepository,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
) : ViewModel() {


    val user = userRepository.user
    var accountModel = userRepository.getAccount()

    private val _dataUi: MutableStateFlow<LoveStoryUiData> = MutableStateFlow(LoveStoryUiData())
    val dataUi: LiveData<LoveStoryUiData> get() = _dataUi.asLiveData()


     fun initData(accountModel: AccountModel?) {
        val emailAddress = accountModel?.email
        val phoneNumber = accountModel?.phone
        val hasEmailAddress = !emailAddress.isNullOrEmpty()
        val state =
            if (hasEmailAddress) ContactDetailsUiState.EDIT else ContactDetailsUiState.ADD
        _dataUi.update {
            it.copy(
                emailAddress = emailAddress,
                contactDetailsState = state,
                phoneNumber = phoneNumber
            )
        }
    }

    fun submitLoveStory() {
        val content = _dataUi.value.story
        val email = _dataUi.value.emailAddress
        if(content.isNullOrEmpty()) return
        val loveStoryRequest = LoveStoryRequest(content,email)
        viewModelScope.launch(ioDispatcher) {
            userRepository.submitLoveStory(loveStoryRequest)
                .catch { e ->
                    withContext(mainDispatcher) {
                        _dataUi.update {
                            it.copy(loveStorySubmitted= null, loveStoryApiError = e.message)
                        }
                        e.printStackTrace()
                    }
                }
                .collect { response ->
                    when (response) {
                        Resource.Loading -> {
                           /*not used*/
                        }

                        is Resource.Success -> {
                            withContext(mainDispatcher){
                                _dataUi.update {
                                    it.copy(loveStorySubmitted= response.data.succeeded)
                                }
                                sendTellYourStorySubmitEvent()
                            }
                        }
                        else -> {}
                    }

                }
        }
    }

    private fun sendTellYourStorySubmitEvent() {
        val eventPremiumType =
            getPremiumTypeEventProperty(user.value)

        sendClevertapEvent(
            ClevertapEventEnum.TELL_YOUR_STORY_SUBMITTED, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType
            )
        )
    }

    fun updateEmailAddress(emailAddress: String?) {
        if(emailAddress.isNullOrEmpty() || !isEmailValid(emailAddress)) return

        _dataUi.update {
            it.copy(emailAddress = emailAddress)
        }
    }

    fun updateContactDetailsState(contactDetailsUiState: ContactDetailsUiState) {
        _dataUi.update {
            it.copy(contactDetailsState = contactDetailsUiState)
        }
    }

    fun updateStory(story: String?) {
        _dataUi.update {
            it.copy(story = story)
        }
    }

    fun onLoveStorySubmit() {
        _dataUi.update {
            it.copy(loveStorySubmitted = null)
        }
    }

    fun resetLoveStoryError() {
        _dataUi.update {
            it.copy(loveStoryApiError = null)
        }
    }

}

enum class ContactDetailsUiState {
    ADD, EDIT, INPUT
}

data class LoveStoryUiData(
    val emailAddress: String? = null,
    val phoneNumber: String? = null,
    val contactDetailsState: ContactDetailsUiState? = null,
    val videoUrlKey: String = BuildConfig.LOVE_STORY_LINK_KEY,
    val story: String? = null,
    val loveStorySubmitted: Boolean? = null,
    val loveStoryApiError: String? = null,
) {
    var enableSubmit: Boolean =
        story != null && story.length >= 9 && (!emailAddress.isNullOrEmpty() && isEmailValid(
            emailAddress
        ))

    fun getThumbnailUrl(context: Context): String? =
        YouTubeUtil.getThumbnailUrl(getVideoUrl(context))

    fun getVideoUrl(context: Context) = context.getStringResourceByName(videoUrlKey)
}
