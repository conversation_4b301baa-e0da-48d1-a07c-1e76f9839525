package com.duaag.android.settings.fragments

import android.content.ActivityNotFoundException
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.applovin.sdk.AppLovinSdk
import com.applovin.sdk.AppLovinSdkConfiguration
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.application.DuaApplication
import com.duaag.android.auth_interfaces.HasFloatingActionButton
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapLogOutTypeValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentSettingsBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsSetUpSourceValues
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.adapters.BenefitsPremiumAdapter
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsSection
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.SettingsWebViewActivity
import com.duaag.android.settings.models.SettingsItem
import com.duaag.android.settings.models.SettingsItemEnums
import com.duaag.android.settings.models.WebViewItem
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signInWithSpotted.SignInWithSpottedActivity
import com.duaag.android.signup.fragment.guidelines.GuidelinesDialogFragment
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.EqualSpacingItemDecoration
import com.duaag.android.utils.IgnoreFirstItemDecoration
import com.duaag.android.utils.RemoteConfigUtils
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.getStringResourceByName
import com.duaag.android.utils.navigateSafer
import com.duaag.android.utils.openWebView
import com.duaag.android.utils.updateLocale
import com.duaag.android.views.SpinningCircleDialog
import com.facebook.AccessToken
import com.mapbox.maps.extension.style.expressions.dsl.generated.get
import timber.log.Timber
import javax.inject.Inject

class SettingsFragment : Fragment() {


    private val data by lazy {
        listOf(
                WebViewItem(
                        getString(R.string.privacy_policy),
                        requireContext().getStringResourceByName(BuildConfig.PRIVACY_POLICY_LINK_KEY),
                        SettingsWebViewActivity::class
                ),
                WebViewItem(
                        getString(R.string.terms_and_conditions),
                    requireContext().getStringResourceByName(BuildConfig.TERMS_AND_CONDITIONS_LINK_KEY),
                        SettingsWebViewActivity::class
                ),
                WebViewItem(
                        getString(R.string.about_app,getString(R.string.web_view_title)),
                       requireContext().getStringResourceByName(BuildConfig.ABOUT_DUA_LINK_KEY),
                        SettingsWebViewActivity::class
                ),
            
                WebViewItem(
                     getString(R.string.help_center),
                       requireContext().getStringResourceByName(BuildConfig.HELP_CENTER_LINK),
                       SettingsWebViewActivity::class
                ),

                 WebViewItem(
                    getString(R.string.faq),
                       requireContext().getStringResourceByName(BuildConfig.FAQ_KEY),
                       SettingsWebViewActivity::class
                 ),
            WebViewItem(
                getString(R.string.submit_request_label),
                requireContext().getStringResourceByName(BuildConfig.SUBMIT_REQUEST_KEY),
                SettingsWebViewActivity::class
            ),
            WebViewItem(
                getString(R.string.safety_tips_label),
                requireContext().getStringResourceByName(BuildConfig.SAFETY_TIPS_KEY),
                SettingsWebViewActivity::class
            )

        )

    }

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) { viewModelFactory }
    private val homeViewModel: HomeViewModel by viewModels({ activity as SettingsActivity }) { viewModelFactory }

    @Inject
    lateinit var duaAccount: DuaAccount

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs

    private var decoration: EqualSpacingItemDecoration? = null
    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!

    private var logOutSpinningCircleDialog: SpinningCircleDialog? = null
    private var appVersion: String? = null
    private var ignoreFirstItemDecoration: IgnoreFirstItemDecoration? = null
    private var spinningCircleDialog: SpinningCircleDialog? = null
    private var isUserOnGDPRRegion: Boolean = false

    private val premiumBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val isPremium = intent?.getBooleanExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, false) ?: false
            if (isPremium) {
                binding.recyclerSettings.adapter?.notifyDataSetChanged()
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)

    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(R.string.settings))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        appVersion = context?.let { duaSharedPrefs.getAppVersion(it) }
        appVersion?.let { settingViewModel.setAppVersion(it) }
        spinningCircleDialog = SpinningCircleDialog(requireContext())
        isUserOnGDPRRegion = (AppLovinSdk.getInstance(requireActivity()).configuration.consentFlowUserGeography ==
                AppLovinSdkConfiguration.ConsentFlowUserGeography.GDPR)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {

        // Inflate the layout for this fragment
        _binding = FragmentSettingsBinding.inflate(inflater)
        binding.lifecycleOwner = this
        binding.viewModel = settingViewModel
        binding.isFacebookLoggedIn = isFacebookLoggedIn()

        logOutSpinningCircleDialog = context?.let { SpinningCircleDialog(it) }

        setupRecyclerView(binding.recyclerSettings, createSettingsAdapter(getRecyclerItems()))


        homeViewModel.showSpinningDialog.observe(viewLifecycleOwner, Observer { show ->
            if (show) {
                spinningCircleDialog?.show()
            } else {
                spinningCircleDialog?.dismiss()
                binding.recyclerSettings.adapter?.notifyDataSetChanged()
            }
        })

        val premiumSubscriptionFilter = IntentFilter(PremiumActivity.PREMIUM_INTENT)
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(premiumBroadcastReceiver, premiumSubscriptionFilter)

        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()

        _binding = null
        decoration = null
        logOutSpinningCircleDialog = null
        spinningCircleDialog = null

        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(premiumBroadcastReceiver)
    }

    private fun createSettingsAdapter(items: List<SettingsItem?>): SettingsAdapter {
        val appName = getString(R.string.dua_walkthrough,getString(R.string.app_name))

        return SettingsAdapter(items, homeViewModel, SettingsSectionClickListener { item ->
            when (item.section) {
                SettingsSection.ACCOUNT -> {
                    when (item.settingsItemEnums) {
                        SettingsItemEnums.VERIFY_YOUR_PROFILE ->{
                            val action = SettingsFragmentDirections.actionSettingsFragmentToAccountVerificationFragment()

                            if (findNavController().currentDestination?.id == R.id.settingsFragment) {
                                findNavController().navigate(action)
                            }
                        }

                       SettingsItemEnums.CHANGE_PASSWORD -> {
                           val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                           sendClevertapEvent(ClevertapEventEnum.CHANGE_YOUR_PASSWORD_INITIATED,
                               mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                           )

                            val action = SettingsFragmentDirections.actionSettingsFragmentToChangePasswordFragment()

                            if (findNavController().currentDestination?.id == R.id.settingsFragment) {
                                findNavController().navigate(action)
                                (settingViewModel as HasFloatingActionButton).setFabVisibility(FloatingActionButtonVisibility.DISABLED)
                            }
                        }

                        SettingsItemEnums.ACCOUNT_SETTINGS -> {
                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            sendClevertapEvent(ClevertapEventEnum.ACCOUNT_SETTINGS_SCREENVIEW,
                                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                            )

                            val action = SettingsFragmentDirections.actionSettingsFragmentToAccountSettingsFragment()

                            if (findNavController().currentDestination?.id == R.id.settingsFragment) {
                                findNavController().navigate(action)
                            }
                        }

                        SettingsItemEnums.NOTIFICATIONS -> {
                            val action = SettingsFragmentDirections.actionSettingsFragmentToNotificationSettingsFragment()

                            if (findNavController().currentDestination?.id == R.id.settingsFragment) {
                                findNavController().navigate(action)
                            }
                        }

                        SettingsItemEnums.APPEARANCE -> {
                            val action = SettingsFragmentDirections.actionSettingsFragmentToAppearanceSettingsFragment()

                            if (findNavController().currentDestination?.id == R.id.settingsFragment) {
                                findNavController().navigate(action)
                            }
                        }

                        SettingsItemEnums.BLOCK_CONTACTS -> {
                            val action = SettingsFragmentDirections.actionSettingsFragmentToBlockContactsFragment()

                            if (findNavController().currentDestination?.id == R.id.settingsFragment) {
                                findNavController().navigate(action)
                            }
                        }

                        SettingsItemEnums.PREMIUM_SETTINGS -> {
                            val action = SettingsFragmentDirections.actionSettingsFragmentToPremiumSettingsFragment()

                            if (findNavController().currentDestination?.id == R.id.settingsFragment) {
                                findNavController().navigate(action)
                            }
                        }

                        else -> ToastUtil.toast("Else")
                    }
                }

                SettingsSection.HELP -> {
                    when (item.settingsItemEnums) {
                        SettingsItemEnums.DUA_WALKTHROUGH ->{
                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            sendClevertapEvent(ClevertapEventEnum.APP_WALKTHROUGH_INITIATED,
                                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                            )

                            val action = SettingsFragmentDirections.actionSettingsFragmentToWelcomeScreen2()
                            findNavController().navigate(action)
                        }

                        SettingsItemEnums.IMAGE_GUIDELINES ->{
                            //            val action=SettingsFragmentDirections.actionSettingsFragmentToGuidelinesDialogFragment()
                            //            view.findNavController().navigate(action)
                            if (childFragmentManager.findFragmentByTag("guidelinesDialog") == null) {
                                GuidelinesDialogFragment.newInstance(premiumTypeEventProperty = getPremiumTypeEventProperty(settingViewModel.userProfile.value)).show(childFragmentManager, "guidelinesDialog")
                            }
                        }

                        SettingsItemEnums.HELP_CENTER -> {
                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            sendClevertapEvent(ClevertapEventEnum.HELP_CENTER_SCREENVIEW,
                                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                            )

                            val action = SettingsFragmentDirections.actionSettingsFragmentToHelpScreen()
                            findNavController().navigate(action)
                       }

                        SettingsItemEnums.SUBMIT_REQUEST -> {
                            requireContext().openWebView(data[5].url, data[5].name, requireActivity())
                        }
                        else -> {
                        }
                    }
                }

                SettingsSection.LEGAL -> {
                    when(item.settingsItemEnums) {

                        SettingsItemEnums.PRIVACY_SETTINGS -> {
                            showAppLovinConsentFlow()
                        }

                        SettingsItemEnums.PRIVACY_POLICY -> {
                            requireContext().openWebView(data[0].url, data[0].name, requireActivity())

                        }

                        SettingsItemEnums.TERMS_AND_CONDITIONS -> {
                            requireContext().openWebView(data[1].url, data[1].name, requireActivity())

                        }

                        SettingsItemEnums.ABOUT_DUA -> {
                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            sendClevertapEvent(ClevertapEventEnum.ABOUT_DUA_SCREENVIEW,
                                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                            )

                            requireContext().openWebView(data[2].url, data[2].name, requireActivity())
                        }

                        SettingsItemEnums.SAFETY_TIPS -> {
                            requireContext().openWebView(data[6].url, data[6].name, requireActivity())

                        }

                        else -> {}

                    }
                }

                SettingsSection.CONNECT -> {
                    when (item.settingsItemEnums) {
                        SettingsItemEnums.INVITE_FRIENDS -> {
                            (requireActivity() as SettingsActivity).checkShareCompat()
                        }

                        SettingsItemEnums.RATE_US -> {
                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            sendClevertapEvent(ClevertapEventEnum.RATE_US_INITIATED,
                                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                            )

                            firebaseLogEvent(FirebaseAnalyticsEventsName.RATE_APP_BUTTONCLICK)
                            val packageName = Uri.parse(requireActivity().packageName)

                            try {
                                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName")))
                            } catch (e: ActivityNotFoundException) {
                                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$packageName")))
                            }
                        }

                        SettingsItemEnums.SOCIAL_MEDIA -> {
                            firebaseLogEvent(FirebaseAnalyticsEventsName.SOCIAL_MEDIA_BUTTONCLICK)
                            val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                            sendClevertapEvent(ClevertapEventEnum.SOCIAL_MEDIA_SCREENVIEW,
                                mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                            )

                            val action = SettingsFragmentDirections.actionSettingsFragmentToSocialMediaFragment()
                            findNavController().navigate(action)
                        }
                        else -> {}
                    }
                }

                SettingsSection.BOTTOM -> {

                    if(DuaApplication.instance.isCredentialUser == false) {
                        setAccountInfoBeforeSigningOutDialog()
                    } else {
                        val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                        val logOutType = ClevertapLogOutTypeValues.REGULAR.values

                        sendClevertapEvent(ClevertapEventEnum.LOG_OUT_INITIATED,
                            mapOf(
                                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                                ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to logOutType,
                            )
                        )

                        findNavController().navigateSafer(R.id.action_settingsFragment_to_loginInfoBottomSheetFragment)
                    }
                }

                SettingsSection.PREMIUM -> {
                    firebaseLogEvent(FirebaseAnalyticsEventsName.GO_PREMIUM_BUTTONCLICK_SETTINGS)

                    requireActivity().openPremiumPaywall(
                        BenefitsPremiumAdapter.PremiumPaywallList.UNBLURRED_ITEM,
                        ClevertapEventSourceValues.SETTINGS,
                        placementId = PurchaselyPlacement.SETTINGS_CALL_TO_ACTION_BUTTON.id,
                        userModel = homeViewModel.userProfile.value
                    )

                }
            }
        })
    }

    private fun setupRecyclerView(recyclerView: RecyclerView?, adapter: SettingsAdapter) {
        recyclerView?.setHasFixedSize(true)
        ignoreFirstItemDecoration = context?.let { IgnoreFirstItemDecoration(it) }
        ignoreFirstItemDecoration?.let { recyclerView?.addItemDecoration(it) }
        recyclerView?.adapter = adapter
    }

    private fun setAccountInfoBeforeSigningOutDialog() {
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)
        builder.apply {
            firebaseLogEvent(FirebaseAnalyticsEventsName.SPOTTED_SING_OUT_POPUP)
            sendClevertapEvent(ClevertapEventEnum.SPOTTED_SING_OUT_POPUP)

            setTitle(resources.getString(R.string.sign_out))
                .setMessage(resources.getString(R.string.before_sign_out))
                .setNegativeButton(resources.getString(R.string.sign_out)) { dialog, _ ->
                    val eventPremiumType = getPremiumTypeEventProperty(homeViewModel.userProfile.value)
                    val logOutType = ClevertapLogOutTypeValues.REGULAR.values

                    sendClevertapEvent(ClevertapEventEnum.LOG_OUT_INITIATED,
                        mapOf(
                            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                            ClevertapEventPropertyEnum.LOG_OUT_TYPE.propertyName to logOutType,
                        )
                    )

                    logOutFromDevice()
                }
                .setPositiveButton(resources.getString(R.string.set_up_account)) { _, _ ->
                    val setupSource = FirebaseAnalyticsSetUpSourceValues.SIGN_OUT.value

                    firebaseLogEvent(FirebaseAnalyticsEventsName.SPOTTED_SETUP_YOUR_ACCOUNT_INITIATED,
                        mapOf(FirebaseAnalyticsParameterName.SETUP_SOURCE.value to setupSource))

                    sendClevertapEvent(
                        ClevertapEventEnum.SPOTTED_SETUP_YOUR_ACCOUNT_INITIATED, mapOf(
                            ClevertapEventPropertyEnum.SETUP_SOURCE.propertyName to setupSource))

                    val intent = Intent(requireActivity() , SignInWithSpottedActivity::class.java)
                    intent.putExtra("SETUP_SOURCE",setupSource)
                    startActivity(intent)
                }

        }
        return builder.create().run { show() }
    }


    private fun createLogOutDialog() {
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)
        builder.apply {

            setTitle(resources.getString(R.string.sign_out))
                    .setMessage(resources.getString(R.string.sign_out_string))
                    .setNegativeButton(resources.getString(R.string.cancel)) { dialog, _ ->
                        dialog.cancel()
                    }
                    .setPositiveButton(resources.getString(R.string.ok_dialog)) { _, _ ->
                        logOutSpinningCircleDialog?.show()
                        logOutFromDevice()
                    }

        }
        return builder.create().run { show() }
    }

    private fun logOutFromDevice() {
        duaAccount.deleteUserDevice() { result ->
            if (result) {
                duaAccount.deleteAllData()
//                ModifiedLingver.getInstance().setLocale(requireContext(), LanguageConstants.LANGUAGE_ENGLISH, "")

                firebaseLogEvent(
                        FirebaseAnalyticsEventsName.LOG_OUT_REGULAR, mapOf(
                        FirebaseAnalyticsParameterName.LOG_OUT_REGULAR_COUNT.value to 1L))

                logOutSpinningCircleDialog?.dismiss()
            } else {
                logOutSpinningCircleDialog?.dismiss()
                ToastUtil.toast(resources.getString(R.string.smthg_went_wrong))
                logError(ErrorStatus.LOG_OUT_SETTINGS)
            }
        }
    }

    fun isFacebookLoggedIn(): Boolean {
        return AccessToken.getCurrentAccessToken() != null
    }

    private fun getRecyclerItems(): List<SettingsItem?> {
        val items by lazy {
            mutableListOf(
                    SettingsItem(0, getString(R.string.settings), isFreemiumOrPremium = true, settingsItemEnums=null, section = SettingsSection.PREMIUM),
                    SettingsItem(0, getString(R.string.account), isHeader = true, settingsItemEnums=null, section = SettingsSection.ACCOUNT),
                    SettingsItem(1, getString(R.string.profile_progress_title_2), settingsItemEnums = SettingsItemEnums.VERIFY_YOUR_PROFILE, section = SettingsSection.ACCOUNT),
                    SettingsItem(2, getString(R.string.change_password_v), settingsItemEnums = SettingsItemEnums.CHANGE_PASSWORD, section = SettingsSection.ACCOUNT),
                    SettingsItem(3, getString(R.string.notification), settingsItemEnums = SettingsItemEnums.NOTIFICATIONS, section = SettingsSection.ACCOUNT),
                    SettingsItem(4, getString(R.string.appearance), settingsItemEnums = SettingsItemEnums.APPEARANCE, section = SettingsSection.ACCOUNT),
                    SettingsItem(5, getString(R.string.block_contacts), settingsItemEnums = SettingsItemEnums.BLOCK_CONTACTS, section = SettingsSection.ACCOUNT),
                    SettingsItem(6, getString(R.string.account_settings), settingsItemEnums = SettingsItemEnums.ACCOUNT_SETTINGS, section = SettingsSection.ACCOUNT),
                    SettingsItem(7, getString(R.string.premium_settings), settingsItemEnums = SettingsItemEnums.PREMIUM_SETTINGS, section = SettingsSection.ACCOUNT),
                    SettingsItem(0, getString(R.string.connect), isHeader = true, settingsItemEnums =null,  section = SettingsSection.CONNECT),
                    SettingsItem(1, getString(R.string.invite_friends), settingsItemEnums = SettingsItemEnums.INVITE_FRIENDS, section = SettingsSection.CONNECT),
                    SettingsItem(2, getString(R.string.rate_us), settingsItemEnums = SettingsItemEnums.RATE_US, section = SettingsSection.CONNECT),
                    SettingsItem(4, getString(R.string.social_media), settingsItemEnums = SettingsItemEnums.SOCIAL_MEDIA, section = SettingsSection.CONNECT),
                    SettingsItem(0, getString(R.string.help), isHeader = true, settingsItemEnums = null, section = SettingsSection.HELP),
                    SettingsItem(1, getString(R.string.help_center), settingsItemEnums = SettingsItemEnums.HELP_CENTER, section = SettingsSection.HELP),
                    SettingsItem(2, getString(R.string.submit_request_label), settingsItemEnums = SettingsItemEnums.SUBMIT_REQUEST, section = SettingsSection.HELP, isLink = true),
                    SettingsItem(3, getString(R.string.dua_walkthrough,getString(R.string.app_name)), settingsItemEnums = SettingsItemEnums.DUA_WALKTHROUGH, section = SettingsSection.HELP),
                    SettingsItem(4, getString(R.string.image_guidelines), settingsItemEnums = SettingsItemEnums.IMAGE_GUIDELINES, section = SettingsSection.HELP),
                    SettingsItem(0, getString(R.string.legal), isHeader = true, settingsItemEnums = null,  section = SettingsSection.LEGAL),
                    SettingsItem(1, getString(R.string.privacy_settings), settingsItemEnums = SettingsItemEnums.PRIVACY_SETTINGS,  section = SettingsSection.LEGAL),
                    SettingsItem(2, getString(R.string.privacy_policy), settingsItemEnums = SettingsItemEnums.PRIVACY_POLICY,  section = SettingsSection.LEGAL, isLink = true),
                    SettingsItem(3, getString(R.string.terms_and_conditions), settingsItemEnums = SettingsItemEnums.TERMS_AND_CONDITIONS, section = SettingsSection.LEGAL, isLink = true),
                    SettingsItem(4, getString(R.string.safety_tips_label), settingsItemEnums = SettingsItemEnums.SAFETY_TIPS, section = SettingsSection.LEGAL, isLink = true),
                    SettingsItem(5, getString(R.string.about_app,getString(R.string.web_view_title)), settingsItemEnums = SettingsItemEnums.ABOUT_DUA, section = SettingsSection.LEGAL, isLink = true),
                    appVersion?.let { SettingsItem(0, it, isBottom = true, settingsItemEnums = null,  section = SettingsSection.BOTTOM) }

            ).also {items ->
                if (!DuaApplication.instance.getPremiumAvailable()) items.removeAt(0)
                if (isUserOnGDPRRegion.not() || RemoteConfigUtils.isAdsEnabled().not()) items.removeAll {
                    it?.settingsItemEnums == SettingsItemEnums.PRIVACY_SETTINGS
                }
            }
        }

        return items
    }


    private fun createRestoreFailedDialog() {
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)
        builder.apply {
            setTitle(R.string.something_is_not_right)
                    .setMessage(R.string.your_subscription_didnt_go_through_please_try_again)
                    .setPositiveButton(R.string.ok_dialog) { dialog, _ ->
                        dialog.cancel()
                    }

        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(ContextCompat.getColor(context, R.color.blue_500))
            }
            show()
        }
    }

    private fun showAppLovinConsentFlow()
    {
        val cmpService = AppLovinSdk.getInstance(requireActivity()).cmpService
        cmpService.showCmpForExistingUser(requireActivity()) { error ->
            if (null == error)
            {
                Timber.tag("AppLovin").d("The CMP alert was shown successfully.")
            }
        }
    }
}
