package com.duaag.android.settings.fragments.account_settings.delete_account

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.duaag.android.R
import com.duaag.android.api.Result
import com.duaag.android.databinding.ShowFriendsBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import androidx.lifecycle.Observer
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.utils.updateLocale
import javax.inject.Inject

class ShowFriendsFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var duaSharedPrefs: DuaSharedPrefs
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) { viewModelFactory }

    private var _binding: ShowFriendsBinding? = null
    private val binding get() = _binding!!


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        _binding = ShowFriendsBinding.inflate(inflater)

        settingViewModel.getUser().observe(viewLifecycleOwner, Observer { userModel ->
            userModel?.let {
                binding.showFriendsSwitch.isChecked = it.hasFriendShip() ?: false
            }
        })

        binding.showFriendsSwitch.setOnClickListener {
            binding.showFriendsSwitch.isChecked.let { isCheck ->
                settingViewModel.updateFriendsShip(isCheck)
                    .observe(viewLifecycleOwner, Observer {
                        when (it) {
                            is Result.Success -> {
                                duaSharedPrefs.setShouldUpdateCards(true)
                                val user = settingViewModel.userProfile.value
                                user?.let { us ->
                                    us.profile.hasFriendshipEnabled = isCheck
                                    settingViewModel.updateShowFriends(us)
                                }
                            }
                            is Result.Error -> {
                                binding.showFriendsSwitch.isChecked =
                                    !binding.showFriendsSwitch.isChecked
                            }
                            else -> {}
                        }
                    })
            }

        }

        return binding.root
    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(R.string.show_me))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding
    }

}