package com.duaag.android.settings.fragments.account_settings.delete_account.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.DeleteReasonsItemBinding
import com.duaag.android.settings.fragments.account_settings.delete_account.model.DeleteReasonsData.DeleteReasonsModel

class DeleteReasonsAdapter(private val clickListener: ReasonsClickListener) : ListAdapter<DeleteReasonsModel, DeleteReasonsAdapter.DeleteResonsViewHolder>(DeleteReasonsDiffCallback()) {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeleteResonsViewHolder {
        val binding: DeleteReasonsItemBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), R.layout.delete_reasons_item, parent, false)
        return DeleteResonsViewHolder(binding)
    }

    override fun onBindViewHolder(holder: DeleteResonsViewHolder, position: Int) {
        holder.bind(getItem(position), clickListener)
    }

    inner class DeleteResonsViewHolder(private var binding: DeleteReasonsItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: DeleteReasonsModel, clickListener: ReasonsClickListener) {
            binding.item = item
            binding.clickListener = clickListener
            binding.executePendingBindings()
        }

    }

    class ReasonsClickListener(val clickListener: (model: DeleteReasonsModel) -> Unit) {
        fun onClick(model: DeleteReasonsModel) = clickListener(model)
    }

    class DeleteReasonsDiffCallback : DiffUtil.ItemCallback<DeleteReasonsModel>() {
        override fun areItemsTheSame(oldItem: DeleteReasonsModel, newItem: DeleteReasonsModel): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DeleteReasonsModel, newItem: DeleteReasonsModel): Boolean {
            return oldItem == newItem
        }

    }

}