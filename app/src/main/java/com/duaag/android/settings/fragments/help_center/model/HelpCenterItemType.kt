package com.duaag.android.settings.fragments.help_center.model

import com.duaag.android.BuildConfig
import com.duaag.android.R

val helpCenterItemStringMap: Map<HelpCenterItemType,Int> by lazy {
    mapOf(
        HelpCenterItemType.VIDEO_HELP to R.string.video_help,
        HelpCenterItemType.FAQ to R.string.faq
    )
}

val helpCenterItemUrlMap: Map<HelpCenterItemType,String> by lazy {
    mapOf(
        HelpCenterItemType.VIDEO_HELP to BuildConfig.VIDEO_HELP_LINK_KEY,
        HelpCenterItemType.FAQ to BuildConfig.FAQ_KEY
    )
}

enum class HelpCenterItemType {
  VIDEO_HELP,
    FAQ
}