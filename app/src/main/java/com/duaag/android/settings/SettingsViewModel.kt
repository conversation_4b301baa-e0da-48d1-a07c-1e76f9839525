package com.duaag.android.settings

import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.api.Result
import com.duaag.android.auth_interfaces.HasEmailInput
import com.duaag.android.auth_interfaces.HasFloatingActionButton
import com.duaag.android.auth_interfaces.HasPhoneInput
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.aws.ChangePasswordCallBack
import com.duaag.android.aws.models.ChangePasswordModel
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.base.models.CommunityInfo
import com.duaag.android.base.models.UserModel
import com.duaag.android.clevertap.*
import com.duaag.android.counters.data.models.CounterEntity
import com.duaag.android.counters.domain.GetUserCountersByConfNamesUseCase
import com.duaag.android.counters.domain.SyncUserCountersUseCase
import com.duaag.android.counters.domain.UpdateUserCounterUseCase
import com.duaag.android.di.ActivityScope
import com.duaag.android.exceptions.PasswordsDoNotMatchException
import com.duaag.android.exceptions.UserDataNotProcessedeException
import com.duaag.android.exceptions.UserDataRequestNoFoundException
import com.duaag.android.exceptions.UserRequestAlreadyExistsException
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsParameterName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.PurchaselyManager
import com.duaag.android.profile_new.models.UpdateUserResponse
import com.duaag.android.settings.fragments.Badge2Status
import com.duaag.android.settings.fragments.account_settings.adjust_profile_info.models.ChangeAdjustProfileEnum
import com.duaag.android.settings.fragments.account_settings.download_data.model.DownloadDataStatus
import com.duaag.android.settings.models.AddEmailModel
import com.duaag.android.settings.models.AddPhoneNumberModel
import com.duaag.android.settings.models.VerifyCodeModel
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.ChangePasswordAuthResult
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.*
import com.duaag.android.utils.livedata.SingleLiveData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import timber.log.Timber
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@ActivityScope
class SettingsViewModel @Inject constructor(
    private val userRepository: UserRepository,
    val duaSharedPrefs: DuaSharedPrefs,
    private val getUserCountersByConfNamesUseCase: GetUserCountersByConfNamesUseCase,
    private val updateUserCounterUseCase: UpdateUserCounterUseCase,
    private val syncUserCountersUseCase: SyncUserCountersUseCase
    ) : ViewModel(), HasFloatingActionButton, HasPhoneInput, HasEmailInput {

    var startDestination = SettingsActivity.DefaultDestination.SETTINGS_FRAGMENT

    val _emailLiveData = MutableLiveData<String>()
    val emailLiveData: LiveData<String>
        get() = _emailLiveData

    val _phoneLiveData = MutableLiveData<String>()
    val phoneLiveData: LiveData<String>
        get() = _phoneLiveData

    val _currentPassword = MutableLiveData<String>()
    val currentPassword: LiveData<String>
        get() = _currentPassword

    val _newPassword = MutableLiveData<String>()
    val newPassword: LiveData<String>
        get() = _newPassword

    val _newPasswordVerify = MutableLiveData<String>()
    val newPasswordVerify: LiveData<String>
        get() = _newPasswordVerify

    val _appVersion = MutableLiveData<String>()
    val appVersion: LiveData<String>
        get() = _appVersion

    private val _fabVisibility: SingleLiveData<FloatingActionButtonVisibility> = SingleLiveData()
    val fabVisibility: LiveData<FloatingActionButtonVisibility>
        get() = _fabVisibility

    private val _changePasswordResultLiveData = SingleLiveData<ChangePasswordAuthResult>()
    val changePasswordResulLiveData: LiveData<ChangePasswordAuthResult>
        get() = _changePasswordResultLiveData

    private val _currentPasswordValidity = SingleLiveData<ChangePasswordAuthResult>()
    val currentPasswordValidity: LiveData<ChangePasswordAuthResult>
        get() = _currentPasswordValidity

    private val _addPhoneNumber = SingleLiveData<Result<String>>()
    val addPhoneNumber: LiveData<Result<String>>
        get() = _addPhoneNumber

    private val _addEmailAddress = SingleLiveData<Result<String>>()
    val addEmailAddress: LiveData<Result<String>>
        get() = _addEmailAddress

    private val _verifyPhoneNumber = SingleLiveData<Result<ResponseBody>>()
    val verifyPhoneNumber: LiveData<Result<ResponseBody>>
        get() = _verifyPhoneNumber

    private val _verifyEmailAddress = SingleLiveData<Result<ResponseBody>>()
    val verifyEmailAddress: LiveData<Result<ResponseBody>>
        get() = _verifyEmailAddress

    private val _tagsFetched = SingleLiveData<Boolean>()
    val tagsFetched: LiveData<Boolean>
        get() = _tagsFetched

    private val _showSpinningDialog = SingleLiveData<Boolean>()
    val showSpinningDialog: LiveData<Boolean>
        get() = _showSpinningDialog

    private val _deleteUser: SingleLiveData<Boolean> = SingleLiveData()
    val deleteUser: LiveData<Boolean>
        get() = _deleteUser

    private val _elapsedTime: MutableLiveData<String> = MutableLiveData("00:00")
    val elapsedTime: LiveData<String>
        get() = _elapsedTime

    private val _showTimer: MutableLiveData<Boolean> = MutableLiveData(false)
    val showTimer: LiveData<Boolean>
        get() = _showTimer

    private val _navigatedFromVerifyInstachat: SingleLiveData<Boolean> = SingleLiveData()
    val navigatedFromVerifyInstachat: LiveData<Boolean>
        get() = _navigatedFromVerifyInstachat

    private val _showChangeNameDialog: SingleLiveData<Void> = SingleLiveData()
    val showChangeNameDialog: LiveData<Void>
        get() = _showChangeNameDialog

    private val _changeBirthdayCalendar: SingleLiveData<Calendar> = SingleLiveData()
    val changeBirthdayCalendar: LiveData<Calendar>
        get() = _changeBirthdayCalendar

    private val _showBirthdayDialog: SingleLiveData<Void> = SingleLiveData()
    val showBirthdayDialog: LiveData<Void>
        get() = _showBirthdayDialog

    private val _showGenderDialog: SingleLiveData<Void> = SingleLiveData()
    val showGenderDialog: LiveData<Void>
        get() = _showGenderDialog

    private val _showCommunityDialog: SingleLiveData<Void> = SingleLiveData()
    val showCommunityDialog: LiveData<Void>
        get() = _showCommunityDialog

    private val _userGender = MutableLiveData<GenderType?>()
    val userGender: LiveData<GenderType?>
        get() = _userGender

    val communitiesList: LiveData<List<CommunityInfo>>
        get() = userRepository.localCommunities.asLiveData()

    private var _chosenCommunity: MutableLiveData<CommunityInfo?> = MutableLiveData(null)
    val chosenCommunity: LiveData<CommunityInfo?>
        get() = _chosenCommunity

    var authMethod: AuthMethod? = null
    val verifyDigits = MutableLiveData<String>()
    var accountModel = userRepository.getAccount()

    val userProfile = userRepository.user

    var hasBadge1 = false

    var userCounters: List<CounterEntity>? = null

    private val _badge2: MutableLiveData<Badge2Status> = MutableLiveData(Badge2Status.NULL)
    val badge2: LiveData<Badge2Status>
        get() = _badge2

    var deleteInput:String? = null
    var codeSentTo :String? = null


    private val _dataUi : MutableStateFlow<SettingsUiData> = MutableStateFlow(SettingsUiData())
    val dataUi : LiveData<SettingsUiData> get() = _dataUi.asLiveData()

    init {
        fetchCommunitiesIfNotCached()
    }
    fun setBadge2Status(status: Badge2Status){
        _badge2.value = status
    }


    fun updateAccountAfterVerification(isChangeLogin:String? = null) {

        viewModelScope.launch(Dispatchers.IO) {
            if (isChangeLogin != null){
                accountModel.value?.let {
                    val user = it.apply {
                        if (isChangeLogin.isEmail) {
                            copy(email = emailLiveData.value?.lowercase())
                        } else {
                            copy(phone = phoneLiveData.value)
                        }
                    }
                    userRepository.updateAccount(user)
                }
                delay(1000)
            } else {
                hasBadge1 = true
            }
            userProfile.value?.let {
                userRepository.updateUser(it.copy(hasBadge1 = true))
            }
        }
    }

    fun userDelete() {
        _deleteUser.value = true
    }

    fun updateChangedBirthdayCalendar(calendar: Calendar) {
        _changeBirthdayCalendar.value = calendar
    }

    fun showBirthdayDialog(){
        _showBirthdayDialog.call()
    }

    fun showGenderDialog(){
        _showGenderDialog.call()
    }

    fun showCommunityDialog(){
        _showCommunityDialog.call()
    }

    fun setGender(gender: GenderType?) {
        _userGender.value = gender
    }

    override fun setFabVisibility(visibility: FloatingActionButtonVisibility) {
        _fabVisibility.value = visibility
    }
    fun getUserCounters(user: UserModel, specificCounterName: String? = null) {
        viewModelScope.launch(Dispatchers.IO) {
            val configurationNames =
                if (specificCounterName != null) listOf(specificCounterName) else
                    listOf(
                        user.counterConfigurationNames.nameChangeCounterCN,
                        user.counterConfigurationNames.communityCN,
                        user.counterConfigurationNames.birthdayCounterCN,
                        user.counterConfigurationNames.genderCounterCN
                    )

            getUserCountersByConfNamesUseCase.invoke(configurationNames)
                .catch { e -> Timber.e(e) }
                .collect { list ->
                if (specificCounterName.isNullOrEmpty()) {
                    userCounters = list
                } else {
                    userCounters?.firstOrNull { it.configurationName == specificCounterName }?.let {
                        userCounters = userCounters?.toMutableList()?.apply {
                            remove(it)
                            add(list.first())
                        }
                    }
                }
            }
        }
    }

    fun updateLanguage(language: String): LiveData<Result<UpdateUserResponse>> {
        val params = mapOf("language" to language)
        val appLanguageKey = ClevertapUserPropertyEnum.APPLANGUAGE.value
        val clevertapUserProperties = mapOf(appLanguageKey to language)
        updateUserProfileInClevertap(clevertapUserProperties)
        return userRepository.updateUser(params)
    }

    fun showChangeNameDialog(){
        _showChangeNameDialog.call()
    }

    fun changeName(name: String, userModel: UserModel) {
        val param = mapOf(ChangeAdjustProfileEnum.FIRST_NAME.value to name)
        viewModelScope.launch(Dispatchers.IO) {
            val oldModel = userModel.copy()

            userModel.firstName = name
            userCounters?.firstOrNull { it.name == userModel.counterConfigurationNames.nameChangeCounterCN }?.let {
                val newValue = it.copy(total = it.total + 1, counter = (it.counter.copy(resetTime = getTimestampAfterPeriodOfTime(System.currentTimeMillis(), 6, Calendar.MONTH))))
                userCounters = userCounters?.toMutableList()?.apply {
                    remove(it)
                    add(newValue)
                }
                updateUserCounterUseCase.invoke(newValue)
            }

            val nameKey = ClevertapUserPropertyEnum.NAME.value
            val clevertapUserProperties = mapOf(nameKey to name)

            userRepository.adjustProfileInfo(param, oldModel, clevertapUserProperties)
            firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_NAME)

            val eventPremiumType = getPremiumTypeEventProperty(userProfile.value)

            sendClevertapEvent(ClevertapEventEnum.CHANGE_NAME, mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType))
        }
    }

    fun updateBirthday(userBirthDate: String, age: Int, userModel: UserModel) {
        val param = mapOf(ChangeAdjustProfileEnum.BIRTHDATE.value to userBirthDate)
        viewModelScope.launch(Dispatchers.IO) {
            val oldModel = userModel.copy()

            userModel.age = age
            userModel.birthDate = userBirthDate
            userCounters?.firstOrNull { it.name == userModel.counterConfigurationNames.birthdayCounterCN }?.let {
                val newValue = it.copy(total = it.total + 1, counter = (it.counter.copy(resetTime = getTimestampAfterPeriodOfTime(System.currentTimeMillis(), 6, Calendar.MONTH))))
                userCounters = userCounters?.toMutableList()?.apply {
                    remove(it)
                    add(newValue)
                }
                updateUserCounterUseCase.invoke(newValue)            }

            val birthdayKey = ClevertapUserPropertyEnum.DOB.value
            val birthdayValue = createDateObjectFromBirthday(userBirthDate)
            val clevertapUserProperties = mapOf(birthdayKey to birthdayValue)

            userRepository.adjustProfileInfo(param, oldModel, clevertapUserProperties)
            PurchaselyManager.clearCache()

            firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_AGE)

            val eventPremiumType = getPremiumTypeEventProperty(userProfile.value)

            sendClevertapEvent(ClevertapEventEnum.CHANGE_AGE, mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType))
        }
    }


    fun updateGender(userGender: String, userModel: UserModel) {
        val param = mapOf(ChangeAdjustProfileEnum.GENDER.value to userGender)
        viewModelScope.launch(Dispatchers.IO) {
            val oldModel = userModel.copy()

            userModel.gender = userGender
            userCounters?.firstOrNull { it.name == userModel.counterConfigurationNames.genderCounterCN }?.let {
                val newValue = it.copy(total = it.total + 1, counter = (it.counter.copy(resetTime = getTimestampAfterPeriodOfTime(System.currentTimeMillis(), 6, Calendar.MONTH))))
                userCounters = userCounters?.toMutableList()?.apply {
                    remove(it)
                    add(newValue)
                }
                updateUserCounterUseCase.invoke(newValue)            }

            val genderKey = ClevertapUserPropertyEnum.GENDER.value
            val userGenderKey = ClevertapUserPropertyEnum.USER_GENDER.value
            val genderValue = userGender
            val clevertapUserProperties = mapOf(genderKey to genderValue, userGenderKey to genderValue)

            PurchaselyManager.clearCache()
            userRepository.adjustProfileInfo(param, oldModel, clevertapUserProperties) { syncCounters ->
                if (syncCounters) {
                    viewModelScope.launch(Dispatchers.IO) {
                        syncUserCountersUseCase.invoke()
                            .catch { e -> Timber.e(e) }
                            .collect { Timber.d("Counters updated") }
                    }
                }

            }

            if (userGender == GenderType.MAN.value) {
                firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_GENDER, mapOf(
                    FirebaseAnalyticsParameterName.FEMALE_TO_MALE.value to 1L))
            } else {
                firebaseLogEvent(FirebaseAnalyticsEventsName.CHANGE_GENDER, mapOf(
                    FirebaseAnalyticsParameterName.MALE_TO_FEMALE.value to 1L))
            }

            val eventPremiumType = getPremiumTypeEventProperty(userProfile.value)

            val genderSwitch= if(userGender == GenderType.WOMAN.value){
                ClevertapGenderSwitchValues.MALE_TO_FEMALE.values
            }else{
                ClevertapGenderSwitchValues.FEMALE_TO_MALE.values
            }

            sendClevertapEvent(
                ClevertapEventEnum.CHANGE_GENDER, mapOf(
                    ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                    ClevertapEventPropertyEnum.GENDER_SWITCH.propertyName to genderSwitch))

        }
    }

    fun updateCommunity(communityInfo: CommunityInfo, userModel: UserModel) {
        val param = mapOf(ChangeAdjustProfileEnum.COMMUNITY.value to communityInfo.id)
        viewModelScope.launch(Dispatchers.IO) {
            val oldModel = userModel.copy()

            userModel.communityInfo = communityInfo
            userCounters?.firstOrNull { it.name == userModel.counterConfigurationNames.communityCN }?.let {
                val newValue = it.copy(total = it.total + 1, counter = (it.counter.copy(resetTime = getTimestampAfterPeriodOfTime(System.currentTimeMillis(), 6, Calendar.MONTH))))
                userCounters = userCounters?.toMutableList()?.apply {
                    remove(it)
                    add(newValue)
                }
                updateUserCounterUseCase.invoke(newValue)
            }

            userRepository.adjustProfileInfo(param, oldModel) { syncCounters ->
                if (syncCounters) {
                    viewModelScope.launch(Dispatchers.IO) {
                        syncUserCountersUseCase.invoke()
                            .catch { e -> Timber.e(e) }
                            .collect { Timber.d("Counters updated") }
                    }
                }

            }

            PurchaselyManager.clearCache()
            sendCommunityChangeEvent()
        }
    }

    private fun sendCommunityChangeEvent() {
        val communityUpdateTo =
            "${duaSharedPrefs.getUserCommunityNameBeforeChange()} to ${chosenCommunity.value?.communityName}}"
        firebaseLogEvent(FirebaseAnalyticsEventsName.COMMUNITY_CHANGED, mapOf(
            FirebaseAnalyticsParameterName.COMMUNITY_UPDATED_TO.value to communityUpdateTo))

        sendClevertapEvent(ClevertapEventEnum.COMMUNITY_CHANGED, mapOf(
            ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
               userProfile.value),
            ClevertapEventPropertyEnum.COMMUNITY_UPDATED_TO.propertyName to communityUpdateTo))


    }



    fun clearViewModelData() {
        _currentPassword.value = null
        _newPassword.value = null
        _newPasswordVerify.value = null
        _emailLiveData.value = null
        _phoneLiveData.value = null
        _addEmailAddress.value = null
        _addPhoneNumber.value = null
    }

    fun getUser(): LiveData<UserModel> {
        return userRepository.user
    }

    fun updateUserInvisible(userModel: UserModel) {
        viewModelScope.launch { userRepository.updateUserInDB(userModel) }
    }

    fun updateShowFriends(userModel: UserModel){
        viewModelScope.launch { userRepository.updateUserInDB(userModel) }
    }

    private var timer: CountDownTimer? = null
    fun setTimer() {
        createTimer()
        _showTimer.value = true
    }

    private fun createTimer() {
        val triggerTime = System.currentTimeMillis() + 60 * 1000
        timer = object : CountDownTimer(triggerTime - System.currentTimeMillis(), 1000) {
            override fun onTick(p0: Long) {
                var millisUntilFinished = p0
                val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                millisUntilFinished -= TimeUnit.MINUTES.toMillis(minutes)

                val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished)

                val string = String.format("%02d:%02d", minutes, seconds)
                _elapsedTime.value = string

                if (millisUntilFinished <= 0) {
                    resetTimer()
                }
            }

            override fun onFinish() {
                resetTimer()
            }


        }
        timer?.start()
    }

    private fun resetTimer() {
        timer?.cancel()
        _elapsedTime.value = "00:00"
        _showTimer.value = false
    }

    fun changePassword() {
        if(_newPasswordVerify.value != _newPassword.value) {
            _changePasswordResultLiveData.postValue(ChangePasswordAuthResult.Error(PasswordsDoNotMatchException()))
            return
        }

        _changePasswordResultLiveData.postValue(ChangePasswordAuthResult.Loading())
        val oldPassword = _currentPassword.value
        val newPassword = _newPassword.value
        val changePasswordModel = ChangePasswordModel(oldPassword ?: "", newPassword ?: "")

        AWSInteractor.resetPassword(changePasswordModel, object : ChangePasswordCallBack {
            override fun onResult(changeResult: Void?) {
                _changePasswordResultLiveData.postValue(ChangePasswordAuthResult.Success(changeResult))

                Timber.tag("CHANGERESULT").d("$changeResult")
            }

            override fun onError(e: Exception) {
                _changePasswordResultLiveData.postValue(ChangePasswordAuthResult.Error(e))

                Timber.tag("CHANGERESULT").e(e, "forgot password error")

            }
        })
    }

    fun validateCurrentPassword() {
        _currentPasswordValidity.postValue(ChangePasswordAuthResult.Loading())
        val oldPassword = _currentPassword.value
        val newPassword = _currentPassword.value
        val changePasswordModel = ChangePasswordModel(oldPassword ?: "", newPassword ?: "")
        AWSInteractor.resetPassword(changePasswordModel, object : ChangePasswordCallBack {
            override fun onResult(changeResult: Void?) {
                _currentPasswordValidity.postValue(ChangePasswordAuthResult.Success(changeResult))
            }

            override fun onError(e: Exception) {
                _currentPasswordValidity.postValue(ChangePasswordAuthResult.Error(e))
                Timber.tag("LIMITEXCEEDED").e(e, "forgot password error")
            }

        })
    }
    fun fetchCommunitiesIfNotCached()  {
        viewModelScope.launch {
            val hasCachedCommunities = userRepository.hasCommunitiesCached()
            if (!hasCachedCommunities) {
                getCommunitiesList()
            }
        }
    }

   private fun getCommunitiesList() {
        viewModelScope.launch(Dispatchers.IO) {
          userRepository.getCommunities()
        }
    }

    fun toggleInvisibleMode(isInvisible: Boolean): LiveData<Result<Boolean>>? {
        if(!_dataUi.value.ghostActiveDialogShownOnce && isInvisible && _dataUi.value.isGhostModeEnabled) {
            _dataUi.update {
                it.copy(isInvisible = true, showGhostActivateDialog = true)
            }
            return null
        }

        val params = mapOf("profile" to mapOf("isInvisible" to isInvisible))
        return userRepository.hideProfile(isInvisible, params)
    }

    fun toggleGhostMode(isChecked: Boolean, eventSource: ClevertapGhostSourceValues){
        val params = mapOf("profile" to mapOf("isGhost" to isChecked))
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.ghostMode(isChecked,params)
                .catch {e->
                   withContext(Dispatchers.Main) {
                       _dataUi.update {
                           it.copy(error = e)
                       }
                   }
                }
                .collect{
                    withContext(Dispatchers.Main) {
                        when (it){
                            is Resource.Success -> {
                                sendGhostModeEvents(isChecked, eventSource,userProfile.value)
                                _dataUi.update { uiData ->
                                    uiData.copy(
                                        ghostMode = it.data,
                                        error = null,
                                        showGhostInfoDialog = it.data,
                                        isInvisible = !it.data
                                    )
                                }
                            }

                            else -> {}
                        }
                    }
                }
        }
    }

    private fun sendGhostModeEvents(
        isChecked: Boolean,
        eventSource: ClevertapGhostSourceValues,
        userProfile:UserModel?
    ) {
        val ghostStatus =
            if (isChecked) ClevertapGhostStatusValues.ON.value else ClevertapGhostStatusValues.OFF.value
        val ghostSource =
            if (userProfile?.profile?.isInvisible == true) ClevertapGhostSourceValues.HIDE_PROFILE.value else eventSource.value
        sendClevertapEvent(
            event = ClevertapEventEnum.GHOST,
            properties = mapOf(
                ClevertapEventPropertyEnum.GHOST_STATUS.propertyName to ghostStatus,
                ClevertapEventPropertyEnum.GHOST_SOURCE.propertyName to ghostSource,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    userProfile
                ),
                ClevertapEventPropertyEnum.COMMUNITY.propertyName to userProfile?.communityInfo?.communityName
            )
        )

        //Since the GA property values are the same with the clevertap property values, we use the same variables
        firebaseLogEvent(
            eventName = FirebaseAnalyticsEventsName.GHOST,
            params = mapOf(
                ClevertapEventPropertyEnum.GHOST_STATUS.propertyName to ghostStatus,
                ClevertapEventPropertyEnum.GHOST_SOURCE.propertyName to ghostSource,
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to getPremiumTypeEventProperty(
                    userProfile
                )
            )
        )

    }


    fun updateFriendsShip(status:Boolean):LiveData<Result<UpdateUserResponse>>{
        val params = mapOf("profile" to mapOf("hasFriendshipEnabled" to status))
        return userRepository.updateUser(params)
    }

    fun getTags(language: String) {
        viewModelScope.launch {
        userRepository.getAllTags(language)
            .catch { e ->
                e.printStackTrace()
                setTagsFetchedSuccesfully(false)
            }.collect {
                userRepository.updateTags(it.tags)
                duaSharedPrefs.setTagsVersion(it.version)
                duaSharedPrefs.setLanguageChange(false)
                setTagsFetchedSuccesfully(true)
            }
        }
    }

    fun setTagsFetchedSuccesfully(fetchedSuccessfully: Boolean) {
        _tagsFetched.value = fetchedSuccessfully
    }

    fun setShowSpinningDialog(show: Boolean) {
        _showSpinningDialog.value = show
    }

    fun setAppVersion(version: String) {
        _appVersion.value = version
    }

    fun setChosenCommunity(model: CommunityInfo) {
        _chosenCommunity.value = model
    }


    fun addPhoneNumber() {
        val phone = phoneLiveData.value
        if(codeSentTo != phone) {
            phone?.let {
                _addPhoneNumber.postValue(Result.Loading)

                resetTimer()
                setTimer()
                viewModelScope.launch(Dispatchers.IO) {
                    val model = AddPhoneNumberModel(it)
                    userRepository.addPhoneNumber(model)
                            .catch { exception ->
                                codeSentTo = null

                                if (exception is Exception)
                                    _addPhoneNumber.postValue(Result.Error(exception))
                            }
                            .collect { value ->
                                when (value) {
                                    is Resource.Success -> {
                                        Timber.tag("ADDPHONENUMBER").d("Sent Code")
                                        codeSentTo = it
                                        _addPhoneNumber.postValue(Result.Success(""))
                                    }
                                    else -> {}
                                }
                            }
                }
            }
        } else {
            Timber.tag("ADDPHONENUMBER").d("Code already sent!")
            _addPhoneNumber.postValue(Result.Success(""))
        }
    }

    fun addEmailAddress() {
        val email = emailLiveData.value?.lowercase()
        if(codeSentTo != email) {
            email?.let {
                _addEmailAddress.postValue(Result.Loading)

                resetTimer()
                setTimer()
                viewModelScope.launch(Dispatchers.IO) {
                    val model = AddEmailModel(it)
                    userRepository.addEmailAddress(model)
                            .catch { exception ->
                                codeSentTo = null

                                if (exception is Exception)
                                    _addEmailAddress.postValue(Result.Error(exception))
                            }
                            .collect { value ->
                                when (value) {
                                    is Resource.Success -> {
                                        Timber.tag("ADDPHONENUMBER").d("Sent Code")
                                        codeSentTo = it
                                        _addEmailAddress.postValue(Result.Success(""))
                                    }
                                    is Resource.Loading -> {
                                    }
                                    is Resource.Error -> {
                                    }
                                }
                            }
                }
            }
        } else {
            Timber.tag("ADDPHONENUMBER").d("Code already sent!")
            _addEmailAddress.postValue(Result.Success(""))
        }
    }

    fun verifyPhoneCode(code: String) {
        _verifyPhoneNumber.postValue(Result.Loading)

        viewModelScope.launch(Dispatchers.IO) {
            val model = VerifyCodeModel(code)
            userRepository.verifyPhoneCode(model)
                    .catch { exception ->
                        if (exception is Exception)
                            _verifyPhoneNumber.postValue(Result.Error(exception))
                    }
                    .collect { value ->
                        when (value) {
                            is Resource.Success -> {
                                _verifyPhoneNumber.postValue(Result.Success(value.data))
                            }
                            is Resource.Loading -> {
                            }
                            is Resource.Error -> {
                            }
                        }
                    }
        }
    }

    fun verifyEmailAddress(code: String) {
        _verifyEmailAddress.postValue(Result.Loading)

        viewModelScope.launch(Dispatchers.IO) {
            val model = VerifyCodeModel(code)
            userRepository.verifyEmailCode(model)
                    .catch { exception ->
                        if (exception is Exception)
                            _verifyEmailAddress.postValue(Result.Error(exception))
                    }
                    .collect { value ->
                        when (value) {
                            is Resource.Success -> {
                                _verifyEmailAddress.postValue(Result.Success(value.data))
                            }
                            is Resource.Loading -> {
                            }
                            is Resource.Error -> {
                            }
                        }
                    }
        }
    }

    fun onResendCodeClicked() {
        codeSentTo = null
        if (emailLiveData.value != null) {
            addEmailAddress()
        } else if (phoneLiveData.value != null) {
            addPhoneNumber()
        }
    }

    override var selectedCountryCodeAsInt: Int? = null

    override fun setPhoneNumber(phone: String) {
        _phoneLiveData.value = phone
    }

    override fun returnEmailLiveData(): MutableLiveData<String> {
        return _emailLiveData
    }


    fun navigatedFromVerifyInstachat() {
        _navigatedFromVerifyInstachat.value = true
    }

    //Download Data region

    var timeStamp : Long = 0L
    var getUrl = ""
    var expiredDate = ""
    var createdDate = ""

    private val _downloadDataStatus: MutableLiveData<DownloadDataStatus> = MutableLiveData(DownloadDataStatus.REQUESTING_DOWNLOAD_DATA)
    val downloadDataStatus: LiveData<DownloadDataStatus>
        get() = _downloadDataStatus

    private val _downloadDirectData =  SingleLiveData<Boolean>()
    val downloadDirectData: LiveData<Boolean>
        get() = _downloadDirectData

    private val _showHideMainLoader=  SingleLiveData<Boolean>()
    val showHideMainLoader: LiveData<Boolean>
        get() = _showHideMainLoader

    private val _showHideDownloadLoader=  SingleLiveData<Boolean>()
    val showHideDownloadLoader: LiveData<Boolean>
        get() = _showHideDownloadLoader

    fun setDownloadDirectData(){
        _downloadDirectData.postValue(true)
    }

    fun setDownloadDataStatus(status: DownloadDataStatus){
        _downloadDataStatus.postValue(status)
    }

    fun setShowHideMainLoader(value: Boolean){
        _showHideMainLoader.postValue(value)
    }
    fun setShowHideDownloadLoader(value: Boolean){
        _showHideDownloadLoader.postValue(value)
    }

    fun downloadData(shouldDownloadDataAgain : Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.downloadMyData()
                .catch { ex ->
                    withContext(Dispatchers.Main) {
                        ex.printStackTrace()
                        when (ex) {
                            is UserDataNotProcessedeException -> {
                                setDownloadDataStatus(DownloadDataStatus.PROCESSING_DOWNLOAD_DATA)
                            }
                            is UserDataRequestNoFoundException -> {
                                setDownloadDataStatus(DownloadDataStatus.REQUESTING_DOWNLOAD_DATA)
                            }
                        }
                        setShowHideMainLoader(false)
                    }
                }.collect {
                    when (it) {
                        is Resource.Success -> {
                            timeStamp = System.currentTimeMillis()
                            getUrl = it.data.url
                            expiredDate = it.data.expire
                            createdDate = it.data.created
                            if (shouldDownloadDataAgain){
                                setDownloadDirectData()
                            }
                            setDownloadDataStatus(DownloadDataStatus.DOWNLOAD_DATA)
                            setShowHideMainLoader(false)
                        }
                        is Resource.Loading -> {
                        }

                        else -> {}
                    }
                }
        }
    }


    fun postUserData (){
        viewModelScope.launch(Dispatchers.IO){
            userRepository.postUserData()
                .catch {ex ->
                    withContext(Dispatchers.Main){
                        ex.printStackTrace()
                        when(ex){
                            is UserRequestAlreadyExistsException -> {
                                downloadData()
                                setDownloadDataStatus(DownloadDataStatus.PROCESSING_DOWNLOAD_DATA)
                                logError(ErrorStatus.USER_REQUEST_ALREADY_EXISTS_EXCEPTION)
                            }
                            else -> {
                                ToastUtil.toast(R.string.smthg_went_wrong)
                                logError(ErrorStatus.DOWNLOAD_DATA)
                            }
                        }
                    }
                }.collect {
                    when (it) {
                        is Resource.Success -> {
                            firebaseLogEvent(FirebaseAnalyticsEventsName.REQUEST_DATA)
                            setDownloadDataStatus(DownloadDataStatus.PROCESSING_DOWNLOAD_DATA)
                        }

                        is Resource.Loading -> {
                        }

                        else -> {}
                    }
                }
        }
    }

    fun updateGhostModeState(enabled: Boolean, isGhostModeEnabled: Boolean, isInvisible: Boolean) {
        _dataUi.update {
            it.copy(ghostMode = enabled, isGhostModeEnabled = isGhostModeEnabled, isInvisible = isInvisible,showGhostInfoDialog = false, showGhostActivateDialog = false)
        }
    }

    fun updateGhostModeState(error: Throwable?){
        _dataUi.update {
            it.copy(error = error)
        }
    }

    fun updateGhostModeState(showGhostActivateDialog: Boolean){
        _dataUi.update {
            it.copy(showGhostActivateDialog = showGhostActivateDialog)
        }
    }

    fun onGhostActiveDialogShown() {
        _dataUi.update {
            it.copy(ghostActiveDialogShownOnce = true, showGhostActivateDialog = false, upgradedToPremium = false)
        }
    }

    fun updateGhostModeState(upgradedToPremium: Boolean?) {
       _dataUi.update {
           it.copy(upgradedToPremium = upgradedToPremium == true)
       }
    }

    //End region

}

data class SettingsUiData(
    val ghostMode: Boolean = false,
    val isGhostModeEnabled: Boolean = false,
    val showGhostInfoDialog: Boolean = false,
    val showGhostActivateDialog: Boolean = false,
    val upgradedToPremium: Boolean = false,
    val ghostActiveDialogShownOnce : Boolean = false,
    val isInvisible: Boolean = false,
    val error: Throwable? = null
)
