package com.duaag.android.settings.fragments.account_settings.delete_account

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapEventSourceValues
import com.duaag.android.clevertap.ClevertapGhostSourceValues
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.DeleteAccountPermanentlyFragmentBinding
import com.duaag.android.exceptions.PremiumException
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.premium_subscription.PremiumActivity
import com.duaag.android.premium_subscription.models.PurchaselyPlacement
import com.duaag.android.premium_subscription.openPremiumPaywall
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.fragments.account_settings.ghost_mode.IncognitoModeDialogFragment
import com.duaag.android.settings.fragments.account_settings.ghost_mode.IncognitoModeType
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.setVisibility
import com.duaag.android.utils.updateLocale
import javax.inject.Inject

class DeleteAccountPermanentlyFragment : Fragment(), IncognitoModeDialogFragment.IncognitoModeDialogListener {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) { viewModelFactory }

    private var _binding: DeleteAccountPermanentlyFragmentBinding? = null
    private val binding get() = _binding!!

    private val premiumBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val isPremium = intent?.getBooleanExtra(PremiumActivity.PREMIUM_INTENT_BROADCAST, false)
                ?: false
            settingViewModel.updateGhostModeState(isPremium)
        }
    }

    companion object {
        fun newInstance() = DeleteAccountPermanentlyFragment()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {

        _binding = DeleteAccountPermanentlyFragmentBinding.inflate(inflater)

        //register receiver for premium subscription events
        val premiumSubscriptionFilter = IntentFilter(PremiumActivity.PREMIUM_INTENT)
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(premiumBroadcastReceiver, premiumSubscriptionFilter)

        binding.deleteAccDesc.text =
            getString(R.string.after_30_days, "30", getString(R.string.app_name))

        binding.let {
            it.lifecycleOwner = viewLifecycleOwner
            it.cardDeletePermanently.setOnSingleClickListener {
                val action =
                    DeleteAccountPermanentlyFragmentDirections.actionDeleteAccountPermanentlyToDeleteAccountReasons()
                findNavController().navigate(action)

                firebaseLogEvent(FirebaseAnalyticsEventsName.DELETE_ACCOUNT_PERMANENTLY_BUTTONCLICK)
            }
        }
        sendScreenEvent()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        settingViewModel.getUser().observe(viewLifecycleOwner) { userModel ->
            userModel?.let {
                settingViewModel.updateGhostModeState(
                    it.profile.isGhost,
                    it.settings.isGhostModeEnabled,
                    it.isInvisible()
                )
            }
        }

        settingViewModel.dataUi.observe(viewLifecycleOwner) { uiData ->
            setVisibility(
                binding.ghostModeView.root,
                uiData.isGhostModeEnabled && !uiData.ghostMode
            )
            if (uiData.error is PremiumException) {
                //clear error
                settingViewModel.updateGhostModeState(error=null)
                requireActivity().openPremiumPaywall(
                    eventSourceClevertap = ClevertapEventSourceValues.GHOST_MODE,
                    placementId = PurchaselyPlacement.GHOST_MODE.id,
                    userModel = settingViewModel.userProfile.value
                )
            }

            if (uiData.showGhostInfoDialog) IncognitoModeDialogFragment.showInfo(requireContext(), childFragmentManager)

            if (uiData.showGhostActivateDialog) {
                settingViewModel.onGhostActiveDialogShown()
                IncognitoModeDialogFragment.showActivate(
                    requireContext(), childFragmentManager)
            }
        }

        binding.ghostModeView.button.setOnSingleClickListener {
            if (!NetworkChecker.isNetworkConnected(requireContext())) {
                ToastUtil.toast(getString(R.string.no_internet_connection))
                return@setOnSingleClickListener
            }
            settingViewModel.toggleGhostMode(
                isChecked = true,
                eventSource = ClevertapGhostSourceValues.DELETE_ACCOUNT)
        }

    }

    private fun sendScreenEvent() {
        val eventPremiumType =
            getPremiumTypeEventProperty(settingViewModel.userProfile.value)

        sendClevertapEvent(
            ClevertapEventEnum.DELETE_ACCOUNT_SCREENVIEW, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(premiumBroadcastReceiver)
        _binding = null
    }

    override fun onButtonClick(dialog: DialogFragment, type: IncognitoModeType) {
       when(type){
           IncognitoModeType.INFO -> {
               dialog.dismissAllowingStateLoss()
               requireActivity().finish()
           }
           IncognitoModeType.ACTIVE -> {
               settingViewModel.toggleGhostMode(
                   isChecked = true,
                   eventSource = ClevertapGhostSourceValues.DELETE_ACCOUNT
               )
               settingViewModel.updateGhostModeState(showGhostActivateDialog = false)
               dialog.dismissAllowingStateLoss()
           }
          else -> {/*not used*/}
       }
    }

    override fun onDismiss(dialog: DialogFragment, type: IncognitoModeType) {
        when(type){
            IncognitoModeType.INFO -> {
                requireActivity().finish()
            }
           else -> {/*not used*/}
        }
    }
}