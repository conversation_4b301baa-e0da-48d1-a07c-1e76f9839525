package com.duaag.android.settings.fragments.account_settings.delete_account.love_story

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.postDelayed
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.LoveStoryFragmentBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.fragments.account_settings.delete_account.DeleteAccountReasonsViewModel
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.addDifferentTypeface
import com.duaag.android.utils.convertDpToPx
import com.duaag.android.utils.hideKeyboard
import com.duaag.android.utils.onKeyboardDone
import com.duaag.android.utils.openAsChromeTab
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.uxcam.UXCam
import javax.inject.Inject

class LoveStoryBottomSheetFragment : BottomSheetDialogFragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val viewModel by viewModels<LoveStoryViewModel>({ activity as SettingsActivity }) { viewModelFactory }
    private var _binding: LoveStoryFragmentBinding? = null
    private val binding get() = _binding
    private var listener: LoveStorySheetListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateLocale(context)
        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
        listener = parentFragment as? LoveStorySheetListener
            ?: throw ClassCastException("$context must implement LoveStorySheetListener")

    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        dialog.setOnShowListener { dialog ->
            val bottomSheetDialog = dialog as BottomSheetDialog
            val parentLayout =
                bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            parentLayout?.let { it ->
                val behaviour = BottomSheetBehavior.from(it)
                setupFullHeight(it)
                behaviour.state = BottomSheetBehavior.STATE_EXPANDED
                behaviour.skipCollapsed = true
                behaviour.isHideable = true
                behaviour.isDraggable = false
            }
        }
        dialog.dismissWithAnimation = true
        return dialog
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = LoveStoryFragmentBinding.inflate(inflater)
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE or WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        sendScreenEvent()
        return binding?.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding?.description?.let { addDifferentTypeface(it,R.string.tell_your_story_description) }
        UXCam.occludeSensitiveView(binding?.contactDetailsText)
        val thumbnailUrl = viewModel.dataUi.value?.getThumbnailUrl(requireContext())
        if (thumbnailUrl != null) {
            binding?.thumbnailImage?.let {
                Glide.with(it)
                    .load(thumbnailUrl)
                    .into(it)
            }
        }
        viewModel.accountModel.observe(viewLifecycleOwner) { accountModel ->
            viewModel.initData(accountModel)
        }
        viewModel.dataUi.observe(viewLifecycleOwner) { uiData ->
            handleButtonState(binding,uiData)
            handleLoveStorySubmission(viewModel,listener,uiData)
            handleApiError(uiData)
            handleContactDetailsState(binding,uiData)
        }
        binding?.emailAddressInput?.addTextChangedListener(afterTextChanged = {
            val input = it.toString()
            viewModel.updateEmailAddress(input)
        })

        binding?.storyInput?.addTextChangedListener {
            viewModel.updateStory(it?.toString())
        }

        binding?.thumbnailCard?.setOnSingleClickListener {
            viewModel.dataUi.value?.getVideoUrl(requireContext()).openAsChromeTab(it.context)
        }

        binding?.storyInput?.onKeyboardDone {
            hideKeyboard()
        }

        binding?.emailAddressInput?.onKeyboardDone {
            binding?.closeButton?.performClick()
        }

        binding?.closeButton?.setOnSingleClickListener {
            handleCloseButtonClick(binding,viewModel)
        }

        binding?.addEmailAddressButton?.setOnSingleClickListener {
            handleAddEmailAddressButtonClick(binding,viewModel)
        }

        binding?.editContactDetailsButton?.setOnSingleClickListener {
            handleEditContactDetailsButtonClick(binding,viewModel)
        }

        binding?.storyInput?.onFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
            handleStoryInputFocusChange(binding,viewModel,hasFocus)
        }

        binding?.emailAddressInput?.onFocusChangeListener =
            View.OnFocusChangeListener { _, hasFocus ->
                handleEmailAddressInputFocusChange(binding,viewModel,hasFocus)
            }

        binding?.btnSubmit?.setOnSingleClickListener {
            listener?.onContinueClicked()
            dismissAllowingStateLoss()
        }

        binding?.navigationIcon?.setOnSingleClickListener {
            dismissAllowingStateLoss()
        }

        binding?.notNowButton?.setOnSingleClickListener {
            listener?.onContinueClicked()
            dismissAllowingStateLoss()
        }
    }

    private fun handleButtonState(binding: LoveStoryFragmentBinding?, uiData: LoveStoryUiData) {
        binding?.btnSubmit?.isEnabled = uiData.enableSubmit
    }

    private fun handleLoveStorySubmission(
        viewModel: LoveStoryViewModel,
        listener: LoveStorySheetListener?,
        uiData: LoveStoryUiData
    ) {
        if (uiData.loveStorySubmitted == true) {
            listener?.onContinueClicked()
            viewModel.onLoveStorySubmit()
            dismissAllowingStateLoss()
        }
    }

    private fun handleApiError(uiData: LoveStoryUiData) {
        if (uiData.loveStoryApiError != null) {
            ToastUtil.toast(getString(R.string.something_is_not_right))
            viewModel.resetLoveStoryError()
        }
    }

    private fun handleContactDetailsState(
        binding: LoveStoryFragmentBinding?,
        uiData: LoveStoryUiData
    ) {
        when (uiData.contactDetailsState) {
            ContactDetailsUiState.ADD -> showAddContactDetails(binding)
            ContactDetailsUiState.EDIT -> showEditContactDetails(binding,uiData.emailAddress ?: "")
            ContactDetailsUiState.INPUT -> showInputContactDetails(binding)
            null -> {
               /*not used*/
            }
        }
    }

    private fun showAddContactDetails(binding: LoveStoryFragmentBinding?) {
        binding?.apply {
            addEmailAddressButton.visibility = View.VISIBLE
            contactDisplayDetails.visibility = View.GONE
            contactInputDetails.visibility = View.GONE
        }
    }

    private fun showEditContactDetails(binding: LoveStoryFragmentBinding?, emailAddress: String) {
        binding?.apply {
            addEmailAddressButton.visibility = View.GONE
            contactInputDetails.visibility = View.GONE
            contactDisplayDetails.visibility = View.VISIBLE
            contactDetailsText.text = emailAddress
        }
    }

    private fun showInputContactDetails(binding: LoveStoryFragmentBinding?) {
        binding?.apply {
            addEmailAddressButton.visibility = View.GONE
            contactDisplayDetails.visibility = View.GONE
            contactInputDetails.visibility = View.VISIBLE
        }
    }

    private fun handleCloseButtonClick(binding: LoveStoryFragmentBinding?,viewModel: LoveStoryViewModel) {
        val contactDetailsUiState =
            if (!viewModel.dataUi.value?.emailAddress.isNullOrEmpty()) ContactDetailsUiState.EDIT
            else ContactDetailsUiState.ADD
        viewModel.updateContactDetailsState(contactDetailsUiState)
        clearFocus(binding)
        binding?.contentContainer?.postDelayed({
            hideKeyboard()
        }, 300)
    }

    private fun handleAddEmailAddressButtonClick(
        binding: LoveStoryFragmentBinding?,
        viewModel: LoveStoryViewModel
    ) {
        viewModel.updateContactDetailsState(ContactDetailsUiState.INPUT)
        binding?.emailAddressInput?.apply {
            setText(viewModel.dataUi.value?.emailAddress)
            showKeyboard()
        }
    }
    private fun handleEditContactDetailsButtonClick(
        binding: LoveStoryFragmentBinding?,
        viewModel: LoveStoryViewModel
    ) {
        viewModel.updateContactDetailsState(ContactDetailsUiState.INPUT)
        binding?.emailAddressInput?.apply {
            setText(viewModel.dataUi.value?.emailAddress)
            showKeyboard()
        }
    }

    private fun handleStoryInputFocusChange(binding:LoveStoryFragmentBinding?,viewModel: LoveStoryViewModel, hasFocus: Boolean) {
        if (hasFocus) {
            smoothScrollToStory(binding)
        }
    }

    private fun handleEmailAddressInputFocusChange(binding: LoveStoryFragmentBinding?,viewModel: LoveStoryViewModel,hasFocus: Boolean) {
        if (hasFocus) {
            smoothScrollToEmailAddress(binding)
        }
    }

    private fun handleBtnSubmitClick() {
        showConfirmationDialog(submit = true)
    }

    @SuppressLint("InflateParams")
    private fun showConfirmationDialog(submit: Boolean = false) {
        val builder = AlertDialog.Builder(requireContext(), R.style.AlertDialogButtonTheme)

        builder.apply {

            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setTitle(resources.getString(R.string.warning_exclamation))
                .setMessage(resources.getString(R.string.delete_account_message))
                .setNegativeButton(resources.getString(R.string.cancel)) { dialog, which ->
                    dialog.cancel()
                }
                .setPositiveButton(resources.getString(R.string.delete)) { dialog, which ->
                    if (!submit) {
                        listener?.onContinueClicked()
                        dismissAllowingStateLoss()
                    } else viewModel.submitLoveStory()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(
                    ContextCompat.getColor(
                        context,
                        R.color.red_500
                    )
                )
            }
            show()
        }
    }

    private fun clearFocus(binding: LoveStoryFragmentBinding?) {
        binding?.storyInput?.clearFocus()
        binding?.emailAddressInput?.clearFocus()
    }

    private fun smoothScrollToEmailAddress(binding: LoveStoryFragmentBinding?) {
        binding?.contentContainer?.postDelayed(300) {
            binding.contactInputDetails.bottom?.let {
                binding.contentContainer.smoothScrollTo(
                    0,
                    it.plus(convertDpToPx(12F)).toInt()
                )
            }
        }
    }

    private fun smoothScrollToStory(binding: LoveStoryFragmentBinding?) {

        binding?.contentContainer?.postDelayed(300) {
            binding.storyInputWrapper.bottom.let {
                binding.contentContainer.smoothScrollTo(
                    0,
                    it.plus(convertDpToPx(12F)).toInt()
                )
            }

        }
    }

    private fun sendScreenEvent() {
        val eventPremiumType =
            getPremiumTypeEventProperty(viewModel.user.value)

        sendClevertapEvent(
            ClevertapEventEnum.TELL_YOUR_STORY_SCREENVIEW, mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener = null
        _binding = null
    }

    private fun setupFullHeight(bottomSheet: View) {
        val layoutParams = bottomSheet.layoutParams
        layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
        bottomSheet.layoutParams = layoutParams
    }

    interface LoveStorySheetListener {
        fun onContinueClicked()
    }

    companion object {
        const val TAG = "LoveStoryBottomSheetFragment"

        fun newInstance(): LoveStoryBottomSheetFragment =
            LoveStoryBottomSheetFragment()
    }
}