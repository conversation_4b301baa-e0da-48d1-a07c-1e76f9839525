package com.duaag.android.settings.fragments

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.BuildConfig
import com.duaag.android.R
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.SocialMediaFragmentBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.models.SocialMediaDataModel
import com.duaag.android.user.DuaAccount
import com.duaag.android.utils.openWebView
import com.duaag.android.utils.setDivider
import com.duaag.android.utils.updateLocale


class SocialMediaFragment : Fragment() {

    private var _binding : SocialMediaFragmentBinding? = null
    private val binding get() = _binding!!


    companion object {
        fun newInstance() = SocialMediaFragment()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = SocialMediaFragmentBinding.inflate(inflater)

        initializeRecyclerView(binding.socialMediaRv,getAdapter(getRecyclerViewItems()))
        return binding.root
    }


    private fun initializeRecyclerView(recyclerView: RecyclerView?, adapter: SocialMediaAdapter) {
        recyclerView?.setHasFixedSize(true)
        recyclerView?.setDivider(R.drawable.recyclerview_divider)
        recyclerView?.adapter = adapter
        }


    override fun onResume() {
        super.onResume()
        (requireActivity() as SettingsActivity).setToolbarTitle(getString(R.string.social_media))
    }

    private fun getAdapter(items: List<SocialMediaDataModel>): SocialMediaAdapter {
        return SocialMediaAdapter(items, SocialMediaClickListener { item ->
            when (item.socialMediaTitle) {
                getString(R.string.facebook) -> {
                    sendSocialMediaReferralEvent(getString(R.string.facebook))

                    val duaFaceBookUrlApp = Uri.parse(getString(R.string.facebook_url_forwarder_in_app))

                    val uri ="$duaFaceBookUrlApp${getString(R.string.app_facebook_link_in_browser)}"

                    isPackageInstalled(getString(R.string.facebook_package_name), uri, getString(R.string.app_facebook_link_in_browser), requireActivity())

                }
               getString(R.string.twitter) -> {
                   sendSocialMediaReferralEvent(getString(R.string.twitter))

                   isPackageInstalled(getString(R.string.twitter_package_name),getString(R.string.app_twitter_link_in_app),getString(R.string.app_twitter_link_in_browser), requireActivity())

               }
                getString(R.string.instagram) -> {
                    sendSocialMediaReferralEvent(getString(R.string.instagram))

                    isPackageInstalled(getString(R.string.instagram_package_name),getString(R.string.app_instagram_link_in_app),getString(R.string.app_instagram_link_in_browser), requireActivity())

                }
                getString(R.string.linkedIn) -> {
                    sendSocialMediaReferralEvent(getString(R.string.linkedIn))

                    isPackageInstalled(getString(R.string.linkedin_package_name),getString(R.string.app_linkedin_link_in_app),getString(R.string.app_linkedin_link_in_browser), requireActivity())

                }
                getString(R.string.pinterest) -> {
                    sendSocialMediaReferralEvent(getString(R.string.pinterest))

                    isPackageInstalled(getString(R.string.pinterest_package_name),getString(R.string.app_pinterest_link_in_app),getString(R.string.app_pinterest_link_in_browser), requireActivity())

                }
               getString(R.string.youtube)-> {
                   sendSocialMediaReferralEvent(getString(R.string.youtube))

                   isPackageInstalled(getString(R.string.youtube_package_name), getString(R.string.app_youtube_link_in_app), getString(R.string.app_youtube_link_in_browser), requireActivity())

               }

            }
        })
    }

    private fun sendSocialMediaReferralEvent(socialMedia: String) {
        val eventPremiumType = getPremiumTypeEventProperty(DuaAccount.user)
        sendClevertapEvent(
            ClevertapEventEnum.SOCIAL_MEDIA_REFERRAL,
            mapOf(
                ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType,
                ClevertapEventPropertyEnum.SOCIAL_MEDIA.propertyName to socialMedia
            )
        )
    }


    private fun getRecyclerViewItems() : List<SocialMediaDataModel>{
      val items by lazy {
                mutableListOf(
                    SocialMediaDataModel(R.drawable.ic_facebook_icon, getString(R.string.facebook)),
                    SocialMediaDataModel(R.drawable.ic_twitter_icon, getString(R.string.twitter)),
                    SocialMediaDataModel(R.drawable.instagram_logo, getString( R.string.instagram)),
                    SocialMediaDataModel(R.drawable.ic_linkedin_icon, getString(R.string.linkedIn)),
                    SocialMediaDataModel(R.drawable.ic_pinterest_icon,getString( R.string.pinterest)),
                    SocialMediaDataModel(R.drawable.ic_youtube_icon, getString(R.string.youtube)) ).also { items ->
                        items.removeAll { !BuildConfig.SOCIAL_MEDIA_SUPPORT.contains(it.socialMediaTitle) }
                }
    }
        return items
}


    private fun isPackageInstalled(packageName: String, uriString: String, url: String, activity: Activity) {
        var uri = Uri.parse(uriString)

        try {
            val pm = requireContext().packageManager
            val applicationInfo: ApplicationInfo = pm.getApplicationInfo(packageName, 0)
            if (applicationInfo.enabled) {
                uri = Uri.parse("$uri")

            }
            val intent = Intent(Intent.ACTION_VIEW, uri)
            startActivity(intent)
        } catch (ignored: PackageManager.NameNotFoundException) {
            requireContext().openWebView(url, getString(R.string.web_view_title), activity)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}

