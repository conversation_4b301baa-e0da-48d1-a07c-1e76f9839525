package com.duaag.android.settings.fragments.account_settings.adjust_profile_info

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.databinding.FragmentChangeNameBinding
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.utils.NetworkChecker
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.addDifferentTypefacePlaceholder
import com.duaag.android.utils.containsSpaces
import com.duaag.android.utils.setOnSingleClickListener
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.duaag.android.utils.validateName
import kotlinx.coroutines.delay
import javax.inject.Inject

class ChangeNameFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingsViewModel by viewModels<SettingsViewModel>({ activity as SettingsActivity}) { viewModelFactory }
    private var _binding: FragmentChangeNameBinding? = null
    private val binding get() = _binding!!
    private var nameChangeListener: TextWatcher? = null


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    private fun toggleNextButton(name: String) {
        val isEmpty = name.isEmpty()
        val hasNameChanged = hasNameChanged(name, settingsViewModel.userProfile.value!!.firstName)
        binding.btnSave.isEnabled = !hasNameChanged && !isEmpty
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentChangeNameBinding.inflate(inflater)

        toggleNextButton(binding.name.text.toString())

        addDifferentTypefacePlaceholder(binding.adjustNameNote,
            stringWithAnnotation = R.string.change_name_in_six_months_an,
            stringWithPlaceholder = R.string.your_name_will_appear_in_your_profile,
            typefaceColor = R.color.title_primary,
            placeholder = R.string.app_name
        )


        binding.name.setText(settingsViewModel.userProfile.value!!.firstName, TextView.BufferType.EDITABLE)

        settingsViewModel.showChangeNameDialog.observe(viewLifecycleOwner) {
            showNameChangeDialog()
        }

        nameChangeListener()

        binding.btnSave.setOnSingleClickListener {
            val isValid = isNameValid(binding.name.text.toString())

            if(isValid) {
                if (NetworkChecker.isNetworkConnected(requireContext()))
                    settingsViewModel.showChangeNameDialog()
                else
                    ToastUtil.toast(R.string.no_internet_connection)
            }
        }

        return binding.root
    }

    fun isNameValid(name: String): Boolean {
        val isValid: Boolean
        val containsSpaces = containsSpaces(name)
        val isNameValid = validateName(name)

        if(containsSpaces) {
            isValid = false
            val error = getString(R.string.space_not_allowed_caption)
            handleNameValidation(error)
        } else if(!isNameValid) {
            isValid = false
            val error = getString(R.string.name_short_caption)
            handleNameValidation(error)
        } else {
            isValid = true
            clearNameValidationError()
        }

        return isValid
    }

    override fun onResume() {
        super.onResume()
        lifecycleScope.launchWhenResumed {
            delay(200)
            binding.name.setSelection(binding.name.length())
            binding.name.requestFocus()
            binding.name.showKeyboard()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.name.removeTextChangedListener(nameChangeListener)
        _binding = null
    }

    private fun hasNameChanged(name1: String, name2: String) : Boolean = name1 == name2

    private fun nameChangeListener() {
        nameChangeListener = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                clearNameValidationError()

                toggleNextButton(binding.name.text.toString())
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) { }
        }

        binding.name.addTextChangedListener(nameChangeListener)
    }

    private fun showNameChangeDialog() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(requireContext(), R.style.AlertDialogButtonTheme)
        builder.apply {
            setTitle(R.string.warning)
                .setMessage(R.string.you_can_change_your_name_once_do_you_want_to_continue)
                .setPositiveButton(R.string.continue_text) { dialog, _ ->
                    (requireActivity() as SettingsActivity).hasNameChanged = true
                    settingsViewModel.changeName(binding.name.text.toString(), settingsViewModel.userProfile.value!!)
                    dialog.cancel()
                    findNavController().popBackStack()
                }
                .setNegativeButton(R.string.cancel) { _, _ -> }
        }
        return builder.create().run {
            show()
        }
    }

    private fun handleNameValidation(text: String) {
        binding.nameDescription.text = text
        binding.nameDescription.visibility = View.VISIBLE
        binding.name.background = ContextCompat.getDrawable(requireContext(), R.drawable.error_corners_12dp)
        binding.name.requestFocus()
    }

    private fun clearNameValidationError() {
        binding.name.background = ContextCompat.getDrawable(requireContext(), R.drawable.edit_text_rounded_corners_12_dp)
        binding.nameDescription.visibility = View.GONE
    }

}