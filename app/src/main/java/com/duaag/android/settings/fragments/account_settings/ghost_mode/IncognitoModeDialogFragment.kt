package com.duaag.android.settings.fragments.account_settings.ghost_mode

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.duaag.android.R
import com.duaag.android.databinding.IncognitoModeLayoutBinding
import com.duaag.android.utils.setOnSingleClickListener


enum class IncognitoModeType(val value: String) {
    INFO("info"), ACTIVE("active"), DISABLE("disable"), ACTIVE_PREMIUM(
        "active_premium"
    )
}

class IncognitoModeDialogFragment : DialogFragment() {

    internal var listener: IncognitoModeDialogListener?=null

    interface IncognitoModeDialogListener {
        fun onButtonClick(dialog: DialogFragment, type: IncognitoModeType)
        fun onDismiss(dialog: DialogFragment, type: IncognitoModeType)
    }

    private var _binding: IncognitoModeLayoutBinding? = null
    private val binding get() = _binding


    override fun onAttach(context: Context) {
        super.onAttach(context)
        try {
            listener = parentFragment as IncognitoModeDialogListener
        } catch (e: ClassCastException) {
            throw ClassCastException(
                (context.toString() +
                        " must implement IncognitoModeDialogListener")
            )
        }
    }

    private var incognitoModeType: IncognitoModeType? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogStyle)
        incognitoModeType = when (arguments?.getString(
            EXTRAS_DIALOG_TYPE
        )) {
            IncognitoModeType.INFO.value -> IncognitoModeType.INFO
            IncognitoModeType.ACTIVE.value -> IncognitoModeType.ACTIVE
            IncognitoModeType.DISABLE.value -> IncognitoModeType.DISABLE
            IncognitoModeType.ACTIVE_PREMIUM.value -> IncognitoModeType.ACTIVE_PREMIUM
            else -> null
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = IncognitoModeLayoutBinding.inflate(inflater)
        if (dialog != null && dialog?.window != null) {
            dialog?.window?.setBackgroundDrawableResource(R.drawable.rounded_dialog_24_dp)
            dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        }
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.title?.text = arguments?.getString(EXTRAS_TITLE)
        binding?.description?.text = arguments?.getString(EXTRAS_DESC)
        binding?.button?.text = arguments?.getString(EXTRAS_BUTTON_TEXT)

        binding?.button?.setOnSingleClickListener {
            incognitoModeType?.let { it1 -> listener?.onButtonClick(this@IncognitoModeDialogFragment, it1) }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        incognitoModeType?.let { listener?.onDismiss(this@IncognitoModeDialogFragment, it) }
    }

    override fun onDetach() {
        super.onDetach()
        listener = null
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val EXTRAS_TITLE = "EXTRAS_TITLE"
        private const val EXTRAS_DESC = "EXTRAS_DESC"
        private const val EXTRAS_BUTTON_TEXT = "EXTRAS_BUTTON_TEXT"
        private const val EXTRAS_DIALOG_TYPE = "EXTRAS_DIALOG_TYPE"

        private const val TAG_ACTIVE = "GhostDialogActive"
        private const val TAG_DISABLE = "GhostDialogDisable"
        private const val TAG_INFO = "GhostDialogInfo"
        private const val TAG_ACTIVE_PREMIUM = "GhostDialogActivePremium"
        fun showInfo(
            context: Context,
            fragmentManager: FragmentManager
        ): IncognitoModeDialogFragment? {
            if (fragmentManager.findFragmentByTag(Companion::class.java.name) != null) return null

            val dialog = IncognitoModeDialogFragment()
            val bundle = Bundle()
            bundle.putString(EXTRAS_DIALOG_TYPE, IncognitoModeType.INFO.value)
            bundle.putString(EXTRAS_TITLE, context.getString(R.string.ghost_mode_active_inline))
            bundle.putString(EXTRAS_DESC, context.getString(R.string.ghost_mode_active_desc))
            bundle.putString(EXTRAS_BUTTON_TEXT, context.getString(R.string.got_it))


            dialog.arguments = bundle

            return dialog.apply {
                if (fragmentManager.findFragmentByTag(TAG_INFO) == null) {
                    show(fragmentManager, TAG_INFO)
                }
            }
        }



    fun showActivateGhost(
        context: Context,
        fragmentManager: FragmentManager
    ): IncognitoModeDialogFragment? {
        if (fragmentManager.findFragmentByTag(Companion::class.java.name) != null) return null
        val dialog = IncognitoModeDialogFragment()
        val bundle = Bundle()
        bundle.putString(EXTRAS_DIALOG_TYPE, IncognitoModeType.ACTIVE_PREMIUM.value)
        bundle.putString(EXTRAS_TITLE, context.getString(R.string.activate_ghost))
        bundle.putString(EXTRAS_DESC, context.getString(R.string.activate_ghost_desc))
        bundle.putString(EXTRAS_BUTTON_TEXT, context.getString(R.string.continue_text))

        dialog.arguments = bundle
        return dialog.apply {
            if (fragmentManager.findFragmentByTag(TAG_ACTIVE_PREMIUM) == null) {
                show(fragmentManager, TAG_ACTIVE_PREMIUM)
            }
        }
    }

    fun showActivate(
        context: Context,
        fragmentManager: FragmentManager
    ): IncognitoModeDialogFragment? {
        if (fragmentManager.findFragmentByTag(Companion::class.java.name) != null) return null
        val dialog = IncognitoModeDialogFragment()
        val bundle = Bundle()
        bundle.putString(EXTRAS_DIALOG_TYPE, IncognitoModeType.ACTIVE.value)
        bundle.putString(EXTRAS_TITLE, context.getString(R.string.activate_ghost))
        bundle.putString(EXTRAS_DESC, context.getString(R.string.activate_ghost_desc))
        bundle.putString(EXTRAS_BUTTON_TEXT, context.getString(R.string.continue_text))

        dialog.arguments = bundle
        return dialog.apply {
            if (fragmentManager.findFragmentByTag(TAG_ACTIVE) == null) {
                show(fragmentManager, TAG_ACTIVE)
            }
        }
    }

    fun showDisable(
        context: Context,
        fragmentManager: FragmentManager
    ): IncognitoModeDialogFragment? {
        if (fragmentManager.findFragmentByTag(Companion::class.java.name) != null) return null

        val dialog = IncognitoModeDialogFragment()
        val bundle = Bundle()
        bundle.putString(EXTRAS_DIALOG_TYPE, IncognitoModeType.DISABLE.value)
        bundle.putString(EXTRAS_TITLE, context.getString(R.string.disable_ghost))
        bundle.putString(EXTRAS_DESC, context.getString(R.string.deactivate_ghost_boost_case))
        bundle.putString(EXTRAS_BUTTON_TEXT, context.getString(R.string.turn_off_ghost_button))

        dialog.arguments = bundle
        return dialog.apply {
            if (fragmentManager.findFragmentByTag(TAG_DISABLE) == null) {
                show(fragmentManager, TAG_DISABLE)
            }
        }
    }
} }