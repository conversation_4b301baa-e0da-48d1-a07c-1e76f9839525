package com.duaag.android.settings.fragments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.duaag.android.R
import com.duaag.android.databinding.BottomSettingsItemBinding
import com.duaag.android.databinding.FremimPremiumLayoutBinding
import com.duaag.android.databinding.HeaderBinding
import com.duaag.android.databinding.RecyclerSettingsItemBinding
import com.duaag.android.home.viewmodels.HomeViewModel
import com.duaag.android.settings.models.SettingsItem
import com.duaag.android.utils.getStringPlaceHolder
import com.duaag.android.utils.setMargin


private const val ITEM_VIEW_TYPE_HEADER = 0
private const val ITEM_VIEW_TYPE_SECTION = 1
private const val ITEM_VIEW_TYPE_BOTTOM = 2
private const val ITEM_PREMIUM_FREEMIUM = 3

class SettingsAdapter(private val settingsItems: List<SettingsItem?>, private val homeViewModel: HomeViewModel, private val clickListener: SettingsSectionClickListener) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (settingsItems[holder.bindingAdapterPosition]?.isHeader) {
            false -> {
                when {
                    settingsItems[holder.bindingAdapterPosition]?.isBottom == true -> {
                        settingsItems[holder.bindingAdapterPosition]?.let { (holder as BottomViewHolder).bind(it, clickListener) }
                    }
                    settingsItems[holder.bindingAdapterPosition]?.isFreemiumOrPremium == true -> {
                        settingsItems[holder.bindingAdapterPosition]?.let { (holder as FreemiumPremiumViewHolder).bind(it, clickListener, homeViewModel) }
                    }
                    else -> {
                        settingsItems[holder.bindingAdapterPosition]?.let { (holder as SectionViewHolder).bind(it, clickListener) }
                    }
                }
            }
            true -> settingsItems[holder.bindingAdapterPosition]?.let { (holder as HeaderViewHolder).bind(it) }
            else -> {}
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_PREMIUM_FREEMIUM -> FreemiumPremiumViewHolder.from(parent)
            ITEM_VIEW_TYPE_HEADER -> HeaderViewHolder.from(parent)
            ITEM_VIEW_TYPE_SECTION -> SectionViewHolder.from(parent)
            ITEM_VIEW_TYPE_BOTTOM -> BottomViewHolder.from(parent)
            else -> throw ClassCastException("Unknown viewType $viewType")
        }

    }

    override fun getItemViewType(position: Int) =
        when {
            settingsItems[position]?.isFreemiumOrPremium == true -> {
                ITEM_PREMIUM_FREEMIUM
            }
            settingsItems[position]?.isHeader == true -> {
                ITEM_VIEW_TYPE_HEADER
            }
            settingsItems[position]?.isBottom == true -> {
                ITEM_VIEW_TYPE_BOTTOM
            }
            else -> {
                ITEM_VIEW_TYPE_SECTION
            }
        }


    override fun getItemCount() = settingsItems.size

    class BottomViewHolder private constructor(val binding: BottomSettingsItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: SettingsItem, clickListener: SettingsSectionClickListener) {
            binding.versionTextView.text = String.format(binding.root.context.getString(R.string.beta_version_1_0, item.title))
            binding.logOutButton.setOnClickListener {
                clickListener.onClick(item)
            }
        }

        companion object {
            fun from(parent: ViewGroup): RecyclerView.ViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding = BottomSettingsItemBinding.inflate(layoutInflater, parent, false)

                return BottomViewHolder(binding)
            }
        }
    }


    class FreemiumPremiumViewHolder private constructor(val binding: FremimPremiumLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: SettingsItem, clickListener: SettingsSectionClickListener, homeViewModel: HomeViewModel) {
            val context = binding.root.context
            val text = "${context.getString(R.string.app_name)} ${context.getString(R.string.dua_premium_2_an)}"

            if (homeViewModel.userProfile.value?.premiumType == null) {
                binding.fremiumItemId.root.visibility=View.VISIBLE
                binding.premiumItemId.root.visibility=View.GONE

                binding.fremiumItemId.duaPremiumTitle.text = text

                binding.fremiumItemId.root.setOnClickListener {
                    clickListener.onClick(item)
                }
            }else {
                binding.fremiumItemId.root.visibility=View.GONE
                binding.premiumItemId.root.visibility=View.VISIBLE

                binding.premiumItemId.duaPremiumTxt.setText(R.string.you_are_premium)
            }
        }

        companion object {
            fun from(parent: ViewGroup): RecyclerView.ViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding = FremimPremiumLayoutBinding.inflate(layoutInflater, parent, false)

                return FreemiumPremiumViewHolder(binding)
            }
        }
    }

    class HeaderViewHolder private constructor(val binding: HeaderBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: SettingsItem) {
            binding.textHeader.text = item.title
            if (item.position == 0) binding.textHeader.setMargin(top = 32f) else binding.textHeader.setMargin(top = 42f)
        }

        companion object {
            fun from(parent: ViewGroup): RecyclerView.ViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding = HeaderBinding.inflate(layoutInflater, parent, false)

                return HeaderViewHolder(binding)
            }
        }
    }

    class SectionViewHolder private constructor(val binding: RecyclerSettingsItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: SettingsItem, clickListener: SettingsSectionClickListener) {
            binding.header.text = item.title
            binding.image.setImageResource(if (item.isLink) R.drawable.ic_arrow_help else R.drawable.ic_chevron_right)
            binding.root.setOnClickListener {
                clickListener.onClick(item)
            }
        }

        companion object {
            fun from(parent: ViewGroup): RecyclerView.ViewHolder {
                val layoutInflater = LayoutInflater.from(parent.context)
                val binding = RecyclerSettingsItemBinding.inflate(layoutInflater, parent, false)

                return SectionViewHolder(binding)
            }
        }
    }


}

class SettingsSectionClickListener(val clickListener: (item:SettingsItem) -> Unit) {
    fun onClick(item:SettingsItem) = clickListener(item)
}