package com.duaag.android.settings.fragments

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.duaag.android.R
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.getPremiumTypeEventProperty
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.databinding.FragmentVerifyNewPasswordBinding
import com.duaag.android.exceptions.PasswordsDoNotMatchException
import com.duaag.android.settings.SettingsActivity
import com.duaag.android.settings.SettingsViewModel
import com.duaag.android.settings.checkForStrengthPassword
import com.duaag.android.settings.models.PasswordType
import com.duaag.android.signup.models.ChangePasswordAuthResult
import com.duaag.android.signup.models.FloatingActionButtonVisibility
import com.duaag.android.utils.onKeyboardDone
import com.duaag.android.utils.showKeyboard
import com.duaag.android.utils.updateLocale
import com.google.android.material.floatingactionbutton.FloatingActionButton
import javax.inject.Inject

class VerifyNewPasswordFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val settingViewModel: SettingsViewModel by viewModels({ activity as SettingsActivity }) {viewModelFactory}

    private var _binding: FragmentVerifyNewPasswordBinding? = null
    private val binding get() = _binding!!


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as SettingsActivity).settingsComponent.inject(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        _binding = FragmentVerifyNewPasswordBinding.inflate(inflater).apply {
            lifecycleOwner = viewLifecycleOwner
            viewModel = settingViewModel

        }
       binding.editTextPasswordVerify.addTextChangedListener(binding.let { TextWatcher1(it) })

        val eventPremiumType = getPremiumTypeEventProperty(settingViewModel.userProfile.value)
        sendClevertapEvent(
            ClevertapEventEnum.VERIFY_YOUR_NEW_PASSWORD,
            mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
        )

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        whenPasswordChange()
        registerCallback()
    }

    private fun registerCallback() {
        settingViewModel.changePasswordResulLiveData.observe(viewLifecycleOwner, Observer {
            when (it) {
                is ChangePasswordAuthResult.Loading -> {
                    binding.progressBar.visibility = View.VISIBLE
                }

                is ChangePasswordAuthResult.Success -> {
                    binding.progressBar.visibility = View.GONE
                    showPasswordChangedDialog()

                    val eventPremiumType = getPremiumTypeEventProperty(settingViewModel.userProfile.value)
                    sendClevertapEvent(
                        ClevertapEventEnum.PASSWORD_CHANGED,
                        mapOf(ClevertapEventPropertyEnum.PREMIUM_TYPE.propertyName to eventPremiumType)
                    )
                }

                is ChangePasswordAuthResult.Error -> {
                    binding.progressBar.visibility = View.GONE
                    when (it.e) {
                        is com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException -> {
                            handleVerifyError(R.string.too_many_times, View.VISIBLE)
                            logError(ErrorStatus.CHANGE_PASSWORD_LIMIT_EXCEEDED_EXCEPTION)
                        }
                        is PasswordsDoNotMatchException -> {
                            handleVerifyError(R.string.pass_do_not_match, View.VISIBLE)
                            logError(ErrorStatus.CHANGE_PASSWORD_PASSWORDS_DO_NOT_MATCH_EXCEPTION)
                        }
                        else ->  {
                            handleVerifyError(R.string.smthg_went_wrong, View.VISIBLE)
                            logError(ErrorStatus.CHANGE_PASSWORD)
                        }
                    }
                }
            }
        })
    }

    private fun handleVerifyError(message: Int, visibility: Int) {
        binding.passwordInfoText.visibility =  if(visibility == View.GONE) View.VISIBLE else View.GONE

        binding.errorCode.setText(message)
        binding.errorCode.visibility = visibility
        binding.editTextPasswordVerify.requestFocus()
        binding.editTextPasswordVerify.showKeyboard()
    }

    private fun clearVerifyError() {
        binding.passwordInfoText.visibility = View.VISIBLE
        binding.errorCode.visibility = View.GONE
    }

    private fun showPasswordChangedDialog() {
        val builder = AlertDialog.Builder(requireContext(), R.style.AlertDialogButtonTheme)
        builder.setCancelable(false)
        builder.apply {
            setTitle(R.string.password_changed)
                    .setMessage(R.string.password_changed_success)
                    .setPositiveButton(R.string.continue_text) { dialog, _ ->
                        findNavController().navigate(VerifyNewPasswordFragmentDirections.actionVerifyNewPasswordFragmentToSettingsFragment())
                    }
        }
        return builder.create().run {
            show()
        }
    }


    /** only for testing*/
    private fun whenPasswordChange() {
        binding.let {
//            it.editTextPassword.addTextChangedListener(TextWatcher1(it))
            it.editTextPasswordVerify.onKeyboardDone {
                if(activity?.findViewById<FloatingActionButton>(R.id.fab_next)?.isEnabled!!){
                    activity?.findViewById<FloatingActionButton>(R.id.fab_next)?.performClick()
                }
            }
        }

    }

    inner class TextWatcher1(private val it: FragmentVerifyNewPasswordBinding) : TextWatcher {
        override fun afterTextChanged(s: Editable?) {

            settingViewModel.newPasswordVerify.value?.let {
                clearVerifyError()

                val strength = checkForStrengthPassword(it)

                when(strength) {
                    PasswordType.NOT_ENOUGH -> {
                        settingViewModel.setFabVisibility(FloatingActionButtonVisibility.DISABLED)
                    }
                    else -> {
                        settingViewModel.setFabVisibility(FloatingActionButtonVisibility.SHOWN)
                    }
                }
            }
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onResume() {
        super.onResume()
        binding.editTextPasswordVerify.requestFocus()
        binding.editTextPasswordVerify.showKeyboard()
    }
}
