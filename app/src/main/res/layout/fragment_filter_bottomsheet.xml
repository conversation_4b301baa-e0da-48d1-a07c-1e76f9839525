<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:background="@color/background"
        android:fitsSystemWindows="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_close_black_24dp"
        app:toolbarId="@+id/toolbar">


        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:layout_scrollFlags="scroll|enterAlways|snap">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/toolbar_title"
                    style="@style/text_style_300"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left"
                    android:layout_marginEnd="64dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/filter_f"
                    android:textColor="@color/title_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.appbar.MaterialToolbar>


    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:fitsSystemWindows="true"
        android:overScrollMode="never"
        android:elevation="0dp"
        app:layout_constraintBottom_toTopOf="@id/apply_container"
        app:layout_constraintTop_toBottomOf="@id/appBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingVertical="24dp">

            <TextView
                android:id="@+id/basic_filters"
                style="@style/text_style_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:ellipsize="end"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:maxLines="1"
                android:text="@string/basic_filters_title"
                android:textColor="@color/title_primary"
                app:layout_constraintStart_toStartOf="@+id/location_container"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/age_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/filter_background"
                android:paddingHorizontal="20dp"
                android:paddingVertical="32dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/age_txt"
                app:layout_constraintWidth_max="345dp">

                <TextView
                    android:id="@+id/range_age_txt"
                    style="@style/text_style_300"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/between_caption_range"
                    android:textColor="@color/description_primary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.duaag.android.views.material_range_bar.RangeBar
                    android:id="@+id/ageRangeBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/range_age_txt"
                    app:mrb_barWeight="4dp"
                    app:mrb_connectingLineColor="@color/pink_500"
                    app:mrb_leftThumbColor="@color/pink_500"
                    app:mrb_pinRadius="0dp"
                    app:mrb_rangeBar="true"
                    app:mrb_rangeBarColor="@color/border"
                    app:mrb_rightThumbColor="@color/pink_500"
                    app:mrb_thumbSize="12dp"
                    app:mrb_tickEnd="60"
                    app:mrb_tickHeight="0dp"
                    app:mrb_tickStart="18" />
            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/radius_txt"
                style="@style/text_style_300"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/radius_f"
                android:textColor="@color/title_primary"
                app:layout_constraintStart_toStartOf="@+id/distance_container"
                app:layout_constraintTop_toBottomOf="@+id/age_container" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/distance_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/filter_background"
                android:paddingHorizontal="20dp"
                android:paddingVertical="32dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/radius_txt"
                app:layout_constraintWidth_max="345dp">

                <TextView
                    android:id="@+id/distance_desc"
                    style="@style/text_style_300"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/up_to_caption_km_away"
                    android:textColor="@color/description_primary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatSeekBar
                    android:id="@+id/radius_seek_bar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:max="150"
                    android:progressDrawable="@drawable/seek_bar"
                    android:thumb="@drawable/seek_thumb"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/distance_desc" />

                <View
                    android:id="@+id/divider"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="32dp"
                    android:background="@color/border"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/radius_seek_bar" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ext_range_container"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider">

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/extended_range"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:theme="@style/SwitchTheme"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/distance_extend_desc"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/distance_extend_desc"
                        style="@style/text_style_100"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="40dp"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:text="@string/expand_distance_when_run_out_caption"
                        android:textColor="@color/description_primary"
                        app:layout_constraintEnd_toStartOf="@id/extended_range"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/age_txt"
                style="@style/text_style_300"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:text="@string/age_f"
                android:textColor="@color/title_secondary"
                app:layout_constraintStart_toStartOf="@+id/age_container"
                app:layout_constraintTop_toBottomOf="@+id/location_container" />


            <TextView
                android:id="@+id/location_header"
                style="@style/text_style_300"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:ellipsize="end"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:maxLines="1"
                android:text="@string/location_f"
                android:textColor="@color/title_secondary"
                app:layout_constraintStart_toStartOf="@+id/location_container"
                app:layout_constraintTop_toBottomOf="@id/basic_filters" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/location_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/filter_background"
                android:paddingHorizontal="20dp"
                android:paddingVertical="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/location_header"
                app:layout_constraintWidth_max="345dp">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    app:layout_constraintBottom_toBottomOf="@+id/img_location"
                    app:layout_constraintStart_toEndOf="@+id/img_location"
                    app:layout_constraintTop_toTopOf="@+id/img_location">


                    <TextView
                        android:id="@+id/location_txt"
                        style="@style/text_style_100"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_normal"
                        android:text="@string/current_location"
                        android:textColor="@color/description_primary"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/chosen_location" />

                    <TextView
                        android:id="@+id/chosen_location"
                        style="@style/text_style_200"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:maxLines="1"
                        android:textAlignment="textStart"
                        android:textColor="@color/title_primary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <ImageView
                    android:id="@+id/imageView30"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layoutDirection="locale"
                    android:rotation="180"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_angle_left" />

                <ImageView
                    android:id="@+id/img_location"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_location_icon" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--
            <TextView
                android:id="@+id/ethnicity_header"
                style="@style/text_style_300"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:ellipsize="end"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:maxLines="1"
                android:text="@string/ethnicity_title"
                android:textColor="@color/title_secondary"
                app:layout_constraintStart_toStartOf="@+id/ethnicity_container"
                app:layout_constraintTop_toBottomOf="@+id/location_container" />

            <Button
                android:id="@+id/ethnicity_reset"
                style="@style/text_style_100"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/times"
                android:ellipsize="end"
                android:background="@color/transparent"
                android:textAllCaps="false"
                android:fontFamily="@font/tt_norms_pro_medium"
                android:maxLines="1"
                android:padding="8dp"
                android:text="@string/reset_label"
                android:textColor="@color/label_secondary"
                android:drawableTint="@color/label_secondary"
                app:layout_constraintBottom_toBottomOf="@+id/ethnicity_header"
                app:layout_constraintEnd_toEndOf="@+id/ethnicity_container"
                app:layout_constraintTop_toTopOf="@+id/ethnicity_header" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ethnicity_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/filter_background"

                android:paddingHorizontal="20dp"
                android:paddingVertical="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ethnicity_header"
                app:layout_constraintWidth_max="345dp">

                <TextView
                    android:id="@+id/textview_yours"
                    style="@style/text_style_200"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:elevation="1dp"
                    android:fontFamily="@font/tt_norms_pro_normal"
                    android:text="@string/yours_caption"
                    android:textColor="@color/title_secondary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textview_yours_value"
                    style="@style/text_style_200"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:elevation="1dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:textColor="@color/title_secondary"
                    app:layout_constraintStart_toEndOf="@id/textview_yours"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Albanian" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/other_ethnicities_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="25dp"
                    android:background="@drawable/filter_background"
                    android:foreground="?attr/selectableItemBackground"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="16dp"
                    app:layout_constraintBottom_toTopOf="@id/any_ethnicity_textview"
                    app:layout_constraintTop_toBottomOf="@id/textview_yours">

                    <TextView
                        android:id="@+id/ethnicities_placeholder"
                        style="@style/text_style_100"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="40dp"
                        android:elevation="1dp"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:text="@string/add_more_ethnicities_label"
                        android:textColor="@color/label_secondary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/ethnicities_add_btn"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/ethnicities_add_btn"
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:scaleType="center"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_plus_gray" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/any_ethnicity_textview"
                    style="@style/text_style_100"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="40dp"
                    android:elevation="1dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/show_any_ethnicity_caption"
                    android:textColor="@color/description_primary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/ethnicity_switch"
                    app:layout_constraintStart_toStartOf="parent" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/ethnicity_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:theme="@style/SwitchTheme"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="@+id/any_ethnicity_textview"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/any_ethnicity_textview" />

            </androidx.constraintlayout.widget.ConstraintLayout>

-->

            <View
                android:id="@+id/filters_divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="40dp"
                android:background="@color/border"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/distance_container" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:layout_marginHorizontal="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/filters_divider"
                app:layout_constraintWidth_max="345dp">

                <TextView
                    android:id="@+id/advanced_filters"
                    style="@style/text_style_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:maxLines="1"
                    android:text="@string/advanced_filters_title"
                    android:textColor="@color/title_primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    />

                <ImageView
                    android:id="@+id/advanced_filters_diamond"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/premium_badge_gradient"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintBottom_toBottomOf="@id/advanced_filters"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/advanced_filters"
                    app:layout_constraintTop_toTopOf="@id/advanced_filters" />

                <TextView
                    android:id="@+id/advanced_filters_desc"
                    style="@style/text_style_200"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:ellipsize="end"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/advanced_filters_premium_desc"
                    android:textColor="@color/description_primary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/advanced_filters"

                    />

                <TextView
                    android:id="@+id/verified_profiles_title"
                    style="@style/text_style_300"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/verified_profiles_title"
                    android:textColor="@color/title_primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/advanced_filters_desc" />

                <Button
                    android:id="@+id/verified_profiles_reset"
                    style="@style/text_style_100"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/transparent"
                    android:drawableEnd="@drawable/times"
                    android:drawableTint="@color/label_secondary"
                    android:ellipsize="end"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:text="@string/reset_label"
                    android:textAllCaps="false"
                    android:textColor="@color/label_secondary"
                    app:layout_constraintBottom_toBottomOf="@+id/verified_profiles_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/verified_profiles_title" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/verified_profiles_container"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/filter_background"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="32dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/verified_profiles_title"
                    app:layout_constraintWidth_max="345dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/non_verified_user_container"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.duaag.android.views.DuaButton
                            android:id="@+id/verify_profile_btn"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:stateListAnimator="@null"
                            android:text="@string/verify_profile_label"
                            app:buttonType="PrimaryWithState"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"

                            />

                        <TextView
                            android:id="@+id/non_verified_profiles_desc"
                            style="@style/text_style_100"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="32dp"
                            android:fontFamily="@font/tt_norms_pro_medium"
                            android:text="@string/get_verified_filter_caption"
                            android:textColor="@color/description_primary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/verify_profile_btn" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/verified_user_container"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:visibility="visible">

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/verified_profiles_switch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:theme="@style/SwitchTheme"
                            app:layout_constraintBottom_toBottomOf="@+id/verified_profiles_desc"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/verified_profiles_desc"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/verified_profiles_desc"
                            style="@style/text_style_100"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="40dp"
                            android:fontFamily="@font/tt_norms_pro_medium"
                            android:text="@string/show_verified_profiles_only_label"
                            android:textColor="@color/description_primary"
                            app:layout_constraintEnd_toStartOf="@id/verified_profiles_switch"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/height_title"
                    style="@style/text_style_300"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/height"
                    android:textColor="@color/title_primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/verified_profiles_container" />

                <Button
                    android:id="@+id/height_reset"
                    style="@style/text_style_100"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/transparent"
                    android:drawableEnd="@drawable/times"
                    android:drawableTint="@color/label_secondary"
                    android:ellipsize="end"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:text="@string/reset_label"
                    android:textAllCaps="false"
                    android:textColor="@color/label_secondary"
                    app:layout_constraintBottom_toBottomOf="@+id/height_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/height_title" />

                <include
                    android:id="@+id/add_info_height"
                    layout="@layout/add_profile_info_component"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/height_title"
                    app:layout_constraintWidth_max="345dp"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/height_container"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/filter_background"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="32dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/height_title"
                    app:layout_constraintWidth_max="345dp">

                    <TextView
                        android:id="@+id/height_desc"
                        style="@style/text_style_300"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/tt_norms_pro_medium"
                        android:text="@string/any_height_caption"
                        android:textColor="@color/description_primary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.duaag.android.views.material_range_bar.RangeBar
                        android:id="@+id/height_range_bar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/height_desc"
                        app:mrb_barWeight="4dp"
                        app:mrb_connectingLineColor="@color/pink_500"
                        app:mrb_leftThumbColor="@color/pink_500"
                        app:mrb_pinRadius="0dp"
                        app:mrb_rangeBar="true"
                        app:mrb_rangeBarColor="@color/border"
                        app:mrb_rightThumbColor="@color/pink_500"
                        app:mrb_thumbSize="12dp"
                        app:mrb_tickEnd="250"
                        app:mrb_tickHeight="0dp"
                        app:mrb_tickStart="80" />

                    <View
                        android:id="@+id/height_blocker"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:clickable="true"
                        android:focusableInTouchMode="true"
                        android:focusable="true"
                        app:layout_constraintTop_toTopOf="@id/height_range_bar"
                        app:layout_constraintStart_toStartOf="@+id/height_range_bar"
                        app:layout_constraintEnd_toEndOf="@id/height_range_bar"
                        app:layout_constraintBottom_toBottomOf="@id/height_range_bar"/>

                    <View
                        android:id="@+id/height_divider"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginTop="32dp"
                        android:background="@color/border"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/height_range_bar" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/ext_height_container"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/height_divider">

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/height_switch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:theme="@style/SwitchTheme"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/extend_height_desc"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/extend_height_desc"
                            style="@style/text_style_100"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="40dp"
                            android:fontFamily="@font/tt_norms_pro_medium"
                            android:text="@string/show_profiles_run_out_caption"
                            android:textColor="@color/description_primary"
                            app:layout_constraintEnd_toStartOf="@id/height_switch"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/looking_for_barrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="add_info_height,height_container" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/languages_barrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="add_info_looking_for,add_looking_for_component" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/religion_barrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="add_info_languages,add_languages_component" />

                <TextView
                    android:id="@+id/looking_for_title"
                    style="@style/text_style_300"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/looking_for"
                    android:textColor="@color/title_primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/looking_for_barrier" />

                <Button
                    android:id="@+id/looking_for_reset"
                    style="@style/text_style_100"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/transparent"
                    android:drawableEnd="@drawable/times"
                    android:drawableTint="@color/label_secondary"
                    android:ellipsize="end"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:text="@string/reset_label"
                    android:textAllCaps="false"
                    android:textColor="@color/label_secondary"
                    app:layout_constraintBottom_toBottomOf="@+id/looking_for_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/looking_for_title" />

                <include
                    android:id="@+id/add_info_looking_for"
                    layout="@layout/add_profile_info_component"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/looking_for_title"
                    app:layout_constraintWidth_max="345dp"
                    tools:visibility="visible" />

                <include
                    android:id="@+id/add_looking_for_component"
                    layout="@layout/add_info_component"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:layout_constraintTop_toBottomOf="@id/looking_for_title" />

                <TextView
                    android:id="@+id/languages_title"
                    style="@style/text_style_300"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/languages"
                    android:textColor="@color/title_primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/languages_barrier" />

                <Button
                    android:id="@+id/language_reset"
                    style="@style/text_style_100"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/transparent"
                    android:drawableEnd="@drawable/times"
                    android:drawableTint="@color/label_secondary"
                    android:ellipsize="end"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:text="@string/reset_label"
                    android:textAllCaps="false"
                    android:textColor="@color/label_secondary"
                    app:layout_constraintBottom_toBottomOf="@+id/languages_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/languages_title" />

                <include
                    android:id="@+id/add_info_languages"
                    layout="@layout/add_profile_info_component"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/languages_title"
                    app:layout_constraintWidth_max="345dp"
                    tools:visibility="visible" />

                <include
                    android:id="@+id/add_languages_component"
                    layout="@layout/add_info_component"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:layout_constraintTop_toBottomOf="@id/languages_title" />

                <TextView
                    android:id="@+id/religion_title"
                    style="@style/text_style_300"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:text="@string/religion"
                    android:textColor="@color/title_primary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/religion_barrier" />

                <Button
                    android:id="@+id/religion_reset"
                    style="@style/text_style_100"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/transparent"
                    android:drawableEnd="@drawable/times"
                    android:drawableTint="@color/label_secondary"
                    android:ellipsize="end"
                    android:fontFamily="@font/tt_norms_pro_medium"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:text="@string/reset_label"
                    android:textAllCaps="false"
                    android:textColor="@color/label_secondary"
                    app:layout_constraintBottom_toBottomOf="@+id/religion_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/religion_title" />

                <include
                    android:id="@+id/add_info_religion"
                    layout="@layout/add_profile_info_component"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/religion_title"
                    app:layout_constraintWidth_max="345dp"
                    tools:visibility="visible" />

                <include
                    android:id="@+id/add_religion_component"
                    layout="@layout/add_info_component"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:layout_constraintTop_toBottomOf="@id/religion_title" />
            </androidx.constraintlayout.widget.ConstraintLayout>



        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <FrameLayout
        android:id="@+id/apply_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" >

        <com.duaag.android.views.DuaButton
            android:id="@+id/apply_btn"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginVertical="12dp"
            android:layout_marginHorizontal="32dp"
            android:stateListAnimator="@null"
            android:text="@string/apply_label"
            app:buttonType="PrimaryWithState" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:indeterminate="true"
            android:theme="@style/ProgressBarTheme"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible"/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
