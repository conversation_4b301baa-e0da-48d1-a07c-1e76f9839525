<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="viewModel"
            type="com.duaag.android.settings.fragments.notifications.NotificationSettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.duaag.android.settings.fragments.notifications.NotificationSettingsFragment">

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="gone"
            android:theme="@style/ProgressBarTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/list"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="25dp"
            android:layout_marginRight="16dp"
            android:overScrollMode="never"
            tools:listitem="@layout/recyclerview_notification_settings_item"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/description"
            tools:itemCount="1" />

        <TextView
            android:id="@+id/description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="22dp"
            android:layout_marginEnd="16dp"
            android:textColor="@color/description_primary"
            android:fontFamily="@font/tt_norms_pro_normal"
            style="@style/text_style_100"
            android:textAlignment="center"
            android:text="@string/manage_notifications_that_you_want_to_recieve"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />



    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

