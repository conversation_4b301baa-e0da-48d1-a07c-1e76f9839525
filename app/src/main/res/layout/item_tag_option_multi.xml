<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:animateLayoutChanges="true"
    android:background="@drawable/border_background_16_dp">

    <TextView
        android:id="@+id/tagText"
        style="@style/text_style_200"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="16dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tagCheckbox"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Yes, I have children" />

    <CheckBox
        android:id="@+id/tagCheckbox"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginStart="16dp"
        android:importantForAccessibility="no"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/radio_unselected" />

</androidx.constraintlayout.widget.ConstraintLayout>