<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        style="@style/text_style_75"
        android:id="@+id/txt_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:paddingBottom="12dp"
        app:layout_constraintBottom_toTopOf="@id/txt_message"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/description_secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/txt_message"
        android:paddingStart="14dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="9dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        app:layout_constraintTop_toBottomOf="@+id/txt_date"
        app:layout_constraintEnd_toEndOf="parent"
        tools:background="@drawable/background_sent_message">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:src="@drawable/ic_info_spam_link_gray"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"/>

        <TextView
            android:id="@+id/title"
            style="@style/text_style_200"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_marginStart="14dp"
            android:fontFamily="@font/tt_norms_pro_bold"
            android:textColor="@color/title_primary"
            android:maxWidth="274dip"
            android:text="@string/spam_alert_title"
            app:layout_constraintStart_toEndOf="@id/icon"
            app:layout_constraintTop_toTopOf="parent"
            />
        <TextView
            android:id="@+id/content_txt"
            style="@style/text_style_200"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom  ="@id/title"
            android:layout_alignParentStart="true"
            android:layout_marginEnd="12dp"
            android:maxWidth="274dip"
            android:autoLink="web"
            android:clickable="false"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColorLink="@color/pink_500"
            android:text="@string/spam_alert_link_sharing_blocked"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <TextView
            android:id="@+id/learnmore_txt"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/title"
            android:layout_alignParentStart="true"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            android:text="@string/learn_more"
            app:layout_constraintTop_toBottomOf="@id/content_txt"
            app:layout_constraintStart_toStartOf="@id/title"
            />

        <TextView
            android:id="@+id/timeStamp"
            style="@style/text_style_50"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/content_txt"
            android:layout_marginEnd="8dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            tools:text="12:42"
            app:layout_constraintTop_toBottomOf="@id/learnmore_txt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
