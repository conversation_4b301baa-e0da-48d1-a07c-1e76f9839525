<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    android:id="@+id/scroll_view"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:overScrollMode="never"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".login.fragments.SignInEmailFragment">

        <TextView
            android:id="@+id/email_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="24dp"
            android:text="@string/email_"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:textColor="@color/title_primary"
            style="@style/text_style_100"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/email_input"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:hint="@string/enter_email_address"
            android:imeOptions="actionNext"
            android:importantForAutofill="yes"
            android:autofillHints="emailAddress"
            android:inputType="textEmailAddress"
            android:paddingStart="16dp"
            android:textColor="@color/title_primary"
            android:textCursorDrawable="@drawable/ic_typing_indicator"
            android:textColorHint="@color/gray_200"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/email_label">
        </EditText>

        <TextView
            android:id="@+id/email_error_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/email_already_in_use"
            android:textColor="@color/red_500"
            style="@style/text_style_100"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/email_input" />

        <TextView
            android:id="@+id/password_label"
            style="@style/text_style_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="24dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/password"
            android:textColor="@color/title_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/forgot"
            app:layout_constraintTop_toBottomOf="@id/email_input" />

        <Button
            android:id="@+id/forgot"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingHorizontal="24dp"
            android:paddingTop="24dp"
            android:layout_marginBottom="4dp"
            android:elevation="2dp"
            android:clickable="true"
            android:focusable="true"
            android:background="@color/transparent"
            android:textAllCaps="false"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/forgot_your_password"
            android:textColor="@color/gray_200"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/password_input_layout"
            app:layout_constraintTop_toBottomOf="@id/email_input" />


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/password_input_layout"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:background="@drawable/edit_text_rounded_corners_12_dp"
            android:layout_marginTop="4dp"
            android:layout_marginHorizontal="24dp"
            app:passwordToggleDrawable="@drawable/ic_password_selector"
            app:passwordToggleEnabled="true"
            app:passwordToggleTint="@color/gray_200"
            app:hintEnabled="false"
            app:hintAnimationEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password_label">

            <EditText
                android:id="@+id/password_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="16dp"
                android:background="#00000000"
                android:fontFamily="@font/tt_norms_pro_normal"
                android:hint="@string/enter_password"
                android:imeOptions="actionDone"
                android:importantForAutofill="yes"
                android:autofillHints="password"
                android:inputType="textPassword"
                android:textColor="@color/title_primary"
                android:textColorHint="@color/gray_200"
                android:textCursorDrawable="@drawable/ic_typing_indicator"
                style="@style/text_style_100" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/password_error_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/tt_norms_pro_normal"
            android:text="@string/boost_profile_in_processing"
            android:textColor="@color/red_500"
            style="@style/text_style_100"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password_input_layout" />

        <com.duaag.android.views.DuaButton
            android:id="@+id/sign_in_button"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="32dp"
            android:clickable="false"
            android:enabled="false"
            android:stateListAnimator="@null"
            android:text="@string/continue_text"
            app:buttonType="PrimaryWithState"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password_error_text"
            app:layout_constraintVertical_bias="1.0" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>

