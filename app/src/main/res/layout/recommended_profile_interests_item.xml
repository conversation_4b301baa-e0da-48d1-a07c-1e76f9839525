<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/selectableItemBackground"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="20dp"
    app:cardElevation="0dp"
    app:cardPreventCornerOverlap="false"
    app:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/user_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:background="@drawable/card_item_gradient_background">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/dislike_button"
            android:layout_width="@dimen/home_screen_button_dimension"
            android:layout_height="@dimen/home_screen_button_dimension"
            android:layout_marginTop="56dp"
            android:layout_marginEnd="23dp"
            android:alpha="0"
            android:background="@android:color/transparent"
            android:src="@drawable/ic_dislike"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:alpha="1" />

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/super_like_button"
            android:layout_width="55dp"
            android:layout_height="55dp"
            android:layout_margin="16dp"
            android:visibility="gone"
            android:alpha="0"
            android:background="@android:color/transparent"
            android:src="@drawable/ic_instachat"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:alpha="1" />

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/like_button"
            android:layout_width="@dimen/home_screen_button_dimension"
            android:layout_height="@dimen/home_screen_button_dimension"
            android:layout_marginStart="23dp"
            android:layout_marginTop="78dp"
            android:alpha="0"
            android:visibility="gone"
            android:background="@android:color/transparent"
            android:src="@drawable/ic_like_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:alpha="1" />

        <View
            android:id="@+id/background_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="1.5dp"
            android:background="@drawable/card_item_modal_popup_20_dp"
            tools:layout_editor_absoluteX="2dp"
            tools:layout_editor_absoluteY="2dp" />

        <TextView
            android:id="@+id/duaQuestionText"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/dua_question_gradient_background"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:paddingHorizontal="8dp"
            android:paddingVertical="7dp"
            android:text="#duaquestion"
            android:textColor="@color/gray_75"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/titleText"
            style="@style/text_style_600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="32dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/duaQuestionText"
            android:text="@string/interests_card" />
        <TextView
            android:id="@+id/selected_tags_txt"
            style="@style/text_style_100"
            tools:text="Selected 1 of 9"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@id/titleText"
            app:layout_constraintStart_toStartOf="@id/titleText"
            app:layout_constraintEnd_toEndOf="@id/titleText"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:textColor="@color/title_secondary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipToPadding="false"
            app:layout_constrainedHeight="true"
            android:layout_marginHorizontal="24dp"
            android:layout_marginBottom="16dp"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/selected_tags_txt"
            app:layout_constraintBottom_toTopOf="@+id/see_more_txt"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/see_more_txt"
            style="@style/text_style_100"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/tt_norms_pro_medium"
            android:text="See more"
            android:padding="8dp"
            android:textColor="@color/title_primary"
            app:layout_constraintBottom_toTopOf="@id/btn_continue"
            app:layout_constraintEnd_toEndOf="@id/btn_continue"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="@id/btn_continue"
            app:drawableEndCompat="@drawable/ic_arrow_down" />
        <com.duaag.android.views.DuaButton
            android:id="@+id/btn_continue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:stateListAnimator="@null"
            android:text="@string/continue_button_reward"
            android:visibility="invisible"
            app:buttonType="Primary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/recyclerView"
            app:layout_constraintStart_toStartOf="@+id/recyclerView"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
