<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/notNowButton"
        style="@style/text_style_200"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:layout_marginEnd="24dp"
        android:padding="8dp"
        android:background="?selectableItemBackgroundBorderless"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/not_now"
        android:textAlignment="center"
        android:textColor="@color/title_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/imageView15"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ruler_illustration"
        android:layout_marginTop="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/notNowButton" />




    <TextView
        android:id="@+id/new_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/feature_profiles_new_indicator"
        android:background="@drawable/rounded_stroke_10_dp"
        style="@style/text_style_100"
        android:fontFamily="@font/tt_norms_pro_demibold"
        android:textColor="@color/title_primary"
        android:layout_marginTop="24dp"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView15" />

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/text_style_600"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:textColor="@color/title_primary"
        android:textAlignment="center"
        android:text="@string/height_promo_title"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/new_text" />

    <TextView
        android:id="@+id/description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/height_promo_desc"
        style="@style/text_style_200"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:textColor="@color/description_primary"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title" />

    <com.duaag.android.views.DuaButton
        android:id="@+id/action_btn"
        android:layout_width="0dp"
        android:layout_height="52dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="40dp"
        android:layout_marginTop="40dp"
        android:stateListAnimator="@null"
        android:text="@string/add_height_label"
        app:buttonType="Primary"
        app:layout_constraintTop_toBottomOf="@id/description"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>