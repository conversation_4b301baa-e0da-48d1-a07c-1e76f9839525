<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/modal_popup"
    android:layout_height="wrap_content"
    android:layout_marginStart="31dp"
    android:layout_marginEnd="30dp">

    <TextView
        android:id="@+id/textView40"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="2dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/report_fishy"
        android:textAlignment="viewStart"
        android:textColor="@color/description_primary"
        style="@style/text_style_200"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView41" />

    <TextView
        android:id="@+id/textView41"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="18dp"
        android:fontFamily="@font/tt_norms_pro_normal"
        android:text="@string/report"
        android:textAlignment="viewStart"
        android:textColor="@color/title_primary"
        style="@style/text_style_300"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/wrong_gender_btn"
        style="@style/buttonReportStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="-10dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/wrong_gender"
        android:textAllCaps="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/fake_profile_btn" />

    <Button
        android:id="@+id/report_inapropriate_btn"
        style="@style/buttonReportStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="-10dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:paddingTop="-10dp"
        android:paddingBottom="-10dp"
        android:text="@string/report_inappropriate"
        android:textAllCaps="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wrong_gender_btn" />

    <Button
        android:id="@+id/other_reasons_btn"
        style="@style/buttonReportStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="-10dp"
        android:layout_marginBottom="10dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/other_unm"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/report_inapropriate_btn" />

    <Button
        android:id="@+id/fake_profile_btn"
        style="@style/buttonReportStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="14dp"
        android:fontFamily="@font/tt_norms_pro_medium"
        android:text="@string/report_fake"
        android:textAllCaps="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView40" />

</androidx.constraintlayout.widget.ConstraintLayout>