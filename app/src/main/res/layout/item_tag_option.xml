<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    android:paddingBottom="12dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:layout_marginBottom="8dp"
    android:background="@drawable/tag_option_background">

    <TextView
        android:id="@+id/tagText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceBody1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/selector_container" 
        android:layout_marginEnd="8dp"
        tools:text="Option Text Long Example" />

    <FrameLayout
        android:id="@+id/selector_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatRadioButton
            android:id="@+id/tagRadioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:focusable="false"
            android:visibility="gone"  />

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/tagCheckbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:focusable="false"
            android:visibility="gone" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 